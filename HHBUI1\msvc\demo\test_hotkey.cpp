﻿#include "hhbui.h"
using namespace HHBUI;

void testhotkey(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 500, 420, L"hello Hotkey", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);
	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);


	auto Hotkey1 = new UIHotkey(window, 100, 80, 150, 35);
	Hotkey1->SetColor(color_background, UIColor(230, 231, 232, 255));
	Hotkey1->SetColor(color_border, UIColor(194, 195, 201, 255));
	Hotkey1->SetColor(color_focus, UIColor(0, 108, 190, 255));

	auto Hotkey2 = new UIHotkey(window, 100, 170, 150, 35);
	Hotkey2->SetColor(color_border, UIColor(255, 87, 34, 255));
	Hotkey2->SetColor(color_focus, UIColor(255, 184, 0, 255));
	Hotkey2->SetkeyW(L"ctrl+shift+t");
	Hotkey2->SetRadius(16, 16, 16, 16);


	window->Show();
}