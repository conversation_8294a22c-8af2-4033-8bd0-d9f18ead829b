﻿#include "pch.h"
#include "datebox.h"
#include <common/winapi.h>
#include "datebox_lunarday.h"
#include <common/memory.h>
#include <sstream>

HHBUI::UIDatebox::UIDatebox(UIBase *hParent, INT x, INT y, INT width, INT height, INT dwStyle, INT dwStyleEx, INT nID)
{
	InitSubControl(hParent, x, y, width, height, L"form-datebox", NULL, (dwStyle == 0 ? eos_textoffset : dwStyle), dwStyleEx, nID, DT_LEFT | DT_VCENTER | DT_SINGLELINE | DT_WORD_ELLIPSIS);
	p_data.state = 0;
	std::time_t t = std::time(nullptr);  // 获取当前时间
	std::tm ptm{};
	localtime_s(&ptm, &t);
	p_data.Year = ptm.tm_year + 1900;
	p_data.Mon = ptm.tm_mon + 1;
	p_data.Mday = ptm.tm_mday;
	p_data.to_Year = p_data.Year;
	p_data.to_Mon = p_data.Mon;
	p_data.to_Mday = p_data.Mday;
	p_data.Wday = ptm.tm_wday == 0 ? 7 : ptm.tm_wday; // Sunday = 7
	p_data.nCalendar = 1;
	p_data.nSohwType = 0;

	p_data.hFont = new UIFont(NULL, 14);
	p_data.hFontSe = new UIFont(NULL, 14, HHBUI::FONT_STYLE_BOLD);
	p_data.hFont_Calendar = new UIFont(NULL, 11);
	SetFontFromFamily(NULL, 14);
	UIColor dwColor;
	GetColor(color_text_normal, dwColor);
	p_data.hBrush = new UIBrush(dwColor);
	p_data.TmpBrush = new UIBrush();
	p_data.TitleBrush = new UIBrush();

	p_data.Items = ExMemAlloc(42 * (24 + sizeof(size_t)));
	update(1);
}

void HHBUI::UIDatebox::Popup()
{
	if (GetTickCount64() - p_data.nProcessTime < 200)
	{
		EndMenu();
		p_data.nProcessTime = GetTickCount64();
		return;
	}
	else if (IsVisible() && p_data.state != 1)
	{
		if (p_data.pWnd)
		{
			delete p_data.pWnd;
			p_data.pWnd = nullptr;
			return;
		}
		p_data.pWnd = new UIWnd(0, 0, 0, 0, NULL, WS_BORDER | WS_SYSMENU | WS_POPUP, WS_EX_TOPMOST | WS_EX_TOOLWINDOW | WS_EX_LAYERED,
			UISTYLE_NOINHERITBKG | UISTYLE_NOCAPTIONTOPMOST | UISTYLE_NOTITLEBAR | UISTYLE_POPUPWINDOW | UISTYLE_NOSHADOW | EWS_COMBOWINDOW, m_data.pWnd->GethWnd(), NULL, (size_t)this, OnWndMsgDateProc);

		UIColor background; UIColor border;
		GetColor(color_background, background);
		if (background.empty())
			background = UIColor(255, 255, 255, 255);
		p_data.pWnd->SetBackgColor(background);
		GetColor(color_border, border);
		p_data.pWnd->SetBorderColor(border, 1);

		if (!m_data.radius.empty())
		{
			p_data.pWnd->SetRadius((m_data.radius.left + m_data.radius.top + m_data.radius.right + m_data.radius.bottom) / 4.0f);
		}
		RECT tmp{};ExRectF unknown{};
		GetWindowRect(m_data.pWnd->GethWnd(), &tmp);

		GetRect(unknown, grt_window, TRUE);
		tmp.left += unknown.left - 1;
		tmp.top += unknown.bottom + 2;
		tmp.right = UIEngine::ScaleValue(354);
		tmp.bottom = UIEngine::ScaleValue(354);
		RECT screen, desk{};
		HHBUI::UIWinApi::GetWndScreenRectEx(m_data.pWnd->GethWnd(), screen, desk);

		if (tmp.top + tmp.bottom - screen.bottom > 0)//超出屏幕底部
		{
			unknown.bottom = m_data.Frame.bottom - m_data.Frame.top;
			tmp.top -= tmp.bottom + unknown.bottom + UIEngine::ScaleValue(4);
		}
		if (tmp.left - screen.right - screen.left + tmp.right + 20 > 0)//超出屏幕右边
		{
			tmp.left = screen.right - tmp.right;
		}
		if (tmp.left < 0)//超出屏幕左边
		{
			tmp.left = 0;
		}
		p_data.pWnd->Move(tmp.left, tmp.top, tmp.right, tmp.bottom);
		p_data.nSohwType = 0;
		p_data.state = 1;

		p_data.dblistview = new UIListView(p_data.pWnd, 15, 45, 340, 300, L"form-list-date", eos_elvs_verticallist, eos_ex_focusable, 0, -1);
		p_data.dblistview->SetPaddingClient(CW_USEDEFAULT, 23);
		p_data.dblistview->SetMsgProc(OnCustomDraw_Proc);
		p_data.dblistview->SetlParam((size_t)this);
		p_data.dblistview->SetItemWidth(44);
		p_data.dblistview->SetItemHeight(44);
		p_data.dblistview->SetItemSplitWidth(2);
		p_data.dblistview->SetItemSplitHeight(1);


		p_data.listview = new UIListView(p_data.pWnd, 15, 65, 340, 300, L"form-list-date", eos_hidden | eos_elvs_verticallist, eos_ex_focusable, 0, -1);
		p_data.listview->SetMsgProc(OnCustomDraw_Proc);
		p_data.listview->SetlParam((size_t)this);
		p_data.listview->SetItemCount(12);
		p_data.listview->SetItemWidth(74);
		p_data.listview->SetItemHeight(74);
		p_data.listview->SetItemSplitWidth(4);
		p_data.listview->SetItemSplitHeight(4);

		p_data.BtnByTitle = new UIStatic(p_data.pWnd, 25, 8, 160, 30, L"", 0, eos_ex_focusable | eos_ex_customdraw, 0, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
		p_data.BtnByTitle->SetMsgProc(OnBtnDraw_Proc);
		p_data.BtnByTitle->SetlParam((size_t)this);
		p_data.BtnByTitle->SetCursor(IDC_HAND);
		p_data.BtnByTitle->SetColor(color_text_normal, UIColor(30, 30, 30, 150));
		p_data.BtnByTitle->SetFontFromFamily(NULL, 14);
		p_data.BtnByTitle->SetTipsText(L"到月视图");

		p_data.BtnByReset = new UIStatic(p_data.pWnd, 205, 8, 30, 30, L"↻", 0, eos_ex_focusable | eos_ex_customdraw);
		p_data.BtnByReset->SetMsgProc(OnBtnDraw_Proc);
		p_data.BtnByReset->SetlParam((size_t)this);
		p_data.BtnByReset->SetCursor(IDC_HAND);
		p_data.BtnByReset->SetColor(color_text_normal, UIColor(30, 30, 30, 150));
		p_data.BtnByReset->SetFontFromFamily(NULL, 18);
		p_data.BtnByReset->SetTipsText(L"到今天");

		p_data.BtnByUpper = new UIStatic(p_data.pWnd, 252, 8, 30, 30, L"▴", 0, eos_ex_focusable | eos_ex_customdraw);
		p_data.BtnByUpper->SetMsgProc(OnBtnDraw_Proc);
		p_data.BtnByUpper->SetlParam((size_t)this);
		p_data.BtnByUpper->SetCursor(IDC_HAND);
		p_data.BtnByUpper->SetColor(color_text_normal, UIColor(30, 30, 30, 150));
		p_data.BtnByUpper->SetFontFromFamily(NULL, 18);
		p_data.BtnByUpper->SetTipsText(L"到上个月");

		p_data.BtnByDown = new UIStatic(p_data.pWnd, 297, 8, 30, 30, L"▾", 0, eos_ex_focusable | eos_ex_customdraw);
		p_data.BtnByDown->SetMsgProc(OnBtnDraw_Proc);
		p_data.BtnByDown->SetlParam((size_t)this);
		p_data.BtnByDown->SetCursor(IDC_HAND);
		p_data.BtnByDown->SetColor(color_text_normal, UIColor(30, 30, 30, 150));
		p_data.BtnByDown->SetFontFromFamily(NULL, 18);
		p_data.BtnByDown->SetTipsText(L"到下个月");

		upTodate(p_data.Year, p_data.Mon);
		p_data.pWnd->Show();
		p_data.fAngle = 180.f;
		Redraw();
		//UIAnimation::Start(this, 0, 180, 0, 0, AniEffect::Default, 15, 0, 1, 1);
	}
}

void HHBUI::UIDatebox::Close()
{
	if (p_data.pWnd)
	{
		delete p_data.pWnd;
		p_data.pWnd = nullptr;
	}
}

BOOL HHBUI::UIDatebox::IsPopup()
{
	return p_data.pWnd != nullptr;
}

void HHBUI::UIDatebox::SetColourArrow(UIColor dwColor)
{
	if (p_data.hBrush)
		delete p_data.hBrush;
	p_data.hBrush = new UIBrush(dwColor);
}

void HHBUI::UIDatebox::SetDate(int nYear, int nMon, int days)
{
	if (nYear < 0)
	{
		return;
	}
	if (nMon < 0 || nMon > 12)
	{
		return;
	}
	if (days < 0 || days > 31)
	{
		return;
	}

	int Wday = get_WeekOfDate(nYear, nMon, days);
	p_data.Year = nYear;
	p_data.Mon = nMon;
	p_data.Mday = days;
	p_data.Wday = Wday;
	update(1);
}

void HHBUI::UIDatebox::GetDate(int& nYear, int& nMon, int& days, int& Wday)
{
	nYear = p_data.Year;
	nMon = p_data.Mon;
	days = p_data.Mday;
	Wday = p_data.Wday;
}

LRESULT HHBUI::UIDatebox::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_DESTROY)
	{
		LPVOID pOld = p_data.Items;
		if (pOld != 0)
		{
			for (int i = 0; i < 42; i++)
			{
				LONG_PTR offset = i * (24 + sizeof(size_t));
				LPVOID Calendar = (LPVOID)__get(pOld, offset + 20);
				if (Calendar)
				{
					ExMemFree(Calendar);
				}
			}
			ExMemFree(pOld);
		}
		delete p_data.hBrush;
		delete p_data.TmpBrush;
		delete p_data.TitleBrush;
		delete p_data.hFont;
		delete p_data.hFontSe;
		delete p_data.hFont_Calendar;
	}
	else if (uMsg == WM_EX_EASING)
	{
		auto easing = (HHBUI::info_Animation*)lParam;
		p_data.fAngle = easing->nCurrentX;
		Redraw();
	}
	else if (uMsg == WM_LBUTTONDOWN)
	{
		Popup();
	}
	return S_OK;
}

LRESULT HHBUI::UIDatebox::OnWndMsgDateProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIDatebox*)window->GetlParam();
	if (uMsg == WM_DESTROY)
	{
		obj->p_data.nProcessTime = GetTickCount64();
		obj->SetState(state_hover | state_down, TRUE);
		obj->p_data.pWnd = nullptr;
		obj->p_data.state = 0;
		obj->p_data.fAngle = 0.f;
		obj->Redraw();
		//UIAnimation::Start(obj, 180, 0, 0, 0, AniEffect::Default, 10, 0, 1, 1);

	}
	else if (uMsg == 11112) {
		LPVOID lpItems = obj->p_data.Items;
		LONG_PTR offset = (wParam - 1) * (24 + sizeof(size_t));
		obj->p_data.Year = __get_int(lpItems, offset + 4);
		obj->p_data.Mon = __get_int(lpItems, offset + 8);
		obj->p_data.Mday = __get_int(lpItems, offset + 12);
		obj->p_data.Wday = __get_int(lpItems, offset + 16);
		obj->update(1);
		obj->DispatchNotify(WMM_DBN_DATETIME, obj->p_data.Year, MAKELONG(obj->p_data.Mon, obj->p_data.Mday));
		PostMessageW(hWnd, WM_CLOSE, 0, 0);

	}
	return S_OK;
}

LRESULT HHBUI::UIDatebox::OnCustomDraw_Proc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	UIDatebox* pOwner = (UIDatebox*)((UIControl*)UIView)->GetlParam();
	auto obj = (UIListView*)UIView;
	static INT nIndex = 0;
	if (pOwner && obj)
	{
		if (uMsg == WM_ERASEBKGND)
		{
			if (pOwner->p_data.nSohwType > 0 || obj != pOwner->p_data.dblistview)
			{
				return S_OK;
			}
			ps_context ps{ 0 };
			RtlMoveMemory(&ps, (LPVOID)wParam, sizeof(ps_context));

			FLOAT nLeft = 0.f;
			LPCWSTR wzTextArray[] = { L"一", L"二", L"三", L"四", L"五", L"六", L"日" };

			for (INT i = 0; i < 7; i++)
			{
				nLeft = (i == 0) ? UIEngine::ScaleValue(2) : nLeft + UIEngine::ScaleValue(46);

				LPCWSTR wzText = wzTextArray[i];

				ps.hCanvas->DrawTextByColor(
					pOwner->p_data.hFontSe,
					wzText,
					DT_VCENTER | DT_CENTER | DT_SINGLELINE,
					nLeft,
					0.f,
					nLeft + UIEngine::ScaleValue(40),
					UIEngine::ScaleValue(16),
					UIColor(0, 0, 0)
				);
			}

			return S_OK;
		}
		else if (uMsg == WMM_EX_CUSTOMDRAW)
		{
			ps_customdraw cd{ 0 };
			RtlMoveMemory(&cd, (LPVOID)lParam, sizeof(ps_customdraw));
			LPVOID lpItems = pOwner->p_data.Items;
			LONG_PTR offset = (cd.iItem - 1) * (24 + sizeof(size_t));

			if (obj == pOwner->p_data.dblistview)
			{
				int type = __get_int(lpItems, offset);
				int year = __get_int(lpItems, offset + 4);
				int mon = __get_int(lpItems, offset + 8);
				int Mday = __get_int(lpItems, offset + 12);
				int Wday = __get_int(lpItems, offset + 16);
				int hjr = __get_int(lpItems, offset + 24);
				LPVOID Calendar = (LPVOID)__get(lpItems, offset + 20);

				pOwner->p_data.TmpBrush->SetColor(UIColor(-16746281));
				UIColor crText{ 255,255,255 };
				UIColor crTextbg{ 255,255,255 };

				std::wstringstream ss;

				if (pOwner->p_data.nCalendar == 0)
					ss << Mday;
				else
					ss << Mday << L"\n" << L" ";
				std::wstring lpwzText = ss.str();
				if (year == pOwner->p_data.Year && mon == pOwner->p_data.Mon && Mday == pOwner->p_data.Mday) {
					if (year == pOwner->p_data.to_Year && mon == pOwner->p_data.to_Mon && Mday == pOwner->p_data.to_Mday)
						lpwzText = L"今天";
					cd.hCanvas->FillRoundRect(pOwner->p_data.TmpBrush, cd.rcPaint.left + 3, cd.rcPaint.top + 3, cd.rcPaint.right - 3, cd.rcPaint.bottom - 3, 4);
				}
				else if (year == pOwner->p_data.to_Year && mon == pOwner->p_data.to_Mon && Mday == pOwner->p_data.to_Mday) {
					lpwzText = L"今天";
					pOwner->p_data.TmpBrush->SetColor(UIColor(255, 184, 0, 255));
					cd.hCanvas->FillRoundRect(pOwner->p_data.TmpBrush, cd.rcPaint.left + 3, cd.rcPaint.top + 3, cd.rcPaint.right - 3, cd.rcPaint.bottom - 3, 4);
				}
				else
				{
					if (type == 2)
					{
						crText = UIColor(0, 0, 0);
						crTextbg = UIColor(31, 31, 31, 255);
						if (hjr == 1)
						{
							crText = UIColor(30, 159, 255, 255);
							crTextbg = crText;
						}

						if (cd.dwState & state_hover)
						{
							pOwner->p_data.TmpBrush->SetColor(UIColor(220, 220, 220, 255));
							cd.hCanvas->FillRoundRect(pOwner->p_data.TmpBrush, cd.rcPaint.left + 1, cd.rcPaint.top + 1, cd.rcPaint.right - 1, cd.rcPaint.bottom - 1, 4);
						}
					}
					else
					{
						crText = UIColor(161, 158, 158, 100);
						if (hjr == 1)
							crText = UIColor(30, 159, 255, 150);
						crTextbg = crText;
					}
				}


				if (pOwner->p_data.nCalendar != 0)
					cd.hCanvas->DrawTextByColor(pOwner->p_data.hFont_Calendar, (LPCWSTR)Calendar, DT_CENTER | DT_WORDBREAK, cd.rcPaint.left, cd.rcPaint.top + UIEngine::ScaleValue(23), cd.rcPaint.right, cd.rcPaint.top + UIEngine::ScaleValue(23) + 30, crTextbg);
				cd.hCanvas->DrawTextByColor(pOwner->p_data.hFont, lpwzText.c_str(), DT_CENTER | DT_WORDBREAK, cd.rcPaint.left, cd.rcPaint.top + UIEngine::ScaleValue(6), cd.rcPaint.right, cd.rcPaint.top + UIEngine::ScaleValue(6) + 30, crText);
			}
			else if (obj == pOwner->p_data.listview)
			{
				int year = __get_int(lpItems, offset + 4);
				int mon = __get_int(lpItems, offset + 8);

				UIColor crText = UIColor(0, 0, 0);
				pOwner->p_data.TmpBrush->SetColor(UIColor(-16746281));
				if (pOwner->p_data.nSohwType == 1 && cd.iItem == pOwner->p_data.Mon && pOwner->p_data.lpYear == pOwner->p_data.Year)//判断月视图
				{
					crText = -1;
					cd.hCanvas->FillRoundRect(pOwner->p_data.TmpBrush, cd.rcPaint.left + 3, cd.rcPaint.top + 3, cd.rcPaint.right - 3, cd.rcPaint.bottom - 3, 4);
				}
				if (pOwner->p_data.nSohwType == 2)
				{
					int nYear = 0;
					if (cd.iItem == 1)
					{
						nYear = pOwner->p_data.lpYear - 1;
					}
					else {
						nYear = pOwner->p_data.lpYear + cd.iItem - 2;
					}
					if (nYear == pOwner->p_data.Year)//判断年视图
					{
						crText = -1;
						cd.hCanvas->FillRoundRect( pOwner->p_data.TmpBrush, cd.rcPaint.left + 3, cd.rcPaint.top + 3, cd.rcPaint.right - 3, cd.rcPaint.bottom - 3, 4);
					}
				}
				if (cd.dwState & state_hover)
				{
					cd.hCanvas->DrawRoundRect(pOwner->p_data.TmpBrush, cd.rcPaint.left, cd.rcPaint.top, cd.rcPaint.right, cd.rcPaint.bottom, 4, 1);
					cd.hCanvas->DrawRoundRect(pOwner->p_data.TmpBrush, cd.rcPaint.left + 1, cd.rcPaint.top + 1, cd.rcPaint.right - 1, cd.rcPaint.bottom - 1, 4, 1);
				}

				WCHAR lpwzText[50];
				if (pOwner->p_data.nSohwType == 1)
				{
					swprintf_s(lpwzText, L"%d月", cd.iItem);
				}
				else if (pOwner->p_data.nSohwType == 2)
				{
					int nYear = 0;
					if (cd.iItem == 1)
					{
						nYear = pOwner->p_data.lpYear - 1;
					}
					else
					{
						nYear = pOwner->p_data.lpYear + cd.iItem - 2;
					}
					swprintf_s(lpwzText, L"%d年", nYear);
				}
				cd.hCanvas->DrawTextByColor(pOwner->p_data.hFont, lpwzText, DT_CENTER | DT_VCENTER | DT_WORDBREAK, cd.rcPaint.left + 2, cd.rcPaint.top, cd.rcPaint.right - 2, cd.rcPaint.bottom, crText);
			}

			return S_FALSE;
		}
		else if (uMsg == WMM_LVN_ITEMCHANGED)
		{
			if (obj == pOwner->p_data.dblistview)
			{
				LPVOID lpItems = pOwner->p_data.Items;
				LONG_PTR offset = (wParam - 1) * (24 + sizeof(size_t));
				int type = __get_int(lpItems, offset);
				if (type == 2)
				{
					PostMessage(hWnd, 11112, wParam, 0);
				}
			}
			else if (obj == pOwner->p_data.listview)
			{
				if (pOwner->p_data.nSohwType == 1)
				{
					pOwner->p_data.lpMon = wParam;
					pOwner->p_data.nSohwType = 0;
					pOwner->toshow(1);
				}
				else if (pOwner->p_data.nSohwType == 2)
				{
					int nYear = 0;
					if (wParam == 1)
					{
						nYear = pOwner->p_data.lpYear - 1;
					}
					else
					{
						nYear = pOwner->p_data.lpYear + wParam - 2;
					}
					pOwner->p_data.lpYear = nYear;
					pOwner->p_data.nSohwType = 1;
					pOwner->toshow(3);
				}
			}
		}
	}
	return S_OK;
}

LRESULT HHBUI::UIDatebox::OnBtnDraw_Proc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	UIDatebox* pOwner = (UIDatebox*)((UIStatic*)UIView)->GetlParam();
	auto obj = (UIStatic*)UIView;
	if (pOwner && obj)
	{
		if (uMsg == WMM_CUSTOMDRAW)
		{
			ps_context ps{ 0 };
			RtlMoveMemory(&ps, (LPVOID)lParam, sizeof(ps_context));

			if (ps.dwState & state_hover)
			{
				pOwner->p_data.TitleBrush->SetColor(UIColor(220, 220, 220, 155));
				ps.hCanvas->FillRoundRect(pOwner->p_data.TitleBrush, ps.rcPaint.left, ps.rcPaint.top, ps.rcPaint.right, ps.rcPaint.bottom, 5);
				ps.hCanvas->DrawTextByColor(ps.hFont, obj->GetText(), ps.dwTextFormat, ps.rcText.left, ps.rcText.top, ps.rcText.right, ps.rcText.bottom, UIColor(30, 30, 30, 150));

			}
			return S_FALSE;
		}
		else if (uMsg == WMM_CLICK)
		{
			if (obj == pOwner->p_data.BtnByTitle)
			{
				if (pOwner->p_data.nSohwType == 0)
				{
					pOwner->p_data.nSohwType = 1;
					pOwner->toshow(2);
					pOwner->p_data.BtnByTitle->SetTipsText(L"到年视图");
				}
				else if (pOwner->p_data.nSohwType == 1) {
					pOwner->p_data.nSohwType = 2;
					pOwner->toshow(3);
					pOwner->p_data.BtnByTitle->SetTipsText(L"到日视图");
				}
				else if (pOwner->p_data.nSohwType == 2) {
					pOwner->p_data.nSohwType = 0;
					pOwner->toshow(1);
					pOwner->p_data.BtnByTitle->SetTipsText(L"到月视图");
				}
			}
			else if (obj == pOwner->p_data.BtnByReset)
			{
				std::wstring strTime = pOwner->getlocaltime(FALSE, &pOwner->p_data.Year, &pOwner->p_data.Mon, &pOwner->p_data.Mday, NULL, NULL, NULL, &pOwner->p_data.Wday);
				pOwner->p_data.lpYear = pOwner->p_data.Year;
				pOwner->p_data.lpMon = pOwner->p_data.Mon;
				pOwner->p_data.nSohwType = 0;
				pOwner->update(1);
				pOwner->toshow(1);
			}
			else if (obj == pOwner->p_data.BtnByUpper)
			{
				if (pOwner->p_data.nSohwType == 0)
				{
					pOwner->p_data.lpMon -= 1;
					if (pOwner->p_data.lpMon == 0) {
						pOwner->p_data.lpYear -= 1;
						pOwner->p_data.lpMon = 12;
					}
					pOwner->toshow(4);
				}
				else if (pOwner->p_data.nSohwType == 1)
				{
					pOwner->p_data.lpYear -= 1;
					pOwner->toshow(3);;
				}
				else if (pOwner->p_data.nSohwType == 2)
				{
					pOwner->p_data.lpYear -= 12;
					pOwner->toshow(3);
				}
			}
			else if (obj == pOwner->p_data.BtnByDown)
			{
				if (pOwner->p_data.nSohwType == 0)
				{
					pOwner->p_data.lpMon += 1;
					if (pOwner->p_data.lpMon == 13)
					{
						pOwner->p_data.lpYear += 1;
						pOwner->p_data.lpMon = 1;
					}
					pOwner->toshow(4);
				}
				else if (pOwner->p_data.nSohwType == 1)
				{
					pOwner->p_data.lpYear += 1;
					pOwner->toshow(3);
				}
				else if (pOwner->p_data.nSohwType == 2)
				{
					pOwner->p_data.lpYear += 12;
					pOwner->toshow(3);
				}
			}
		}
	}
	return S_OK;
}


void HHBUI::UIDatebox::OnPaintProc(ps_context ps)
{
	auto pDeviceContext = (ID2D1DeviceContext*)ps.hCanvas->GetContext(0);
	UIDrawContext::DrawRotatedLines(pDeviceContext, (ID2D1Brush*)p_data.hBrush->GetContext(), ps.rcPaint.right - 35, (ps.uHeight - static_cast<FLOAT>(35)) / 2, 35, 35, p_data.fAngle);
	UIColor text_normal;
	GetColor(color_text_normal, text_normal);
	ps.hCanvas->DrawTextByColor(ps.hFont, m_data.pstrTitle, DT_SINGLELINE | DT_VCENTER | DT_LEFT | DT_WORD_ELLIPSIS, 5,
		5,
		ps.uWidth + 2 - ps.uHeight,
		ps.uHeight - 3, text_normal);
}

void HHBUI::UIDatebox::update(INT Type)
{
	WCHAR lpTitle[50];
	if (Type == 0)
	{
		if (p_data.nSohwType == 0) {
			swprintf_s(lpTitle, L">>> %d年%d月 <<<", p_data.lpYear, p_data.lpMon);
		}
		else if (p_data.nSohwType == 1) {
			swprintf_s(lpTitle, L"→ %d年 ←", p_data.lpYear);
		}
		else if (p_data.nSohwType == 2) {
			swprintf_s(lpTitle, L"→ %d-%d ←", p_data.lpYear - 1, p_data.lpYear + 10);
		}
		p_data.BtnByTitle->SetText(lpTitle);
	}
	else
	{
		BOOL bsimpleline = FLAGS_CHECK(m_data.dwStyle, eos_date_simpleline);
		BOOL ballcomma = FLAGS_CHECK(m_data.dwStyle, eos_date_allcomma);
		TCHAR str[][4] = { L"", L"一", L"二" ,L"三", L"四", L"五", L"六", L"日" };
		LPCWSTR lpname = L"%d-%d-%d 星期%s";
		if (bsimpleline)
			lpname = L"%d-%d-%d";
		else if (ballcomma)
			lpname = L"%d,%d,%d 星期%s";

		swprintf_s(lpTitle, lpname, p_data.Year, p_data.Mon, p_data.Mday, str[p_data.Wday]);
		SetText(lpTitle);
	}
}

void HHBUI::UIDatebox::upTodate(int nYear, int nMon)
{
	p_data.lpYear = nYear;
	p_data.lpMon = nMon;
	int DayOfWeek = get_WeekOfDate(nYear, nMon, 1);
	int DayCount = 0;
	int MdayCount = 0;

	if (DayOfWeek != 1) {
		INT Year = nYear;
		INT Mon = nMon - 1;
		if (Mon == 0) {
			Year -= 1;
			Mon = 12;
		}

		MdayCount = get_MdayCount(Year, Mon);

		for (int i = DayOfWeek - 1; i > 0; i--) {
			DayCount++;
			upToTime(1, DayCount, Year, Mon, MdayCount - i + 1, DayOfWeek - i);
		}
	}

	MdayCount = get_MdayCount(nYear, nMon);

	int ddd = 0;
	for (int i = 1; i <= MdayCount; i++) {
		DayCount++;
		if (i == 1) {
			ddd = DayOfWeek;
		}
		else {
			ddd = ddd + 1;
		}

		if (ddd > 7) {
			ddd = 1;
		}
		upToTime(2, DayCount, nYear, nMon, i, ddd);
	}

	if (p_data.Year > 0) {
		INT Year = nYear;
		INT Mon = nMon + 1;
		if (Mon == 13) {
			Year += 1;
			Mon = 1;
		}

		for (int i = 1; i < 15; i++) {
			DayCount++;
			if (DayCount <= 42) {
				ddd = ddd + 1;
				if (ddd > 7) {
					ddd = 1;
				}
				upToTime(3, DayCount, Year, Mon, i, ddd);
			}
			else {
				break;
			}
		}
	}
	p_data.dblistview->SetItemCount(42);
	update();
}

void HHBUI::UIDatebox::upToTime(int type, int index, int year, int mon, int Mday, int Wday)
{
	LPVOID lpItems = p_data.Items;
	LONG_PTR offset = (index - 1) * (24 + sizeof(size_t));
	auto Calendar = (LPVOID)__get(lpItems, offset + 20);
	__set_int(lpItems, offset, type);
	__set_int(lpItems, offset + 4, year);
	__set_int(lpItems, offset + 8, mon);
	__set_int(lpItems, offset + 12, Mday);
	__set_int(lpItems, offset + 16, Wday);
	__set_int(lpItems, offset + 24, 0);
	if (Calendar != 0) {
		LocalFree(Calendar);
		Calendar = 0;
	}

	LPCWSTR LunarCalendar = L"";

	int jr = 0;
	int jq = 0;
	//取农历
	int LunarCalendarDay = Get_LunarCalendar(year, mon, Mday, &jr, &jq);
	if (jr > 0)
	{
		__set_int(lpItems, offset + 24, 1);
		LunarCalendar = Chjrmc[jr];
	}
	else if (jq > 0)
	{
		if (jq > 24)
		{
			jq = jq - 24;
		}
		LunarCalendar = Chjqmc[jq];
	}
	else
	{
		if ((LunarCalendarDay & 0x3F) == 1)
		{
			LunarCalendar = ChMonth[(LunarCalendarDay & 0x3C0) >> 6];
		}
		else
		{
			LunarCalendar = ChDay[LunarCalendarDay & 0x3F];
		}
	}

	__set(lpItems, offset + 20, (LONG_PTR)StrDupW(LunarCalendar));
}

void HHBUI::UIDatebox::toshow(int type)
{
	if (type == 1) {
		p_data.dblistview->Show(TRUE);
		p_data.listview->Show(FALSE);
		upTodate(p_data.lpYear, p_data.lpMon);
	}
	else if (type == 2) {
		p_data.dblistview->Show(FALSE);
		p_data.listview->SetItemCount(12);
		p_data.listview->Show(TRUE);
		update();
	}
	else if (type == 3) {
		p_data.listview->SetItemCount(12);
		update();
	}
	else {
		upTodate(p_data.lpYear, p_data.lpMon);
	}
}

std::wstring HHBUI::UIDatebox::getlocaltime(BOOL dwprintf, INT* year, INT* month, INT* days, INT* hour, INT* min, INT* sec, INT* wdays)
{
	std::wstring strTime;
	INT tmpyear = 0, tmpmonth = 0, tmpdays = 0, tmphour = 0, tmpmin = 0, tmpsec = 0, tmpwdays = 0;
	time_t t;//先创建一个time_t类型的变量t
	tm tmp;//创建一个新时间的结构体tmp.也可以时结构体指针 struct tm *p;
	time(&t);//使用该函数就可得到当前系统时间，
	localtime_s(&tmp, &t);//由于此时变量t中的系统时间值为日历时间，我们需要调用本地时间函
	tmpyear = tmp.tm_year + 1900;//年
	tmpmonth = tmp.tm_mon + 1;//月
	tmpdays = tmp.tm_mday;//日   每月的第n天
	tmphour = tmp.tm_hour;//时
	tmpmin = tmp.tm_min;//分
	tmpsec = tmp.tm_sec;//秒
	tmpwdays = tmp.tm_wday;//周
	if (year)
		*year = tmpyear;
	if (month)
		*month = tmpmonth;
	if (days)
		*days = tmpdays;
	if (hour)
		*hour = tmphour;
	if (min)
		*min = tmpmin;
	if (sec)
		*sec = tmpsec;
	if (wdays)
		*wdays = tmpwdays;
	if (dwprintf)
	{
		strTime = vstring::format(L"%02d/%02d/%02d %02d:%02d:%02d-%02d", tmpyear, tmpmonth, tmpdays, tmphour, tmpmin, tmpsec, tmpwdays);
	}
	return strTime;
}

