﻿#pragma once
/*元素列表 用于派生继承*/
namespace HHBUI
{
	enum ObjListFlags
	{
		eos_elvs_verticallist = 0,                   //纵向列表
		eos_elvs_horizontallist = 1,                   //横向列表
		eos_elvs_allowmultiple = 0x08,                //允许多选
		eos_elvs_allowskn = 0x800,               //关闭主题绘制
		eos_elvs_itemtracking = 0x10,                //表项跟踪
		eos_elvs_showcheck = 0x40,                //取消默认检查框 此时多选以ctrl按住触发 用于普通列表框
		//列表事件
		WMM_LVN_ITEMCHANGED = -101,                // 事件_列表_现行选中项被改变
		WMM_LVN_ITEMSELECTD = -102,                // 事件_列表_表项选中状态
		WMM_LVN_ITEMSELECTC = -103,                // 事件_列表_表项选中状态取消
		WMM_LVN_ITEMDRAGDROP_BEGIN = -106,                // 事件_列表_表项正在被拖拽 wParam为选中项目，lParam为当前坐标
		WMM_LVN_ITEMDRAGDROP_END = -107,                // 事件_列表_表项结束拖拽
		WMM_LVN_HOTTRACK = -121,                // 事件_列表_表项热点跟踪
	};
	enum TableFlags
	{
		eos_table_showtable = 0x20,                 //表格方案
		eos_table_drawhorizontalline = 0x100,                //绘制横线
		eos_table_drawverticalline = 0x200,                //绘制竖线
		eos_table_nohead = 0x400,                //无表头
		eos_table_allowmultiple = 0x800,                //允许多选
		eos_table_allowediting = 0x1000,               //允许双击编辑

		//报表表头风格
		cs_table_clickable = 1,               //可点击
		cs_table_lockwidth = 2,               //锁定宽度
		cs_table_checkbox = 4,                //选择框
		cs_table_sortable_ab = 16,            //可排序-字母
		cs_table_sortable_number = 32,        //可排序-数值
	};
	class TOAPI UIListView : public UIControl
	{
	public:
		UIListView(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpClsname, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat, BOOL fwScale = false);
		//测试命中
		INT GetHittest();
		//设置项目总数
		void SetItemCount(INT nCount, LPARAM lParam = 0);
		//设置项目宽度
		void SetItemWidth(INT nWidth);
		//设置项目高度
		void SetItemHeight(INT nHeight);
		//设置项目间隔宽度
		void SetItemSplitWidth(INT nWidth);
		//设置项目间隔高度
		void SetItemSplitHeight(INT nHeight);
		//保证显示
		void SetEnsureVisible(INT iItem);
		//更新索引
		void UpdateIndex(INT start_iItem, INT end_iItem = 0);
		//获取项目状态
		INT GetItemState(INT iItem);
		//设置项目状态
		void SetItemState(INT iItem, INT fstate);
		//取可视区表项数
		INT GetCountPerpage();
		//取表项矩形
		BOOL GetItemRect(INT iItem, ExRectF& lpRc);
		//取现行选中
		INT GetSelect();
		//设置现行选中
		void SetSelect(INT iItem, BOOL bShow = false);
		//取可视区起始索引
		INT GetTopIndex();
		//取鼠标所在表项
		INT GetHotItem();

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		//派生消息
		EXMETHOD LRESULT OnPsProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) { return S_OK; }
		//自定义绘制
		EXMETHOD BOOL OnPsDraw(ps_context ps) { return FALSE; }
		//自定义绘制项目
		EXMETHOD BOOL OnPsCustomDraw(INT iItem, ps_customdraw ps) { return FALSE; }
		void lv_size(LPARAM lParam);
		void lv_updatesbvalue();
		void lv_updateviewindex(BOOL bHView);
		BOOL lv_checkitem_view(INT iItem);
		BOOL lv_checkitem(INT iItem);
		void lv_rectfromiitem(INT iItem, BOOL bHView, ExRectF& rcItem);
		void lv_drawitem(ps_context ps, INT iItem, ExRectF rcClip, ExRectF rcItem);
		INT lv_getitemstate(LPVOID lpItems, INT iItem);
		void lv_mouseleave();
		void lv_mousemove(HWND hWnd, WPARAM wParam, LPARAM lParam);
		void lv_item_changestate(LPVOID lpItems, INT iItem, INT state, BOOL bRemove, INT nEvent, WPARAM wParam, LPARAM lParam);
		void lv_setitemstate(LPVOID lpItems, INT iItem, INT dwState, BOOL bRemove);
		void lv_redrawitem(INT iItem);
		INT lv_itemfrompos(INT x, INT y, INT& offsetPosX, INT& offsetPosY);
		BOOL lv_queryitemstate(LPVOID lpItems, INT iItem, INT dwState);
		size_t lv_reselect(INT iItem, BOOL bShow);
		BOOL lv_showitem(INT iItem, BOOL bCheck);
		void lv_getscrollbarvalue(BOOL bHSB, INT& oPos, INT& nLine, INT& nPage, INT& nView);
		INT lv_checkpos(INT nPos, INT nView, INT nPage);
		void lv_btndown(INT uMsg, size_t wParram, LPARAM lParam);
		void lv_itemselectone(INT uMsg, LPVOID lpItems, INT nCount, INT iCur, INT iSelect);
		void lv_itemselectzero(INT uMsg, LPVOID lpItems, INT nCount, INT iCur, INT iSelect);
		void lv_onvscrollbar(INT uMsg, WPARAM wParam, LPARAM lParam);
		void lv_setitemcount(INT nCount, LPARAM lParam);
		void lv_setitemstate(INT iItem, INT dwState);

		struct ListviewData
		{
			INT width_item = 0; //项目宽度
			INT width_spitem = 0;//项目固定宽度
			INT height_item = 0;
			INT width_split = 0; //间隔宽度
			INT height_split = 0;
			INT width_spec = 0; //特殊项目宽度
			INT height_spec = 0;
			INT width_view = 0; //项目总宽度
			INT height_view = 0;
			INT count_selects = 0; //选中项目数
			INT count_items = 0;	//项目总数
			INT count_view = 0;	//可视总数量
			INT count_view_h = 0;	//可视横向数量
			INT count_view_v = 0;	//可视纵向数量
			INT index_down = 0;  //按下项目
			INT index_select = 0;	//当前选中
			INT index_mouse = 0;	//悬浮项目
			INT index_start = 0;
			INT index_end = 0;
			INT index_track_start = 0;
			INT index_track_end = 0;
			INT nHittest = 0;
			INT nOffsetX = 0;
			INT nOffsetY = 0;
			LPVOID lpItems = nullptr;
			INT down_x = 0;
			INT down_y = 0;
			BOOL fctrl = false; //ctrl是否被按下
			UIBrush *cr_select = nullptr;
			ExPointF pots[5]{};
		}p_data;
	};
} 
