﻿#include "hhbui.h"
using namespace HHBUI;

void testtabs(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 870, 550, L"hello Tabs", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto tabs1 = new UITabs(window, 20, 60, 400, 150);
	tabs1->SetHeaderSize(80, 40);
	tabs1->SetColor(color_border, UIColor(89, 89, 91, 60));//边框颜色是为了更直观看到Tabs位置和大小，可以不加
	tabs1->AddPane(L"Pane1");
	tabs1->AddPane(L"Pane2");
	tabs1->AddPane(L"Pane3");
	tabs1->AddPane(L"Pane4");
	tabs1->AddPane(L"Pane5");

	new UIStatic(tabs1->GetPane(0), 30, 30, 180, 30, L"test Static1");
	new UIStatic(tabs1->GetPane(1), 30, 30, 180, 30, L"test Static2");
	tabs1->SetPane(0);

	auto tabs2 = new UITabs(window, 20, 240, 400, 300);
	tabs2->SetColor(color_border, UIColor(89, 89, 91, 60));
	tabs2->SetTabsPoint(UITabs::tabs_point::left);//页头四个方向可以自定义
	tabs2->EnableStretch(TRUE);//可以开启页头宽/高自撑满
	tabs2->AddPane(L"Pane1");
	tabs2->AddPane(L"Pane2");
	tabs2->AddPane(L"Pane3");
	tabs2->AddPane(L"Pane4");
	tabs2->AddPane(L"Pane5");

	new UIStatic(tabs2->GetPane(0), 30, 30, 180, 30, L"test Static1");
	new UIStatic(tabs2->GetPane(1), 30, 30, 180, 30, L"test Static2");
	tabs2->SetPane(0);
	tabs2->Lock(20, 240, -1, 10);

	auto tabs3 = new UITabs(window, 440, 60, 400, 150);
	tabs3->SetColor(color_border, UIColor(89, 89, 91, 60));
	tabs3->SetTabsType(UITabs::tabs_type::card);//可以设置页头风格
	tabs3->SetTabsPoint(UITabs::tabs_point::bottom);
	tabs3->EnableStretch(TRUE);
	tabs3->AddPane(L"Pane1", 170201);
	tabs3->AddPane(L"Pane2", 170202);
	tabs3->AddPane(L"Pane3", 170203);
	tabs3->AddPane(L"Pane4", 170204);
	tabs3->AddPane(L"Pane5", 170205);
	tabs3->AddPane(L"Pane6", 170206);

	new UIStatic(tabs3->GetPane(0), 30, 30, 180, 30, L"test Static1");
	new UIStatic(tabs3->GetPane(1), 30, 30, 180, 30, L"test Static2");

	tabs3->SetPane(0);
	tabs3->Lock(440, 60, 20, -1);

	auto tabs4 = new UITabs(window, 440, 240, 400, 150);
	tabs4->SetColor(color_border, UIColor(89, 89, 91, 60));
	tabs4->SetTabsType(UITabs::tabs_type::card);
	tabs4->SetTabsPoint(UITabs::tabs_point::right);
	tabs4->AddPane(L"Pane1", 170201);
	tabs4->AddPane(L"Pane2", 170202);
	tabs4->AddPane(L"Pane3", 170203);
	tabs4->AddPane(L"Pane4", 170204);
	tabs4->AddPane(L"Pane5", 170205);
	tabs4->AddPane(L"Pane6", 170206);

	new UIStatic(tabs4->GetPane(0), 30, 30, 180, 30, L"test Static1");
	new UIStatic(tabs4->GetPane(1), 30, 30, 180, 30, L"test Static2");
	
	tabs4->SetPane(0);
	tabs4->Lock(440, 240, 20, 10);


	window->Show();
	//window->MessageLoop();
}