﻿#include "hhbui.h"

using namespace HHBUI;
LRESULT CALLBACK OnTour_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	if (nCode == WMM_CLICK)
	{

	}
	return S_OK;
}
void testtour(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 700, 500, L"hello Tour", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_BTN_HELP | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto btn = new UIButton(window, 290, 70, 100, 36, L"我是一个摆设按钮", 0, eos_ex_autosize);
	btn->SetStyle(fill, success);
	btn->SetEvent(WMM_CLICK, OnTour_Event);


	auto tour = new UITour(window, 0, 0, 700, 500, L"向导提示");
	tour->SetColor(color_background, UIColor(0, 0, 0, 220));
	tour->SetColor(color_text_normal, UIColor(255, 255, 255));
	tour->SetRadius(10, 10, 10, 10);
	tour->SetFontFromFamily(NULL, 25);
	tour->Lock(0, 0, 0, 0);

	//添加一个帧，有两个高亮区域
	tour->AddFrame(_T("第1个页面的说明"), FALSE, { 50, 170, 150, 170 + 124 }, { 100, 132, 200, 56 });
	//添加一个帧，有3个高亮区域，分别绑定到指定的子控件句柄
	ExRectF tmp{};
	btn->GetRect(tmp);
	tmp.OffsetSize(window->GetSideSize());
	tour->AddFrame(_T("第2个页面的说明\r\n第二行🌱🌲还可以增加内容"), FALSE, tmp);
	//添加一个帧，没有高亮区域
	tour->AddFrame(_T("The End"), FALSE);




	window->Show();
	//window->MessageLoop();
}