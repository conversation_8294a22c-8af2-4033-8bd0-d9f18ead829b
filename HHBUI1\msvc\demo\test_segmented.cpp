﻿#include "hhbui.h"
using namespace HHBUI;
LRESULT CALLBACK OnObjEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	if (nCode == WMM_SMT_ITEMCHANGED) {
		auto winm = (UIWnd*)pWnd;
		auto seg = (UISegmented*)UIView;
		output(L"选中项被改变，nID：", nID, L"，索引：", wParam, L"，标题：", seg->GetItemTitle(wParam));
	}
	return S_OK;
}
void testsegmented(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 1000, 450, L"hello Segmented", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);
	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);
	LPCWSTR str[] = { L"测试选项1",L"更长的测试选项2",L"测试3",L"4",L"项5" };

	auto label = new UIStatic(window, 24, 48, 400, 24, L"分段控制器：自适应子项宽度：", 0, 0, 0, Left | Middle);
	label->SetColor(color_text_normal, UIColor(119, 119, 121));
	label->SetFontFromFamily(L"微软雅黑", 12, 0);
	auto seg1 = new UISegmented(window, 24, 72, 400, 36, 10001);
	seg1->SetEvent(WMM_SMT_ITEMCHANGED, OnObjEvent);
	for (auto it : str) seg1->AddItem(it);
	seg1->SetItemSelected(4);
	seg1->Update();

	label = new UIStatic(window, 24, 120, 400, 24, L"分段控制器：子项撑开（均分总宽度）：可禁用子项：", 0, 0, 0, Left | Middle);
	label->SetColor(color_text_normal, UIColor(119, 119, 121));
	label->SetFontFromFamily(L"微软雅黑", 12, 0);
	auto seg2 = new UISegmented(window, 24, 144, 400, 36, 10002);
	seg2->SetEvent(WMM_SMT_ITEMCHANGED, OnObjEvent);
	for (auto it : str) seg2->AddItem(it);
	seg2->SetItemBan(0, true);
	seg2->SetItemBan(3, true);
	seg2->SetDisplayMode(1);
	seg2->SetItemSelected(1);
	seg2->Update();

	label = new UIStatic(window, 24, 192, 400, 24, L"分段控制器：子项自定义固定宽度：", 0, 0, 0, Left | Middle);
	label->SetColor(color_text_normal, UIColor(119, 119, 121));
	label->SetFontFromFamily(L"微软雅黑", 12, 0);
	auto seg3 = new UISegmented(window, 24, 216, 400, 36, 10003);
	seg3->SetEvent(WMM_SMT_ITEMCHANGED, OnObjEvent);
	for (auto it : str) seg3->AddItem(it);
	seg3->SetDisplayMode(2);
	seg3->SetItemWidth(60);
	seg3->SetItemSelected(2);
	seg3->Update();

	label = new UIStatic(window, 436, 48, 24, 400, L"分段控制器：垂直排列：自适应：", 0, 0, 0, Left | Middle | Vertical);
	label->SetColor(color_text_normal, UIColor(119, 119, 121));
	label->SetFontFromFamily(L"微软雅黑", 12, 0);
	auto seg4 = new UISegmented(window, 460, 48, 96, 376, 10004);
	seg4->SetEvent(WMM_SMT_ITEMCHANGED, OnObjEvent);
	for (auto it : str) seg4->AddItem(it);
	seg4->SetIsHorizontal(false);
	seg4->SetItemSelected(4);
	seg4->Update();

	label = new UIStatic(window, 568, 48, 24, 400, L"分段控制器：垂直排列：自定义固定高度：可禁用子项：", 0, 0, 0, Left | Middle | Vertical);
	label->SetColor(color_text_normal, UIColor(119, 119, 121));
	label->SetFontFromFamily(L"微软雅黑", 12, 0);
	auto seg5 = new UISegmented(window, 592, 48, 96, 376, 10005);
	seg5->SetEvent(WMM_SMT_ITEMCHANGED, OnObjEvent);
	for (auto it : str) seg5->AddItem(it);
	seg5->SetItemBan(1, true);
	seg5->SetIsHorizontal(false);
	seg5->SetDisplayMode(2);
	seg5->SetItemWidth(60);
	seg5->SetItemSelected(0);
	seg5->Update();

	label = new UIStatic(window, 24, 264, 400, 24, L"分段控制器：图标在上：自定义固定宽度：", 0, 0, 0, Left | Middle);
	label->SetColor(color_text_normal, UIColor(119, 119, 121));
	label->SetFontFromFamily(L"微软雅黑", 12, 0);
	auto seg6 = new UISegmented(window, 24, 288, 400, 60, 10006);
	seg6->SetEvent(WMM_SMT_ITEMCHANGED, OnObjEvent);
	for (auto it : str) seg6->AddItem(it, new UIImage(L"icons\\Segmented_icon.png"));
	seg6->SetIconPos(1);
	seg6->SetDisplayMode(2);
	seg6->SetItemWidth(80);
	seg6->SetItemSelected(3);
	seg6->Update();

	label = new UIStatic(window, 24, 360, 400, 24, L"分段控制器：图标在左：自适应：", 0, 0, 0, Left | Middle);
	label->SetColor(color_text_normal, UIColor(119, 119, 121));
	label->SetFontFromFamily(L"微软雅黑", 12, 0);
	auto seg7 = new UISegmented(window, 24, 384, 400, 40, 10007);
	seg7->SetEvent(WMM_SMT_ITEMCHANGED, OnObjEvent);
	for (auto it : str) seg7->AddItem(it, new UIImage(L"icons\\Segmented_icon.png"));
	seg7->SetIconPos(2);
	seg7->SetItemSelected(4);
	seg7->Update();

	label = new UIStatic(window, 700, 48, 24, 400, L"分段控制器：垂直排列：图标在上：撑开：", 0, 0, 0, Left | Middle | Vertical);
	label->SetColor(color_text_normal, UIColor(119, 119, 121));
	label->SetFontFromFamily(L"微软雅黑", 12, 0);
	auto seg8 = new UISegmented(window, 724, 48, 96, 376, 10008);
	seg8->SetEvent(WMM_SMT_ITEMCHANGED, OnObjEvent);
	for (auto it : str) seg8->AddItem(it, new UIImage(L"icons\\Segmented_icon.png"));
	seg8->SetIsHorizontal(false);
	seg8->SetIconPos(1);
	seg8->SetDisplayMode(1);
	seg8->SetItemSelected(0);
	seg8->Update();

	label = new UIStatic(window, 832, 48, 24, 400, L"分段控制器：垂直排列：图标在左：自适应：", 0, 0, 0, Left | Middle | Vertical);
	label->SetColor(color_text_normal, UIColor(119, 119, 121));
	label->SetFontFromFamily(L"微软雅黑", 12, 0);
	auto seg9 = new UISegmented(window, 856, 48, 120, 376, 10009);
	seg9->SetEvent(WMM_SMT_ITEMCHANGED, OnObjEvent);
	for (auto it : str) seg9->AddItem(it, new UIImage(L"icons\\Segmented_icon.png"));
	seg9->SetIsHorizontal(false);
	seg9->SetIconPos(2);
	seg9->SetItemSelected(3);
	seg9->Update();

	
	window->Show();
	//window->MessageLoop();
}