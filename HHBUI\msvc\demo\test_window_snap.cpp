﻿#include "hhbui.h"
using namespace HHBUI;

// 全局变量存储创建的窗口
std::vector<UIWnd*> g_windows;

// 窗口消息回调 (MsgPROC类型)
LRESULT CALLBACK OnSnapTestWndProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
    auto window = (UIWnd*)pWnd;
    auto obj = (UIControl*)UIView;

    // 处理窗口消息
    return S_OK;
}

// 控件事件回调 (EventHandlerPROC类型)
LRESULT CALLBACK OnSnapTestEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
    auto window = (UIWnd*)pWnd;
    auto obj = (UIControl*)UIView;

    if (nCode == WMM_CLICK)
    {
        switch (nID)
        {
        case 1001: // 吸附到左侧
            window->SnapToPosition(SnapPosition::Left, true);
            break;

        case 1002: // 吸附到右侧
            window->SnapToPosition(SnapPosition::Right, true);
            break;

        case 1003: // 吸附到顶部
            window->SnapToPosition(SnapPosition::Top, true);
            break;

        case 1004: // 吸附到底部
            window->SnapToPosition(SnapPosition::Bottom, true);
            break;

        case 1005: // 吸附到左上角
            window->SnapToPosition(SnapPosition::TopLeft, true);
            break;

        case 1006: // 吸附到右上角
            window->SnapToPosition(SnapPosition::TopRight, true);
            break;

        case 1007: // 吸附到左下角
            window->SnapToPosition(SnapPosition::BottomLeft, true);
            break;

        case 1008: // 吸附到右下角
            window->SnapToPosition(SnapPosition::BottomRight, true);
            break;

        case 1009: // 居中
            window->SnapToPosition(SnapPosition::Center, true);
            break;

        case 1010: // 最大化
            window->SnapToPosition(SnapPosition::Maximize, true);
            break;

        case 1011: // 重置到原始位置
            window->ResetToOriginalPosition(true);
            break;

        case 1012: // 保存当前位置为原始位置
            window->SaveCurrentAsOriginal();
            break;

        case 1013: // 启用/禁用吸附
        {
            static bool snapEnabled = true;
            snapEnabled = !snapEnabled;
            window->EnableWindowSnapping(snapEnabled);

            auto btn = (UIButton*)obj;
            btn->SetText(snapEnabled ? L"禁用吸附" : L"启用吸附");
            break;
        }

        case 1014: // 层叠布局
            UIWnd::ArrangeWindows(g_windows, LayoutMode::Cascade);
            break;

        case 1015: // 水平平铺
            UIWnd::ArrangeWindows(g_windows, LayoutMode::TileHorizontal);
            break;

        case 1016: // 垂直平铺
            UIWnd::ArrangeWindows(g_windows, LayoutMode::TileVertical);
            break;

        case 1017: // 网格布局
            UIWnd::ArrangeWindows(g_windows, LayoutMode::Grid);
            break;

        case 1018: // 智能布局
            UIWnd::ArrangeWindows(g_windows, LayoutMode::Smart);
            break;

        case 1019: // 建议最优位置
        {
            RECT suggested = window->SuggestOptimalPosition();
            window->Move(suggested.left, suggested.top,
                        suggested.right - suggested.left,
                        suggested.bottom - suggested.top, true);
            break;
        }

        case 1020: // 创建新窗口
        {
            static int windowCount = 1;
            std::wstring title = L"测试窗口 " + std::to_wstring(++windowCount);

            auto newWindow = new UIWnd(100, 100, 600, 400, title.c_str(), 0, 0,
                UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
                UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, 0);

            newWindow->SetBackgColor(UIColor(240, 248, 255, 255));
            newWindow->SetBorderColor(UIColor(100, 150, 255, 255));
            newWindow->SetShadowColor(UIColor(100, 150, 255, 100));
            newWindow->SetRadius(8);

            // 添加一些控件
            auto label = new UIStatic(newWindow, 20, 20, 560, 30,
                (L"这是" + title + L"，可以拖拽测试吸附功能").c_str());
            label->SetColor(color_text_normal, UIColor(60, 60, 60, 255));

            auto closeBtn = new UIButton(newWindow, 250, 350, 100, 30, L"关闭窗口");
            closeBtn->SetEvent(WMM_CLICK, [](LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam) -> LRESULT {
                auto window = (UIWnd*)pWnd;

                // 从全局列表中移除
                auto it = std::find(g_windows.begin(), g_windows.end(), window);
                if (it != g_windows.end()) {
                    g_windows.erase(it);
                }

                window->SendMsg(WM_CLOSE);
                return S_OK;
            });

            g_windows.push_back(newWindow);
            newWindow->Show();
            break;
        }
        }
    }

    return S_OK;
}

void testwindowsnap(HWND hWnd)
{
    // 创建主控制窗口
    auto window = new UIWnd(50, 50, 800, 600, L"窗口吸附和布局管理测试", 0, 0,
        UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
        UISTYLE_CENTERWINDOW | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd, NULL, 0, OnSnapTestWndProc);

    window->SetBackgColor(UIColor(248, 250, 252, 255));
    window->SetBorderColor(UIColor(59, 130, 246, 255));
    window->SetShadowColor(UIColor(59, 130, 246, 100));
    window->SetRadius(10);

    // 配置吸附设置
    SnapConfig config;
    config.enabled = true;
    config.sensitivity = 15;
    config.edgeMargin = 5;
    config.snapToWindows = true;
    config.snapToScreen = true;
    config.showPreview = true;
    config.previewColor = UIColor(59, 130, 246, 128);
    window->SetSnapConfig(config);

    g_windows.push_back(window);

    // 添加标题
    auto title = new UIStatic(window, 20, 20, 760, 40, L"窗口吸附和布局管理功能测试");
    title->SetColor(color_text_normal, UIColor(30, 30, 30, 255));
    title->SetFontFromFamily(L"Microsoft YaHei", 18, HHBUI::FONT_STYLE_BOLD);

    // 吸附功能按钮组
    auto snapGroup = new UIStatic(window, 20, 80, 760, 120, L"");
    snapGroup->SetColor(color_background, UIColor(255, 255, 255, 255));
    snapGroup->SetColor(color_border, UIColor(220, 220, 220, 255));
    snapGroup->SetRadius(8, 8, 8, 8);

    auto snapTitle = new UIStatic(snapGroup, 10, 10, 200, 25, L"窗口吸附功能:");
    snapTitle->SetColor(color_text_normal, UIColor(60, 60, 60, 255));
    snapTitle->SetFontFromFamily(L"Microsoft YaHei", 14, HHBUI::FONT_STYLE_BOLD);

    // 创建吸附按钮
    const wchar_t* snapLabels[] = {
        L"左侧", L"右侧", L"顶部", L"底部",
        L"左上", L"右上", L"左下", L"右下",
        L"居中", L"最大化"
    };

    for (int i = 0; i < 10; ++i) {
        int x = 10 + (i % 5) * 145;
        int y = 40 + (i / 5) * 35;

        auto btn = new UIButton(snapGroup, x, y, 135, 30, snapLabels[i], 0, 0, 1001 + i);
        btn->SetStyle(fill, primary);
        btn->SetRadius(6.0f);
        btn->SetEvent(WMM_CLICK, OnSnapTestEvent);
    }

    // 控制功能按钮组
    auto controlGroup = new UIStatic(window, 20, 220, 760, 90, L"");
    controlGroup->SetColor(color_background, UIColor(255, 255, 255, 255));
    controlGroup->SetColor(color_border, UIColor(220, 220, 220, 255));
    controlGroup->SetRadius(8, 8, 8, 8);

    auto controlTitle = new UIStatic(controlGroup, 10, 10, 200, 25, L"控制功能:");
    controlTitle->SetColor(color_text_normal, UIColor(60, 60, 60, 255));
    controlTitle->SetFontFromFamily(L"Microsoft YaHei", 14, HHBUI::FONT_STYLE_BOLD);

    const wchar_t* controlLabels[] = {
        L"重置位置", L"保存位置", L"禁用吸附", L"建议位置", L"创建窗口"
    };

    for (int i = 0; i < 5; ++i) {
        auto btn = new UIButton(controlGroup, 10 + i * 145, 40, 135, 30, controlLabels[i], 0, 0, 1011 + i);
        btn->SetStyle(fill, success);
        btn->SetRadius(6.0f);
        btn->SetEvent(WMM_CLICK, OnSnapTestEvent);
    }

    // 布局管理按钮组
    auto layoutGroup = new UIStatic(window, 20, 330, 760, 90, L"");
    layoutGroup->SetColor(color_background, UIColor(255, 255, 255, 255));
    layoutGroup->SetColor(color_border, UIColor(220, 220, 220, 255));
    layoutGroup->SetRadius(8, 8, 8, 8);

    auto layoutTitle = new UIStatic(layoutGroup, 10, 10, 200, 25, L"布局管理:");
    layoutTitle->SetColor(color_text_normal, UIColor(60, 60, 60, 255));
    layoutTitle->SetFontFromFamily(L"Microsoft YaHei", 14, HHBUI::FONT_STYLE_BOLD);

    const wchar_t* layoutLabels[] = {
        L"层叠布局", L"水平平铺", L"垂直平铺", L"网格布局", L"智能布局"
    };

    for (int i = 0; i < 5; ++i) {
        auto btn = new UIButton(layoutGroup, 10 + i * 145, 40, 135, 30, layoutLabels[i], 0, 0, 1014 + i);
        btn->SetStyle(fill, warning);
        btn->SetRadius(6.0f);
        btn->SetEvent(WMM_CLICK, OnSnapTestEvent);
    }

    // 说明文本
    auto instructions = new UIStatic(window, 20, 440, 760, 120,
        L"使用说明:\n"
        L"1. 点击吸附按钮可以将当前窗口吸附到指定位置\n"
        L"2. 拖拽窗口时会自动检测吸附位置并显示预览\n"
        L"3. 创建多个窗口后可以使用布局管理功能统一排列\n"
        L"4. 可以保存和恢复窗口位置\n"
        L"5. 支持启用/禁用吸附功能");
    instructions->SetColor(color_text_normal, UIColor(80, 80, 80, 255));
    instructions->SetColor(color_background, UIColor(248, 250, 252, 255));

    window->Show();
}
