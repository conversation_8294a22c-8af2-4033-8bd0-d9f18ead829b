﻿#include "hhbui.h"

using namespace HHBUI;
LRESULT CALLBACK OnColorMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARA<PERSON> wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIColorPicker*)UIView;
	if (uMsg == MCM_GETCOLOR)
	{
		auto pickercr1 = (UIColorPicker*)window->FindUIView(L"6001");
	
		UIColor Color;
		obj->GetColour(Color);
		pickercr1->SetColor(color_border, Color, TRUE);
		pickercr1->SetColour(Color);

		if (nID != 6002)//取消自身事件回调的消息
		{
			auto pickercr2 = (UIColorPicker*)window->FindUIView(L"6002");
			pickercr2->SetColour(Color);
		}

		window->SetBorderColor(Color);
	}
	return S_OK;
}
void testcolorpicker(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 700, 500, L"hello ColorPicker", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto pickercr = new UIColorPicker(window, 20, 70, 120, 35, 0, 0, 6001);
	pickercr->SetColor(color_border, UIColor(L"#ca5100"));
	pickercr->SetColour(UIColor(255, 255, 255, 127));
	pickercr->SetRadius(17.5, 17.5, 17.5, 17.5);
	pickercr->SetMsgProc(OnColorMsgProc);

	auto pickercr1 = new UIColorPicker(window, 370, 70, 25, 200, eos_picker_colorh, 0, 6002);
	pickercr1->SetColour(UIColor(L"#ca5100"));
	pickercr1->SetMsgProc(OnColorMsgProc);

	auto pickercr2 = new UIColorPicker(window, 400, 70, 200, 200, eos_picker_colors);
	pickercr2->SetColour(UIColor(L"#ca5100"));
	pickercr2->SetMsgProc(OnColorMsgProc);


	auto pickercr3 = new UIColorPicker(window, 200, 70, 120, 35, 0, 0, 6003);
	pickercr3->SetColor(color_border, UIColor(L"#ca5100"));
	pickercr3->SetColour(UIColor(L"#ca5100"));

	window->Show();
	//window->MessageLoop();
}