﻿#include "hhbui.h"
using namespace HHBUI;
UIMenu* m_bMenu1 = nullptr;
UIBrush* m_lpBackgBrush = nullptr;

LRESULT CALLBACK OnDiyMenuMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	if (uMsg == WM_INITMENUPOPUP)
	{
		if ((HMENU)wParam == m_bMenu1->GetMenu())//只改变主菜单
		{
			ExRectF lptRect{};
			window->GetRect(lptRect);
			window->Move(CW_USEDEFAULT, CW_USEDEFAULT, lptRect.right + UIEngine::fScale(8), lptRect.bottom + UIEngine::fScale(65));

			auto bstatic_pic = new UIStatic(window, 0, 0, lptRect.right / UIEngine::GetDefaultScale(), 80);
			bstatic_pic->SetColor(color_border, UIColor(0, 108, 190, 255));
			bstatic_pic->SetRadius(8, 8, 0, 0);

			LPVOID imgdata; size_t retSize = 0;
			UIreadFile(LR"(icons\\pic.jpg)", imgdata, retSize);
			bstatic_pic->SetBackgImage(imgdata, retSize, 0, 0, bir_epault);

			auto UIView = (UIItem*)window->FindUIView(L"form-item");
			while (UIView != 0)
			{
				ExRectF bkret{};
				UIView->GetRect(bkret);
				UIView->Move(CW_USEDEFAULT, bkret.top + 80, bkret.right + 20);
				UIView = (UIItem*)UIView->GetNode(GW_HWNDNEXT);
			}

			if (!m_lpBackgBrush)
				m_lpBackgBrush = new UIBrush(0.0f, 0.0f, (FLOAT)lptRect.right + UIEngine::fScale(8), (FLOAT)lptRect.bottom + UIEngine::fScale(80), UIColor(142, 11, 229, 255), UIColor(208, 90, 11, 255));
		}
	}
	else if (uMsg == WM_ERASEBKGND)
	{
		auto canvas = (UICanvas*)wParam;
		INT width = GET_X_LPARAM(lParam);
		INT height = GET_Y_LPARAM(lParam);

		
		canvas->FillRoundRect(m_lpBackgBrush, 0, 0, width, height, window->GetRadius());
		return S_FALSE;
	}
	return S_OK;
}
LRESULT CALLBACK OnMenuEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto obj = (UIButton*)UIView;
	auto window = (UIWnd*)pWnd;
	if (nCode == WMM_CLICK)
	{
		if (nID == 1001)
		{
			m_bMenu1->Popup(window);
		}
		else if (nID == 1002)
		{
			m_bMenu1->Popup(window, 0, 0, 0, UIColor(16, 124, 16, 255), UIColor(255, 255, 255, 255), UIColor(47, 54, 60, 255), UIColor(255, 87, 34, 155), UIColor(255, 87, 34, 255), UIColor(0, 108, 190, 255));
		
		}
		else if (nID == 1003)
		{
			m_bMenu1->Popup(window, 0, 0, 0, UIColor(255, 255, 255, 255), {}, {}, UIColor(255, 87, 34, 155), UIColor(255, 87, 34, 255), UIColor(234, 234, 234, 55), OnDiyMenuMsgProc);

		}
	}
	return S_OK;
}
void testmenu(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 800, 500, L"hello Menu", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto Button1 = new UIButton(window, 20, 70, 120, 32, L"弹出默认菜单", 0, 0, 1001);
	Button1->SetEvent(WMM_CLICK, OnMenuEvent);
	auto Button2 = new UIButton(window, 20, 120, 120, 32, L"弹出自定义菜单", 0, 0, 1002);
	Button2->SetEvent(WMM_CLICK, OnMenuEvent);
	auto Button3 = new UIButton(window, 20, 170, 120, 32, L"弹出DIY菜单", 0, 0, 1003);
	Button3->SetEvent(WMM_CLICK, OnMenuEvent);

	//创建子菜单
	auto bMenu = new UIMenu();
	bMenu->Append(MF_STRING, 2000, L"名称");
	std::wstring xml = LR"(<svg t="1717416270923" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21186" width="16" height="16"><path d="M896 128a64 64 0 0 1 64 64v384a64 64 0 0 1-64 64h-256a64 64 0 0 1-64-64V192a64 64 0 0 1 64-64h256z m0 64h-256v384h256V192z" fill="#d81e06" p-id="21187"></path><path d="M64 704h64v192H64zM896 704h64v192h-64z" fill="#d81e06" p-id="21188"></path><path d="M64 768h832v64H64z" fill="#d81e06" p-id="21189"></path><path d="M256 128a192 192 0 0 1 192 192v128a192 192 0 1 1-384 0V320a192 192 0 0 1 192-192z m0 64a128 128 0 0 0-127.68 118.4L128 320v128a128 128 0 0 0 255.68 9.6L384 448V320a128 128 0 0 0-128-128z" fill="#d81e06" p-id="21190"></path></svg>)";
	auto hpng1 = new UIImage(xml.c_str(), TRUE, 16, 16);
	bMenu->Append(MF_STRING, 2001, L"大小", hpng1);
	bMenu->Append(MF_STRING, 2002, L"项目类型");
	bMenu->Append(MF_STRING, 2003, L"修改日期");
	//创建主菜单
	m_bMenu1 = new UIMenu();
	xml = LR"(<svg t="1717415967502" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12984" width="16" height="16"><path d="M387.925333 128A81.493333 81.493333 0 0 1 469.333333 209.408v178.517333A81.493333 81.493333 0 0 1 387.925333 469.333333H209.365333A81.493333 81.493333 0 0 1 128 387.925333V209.408A81.493333 81.493333 0 0 1 209.365333 128h178.56z m426.666667 0A81.493333 81.493333 0 0 1 896 209.408v178.517333A81.493333 81.493333 0 0 1 814.592 469.333333h-178.56A81.493333 81.493333 0 0 1 554.666667 387.925333V209.408A81.493333 81.493333 0 0 1 636.032 128h178.56z m-426.666667 426.666667A81.493333 81.493333 0 0 1 469.333333 636.032v178.602667A81.493333 81.493333 0 0 1 387.925333 896H209.365333A81.493333 81.493333 0 0 1 128 814.634667v-178.602667A81.493333 81.493333 0 0 1 209.365333 554.666667h178.56zM725.333333 855.808A130.645333 130.645333 0 0 0 855.808 725.333333 130.602667 130.602667 0 0 0 725.333333 594.858667 130.602667 130.602667 0 0 0 594.858667 725.333333 130.645333 130.645333 0 0 0 725.333333 855.808zM725.333333 554.666667c94.122667 0 170.666667 76.544 170.666667 170.666666 0 94.08-76.544 170.666667-170.666667 170.666667s-170.666667-76.586667-170.666666-170.666667c0-94.122667 76.544-170.666667 170.666666-170.666666z" fill="#387AC9" p-id="12985"></path></svg>)";

	auto hpng2 = new UIImage(xml.c_str(), TRUE, 16, 16);
	m_bMenu1->Append(MF_STRING, 1000, L"查看", hpng2);
	m_bMenu1->Append(MF_POPUP, (size_t)bMenu, L"排序方式");//绑定子菜单
	m_bMenu1->Append(MF_DISABLED, 1001, L"刷新");
	m_bMenu1->Append(MF_SEPARATOR, 1002, L"");
	m_bMenu1->Append(MF_STRING, 1003, L"撤销 移动\tCtrl+&Z");

	xml = LR"(<svg t="1717416116125" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15858" width="16" height="16"><path d="M622.6944 0h-409.6c-56.32 0-101.888 46.08-101.888 102.4l-0.512 819.2c0 56.32 45.568 102.4 101.888 102.4h614.912c56.32 0 102.4-46.08 102.4-102.4V307.2l-307.2-307.2z m102.4 819.2h-409.6v-102.4h409.6v102.4z m0-204.8h-409.6v-102.4h409.6v102.4z m-153.6-256V76.8l281.6 281.6h-281.6z" fill="#30BA78" p-id="15859"></path></svg>)";
	auto hpng3 = new UIImage(xml.c_str(), TRUE, 16, 16);
	m_bMenu1->Append(MF_STRING, 1004, L"新建", hpng3);
	m_bMenu1->Append(MF_SEPARATOR, 1005, L"");
	m_bMenu1->Append(MF_STRING, 1006, L"显示设置");
	m_bMenu1->Append(MF_STRING, 1007, L"个性化\tCtrl+&T");
	m_bMenu1->Append(MF_SEPARATOR, 1008, L"");
	m_bMenu1->Append(MF_STRING, 1009, L"在终端中打开");
	m_bMenu1->Append(MF_SEPARATOR, 1010, L"");
	m_bMenu1->Append(MF_STRING, 1011, L"Hello, HHBUI Menu test1");
	window->Show();
}