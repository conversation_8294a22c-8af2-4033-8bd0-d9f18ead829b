﻿#include "hhbui.h"
using namespace std;
using namespace HHBUI;

LRESULT CALLBACK OnButton_Chart_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (nCode == WMM_CLICK)
	{
		if (nID == 1001) {
			auto chart1 = (UIChart*)window->FindUIView(L"10001");
			auto chart2 = (UIChart*)window->FindUIView(L"10002");
			auto chart3 = (UIChart*)window->FindUIView(L"10003");
			auto chart4 = (UIChart*)window->FindUIView(L"10004");
			auto chart5 = (UIChart*)window->FindUIView(L"10005");

			chart1->ClearItem();
			chart2->ClearItem();
			chart3->ClearItem();
			chart4->ClearItem();
			chart5->ClearItem();

			random_device rd;
			mt19937 gen(rd());
			uniform_int_distribution<> dis(0, 100);
			uniform_int_distribution<> cls(0, 255);

			for (int i = 0; i < 5; i++) {
				wstring wtr = L"测试数据";
				wtr.append(to_wstring(i + 1));

				chart1->AddItem(wtr.c_str(), dis(gen), UIColor(cls(gen), cls(gen), cls(gen)));
				chart2->AddItem(wtr.c_str(), dis(gen), UIColor(cls(gen), cls(gen), cls(gen)));
				chart5->AddItem(wtr.c_str(), dis(gen), UIColor(cls(gen), cls(gen), cls(gen)));
			}
			for (int i = 0; i < 10; i++) {
				wstring wtr = L"测试数据";
				wtr.append(to_wstring(i + 1));

				chart3->AddItem(wtr.c_str(), dis(gen), UIColor(cls(gen), cls(gen), cls(gen)));
				chart4->AddItem(wtr.c_str(), dis(gen), UIColor(cls(gen), cls(gen), cls(gen)));
			}

			chart1->Update();
			chart2->Update();
			chart3->Update();
			chart4->Update();
			chart5->Update();
		}
	}
	return S_OK;
}

void testchart(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 1050, 650, L"hello Chart", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_BTN_HELP | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	random_device rd;
	mt19937 gen(rd());
	uniform_int_distribution<> dis(0, 100);
	uniform_int_distribution<> cls(0, 255);

	auto chart1 = new UIChart(window, 24, 48, 360, 300, L"测试：柱状图-竖向", chart_type::bar, 10001);
	chart1->SetColor(color_text_hover, UIColor(20, 126, 255));
	chart1->SetColor(color_text_ban, UIColor(119, 119, 121));
	chart1->SetColor(color_background, UIColor(230, 245, 255));
	for (int i = 0; i < 5; i++) {
		wstring wstr = L"测试数据";
		wstr.append(to_wstring(i + 1));
		chart1->AddItem(wstr.c_str(), (float)dis(gen), UIColor(cls(gen), cls(gen), cls(gen)));
	}
	chart1->Update();


	auto chart2 = new UIChart(window, 396, 48, 360, 300, L"测试：柱状图-横向", chart_type::bar, 10002);
	chart2->SetColor(color_text_hover, UIColor(20, 126, 255));
	chart2->SetColor(color_text_ban, UIColor(119, 119, 121));
	chart2->SetColor(color_background, UIColor(230, 245, 255));
	for (int i = 0; i < 5; i++) {
		wstring wstr = L"测试数据";
		wstr.append(to_wstring(i + 1));
		chart2->AddItem(wstr.c_str(), (float)dis(gen), UIColor(cls(gen), cls(gen), cls(gen)));
	}
	chart2->SetDataDisplayMode(2);
	chart2->SetBarPoint(false);
	chart2->Update();

	auto chart3 = new UIChart(window, 24, 360, 360, 260, L"测试：折线图", chart_type::line, 10003);
	chart3->SetColor(color_text_hover, UIColor(20, 126, 255));
	chart3->SetColor(color_text_ban, UIColor(119, 119, 121));
	chart3->SetColor(color_background, UIColor(230, 245, 255));
	for (int i = 0; i < 10; i++) {
		wstring wstr = L"测试数据";
		wstr.append(to_wstring(i + 1));
		chart3->AddItem(wstr.c_str(), (float)dis(gen), UIColor(cls(gen), cls(gen), cls(gen)));
	}
	chart3->Update();

	auto chart4 = new UIChart(window, 396, 360, 360, 260, L"测试：曲线图", chart_type::line, 10004);
	chart4->SetColor(color_text_hover, UIColor(20, 126, 255));
	chart4->SetColor(color_text_ban, UIColor(119, 119, 121));
	chart4->SetColor(color_background, UIColor(230, 245, 255));
	for (int i = 0; i < 10; i++) {
		wstring wstr = L"测试数据";
		wstr.append(to_wstring(i + 1));
		chart4->AddItem(wstr.c_str(), (float)dis(gen), UIColor(cls(gen), cls(gen), cls(gen)));
	}
	chart4->SetLineCurve(true);
	chart4->Update();

	auto chart5 = new UIChart(window, 768, 48, 260, 300, L"测试：饼图", chart_type::pie, 10005);
	chart5->SetColor(color_text_hover, UIColor(20, 126, 255));
	chart5->SetColor(color_text_ban, UIColor(119, 119, 121));
	chart5->SetColor(color_background, UIColor(230, 245, 255));
	for (int i = 0; i < 5; i++) {
		wstring wstr = L"测试数据";
		wstr.append(to_wstring(i + 1));
		chart5->AddItem(wstr.c_str(), (float)dis(gen), UIColor(cls(gen), cls(gen), cls(gen)));
	}
	chart5->Update();

	auto btn = new UIButton(window, 768, 360, 200, 40, L"刷新数据", 0, 0, 1001);
	btn->SetEvent(WMM_CLICK, OnButton_Chart_Event);


	window->Show();
	//window->MessageLoop();
}