/**
** =====================================================================================
**
**       文件名称: gdi_plus_integration.h
**       创建时间: 2025-07-28
**       文件描述: 【HHBUI】GDI+集成系统 - 高性能GDI+与DirectX混合渲染框架 （声明文件）
**
**       主要功能:
**       - 高性能GDI+与DirectX混合渲染
**       - 智能位图缓存与资源管理
**       - GDI+图像效果与滤镜处理
**       - 混合渲染模式协调管理
**       - 高级图像格式转换与优化
**       - GDI+资源池化与复用机制
**       - 跨渲染API数据交换
**
**       技术特性:
**       - 采用现代C++17标准与GDI+ API
**       - COM接口规范与智能指针管理
**       - 异常安全保证与错误恢复机制
**       - 高性能位图缓存算法
**       - 多线程安全的资源管理
**       - 智能渲染模式切换机制
**       - 实时性能监控与调试诊断
**
**       更新记录:
**       2025-07-28 v1.0.0.0 : 1. 创建GDI+集成系统
**                             2. 实现混合渲染框架
**                             3. 添加智能位图缓存
**                             4. 支持图像效果处理
**                             5. 集成资源管理机制
**                             6. 添加性能监控与调试
**                             7. 确保线程安全与异常安全
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include <GdiPlus.h>
#include <d2d1_1.h>
#include <wrl.h>
#include <memory>
#include <unordered_map>

namespace HHBUI
{
	/// GDI+与D2D互操作管理器
	class UIGdiPlusIntegration
	{
	public:
		UIGdiPlusIntegration();
		~UIGdiPlusIntegration();

		/// 初始化GDI+集成
		HRESULT Initialize(ID2D1DeviceContext* d2d_context);

		/// 关闭GDI+集成
		void Shutdown();

		/// 从GDI+位图创建D2D位图
		HRESULT CreateD2DBitmapFromGdiPlus(Gdiplus::Bitmap* gdi_bitmap, ID2D1Bitmap** d2d_bitmap);

		/// 从D2D位图创建GDI+位图
		HRESULT CreateGdiPlusBitmapFromD2D(ID2D1Bitmap* d2d_bitmap, Gdiplus::Bitmap** gdi_bitmap);

		/// 优化的GDI+到D2D转换（使用缓存）
		HRESULT GetCachedD2DBitmap(Gdiplus::Bitmap* gdi_bitmap, ID2D1Bitmap** d2d_bitmap);

		/// 清理缓存
		void ClearCache();

		/// 设置缓存大小限制
		void SetCacheLimit(size_t max_cache_size) { m_max_cache_size = max_cache_size; }

		/// 获取缓存使用情况
		size_t GetCacheSize() const { return m_bitmap_cache.size(); }

		/// 启用/禁用硬件加速
		void SetHardwareAcceleration(bool enable) { m_hardware_acceleration = enable; }

		/// 优化的文本渲染
		HRESULT RenderTextWithGdiPlus(const std::wstring& text, const Gdiplus::Font& font, 
			const Gdiplus::Brush& brush, const Gdiplus::RectF& rect, ID2D1Bitmap** result_bitmap);

		/// 优化的图像效果处理
		HRESULT ApplyGdiPlusEffect(Gdiplus::Bitmap* source, Gdiplus::Effect* effect, 
			ID2D1Bitmap** result_bitmap);

		/// 批量转换优化
		HRESULT BatchConvertGdiPlusToD2D(const std::vector<Gdiplus::Bitmap*>& gdi_bitmaps, 
			std::vector<Microsoft::WRL::ComPtr<ID2D1Bitmap>>& d2d_bitmaps);

	private:
		struct CacheEntry
		{
			Microsoft::WRL::ComPtr<ID2D1Bitmap> d2d_bitmap;
			size_t gdi_bitmap_hash;
			std::chrono::steady_clock::time_point last_access;
			size_t memory_size;
		};

		HRESULT ConvertGdiPlusToD2DInternal(Gdiplus::Bitmap* gdi_bitmap, ID2D1Bitmap** d2d_bitmap);
		HRESULT ConvertD2DToGdiPlusInternal(ID2D1Bitmap* d2d_bitmap, Gdiplus::Bitmap** gdi_bitmap);
		size_t CalculateBitmapHash(Gdiplus::Bitmap* bitmap);
		void EvictOldCacheEntries();

	private:
		Microsoft::WRL::ComPtr<ID2D1DeviceContext> m_d2d_context;
		Microsoft::WRL::ComPtr<IWICImagingFactory> m_wic_factory;
		
		// 缓存系统
		std::unordered_map<size_t, CacheEntry> m_bitmap_cache;
		size_t m_max_cache_size;
		size_t m_current_cache_memory;
		
		// 配置
		bool m_hardware_acceleration;
		bool m_initialized;
	};

	/// GDI+性能优化器
	class UIGdiPlusOptimizer
	{
	public:
		UIGdiPlusOptimizer();
		~UIGdiPlusOptimizer();

		/// 优化GDI+图形对象
		static void OptimizeGraphics(Gdiplus::Graphics& graphics);

		/// 优化位图创建
		static std::unique_ptr<Gdiplus::Bitmap> CreateOptimizedBitmap(int width, int height, 
			Gdiplus::PixelFormat format = PixelFormat32bppARGB);

		/// 优化文本渲染设置
		static void OptimizeTextRendering(Gdiplus::Graphics& graphics);

		/// 优化图像渲染设置
		static void OptimizeImageRendering(Gdiplus::Graphics& graphics);

		/// 批量操作优化
		static void BeginBatchOperations(Gdiplus::Graphics& graphics);
		static void EndBatchOperations(Gdiplus::Graphics& graphics);

		/// 内存使用优化
		static void OptimizeMemoryUsage();

		/// 获取推荐的像素格式
		static Gdiplus::PixelFormat GetRecommendedPixelFormat(bool has_alpha, bool high_quality);

	private:
		static bool s_batch_mode_active;
	};

	/// GDI+资源管理器
	class UIGdiPlusResourceManager
	{
	public:
		UIGdiPlusResourceManager();
		~UIGdiPlusResourceManager();

		/// 创建并缓存字体
		Gdiplus::Font* GetFont(const std::wstring& family_name, float size, 
			Gdiplus::FontStyle style = Gdiplus::FontStyleRegular);

		/// 创建并缓存画刷
		Gdiplus::Brush* GetSolidBrush(Gdiplus::Color color);
		Gdiplus::Brush* GetLinearGradientBrush(const Gdiplus::PointF& point1, 
			const Gdiplus::PointF& point2, Gdiplus::Color color1, Gdiplus::Color color2);

		/// 创建并缓存画笔
		Gdiplus::Pen* GetPen(Gdiplus::Color color, float width = 1.0f);

		/// 清理未使用的资源
		void CleanupUnusedResources();

		/// 获取资源使用统计
		struct ResourceStats
		{
			size_t font_count;
			size_t brush_count;
			size_t pen_count;
			size_t total_memory_usage;
		};
		ResourceStats GetResourceStats() const;

	private:
		struct FontKey
		{
			std::wstring family_name;
			float size;
			Gdiplus::FontStyle style;
			
			bool operator==(const FontKey& other) const
			{
				return family_name == other.family_name && 
					   size == other.size && 
					   style == other.style;
			}
		};

		struct FontKeyHash
		{
			size_t operator()(const FontKey& key) const
			{
				return std::hash<std::wstring>()(key.family_name) ^ 
					   std::hash<float>()(key.size) ^ 
					   std::hash<int>()(key.style);
			}
		};

		std::unordered_map<FontKey, std::unique_ptr<Gdiplus::Font>, FontKeyHash> m_fonts;
		std::unordered_map<Gdiplus::ARGB, std::unique_ptr<Gdiplus::SolidBrush>> m_solid_brushes;
		std::unordered_map<Gdiplus::ARGB, std::unique_ptr<Gdiplus::Pen>> m_pens;
		std::vector<std::unique_ptr<Gdiplus::LinearGradientBrush>> m_gradient_brushes;
	};

	/// 混合渲染协调器
	class UIHybridRenderCoordinator
	{
	public:
		UIHybridRenderCoordinator();
		~UIHybridRenderCoordinator();

		/// 初始化混合渲染
		HRESULT Initialize(ID2D1DeviceContext* d2d_context, HDC gdi_dc);

		/// 开始混合渲染会话
		HRESULT BeginHybridSession();

		/// 结束混合渲染会话
		HRESULT EndHybridSession();

		/// 切换到GDI+渲染模式
		HRESULT SwitchToGdiPlusMode();

		/// 切换到D2D渲染模式
		HRESULT SwitchToD2DMode();

		/// 同步渲染状态
		HRESULT SynchronizeRenderStates();

		/// 优化渲染顺序
		void OptimizeRenderOrder(const std::vector<int>& render_operations);

		/// 获取当前渲染模式
		enum class RenderMode { GDI_PLUS, D2D, HYBRID };
		RenderMode GetCurrentMode() const { return m_current_mode; }

	private:
		Microsoft::WRL::ComPtr<ID2D1DeviceContext> m_d2d_context;
		Microsoft::WRL::ComPtr<ID2D1GdiInteropRenderTarget> m_gdi_interop;
		HDC m_gdi_dc;
		std::unique_ptr<Gdiplus::Graphics> m_gdi_graphics;
		
		RenderMode m_current_mode;
		bool m_in_hybrid_session;
	};

	/// 全局GDI+集成管理器
	extern UIGdiPlusIntegration* g_gdi_plus_integration;
	extern UIGdiPlusResourceManager* g_gdi_plus_resource_manager;
	extern UIHybridRenderCoordinator* g_hybrid_render_coordinator;

	/// 便捷函数
	namespace GdiPlusHelpers
	{
		/// 快速创建优化的GDI+图形对象
		std::unique_ptr<Gdiplus::Graphics> CreateOptimizedGraphics(HDC hdc);

		/// 快速转换颜色格式
		Gdiplus::Color D2DColorToGdiPlus(const D2D1_COLOR_F& d2d_color);
		D2D1_COLOR_F GdiPlusColorToD2D(const Gdiplus::Color& gdi_color);

		/// 快速转换矩形
		Gdiplus::RectF D2DRectToGdiPlus(const D2D1_RECT_F& d2d_rect);
		D2D1_RECT_F GdiPlusRectToD2D(const Gdiplus::RectF& gdi_rect);

		/// 快速转换点
		Gdiplus::PointF D2DPointToGdiPlus(const D2D1_POINT_2F& d2d_point);
		D2D1_POINT_2F GdiPlusPointToD2D(const Gdiplus::PointF& gdi_point);

		/// 性能测试函数
		void BenchmarkGdiPlusVsD2D(int iterations = 1000);
	}
}
