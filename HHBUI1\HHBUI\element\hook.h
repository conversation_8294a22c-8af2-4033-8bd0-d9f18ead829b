﻿#pragma once
namespace HHBUI
{
	enum MsgStyleExFlags : uint32_t
	{
		styleex_noshadow = 0x8000000,           //不显示阴影 信息框/菜单有效
		styleex_noinheritbkg = 0x20000000,      //不继承父窗口背景数据 信息框有效
		styleex_centewindow = 0x40000000,       //居父窗口中间 信息框有效
		styleex_windowicon = 0x80000000,        //显示窗口图标 信息框有效
	};
	struct EX_EVENT_HANDLER
	{
		LPVOID parent;
		EventHandlerPROC pfnCallback;
	};
	struct mbp_s
	{
		LPVOID pWnd;
		LPCWSTR lpText;
		LPCWSTR lpTitle;
		INT uType;
		LPCWSTR lpCheckBox;
		LPVOID lpCheckBoxChecked;
		LPVOID lpCheckBoxObj;
		INT dwFlags;
		MsgPROC lpfnNotifyCallback;
		UIColor crText;
		UIColor crBackground;
		UIColor crshadow;
		HWND hWnd;
		INT radius;
		LPVOID dwButfig;
	};
	struct ti_s
	{
		INT cbSize_;
		INT uFlags_;
		HWND hWnd_;
		LPVOID uId_;
		INT rect_left_;
		INT rect_top_;
		INT rect_right_;
		INT rect_bottom_;
		INT hinst_;
		LPCWSTR lpszText_;
		LPARAM lParam_;
	};
	struct menu_s
	{
		LPVOID hMenu;
		INT uFlags;
		INT x;
		INT y;
		size_t nReserved;
		RECT* lpRC;
		MsgPROC pfnCallback;
		BOOL IsRadius;
		UIColor crBorder;
		UIColor crshadow;
		UIColor crText;
		UIColor crTextHot;
		UIColor crBackground;
		UIColor crItemHot;
	};
	class UIhook
	{
	public:
		static LPVOID Thunkwindow(HWND hWnd, ThunkPROC pfnProc, LPVOID dwData);
		static LRESULT CALLBACK hook_api_proc(INT code, WPARAM wParam, LPARAM lParam);
		static LRESULT hook_api_oncreate(INT code, HWND hWnd, LPARAM lParam);
		static void hook_api_msgbox_drawinfo(UIWnd* pWnd, UICanvas* cvBkg, INT width, INT height);
		static void hook_api_msgbox_initdialog(HWND hWnd, UIWnd* pWnd, WPARAM wParam, LPARAM lParam);
		static void hook_api_menu_initdialog(HWND hWnd);
		static LRESULT CALLBACK hook_api_menu_proc(HK_THUNK_DATA* pData, UINT uMsg, WPARAM wParam, LPARAM lParam);
	};
}
