﻿#include "hhbui.h"

using namespace HHBUI;
void testlist(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 1100, 500, L"hello List", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	//测试纵向列表
	auto list = new UIList(window, 30, 50, 300, 400, eos_scroll_controlbutton | eos_scroll_v | eos_elvs_verticallist | eos_elvs_allowmultiple, 0, 0, Middle | Left);
	list->SetColor(color_background, UIColor(L"#ebefed"));
	//list->SetColor(color_text_normal, UIColor(196, 43, 28, 255));
	list->SetColor(color_text_hover, UIColor(236, 151, 31, 255));
	list->SetColor(color_text_down, UIColor(0, 108, 190, 255));
	list->SetScrollColor(UIColor(1, 170, 237, 255), UIColor(30, 159, 255, 255), UIColor(30, 159, 255, 255), UIColor(255, 87, 34, 255), UIColor(255, 87, 34, 255));
	list->SetScrollRadius(TRUE);
	list->SetItemHeight(40);
	list->SetItemSplitHeight(5);

	//测试横向列表
	auto list2 = new UIList(window, 350, 50, 300, 400, eos_scroll_controlbutton | eos_scroll_h | eos_elvs_horizontallist, 0, 0, Middle | Left);
	list2->SetColor(color_background, UIColor(Wheat));
	list2->SetColor(color_text_normal, UIColor(196, 43, 28, 255));
	list2->SetColor(color_text_hover, UIColor(236, 151, 31, 255));
	list2->SetColor(color_text_down, UIColor(0, 108, 190, 255));
	list2->SetScrollRadius(TRUE);
	list2->SetItemWidth(150);
	list2->SetItemHeight(60);

	//自定义项目尺寸列表
	auto list3 = new UIList(window, 680, 50, 400, 400, eos_scroll_controlbutton | eos_scroll_v | eos_elvs_verticallist, 0, 0, Middle | Left);
	list3->SetColor(color_background, UIColor(L"#ebefed"));
	list3->SetColor(color_text_normal, UIColor(196, 43, 28, 255));
	list3->SetColor(color_text_hover, UIColor(236, 151, 31, 255));
	list3->SetColor(color_text_down, UIColor(0, 108, 190, 255));
	list3->SetScrollColor(UIColor(1, 170, 237, 255), UIColor(30, 159, 255, 255), UIColor(30, 159, 255, 255), UIColor(255, 87, 34, 255), UIColor(255, 87, 34, 255));
	list3->SetScrollRadius(TRUE);
	list3->SetItemWidth(150);
	list3->SetItemHeight(50);
	list3->SetItemSplitHeight(5);
	window->Layout_Init(elt_absolute);
	window->Layout_Absolute_Setedge(list3, elcp_absolute_left, elcp_absolute_type_px, 680);
	window->Layout_Absolute_Setedge(list3, elcp_absolute_top, elcp_absolute_type_px, 50);
	window->Layout_Absolute_Setedge(list3, elcp_absolute_right, elcp_absolute_type_px, 10);
	window->Layout_Absolute_Setedge(list3, elcp_absolute_bottom, elcp_absolute_type_px, 50);


	for (size_t i = 0; i < 1000; i++)
	{
		std::wstring text = L"我是测试列表项目" + std::to_wstring(i + 1);
		list->AddItem(text.c_str());
		list2->AddItem(text.c_str());
		list3->AddItem(text.c_str());
	}
	auto img1 = new UIImage(L"icons\\lollipop.png");
	list->SetItem(2, NULL, img1);
	list2->SetItem(2, NULL, img1);
	list3->SetItem(2, NULL, img1);

	list->Update();
	list2->Update();
	//list2->SetSelect(50);
	//list2->SetEnsureVisible(50);

	list3->Update();

	list->SetItemState(2, state_select);


	window->Show();
	//window->MessageLoop();
}