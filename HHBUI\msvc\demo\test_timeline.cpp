﻿#include "hhbui.h"
using namespace HHBUI;
void testtimeline(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 776, 572, L"hello TimeLine", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);
	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	LPCWSTR trTitle[] = { L"标题1",L"测试标题2",L"比较长的测试标题3",L"很长很长很长很长很长很长很长的测试标题3",L"标题4",L"English Title 5",L"标题6",L"标题7",L"标题8" };
	LPCWSTR trText[] = {
		L"横看成岭侧成峰，远近高低各不同",
		L"出其东门，有女如云。虽则如云，匪我思存。缟衣綦巾，聊乐我员。",
		L"桃李春风一杯酒，江湖夜雨十年灯。",
		L"人生到处知何似，应似飞鸿踏雪泥。",
		L"风一更，雪一更，聒碎乡心梦不成，故园无此声。",
		L"若待明朝风雨过，人在天涯！春在天涯。",
		L"大江东去，浪淘尽，千古风流人物。\n故垒西边，人道是，三国周郎赤壁。\n乱石穿空，惊涛拍岸，卷起千堆雪。\n江山如画，一时多少豪杰。\n遥想公瑾当年，小乔初嫁了，雄姿英发。\n羽扇纶巾，谈笑间，樯橹灰飞烟灭。\n故国神游，多情应笑我，早生华发。\n人生如梦，一尊还酹江月。",
		L"水光潋滟晴方好，山色空蒙雨亦奇",
	};

	auto label = new UIStatic(window, 24, 48, 120, 24, L"时间轴：默认：", 0, 0, 0, Left | Middle);
	label->SetFontFromFamily(L"微软雅黑", 10);
	label->SetColor(color_text_normal, UIColor(149, 149, 151));
	auto tl1 = new UITimeLine(window, 24, 72, 120, 500, 10001);
	for (int i = 0; i < 8; i++) tl1->AddItem(trText[i], L"2025-4-2 14:45");
	tl1->Update();

	label = new UIStatic(window, 156, 48, 200, 24, L"时间轴：显示标题：", 0, 0, 0, Left | Middle);
	label->SetFontFromFamily(L"微软雅黑", 10);
	label->SetColor(color_text_normal, UIColor(149, 149, 151));
	auto tl2 = new UITimeLine(window, 156, 72, 200, 500, 10002);
	for (int i = 0; i < 8; i++) tl2->AddItem(trText[i], L"2025-4-2 14:45", trTitle[i]);
	tl2->SetShowTitle(true);
	tl2->Update();

	label = new UIStatic(window, 368, 48, 120, 24, L"时间轴：时间戳在下：", 0, 0, 0, Left | Middle);
	label->SetFontFromFamily(L"微软雅黑", 10);
	label->SetColor(color_text_normal, UIColor(149, 149, 151));
	auto tl3 = new UITimeLine(window, 368, 72, 120, 500, 10003);
	for (int i = 0; i < 8; i++) tl3->AddItem(trText[i], L"2025-4-2 14:45");
	tl3->SetShowTimeTop(false);
	tl3->Update();

	label = new UIStatic(window, 500, 48, 120, 24, L"时间轴：自定义颜色：", 0, 0, 0, Left | Middle);
	label->SetFontFromFamily(L"微软雅黑", 10);
	label->SetColor(color_text_normal, UIColor(149, 149, 151));
	auto tl4 = new UITimeLine(window, 500, 72, 120, 500, 10004);
	for (int i = 0; i < 8; i++) {
		tl4->AddItem(trText[i], L"2025-4-2 14:45", trTitle[i]);
		UIColor clr = UIColor(randoms(0, 255), randoms(0, 255), randoms(0, 255));
		tl4->SetItemTitleColor(i, clr);
		tl4->SetItemTextColor(i, clr);
		tl4->SetItemTimeColor(i, clr);
		tl4->SetItemNodeColor(i, clr);
	}
	tl4->SetShowTitle(true);
	tl4->SetShowTimeTop(false);
	tl4->Update();

	label = new UIStatic(window, 632, 48, 120, 24, L"时间轴：杀马特版：", 0, 0, 0, Left | Middle);
	label->SetFontFromFamily(L"微软雅黑", 10);
	label->SetColor(color_text_normal, UIColor(149, 149, 151));
	auto tl5 = new UITimeLine(window, 632, 72, 120, 500, 10005);
	for (int i = 0; i < 8; i++) {
		tl5->AddItem(trText[i], L"2025-4-2 14:45", trTitle[i]);
		tl5->SetItemTitleColor(i, UIColor(randoms(0, 255), randoms(0, 255), randoms(0, 255)));
		tl5->SetItemTextColor(i, UIColor(randoms(0, 255), randoms(0, 255), randoms(0, 255)));
		tl5->SetItemTimeColor(i, UIColor(randoms(0, 255), randoms(0, 255), randoms(0, 255)));
		tl5->SetItemNodeColor(i, UIColor(randoms(0, 255), randoms(0, 255), randoms(0, 255)));
	}
	tl5->SetShowTitle(true);
	tl5->Update();

	window->Show();
	//window->MessageLoop();
}