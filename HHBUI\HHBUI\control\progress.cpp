﻿#include "pch.h"
#include "progress.h"
#include <random>
using namespace std;

HHBUI::UIProgress::UIProgress(UIBase *hParent, INT x, INT y, INT width, INT height, LPCTSTR title, INT nID, INT dwStyle, INT dwStyleEx, INT textFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-progress", title, dwStyle, dwStyleEx, nID, textFormat);
	p_data.rad = min(height, width) / 2;
	SetColor(color_text_normal, UIColor(253, 253, 255, 255));
}

void HHBUI::UIProgress::SetBarRadius(UINT radius)
{
	p_data.rad = radius;
}

void HHBUI::UIProgress::SetType(progress_type type)
{
	p_data.type = type;
	if (type != stroke) SetColor(color_text_normal, UIColor(29, 29, 31, 255));
	if (type == wave)
	{
		p_data.intLeftX = -200;
		p_data.waveWidth = 200;
		p_data.waveHeight = 30;
		p_data.bubbleIntervalTime = 200;
		p_data.lastBubbleTime = 0;
		p_data.lstBubble = std::vector<Bubble_s>();
	}
}

void HHBUI::UIProgress::SetPoint(progress_point point)
{
	p_data.point = point;
}

void HHBUI::UIProgress::SetBarColor(UIColor nor, UIColor fill)
{
	if (!nor.empty()) p_data.clr[0] = nor;
	if (!fill.empty()) p_data.clr[1] = fill;
}

void HHBUI::UIProgress::SetValue(INT value)
{
	p_data.value = value;
	Redraw();
}

INT HHBUI::UIProgress::GetValue()
{
	return p_data.value;
}

void HHBUI::UIProgress::SetRange(INT range)
{
	p_data.range = range;
	Redraw();
}

INT HHBUI::UIProgress::GetRange()
{
	return p_data.range;
}

void HHBUI::UIProgress::SetLineWidth(UINT width)
{
	p_data.wid = width;
}

void HHBUI::UIProgress::IsShwoText(BOOL isshow)
{
	p_data.bshowText = isshow;
}
void HHBUI::UIProgress::SetWaveWidth(INT fWidth)
{
	p_data.waveWidth = fWidth / 10 * 10;
	p_data.intLeftX = fWidth * -1;
}
void HHBUI::UIProgress::SetWaveHeight(INT fHeight)
{
	p_data.waveHeight = fHeight;
}
void HHBUI::UIProgress::SetWaveBubbleSpeed(INT fSpeed)
{
	p_data.bubbleSpeed = fSpeed;
}
LRESULT HHBUI::UIProgress::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_TIMER)
	{
		if (p_data.type == wave)
		{
			p_data.intLeftX -= 10;
			if (p_data.intLeftX == p_data.waveWidth * -2)
				p_data.intLeftX = p_data.waveWidth * -1;

			if (p_data.bubbleSpeed > 0)
			{
				p_data.lastBubbleTime += wParam; 

				if (p_data.lastBubbleTime >= p_data.bubbleIntervalTime)
				{
					p_data.lastBubbleTime = 0;

					std::random_device rd;
					std::mt19937 rng(rd());
					std::uniform_int_distribution<int> uni(0, m_data.Frame.right - m_data.Frame.left);

					int b_x = uni(rng);

					Bubble_s b;
					b.Key = std::to_string(std::rand());
					b.BubblePoint = ExPoint(b_x, 0);
					b.BubbleWidth = 5;

					if (p_data.lstBubble.size() >= 100)
					{
						p_data.lstBubble.erase(p_data.lstBubble.begin()); 
					}
					p_data.lstBubble.emplace_back(b);
				}
			}

			Redraw();
		}
	}
	else if (uMsg == WM_SIZE)
	{
		int pwidth = GET_X_LPARAM(lParam);
		int pheight = GET_Y_LPARAM(lParam);


	}
	return S_OK;
}

void HHBUI::UIProgress::OnPaintProc(ps_context ps)
{
	auto br = UIBrush(p_data.clr[0]);

	float val = (float)p_data.value / p_data.range;
	auto tit = vstring::replace_all(GetText(), L"{{value}}", std::to_wstring(p_data.value));
	INT rod = p_data.rad * ps.dpi;
	UIColor crTitle;
	GetColor(color_text_normal, crTitle);

	if (p_data.type == stroke) {
		ps.hCanvas->FillRoundRect(&br, 0, 0, ps.uWidth, ps.uHeight, rod);

		br.SetColor(p_data.clr[1]);
		if (p_data.point == left) {
			ps.hCanvas->FillRoundRect(&br, (float)ps.uWidth - val * ps.uWidth, 0, ps.uWidth, ps.uHeight, rod);
			if (p_data.bshowText) ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Left | TextFormat::Middle, (float)ps.uWidth - val * ps.uWidth + 4, 0, ps.uWidth - 4, ps.uHeight, crTitle);
		}
		else if (p_data.point == top) {
			ps.hCanvas->FillRoundRect(&br, 0, (float)ps.uHeight - val * ps.uHeight, ps.uWidth, ps.uHeight, rod);
			if (p_data.bshowText) ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Top | TextFormat::Center, 0, (float)ps.uHeight - val * ps.uHeight + 4, ps.uWidth, ps.uHeight - 4, crTitle);
		}
		else if (p_data.point == right) {
			ps.hCanvas->FillRoundRect(&br, 0, 0, (float)val * ps.uWidth, ps.uHeight, rod);
			if (p_data.bshowText) ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Right | TextFormat::Middle, 4, 0, (float)val * ps.uWidth - 4, ps.uHeight, crTitle);
		}
		else if (p_data.point == bottom) {
			ps.hCanvas->FillRoundRect(&br, 0, 0, ps.uWidth, (float)val * ps.uHeight, rod);
			if (p_data.bshowText) ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Bottom | TextFormat::Center, 0, 0, ps.uWidth, (float)val * ps.uHeight - 4, crTitle);
		}
	}
	else if (p_data.type == line) {
		float tw = 0, th = 0;
		if (p_data.bshowText) ps.hCanvas->CalcTextSize(ps.hFont, tit.c_str(), TextFormat::Middle, ps.uWidth, ps.uHeight, &tw, &th);

		if (p_data.point == left) {
			INT atl = (ps.uHeight - p_data.wid) / 2;
			ps.hCanvas->FillRoundRect(&br, tw, atl, ps.uWidth, atl + p_data.wid, rod);
			br.SetColor(p_data.clr[1]);
			ps.hCanvas->FillRoundRect(&br, (float)ps.uWidth - val * (ps.uWidth - tw), atl, ps.uWidth, atl + p_data.wid, rod);
			if (p_data.bshowText) ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Right | TextFormat::Middle, 0, 0, tw, ps.uHeight, crTitle);
		}
		else if (p_data.point == top) {
			INT atl = (ps.uWidth - p_data.wid) / 2;
			ps.hCanvas->FillRoundRect(&br, atl, th, atl + p_data.wid, ps.uHeight, rod);
			br.SetColor(p_data.clr[1]);
			ps.hCanvas->FillRoundRect(&br, atl, (float)ps.uHeight - val * (ps.uHeight - th), atl + p_data.wid, ps.uHeight, rod);
			if (p_data.bshowText) ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Center | TextFormat::Middle, 0, 0, ps.uWidth, th, crTitle);
		}
		else if (p_data.point == right) {
			INT atl = (ps.uHeight - p_data.wid) / 2;
			ps.hCanvas->FillRoundRect(&br, 0, atl, ps.uWidth - tw, atl + p_data.wid, rod);
			br.SetColor(p_data.clr[1]);
			ps.hCanvas->FillRoundRect(&br, 0, atl, (float)val * (ps.uWidth - tw), atl + p_data.wid, rod);
			if (p_data.bshowText) ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Left | TextFormat::Middle, ps.uWidth - tw, 0, ps.uWidth, ps.uHeight, crTitle);
		}
		else if (p_data.point == bottom) {
			INT atl = (ps.uWidth - p_data.wid) / 2;
			ps.hCanvas->FillRoundRect(&br, atl, 0, atl + p_data.wid, ps.uHeight - th, rod);
			br.SetColor(p_data.clr[1]);
			ps.hCanvas->FillRoundRect(&br, atl, 0, atl + p_data.wid, (float)val * (ps.uHeight - th), rod);
			if (p_data.bshowText) ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Center | TextFormat::Middle, 0, ps.uHeight - th, ps.uWidth, ps.uHeight, crTitle);
		}
	}
	else if (p_data.type == ring || p_data.type == ring_round) {
		UINT rr = floor(min(ps.uWidth, ps.uHeight) / 2), wr = ceil(p_data.wid / 2);

		UIPath pbk;
		pbk.BeginPath();
		pbk.AddEllipse(wr, wr, rr * 2 - wr, rr * 2 - wr);
		pbk.EndPath();

		ps.hCanvas->DrawPath(&br, &pbk, p_data.wid);
		br.SetColor(p_data.clr[1]);

		UIPath pfill;
		pfill.BeginPath();
		pfill.StartFigure(rr, wr);
		float angle = 360 * val * M_PI / 180;
		//float endx = rr + (rr - wr) * cos(angle), endy = rr + (rr - wr) * sin(angle);
		float endx = rr + (rr - wr) * cos(angle / 2), endy = rr + (rr - wr) * sin(angle / 2);
		pfill.ArcTo(rr - wr, rr - wr, 0, true, true, endx, endy);


		pfill.FinishFigure();
		pfill.EndPath();

		ps.hCanvas->DrawPath(&br, &pfill, p_data.wid - 2, 0, p_data.type == ring_round ? D2D1_CAP_STYLE_ROUND : D2D1_CAP_STYLE_SQUARE);

		if (p_data.bshowText)
			ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Center | TextFormat::Middle, p_data.wid, p_data.wid, ps.uWidth - p_data.wid, ps.uHeight - p_data.wid, crTitle);
	}
	else if (p_data.type == dashboard) {

	}
	else if (p_data.type == wave) {

		HDC hDC = nullptr;
		ps.hCanvas->GetDC(&hDC);
		if (hDC)
		{
			Gdiplus::Graphics g(hDC);
			g.Clear(0);
			g.SetCompositingQuality(Gdiplus::CompositingQuality::CompositingQualityHighQuality);
			g.SetSmoothingMode(Gdiplus::SmoothingMode::SmoothingModeAntiAlias);
			g.SetInterpolationMode(Gdiplus::InterpolationMode::InterpolationModeHighQualityBicubic);
			if (!p_data.clr[0].empty())
			{
				Gdiplus::SolidBrush sh(p_data.clr[0].GetARGB());
				g.FillRectangle(&sh, 0, 0, ps.uWidth, ps.uHeight);
			}
			std::vector<Gdiplus::Point> outline1;
			std::vector<Gdiplus::Point> outline2;

			int m_intX = p_data.intLeftX, m_value = (int)(double)p_data.value / (double)p_data.range * ps.uHeight - p_data.waveHeight;
			while (m_intX < (int)ps.uWidth + p_data.waveWidth)
			{
				outline1.emplace_back(Gdiplus::Point(m_intX, ps.uHeight - m_value));
				outline1.emplace_back(Gdiplus::Point(m_intX + p_data.waveWidth / 2, ps.uHeight - m_value - p_data.waveHeight));

				outline2.emplace_back(Gdiplus::Point(m_intX + p_data.waveWidth / 2, ps.uHeight - m_value));
				outline2.emplace_back(Gdiplus::Point(m_intX + p_data.waveWidth / 2 + p_data.waveWidth / 2, ps.uHeight - m_value - p_data.waveHeight));

				m_intX += p_data.waveWidth;
			}

			Gdiplus::GraphicsPath path1;
			path1.AddCurve(outline1.data(), static_cast<int>(outline1.size()), 0.5F);
			path1.AddLine(ps.uWidth + 1, -1, ps.uWidth + 1, ps.uHeight + m_value);
			path1.AddLine(ps.uWidth + 1, ps.uHeight + m_value, -1, ps.uHeight + m_value);
			path1.AddLine(-1, ps.uHeight + m_value, -1, -1);
			outline1.clear();

			Gdiplus::GraphicsPath path2;
			path2.AddCurve(outline2.data(), static_cast<int>(outline2.size()), 0.5F);
			path2.AddLine(ps.uWidth + 1, -1, ps.uWidth + 1, ps.uHeight + m_value);
			path2.AddLine(ps.uWidth + 1, ps.uHeight + m_value, -1, ps.uHeight + m_value);
			path2.AddLine(-1, ps.uHeight + m_value, -1, -1);
			outline2.clear();

			Gdiplus::SolidBrush brush1(p_data.clr[1].GetARGB());
			g.FillPath(&brush1, &path1);

			Gdiplus::SolidBrush brush2(p_data.clr[1].GetARGB());
			g.FillPath(&brush2, &path2);


			for (auto& item : p_data.lstBubble) {
				Gdiplus::GraphicsPath gp;
				Gdiplus::Rect rec(item.BubblePoint.x - item.BubbleWidth / 2,
					ps.uHeight - m_value - (item.BubblePoint.y - item.BubbleWidth / 2),
					item.BubbleWidth / 3 * 2,
					item.BubbleWidth / 3 * 2);
				gp.AddEllipse(rec);

				Gdiplus::PathGradientBrush pb(&gp);
				pb.SetCenterColor(Gdiplus::Color(150, 255, 255, 255));

				Gdiplus::Color colors[] = { Gdiplus::Color(50, 255, 255, 255) };
				INT count = 1;
				pb.SetSurroundColors(colors, &count);
				g.FillPath(&pb, &gp);

				item.BubblePoint = ExPoint(item.BubblePoint.x, item.BubblePoint.y + p_data.bubbleSpeed);
				item.BubbleWidth += 1;
			}
			ps.hCanvas->ReleaseDC();

			if (p_data.bshowText)
			{
				ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Center | TextFormat::Middle, p_data.wid, p_data.wid, ps.uWidth - p_data.wid, ps.uHeight - p_data.wid, crTitle);
			}
		}
	}
}
