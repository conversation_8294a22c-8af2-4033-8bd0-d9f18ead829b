﻿#pragma once
#include <wincodec.h>
#include <d3d11.h>
#include <d2d1_1.h>
#include <d2d1_3.h>
#include <d2d1.h>
#include <dwrite.h>
#include <dwrite_3.h>
#include <dxgi1_2.h>
#pragma comment(lib, "dxgi.lib")
#pragma comment(lib, "d3d11.lib")
#pragma comment(lib, "d2d1.lib")
#pragma comment(lib, "Dwrite.lib")
#pragma comment(lib, "dxguid.lib")
#pragma comment(lib, "windowscodecs.lib")
#include <d2d1effects.h>
#include <d2d1effects_2.h>
#include <d2d1effectauthor.h>
#include <d2d1effecthelpers.h>
#include <Initguid.h>
#include <d3dcompiler.h>
#pragma comment(lib, "d3dcompiler.lib")
#include <shlwapi.h>
#pragma comment(lib, "shlwapi.lib")
#pragma comment(lib, "winmm.lib")
#pragma region 忽略警告配置项

// "=": 从"XX"转换到"YY"，可能丢失数据
#pragma warning(disable: 4244)

// "XXX": 未引用的标签
#pragma warning(disable: 4102)

// 非成员运算符 new 或 delete 函数均不可声明为内联函数
#pragma warning(disable: 4595)

//类函数宏的调用"XXX"参数不足
#pragma warning(disable: 4003)

//"XXX": 类型名称以前使用"class"现在使用的是"struct"
#pragma warning(disable: 4099)
//需要有 dll 接口以供"XXX"的客户端使用
#pragma warning(disable: 4251)
#pragma warning(disable: 4996)
#pragma endregion
#define HHBUI_VERSION       L"1.0.0.01250720 Alpha (Windows)"
#define HHBUI_VERSION_NUM   10001250720

namespace HHBUI
{
	//读文件到缓冲区
	HRESULT TOAPI UIreadFile(LPCWSTR filePath, LPVOID& retData, size_t& retSize);
	//写入文件
	HRESULT TOAPI UIWriteFile(LPCWSTR file, const LPVOID data, size_t size);
	//取文件尺寸
	HRESULT TOAPI UIGetFileSize(LPCWSTR file, size_t& r_size);
	/*
	* @brief 文件对话框
	* @param hWnd 父窗口句柄
	* @param bOpenFileDialog TRUE for file open, FALSE for file save
	* @param lpszFilter 过滤器 默认： 所有文件|*.*||
	* @param lpszDefExt 默认保存文件后缀 保存模式有效
	* @param lpszFileName 默认保存文件名 保存模式有效
	* @param retstrFile 返回文件名 多个文件会以|分割
	* @return [BOOL]
	*/
	BOOL TOAPI UIfileOpenDlg(HWND hWnd, BOOL bOpenFileDialog, LPCWSTR lpszFilter, LPCTSTR lpszDefExt, LPCWSTR lpszFileName, LPCWSTR* retstrFile);
	/*
	* @brief 对话框_浏览文件夹
	* @param hWnd 窗口句柄
	* @param lpszTitle 标题
	* @param retstrFile 返回目录
	* @return [BOOL]
	*/
	BOOL TOAPI UIfileOpenFolder(HWND hWnd, LPCWSTR lpszTitle, LPCWSTR* retstrFile);
	/*
	* @brief 查找指定文件，并将文件名保存在files容器
	* @param file_name：例如 D:\\Music\\*abc*.mp3，则将查找D:\Music目录下所有包含abc的mp3文件
	* @param fun_is_valid: 一个函数对象，用于判断找到的文件是否要添加到files中，参数为文件名。默认全部返回true
	*/
	void TOAPI UIGetFiles(std::wstring file_name, std::vector<std::wstring>& files, std::function<bool(const std::wstring&)> fun_is_valid = [](const std::wstring& file_path) { return true; });
	/*
	* @brief 取文件名
	* @param fWiExt 去除文件扩展名
	*/
	LPCWSTR TOAPI UIGetFileName(LPCWSTR filePath, BOOL fWiExt = false);

	// ==================== 新增增强版文件操作函数 ====================

	// 增强版文件读取函数 - 支持多路径尝试和详细错误信息
    HRESULT TOAPI UIreadFileEx(LPCWSTR filePath, LPVOID& retData, size_t& retSize,
                               BOOL tryAlternatePaths = TRUE,
                               BOOL enableDebugOutput = FALSE);

    // 安全读取文件 - 自动处理异常并提供默认值
    template<typename T>
    HRESULT TOAPI UIreadFileSafe(LPCWSTR filePath, T& retData, size_t& retSize,
                                 T defaultData = nullptr, size_t defaultSize = 0) {
        HRESULT hr = UIreadFileEx(filePath, (LPVOID&)retData, retSize, TRUE, FALSE);
        if (FAILED(hr) && defaultData != nullptr) {
            retData = defaultData;
            retSize = defaultSize;
            return S_FALSE; // 返回S_FALSE表示使用了默认值
        }
        return hr;
    }

    // 路径处理工具函数

    // 检查文件是否存在
    BOOL TOAPI UIFileExists(LPCWSTR filePath);

    // 获取应用程序目录
    HRESULT TOAPI UIGetAppDirectory(LPWSTR outPath, DWORD bufferSize);

    // 将相对路径转换为绝对路径
    HRESULT TOAPI UIGetAbsolutePath(LPCWSTR relativePath, LPWSTR absolutePath, DWORD bufferSize);

    // 标准化文件路径（处理../和./等）
    HRESULT TOAPI UINormalizePath(LPCWSTR path, LPWSTR normalizedPath, DWORD bufferSize);

    // 图像文件专用函数

    // 图像文件加载函数 - 使用WIC处理各种图像格式
    HRESULT TOAPI UIreadImageFile(LPCWSTR filePath, LPVOID& retData, size_t& retSize,
                                 BOOL tryAlternatePaths = TRUE);

    // 图像格式转换函数 - 转换为目标格式
    HRESULT TOAPI UIConvertImageFormat(LPVOID srcData, size_t srcSize,
                                      LPVOID& dstData, size_t& dstSize,
                                      GUID targetFormat = GUID_ContainerFormatPng);

    // 文件操作辅助功能

    // 安全创建目录（包括所有必要的父目录）
    HRESULT TOAPI UICreateDirectory(LPCWSTR dirPath);

    // 设置默认资源目录
    HRESULT TOAPI UISetResourceDirectory(LPCWSTR resourceDir);

    // 获取当前资源目录
    LPCWSTR TOAPI UIGetResourceDirectory();

    // 从资源目录加载文件
    HRESULT TOAPI UIreadResourceFile(LPCWSTR fileName, LPVOID& retData, size_t& retSize);
}
