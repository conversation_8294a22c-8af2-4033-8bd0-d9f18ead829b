﻿#include "pch.h"
#include "segmented.h"
HHBUI::UISegmented::UISegmented(UIBase* hParent, INT x, INT y, INT w, INT h, INT nID)
{
	InitSubControl(hParent, x, y, w, h, L"form-segmented", L"", 0, 0, nID);

	p_data.w = UIEngine::ScaleValue(w);
	p_data.h = UIEngine::ScaleValue(h);

	SetColor(color_text_normal, UIColor(59, 59, 61));
	SetColor(color_text_hover, UIColor(253, 253, 255));
	SetColor(color_text_ban, UIColor(119, 119, 121));
	SetColor(color_background, UIColor(245, 245, 250));
	SetColor(color_focus, UIColor(235, 235, 240));
	SetColor(color_text_shadow, UIColor(20, 126, 255));
	SetRadius(4, 4, 4, 4);
	SetFontFromFamily(L"微软雅黑", 13, 0);

	p_data.brGen = new UIBrush(m_data.Color.crNormal);
}

INT HHBUI::UISegmented::AddItem(LPCWSTR title, UIImage* icon, INT nIndex)
{
	item_info item{};
    item.title = title;
    item.icon = icon;

	int index = -1;
	if (nIndex < 0) {
        p_data.list.push_back(item);
        index = (INT)p_data.list.size() - 1;
	}
	else {
        p_data.list.insert(p_data.list.begin() + nIndex, item);
        index = nIndex;
	}

    return index;
}

BOOL HHBUI::UISegmented::SetItemTitle(INT nIndex, LPCWSTR title)
{
	if (nIndex >= 0 && nIndex < p_data.list.size()) {
		p_data.list.at(nIndex).title = title;
		return true;
	}
    return false;
}

LPCWSTR HHBUI::UISegmented::GetItemTitle(INT nIndex)
{
	if (nIndex >= 0 && nIndex < p_data.list.size()) {
		return p_data.list.at(nIndex).title;
	}
    return nullptr;
}

BOOL HHBUI::UISegmented::SetItemIcon(INT nIndex, UIImage* icon)
{
	if (nIndex >= 0 && nIndex < p_data.list.size()) {
		auto item = &p_data.list.at(nIndex);
		if (item->icon) delete item->icon;
		item->icon = icon;
		return true;
	}
	return false;
}

HHBUI::UIImage* HHBUI::UISegmented::GetItemIcon(INT nIndex)
{
	if (nIndex >= 0 && nIndex < p_data.list.size()) {
		auto* item = &p_data.list.at(nIndex);

		if (item->icon) {
			UIImage* nicon = new UIImage();
			item->icon->Copy(&nicon);
			return nicon;
		}
	}
	return nullptr;
}

BOOL HHBUI::UISegmented::SetItemBan(INT nIndex, BOOL ban)
{
	if (nIndex >= 0 && nIndex < p_data.list.size()) {
		p_data.list.at(nIndex).ban = ban;
		return true;
	}
	return false;
}

BOOL HHBUI::UISegmented::GetItemIsBan(INT nIndex)
{
	if (nIndex >= 0 && nIndex < p_data.list.size()) {
		return p_data.list.at(nIndex).ban;
	}
	return false;
}

BOOL HHBUI::UISegmented::DelItem(INT nIndex)
{
	if (nIndex >= 0 && nIndex < p_data.list.size()) {
		if(p_data.list.at(nIndex).icon) delete p_data.list.at(nIndex).icon;
		p_data.list.erase(p_data.list.begin() + nIndex);
		return true;
	}
    return false;
}

void HHBUI::UISegmented::ClearItems()
{
	for (auto& item : p_data.list) {
        if (item.icon) {
            delete item.icon;
        }
	}
    p_data.list.clear();
}


BOOL HHBUI::UISegmented::SetItemSelected(INT nIndex)
{
	if (nIndex >= 0 && nIndex < p_data.list.size() && nIndex != p_data.isel) {
		p_data.isel = nIndex;
		Redraw();
		DispatchNotify(WMM_SMT_ITEMCHANGED, p_data.isel, 0);
		return true;
	}
	return false;
}

INT HHBUI::UISegmented::GetItemSelected()
{
	return p_data.isel;
}

INT HHBUI::UISegmented::GetItemCount()
{
	return (INT)p_data.list.size();
}

BOOL HHBUI::UISegmented::GetItemExist(INT nIndex)
{
	if (nIndex >= 0 && nIndex < p_data.list.size()) {
		return true;
	}
	return false;
}


void HHBUI::UISegmented::SetItemWidth(INT w)
{
	p_data.iw = w;
}

void HHBUI::UISegmented::SetDisplayMode(INT mode)
{
	if (mode >= 0 && mode <= 2) {
		p_data.dmode = mode;
	}
}

void HHBUI::UISegmented::SetIconPos(INT pos)
{
	if (pos >= 0 && pos <= 2) {
		p_data.ipos = pos;
	}
}

void HHBUI::UISegmented::SetIsHorizontal(BOOL fHorizontal)
{
	p_data.fhor = fHorizontal;
}

void HHBUI::UISegmented::Update()
{
	calu_data();
	Redraw();
}


void HHBUI::UISegmented::calu_data()
{
	if (p_data.fhor) {

		float iw = 0.f;
		if (p_data.dmode == 1) iw = (float)p_data.w / p_data.list.size();
		else if (p_data.dmode == 2) iw = UIEngine::ScaleValue(p_data.iw);

		float bpot = 0.f;
		for (auto& item : p_data.list) {
			item.rc.top = 0;
			item.rc.bottom = p_data.h;

			item.rc.left = bpot;

			float tw = 0, th = 0;
			UICanvas::CalcTextSize(m_data.hFont, item.title, SingleLine, p_data.w, p_data.h, &tw, &th);
			item.trc.left = item.trc.top = 0;
			item.trc.right = tw;
			item.trc.bottom = th;

			if(p_data.dmode < 1 || p_data.dmode > 2) {
				iw = tw + UIEngine::ScaleValue(16);
			}
			if (p_data.ipos == 2) {
				iw += th * 1.08f;
			}
			
			item.trc.right = std::min(item.trc.right, iw);

			item.rc.right = item.rc.left + iw;
			bpot += iw;
		}
	}
	else {
		float ih = 0.f;
		if (p_data.dmode == 1) ih = (float)p_data.h / p_data.list.size();
		else if (p_data.dmode == 2) ih = UIEngine::ScaleValue(p_data.iw);

		float bpot = 0.f;
		for (auto& item : p_data.list) {
			item.rc.left = 0;
			item.rc.right = p_data.w;

			item.rc.top = bpot;

			float tw = 0, th = 0;
			UICanvas::CalcTextSize(m_data.hFont, item.title, SingleLine, p_data.w, p_data.h, &tw, &th);
			item.trc.left = item.trc.top = 0;
			item.trc.right = tw;
			item.trc.bottom = th;

			if (p_data.dmode < 1 || p_data.dmode > 2) {
				if (p_data.ipos == 1) ih = th * 3.6f;
				else ih = th * 2.f;
			}

			item.trc.right = std::min(item.trc.right, item.rc.right);

			item.rc.bottom = item.rc.top + ih;
			bpot += ih;
		}
	}

	//计算文本矩形和图标矩形
	if (p_data.ipos == 1) {
		for (auto& item : p_data.list) {
			float ir = std::min(item.rc.bottom - item.rc.top - item.trc.bottom, item.rc.right - item.rc.left);
			ir *= 0.64f;

			item.irc.left = item.rc.left + (item.rc.right - item.rc.left - ir) / 2.f;
			item.irc.right = item.irc.left + ir;
			item.irc.top = item.rc.top + (item.rc.bottom - item.rc.top - item.trc.bottom - ir - UIEngine::ScaleValue(4)) / 2.f;
            item.irc.bottom = item.irc.top + ir;

			item.trc.left = item.rc.left + (item.rc.right - item.rc.left - item.trc.right) / 2.f;
			item.trc.right = item.trc.left + item.trc.right;
			item.trc.top = item.irc.bottom + UIEngine::ScaleValue(4);
            item.trc.bottom = item.trc.top + item.trc.bottom;
		}
	}
	else if (p_data.ipos == 2) {
		for (auto& item : p_data.list) {
			float ir = item.trc.bottom * 1.08f;

			item.irc.left = item.rc.left + (item.rc.right - item.rc.left - ir - item.trc.right - UIEngine::ScaleValue(4)) / 2.f;
            item.irc.right = item.irc.left + ir;
			item.irc.top = item.rc.top + (item.rc.bottom - item.rc.top - ir) / 2.f;
            item.irc.bottom = item.irc.top + ir;

			item.trc.left = item.irc.right + UIEngine::ScaleValue(4);
            item.trc.right = item.trc.left + item.trc.right;
			item.trc.top = item.rc.top + (item.rc.bottom - item.rc.top - item.trc.bottom) / 2.f;
            item.trc.bottom = item.trc.top + item.trc.bottom;
		}
	}
	else {
		for (auto& item : p_data.list) {
			item.trc.left = item.rc.left + UIEngine::ScaleValue(2);
            item.trc.right = item.rc.right - UIEngine::ScaleValue(2);
            item.trc.top = item.rc.top + UIEngine::ScaleValue(2);
            item.trc.bottom = item.rc.bottom - UIEngine::ScaleValue(2);
		}
	}
}

LRESULT HHBUI::UISegmented::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_MOUSEMOVE) {
		p_data.ihov = -1;
		POINT mpt{ GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam) };
		for (int i = 0; i < p_data.list.size(); i++) {
			if (p_data.list.at(i).rc.PtInRect(mpt.x, mpt.y)) {
				if(!p_data.list.at(i).ban) p_data.ihov = i;
			}
		}
		Redraw();
	}
	else if (uMsg == WM_MOUSELEAVE) {
		p_data.ihov = -1;
		Redraw();
	}
	else if (uMsg == WM_LBUTTONDOWN) {
		POINT mpt{ GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam) };
		for (int i = 0; i < p_data.list.size(); i++) {
			if (p_data.list.at(i).rc.PtInRect(mpt.x, mpt.y)) {
				if (i != p_data.isel && !p_data.list.at(i).ban) {
					p_data.isel = i;
					DispatchNotify(WMM_SMT_ITEMCHANGED, i, lParam);
				}
			}
		}
		Redraw();
	}
	else if (uMsg == WM_DESTROY) {
		ClearItems();
		if (p_data.brGen) delete p_data.brGen;
	}
	return S_OK;
}

void HHBUI::UISegmented::OnPaintProc(ps_context ps)
{
	BeginPaint(ps);

	for (int i = 0; i < p_data.list.size(); i++) {
		auto item = p_data.list.at(i);

		if (p_data.isel == i) {
			p_data.brGen->SetColor(m_data.Color.crShadow);
			ps.hCanvas->FillCustomRoundRect(p_data.brGen, item.rc.left, item.rc.top, item.rc.right, item.rc.bottom,
				m_data.radius.left, m_data.radius.top, m_data.radius.right, m_data.radius.bottom);
		}else if (p_data.ihov == i) {
			p_data.brGen->SetColor(m_data.Color.crFocus);
			ps.hCanvas->FillCustomRoundRect(p_data.brGen, item.rc.left, item.rc.top, item.rc.right, item.rc.bottom,
				m_data.radius.left, m_data.radius.top, m_data.radius.right, m_data.radius.bottom);
		}

		if((p_data.ipos == 1 || p_data.ipos == 2) && item.icon){
			ps.hCanvas->DrawImageRect(item.icon, item.irc.left, item.irc.top, item.irc.right, item.irc.bottom);
		}

		if (p_data.isel == i) {
			ps.hCanvas->DrawTextByColor(ps.hFont, item.title, Center | Middle | SingleLine | EndEllipsis
				, item.trc.left, item.trc.top, item.trc.right, item.trc.bottom, m_data.Color.crHover);
		}
		else if (item.ban) {
            ps.hCanvas->DrawTextByColor(ps.hFont, item.title, Center | Middle | SingleLine | EndEllipsis
				, item.trc.left, item.trc.top, item.trc.right, item.trc.bottom, m_data.Color.crBan);
		}
		else {
            ps.hCanvas->DrawTextByColor(ps.hFont, item.title, Center | Middle | SingleLine | EndEllipsis
				, item.trc.left, item.trc.top, item.trc.right, item.trc.bottom, m_data.Color.crNormal);
		}
	}

	EndPaint();
}
