﻿/**
** =====================================================================================
**
**       文件名称: object_api.h
**       创建时间: 2025-08-03 (优化版本)
**       文件描述: 【HHBUI】现代化COM对象接口系统 - 高级COM接口设计框架 （声明文件）
**
**       主要功能:
**       - 现代化COM接口基础设计
**       - 类型安全的接口查询机制
**       - 智能引用计数管理
**       - 异常安全的对象生命周期
**       - 高性能接口调用优化
**       - 调试与诊断支持
**       - 跨模块兼容性保证
**
**       技术特性:
**       - 采用现代C++17标准与COM规范
**       - 类型安全的模板化接口设计
**       - 异常安全保证与错误恢复机制
**       - 高性能引用计数算法
**       - 智能指针兼容设计
**       - 编译时接口验证
**       - 实时调试与诊断支持
**
** =====================================================================================
**/

#pragma once
#include <Unknwn.h>
#include "application/define.h"
#include <string>
#include <string_view>
#include <memory>
#include <type_traits>
#include <atomic>
#include <mutex>
#include <optional>
#include <functional>

namespace HHBUI
{
	// ==================== 前向声明 ====================

	class IObjectManager;
	class IObjectFactory;
	class IObjectInspector;

	// ==================== 对象状态枚举 ====================

	/// 对象生命周期状态
	enum class ObjectState : uint8_t
	{
		UNINITIALIZED = 0,
		INITIALIZING,
		INITIALIZED,
		FINALIZING,
		FINALIZED,
		ERROR_STATE
	};

	/// 对象类型枚举
	enum class ObjectType : uint32_t
	{
		UNKNOWN = 0,
		UI_CONTROL,
		RENDER_OBJECT,
		RESOURCE_OBJECT,
		ANIMATION_OBJECT,
		LAYOUT_OBJECT,
		CUSTOM_OBJECT = 0x1000
	};

	// ==================== 对象信息结构 ====================

	/// 对象创建信息
	struct ObjectCreateInfo
	{
		ObjectType type = ObjectType::UNKNOWN;
		std::wstring name;
		std::wstring description;
		uint32_t flags = 0;
		void* user_data = nullptr;
		std::function<void(IUnknown*)> destruction_callback = nullptr;
	};

	/// 对象统计信息
	struct ObjectStats
	{
		uint32_t total_objects = 0;
		uint32_t active_objects = 0;
		uint64_t total_memory_usage = 0;
		uint64_t peak_memory_usage = 0;
		std::chrono::steady_clock::time_point creation_time;
		std::chrono::milliseconds total_lifetime{ 0 };
	};

	// ==================== 基础对象接口 ====================

	/// 现代化基础对象接口 - 扩展IUnknown
	EXINTERFACE("5968D879-F383-4d26-9AB9-B74B7E25B9C7") IObject : public IUnknown
	{
		/// 获取对象类型
		/// @return 对象类型枚举
		EXMETHOD ObjectType GetObjectType() const PURE;

		/// 获取对象状态
		/// @return 对象当前状态
		EXMETHOD ObjectState GetObjectState() const PURE;

		/// 获取对象名称
		/// @return 对象名称字符串视图
		EXMETHOD std::wstring_view GetObjectName() const PURE;

		/// 设置对象名称
		/// @param name 新的对象名称
		/// @return 操作结果
		EXMETHOD HRESULT SetObjectName(std::wstring_view name) PURE;

		/// 获取对象描述
		/// @return 对象描述字符串视图
		EXMETHOD std::wstring_view GetObjectDescription() const PURE;

		/// 设置对象描述
		/// @param description 新的对象描述
		/// @return 操作结果
		EXMETHOD HRESULT SetObjectDescription(std::wstring_view description) PURE;

		/// 获取对象创建时间
		/// @return 创建时间点
		EXMETHOD std::chrono::steady_clock::time_point GetCreationTime() const PURE;

		/// 获取对象生存时间
		/// @return 生存时间（毫秒）
		EXMETHOD std::chrono::milliseconds GetLifetime() const PURE;

		/// 获取对象内存使用量
		/// @return 内存使用量（字节）
		EXMETHOD uint64_t GetMemoryUsage() const PURE;

		/// 检查对象是否有效
		/// @return true如果对象有效
		EXMETHOD bool IsValid() const PURE;

		/// 初始化对象
		/// @param create_info 创建信息
		/// @return 初始化结果
		EXMETHOD HRESULT Initialize(const ObjectCreateInfo* create_info = nullptr) PURE;

		/// 终结对象
		/// @return 终结结果
		EXMETHOD HRESULT Finalize() PURE;

		/// 克隆对象
		/// @param cloned_object 输出克隆的对象
		/// @return 克隆结果
		EXMETHOD HRESULT Clone(IObject** cloned_object) const PURE;

		/// 比较对象
		/// @param other 要比较的对象
		/// @return 比较结果（0=相等，<0=小于，>0=大于）
		EXMETHOD int32_t Compare(IObject* other) const PURE;

		/// 序列化对象
		/// @param data 输出序列化数据
		/// @param size 输出数据大小
		/// @return 序列化结果
		EXMETHOD HRESULT Serialize(void** data, uint32_t* size) const PURE;

		/// 反序列化对象
		/// @param data 序列化数据
		/// @param size 数据大小
		/// @return 反序列化结果
		EXMETHOD HRESULT Deserialize(const void* data, uint32_t size) PURE;
	};

	// ==================== 对象管理接口 ====================

	/// 对象管理器接口
	EXINTERFACE("A1B2C3D4-E5F6-7890-ABCD-EF1234567890") IObjectManager : public IObject
	{
		/// 注册对象类型
		/// @param type 对象类型
		/// @param factory 对象工厂
		/// @return 注册结果
		EXMETHOD HRESULT RegisterObjectType(ObjectType type, IObjectFactory* factory) PURE;

		/// 注销对象类型
		/// @param type 对象类型
		/// @return 注销结果
		EXMETHOD HRESULT UnregisterObjectType(ObjectType type) PURE;

		/// 创建对象
		/// @param type 对象类型
		/// @param create_info 创建信息
		/// @param object 输出创建的对象
		/// @return 创建结果
		EXMETHOD HRESULT CreateObject(ObjectType type, const ObjectCreateInfo* create_info, IObject** object) PURE;

		/// 销毁对象
		/// @param object 要销毁的对象
		/// @return 销毁结果
		EXMETHOD HRESULT DestroyObject(IObject* object) PURE;

		/// 查找对象
		/// @param name 对象名称
		/// @param object 输出找到的对象
		/// @return 查找结果
		EXMETHOD HRESULT FindObject(std::wstring_view name, IObject** object) PURE;

		/// 枚举对象
		/// @param type 对象类型（UNKNOWN表示所有类型）
		/// @param callback 枚举回调函数
		/// @return 枚举结果
		EXMETHOD HRESULT EnumerateObjects(ObjectType type, std::function<bool(IObject*)> callback) PURE;

		/// 获取对象统计信息
		/// @return 统计信息
		EXMETHOD ObjectStats GetObjectStats() const PURE;

		/// 强制垃圾回收
		/// @return 回收的对象数量
		EXMETHOD uint32_t ForceGarbageCollection() PURE;

		/// 设置内存限制
		/// @param limit_bytes 内存限制（字节）
		/// @return 设置结果
		EXMETHOD HRESULT SetMemoryLimit(uint64_t limit_bytes) PURE;

		/// 获取内存限制
		/// @return 内存限制（字节）
		EXMETHOD uint64_t GetMemoryLimit() const PURE;
	};

	/// 对象工厂接口
	EXINTERFACE("B2C3D4E5-F617-8901-BCDE-F23456789012") IObjectFactory : public IObject
	{
		/// 创建对象实例
		/// @param create_info 创建信息
		/// @param object 输出创建的对象
		/// @return 创建结果
		EXMETHOD HRESULT CreateInstance(const ObjectCreateInfo* create_info, IObject** object) PURE;

		/// 获取支持的对象类型
		/// @return 对象类型
		EXMETHOD ObjectType GetSupportedType() const PURE;

		/// 检查是否可以创建对象
		/// @param create_info 创建信息
		/// @return true如果可以创建
		EXMETHOD bool CanCreateObject(const ObjectCreateInfo* create_info) const PURE;

		/// 获取对象创建成本估算
		/// @param create_info 创建信息
		/// @return 成本估算（内存字节数）
		EXMETHOD uint64_t EstimateCreationCost(const ObjectCreateInfo* create_info) const PURE;
	};

	/// 对象检查器接口（用于调试和诊断）
	EXINTERFACE("C3D4E5F6-A7B8-9012-CDEF-345678901234") IObjectInspector : public IObject
	{
		/// 检查对象完整性
		/// @param object 要检查的对象
		/// @return 检查结果
		EXMETHOD HRESULT ValidateObject(IObject* object) PURE;

		/// 获取对象详细信息
		/// @param object 要检查的对象
		/// @return 详细信息字符串
		EXMETHOD std::wstring GetObjectDetails(IObject* object) PURE;

		/// 检查内存泄漏
		/// @return 泄漏的对象数量
		EXMETHOD uint32_t CheckMemoryLeaks() PURE;

		/// 生成对象报告
		/// @param file_path 报告文件路径
		/// @return 生成结果
		EXMETHOD HRESULT GenerateReport(std::wstring_view file_path) PURE;

		/// 设置调试模式
		/// @param enable 是否启用调试模式
		EXMETHOD void SetDebugMode(bool enable) PURE;

		/// 检查是否为调试模式
		/// @return true如果为调试模式
		EXMETHOD bool IsDebugMode() const PURE;
	};

} // namespace HHBUI
