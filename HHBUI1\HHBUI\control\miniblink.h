﻿#pragma once
#include "ThirdParty/miniblink/wke.h"
namespace HHBUI
{
	enum MbFlags
	{
		WMM_MB_TITLECHANGED = 10001,              //设置标题变化的通知回调 lParam为当前标题
		WMM_MB_URLCHANGED = 10002,              //url改变回调  lParam为当前url
		WMM_MB_ALERT = 10003,              //网页调用alert会走到这个接口填入的回调 lParam为内容
		WMM_MB_NAVIGATION = 10004,              //wkeNavigationCallback回调的返回值，如果是false，表示可以继续进行浏览，true表示阻止本次浏览。wParam为wkeNavigationType lParam为当前标题
		WMM_MB_CREATEVIEW = 10005,              //网页点击a标签创建新窗口时将触发回调 wParam为wkeWindowFeatures lParam为当前url
		WMM_MB_DOCUMENTREADY = 10006,              //对应js里的body onload事件
		WMM_MB_DOWNLOAD = 10007,              //页面下载事件回调。点击某些链接，触发下载会调用 lParam为 string url
	};
	class TOAPI UIMiniBlink : public UIControl
	{
	public:
		UIMiniBlink() = default;
		UIMiniBlink(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT nID = 0, INT dwStyle = 0, INT dwStyleEx = 0);
		/*设置动态库路径 成功返回浏览器句柄*/
		wkeWebView SetWkeDllPath(LPCWSTR libPath);
		/*设置UA*/
		void SetUserAgent(LPCSTR ua);
		/*加载自URL*/
		void LoadURLW(LPCWSTR url);
		/*加载自文件*/
		void LoadFileW(LPCWSTR file);
		/*加载自HTML*/
		void LoadHTMLW(LPCWSTR html);
		/*后退*/
		void GoBack();
		/*前进*/
		void GoForward();
		/*刷新网页*/
		void Reload();
		/*运行脚本*/
		void RunJSW(LPCWSTR cJs);





	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		DWORD GetEventFlags(WPARAM wParam, BOOL fKeyEvent);
		void UpdateCursor();
		static wkeWebView WKE_CALL_TYPE OnWkeCreateView(wkeWebView webView, void* mb, wkeNavigationType navigationType, const wkeString url, const wkeWindowFeatures* windowFeatures);
		static void WKE_CALL_TYPE OnWkePaintUpdate(wkeWebView hWebView, void* mb, const HDC hDC, INT x, INT y, INT cx, INT cy);
		static void WKE_CALL_TYPE OnWkeTitleChanged(wkeWebView webView, void* mb, wkeString title);
		static void WKE_CALL_TYPE OnWkeURLChanged(wkeWebView webView, void* mb, wkeString url);
		static bool WKE_CALL_TYPE OnWkeNavigation(wkeWebView webView, void* mb, wkeNavigationType navigationType, wkeString url);
		static void WKE_CALL_TYPE OnWkeDocumentReady(wkeWebView webView, void* mb);
		static bool WKE_CALL_TYPE OnWkeDownload(wkeWebView webView, void* mb, const char* url);

		struct miniblink_s
		{
			wkeWebView hWebView = nullptr;
			LPCTSTR curpageurl = nullptr;
			int cursor = 0;
		}p_data;
	};
}
