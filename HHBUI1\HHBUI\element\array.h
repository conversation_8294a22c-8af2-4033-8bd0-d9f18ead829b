﻿#pragma once
namespace HHBUI
{
	typedef BOOL(CALLBACK* ArrayEnumPROC)(LPVOID, INT, size_t, size_t);

	class UIarray
	{
	public:
		UIarray();
		~UIarray();
		//合法性检查
		BOOL TOAPI empty(size_t nIndex = 0);
		//添加成员 index插入位置 默认放在最后
		size_t TOAPI insert(size_t value, size_t index = 0);
		//删除成员
		BOOL TOAPI erase(size_t index);
		//置成员
		BOOL TOAPI set(size_t index, size_t value);
		//获取成员 必须＞0
		size_t TOAPI get(size_t index);
		//置附加参数
		void TOAPI setextra(size_t extra);
		//获取附加参数
		size_t TOAPI getextra();
		//重定义
		BOOL TOAPI redefine(size_t size);
		//清空
		void TOAPI clear();
		//取成员数
		size_t TOAPI size();
		//返回容器的第一个元素
		size_t TOAPI begin();
		//返回容器的最后一个元素
		size_t TOAPI end();
		/*
	 * Emum
	 * @brief    枚举成员
	 * @param    fun 回调函数 onEnum(hArray,nIndex,pvItem,nType,pvParam)
	 * @param    pvParam 回调参数
	 * @return   返回中断的索引,枚举完毕返回0
	 */
		size_t TOAPI emum(LPVOID fun, size_t pvParam = NULL);
		/*
	 * Sort
	 * @brief    排序
	 * @param    fDesc 是否倒序
	 * @param    fun 自定义排序回调函数
	 * @param    extra1\extra2 附加参数
	 * @return   返回BOOL
	 */
		BOOL TOAPI sort(BOOL fDesc, ArrayComparePROC fun = 0, size_t extra1 = 0, size_t extra2 = 0);

	private:
		std::vector<size_t> m_data{};
		size_t m_extra = 0;
	};
}


