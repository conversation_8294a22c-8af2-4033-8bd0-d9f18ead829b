﻿#include "hhbui.h"
using namespace HHBUI;
LRESULT CALLBACK OnButton_cb_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (nCode == WMM_CLICK)
	{
		auto Combo = (UIComboBox*)window->FindUIView(L"7070");
		if (nID == 2001)
		{
			Combo->Show(0);
		}
		else if (nID == 2002)
		{
			Combo->Show(1);
		}
		else if (nID == 2003)
		{
			Combo->Popup();
		}
		else if (nID == 2004)
		{
			Combo->Close();
		}
	}
	return S_OK;
}

void testcombobox(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 500, 400, L"hello ComboBox", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);
	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);


	auto Combo1 = new UIComboBox(window, 30, 60, 150, 30, L"我是组合框", 0, 0, 7070, SingleLine | Middle);
	Combo1->SetColor(color_border, UIColor(L"#cccedb"));
	Combo1->SetColor(color_focus, UIColor(L"#ca5100"));
	Combo1->SetColor(color_text_hover, UIColor(L"#0099ff"));
	

	auto Combo2 = new UIComboBox(window, 220, 60, 150, 30, L"我是组合框", 0, 0, 0, SingleLine | Middle);
	Combo2->SetColor(color_background, UIColor(L"#DBEBB0"));
	Combo2->SetColor(color_border, UIColor(L"#cccedb"));
	Combo2->SetColor(color_focus, UIColor(L"#006cbe"));
	Combo2->SetColor(color_text_hover, UIColor(L"#0099ff"));
	Combo2->SetRadius(15, 15, 15, 15);
	//Combo2->SetFontFromFamily(L"Maple Mono");

	auto Combo3 = new UIComboBox(window, 30, 130, 150, 30, L"我不可编辑", eos_edit_disablemenu | eos_edit_readonly | eos_edit_hideselection, 0, 0, SingleLine | Middle);
	Combo3->SetColor(color_border, UIColor(L"#006cbe"));

	for (size_t i = 0; i < 100; i++)
	{
		auto item1 = new ListItem();
		std::wstring text = L"我是测试项目" + std::to_wstring(i + 1);
		item1->text = text.c_str();
		Combo1->AddItem(item1);

		auto item2 = new ListItem();
		text = L"列表项目" + std::to_wstring(i + 1);
		item2->text = text.c_str();
		Combo2->AddItem(item2);
	}
	Combo1->SetCurSelItem(10);
	//Combo2->SetCurSelItem(60);

	auto btn1 = new UIButton(window, 70, 270, 100, 36, L"隐藏", 0, 0, 2001);
	btn1->SetStyle(fill, primary);
	btn1->SetEvent(WMM_CLICK, OnButton_cb_Event);
	auto btn2 = new UIButton(window, 70, 310, 100, 36, L"显示", 0, 0, 2002);
	btn2->SetStyle(fill, primary);
	btn2->SetEvent(WMM_CLICK, OnButton_cb_Event);
	auto btn3 = new UIButton(window, 190, 270, 100, 36, L"弹出或关闭", 0, 0, 2003);
	btn3->SetStyle(fill, primary);
	btn3->SetEvent(WMM_CLICK, OnButton_cb_Event);
	auto btn4 = new UIButton(window, 190, 310, 100, 36, L"关闭", 0, 0, 2004);
	btn4->SetStyle(fill, primary);
	btn4->SetEvent(WMM_CLICK, OnButton_cb_Event);

	window->Show();
	window->MessageLoop();
}