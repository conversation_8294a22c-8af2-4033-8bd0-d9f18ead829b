﻿#include "pch.h"
#include "brush.h"
#include "common/winapi.h"
#include <common/Exception.h>

HHBUI::UIBrush::UIBrush()
{
	br_CreateBrush(UIColor(0, 0, 0, 0));
}

HHBUI::UIBrush::UIBrush(UIColor argb)
{
	br_CreateBrush(argb);
}
inline D2D1_EXTEND_MODE GetD2DExtendMode(HHBUI::BrushModeEx mode)
{
	switch (mode)
	{
	case HHBUI::BrushModeEx::None: return D2D1_EXTEND_MODE_CLAMP;
	case HHBUI::BrushModeEx::Tile: return D2D1_EXTEND_MODE_WRAP;
	case HHBUI::BrushModeEx::Mirror: return D2D1_EXTEND_MODE_MIRROR;
	default: return D2D1_EXTEND_MODE_CLAMP;
	}
}
HHBUI::UIBrush::UIBrush(UICanvas* canvas_src, BrushModeEx extendmode, DWORD alpha)
{
	ID2D1DeviceContext* m_dc = (ID2D1DeviceContext*)canvas_src->GetContext(0);
	ID2D1Bitmap1* bitmap = (ID2D1Bitmap1*)canvas_src->GetContext(3);
	throw_if_false(m_dc, EE_NOREADY, L"渲染目标为空");

	//获取扩展模式
	D2D1_EXTEND_MODE mode = GetD2DExtendMode(extendmode);

	//生成属性
	D2D1_BRUSH_PROPERTIES bp = D2D1::BrushProperties(alpha / 255.0F);
	D2D1_BITMAP_BRUSH_PROPERTIES1 bbp = D2D1::BitmapBrushProperties1(
		mode, mode, D2D1_INTERPOLATION_MODE_LINEAR
	);
	//创建画刷
	throw_if_failed(
		m_dc->CreateBitmapBrush(
			bitmap, bbp, bp, (ID2D1BitmapBrush1**)&m_brush
		), L"创建位图画刷失败"
	);
}
HHBUI::UIBrush::UIBrush(UIImage* hImage, INT mode, BrushModeEx extendmode, DWORD alpha)
{
	ID2D1Bitmap* m_d2d_bitmap = (ID2D1Bitmap*)hImage->GetContext();
	//获取扩展模式
	auto extend_mode = GetD2DExtendMode(extendmode);

	D2D1_RECT_F m_src_rect{};
	m_src_rect.left = m_src_rect.top = 0;
	m_src_rect.right = m_d2d_bitmap->GetSize().width;
	m_src_rect.bottom = m_d2d_bitmap->GetSize().height;
	//创建图像画刷
	throw_if_failed(
		UIDrawContext::ToList.d2d_dc->CreateImageBrush(
			m_d2d_bitmap, D2D1::ImageBrushProperties(
				m_src_rect,
				extend_mode, extend_mode,
				(D2D1_INTERPOLATION_MODE)mode
			), D2D1::BrushProperties(alpha / 255.0F), (ID2D1ImageBrush**)&m_brush
		), L"创建位图画刷失败");
}
void HHBUI::UIBrush::SetImgTransformToRect(float Width, float Height, float left, float top, float right, float bottom)
{
	if (m_brush)
	{
		D2D1_MATRIX_3X2_F m_transform_src = D2D1::Matrix3x2F::Identity();
		D2D1_MATRIX_3X2_F m_transform_user = D2D1::Matrix3x2F::Identity();
		//获取最终目标矩形
		ExRectF dst_rect = ExRectF(left, top, right, bottom).Normalize();

		//计算缩放比例
		float scale_x = dst_rect.Width() / Width;
		float scale_y = dst_rect.Height() / Height;

		//生成变换矩阵
		ExMatrix3x2 transform;
		transform.Scale(scale_x, scale_y).Translate(left, top);

		//设置变换
		m_transform_user = ExMatrix3x2::MakeScale(scale_x, scale_y).Translate(left, top).ToD2D();
		((ID2D1ImageBrush*)m_brush)->SetTransform(m_transform_src * m_transform_user);
	}
}
HHBUI::UIBrush::UIBrush(FLOAT xStart, FLOAT yStart, FLOAT xEnd, FLOAT yEnd, UIColor crBegin, UIColor crEnd, BrushModeEx extendmode, BOOL isGamma1_0)
{
	UIColor arrStopPts[] = { crBegin ,crEnd };
	FLOAT arrINTPts[] = { 0, 1 };
	br_CreatGradientBrush(xStart, yStart, xEnd, yEnd, arrStopPts, arrINTPts, 2, extendmode, isGamma1_0);
}
HHBUI::UIBrush::UIBrush(FLOAT xStart, FLOAT yStart, FLOAT xEnd, FLOAT yEnd, const UIColor* arrStopPts, const FLOAT* arrINT, INT cStopPts, BrushModeEx extendmode, BOOL isGamma1_0)
{
	br_CreatGradientBrush(xStart, yStart, xEnd, yEnd, arrStopPts, arrINT, cStopPts, extendmode, isGamma1_0);
}
void HHBUI::UIBrush::SetLinearBeginPoint(float x, float y)
{
	if (m_brush)
	{
		D2D1_POINT_2F pt{ x, y };
		((ID2D1LinearGradientBrush*)m_brush)->SetStartPoint(pt);
	}
}
void HHBUI::UIBrush::SetLinearEndPoint(float x, float y)
{
	if (m_brush)
	{
		D2D1_POINT_2F pt{ x, y };
		((ID2D1LinearGradientBrush*)m_brush)->SetEndPoint(pt);
	}
}
void HHBUI::UIBrush::SetLinearPoints(const ExPointF* begin_point, const ExPointF* end_point)
{
	if (m_brush)
	{
		if (begin_point) {
			D2D1_POINT_2F pt{ begin_point->x, begin_point->y };
			((ID2D1LinearGradientBrush*)m_brush)->SetStartPoint(pt);
		}
		if (end_point) {
			D2D1_POINT_2F pt{ end_point->x, end_point->y };
			((ID2D1LinearGradientBrush*)m_brush)->SetEndPoint(pt);
		}
	}
}
HHBUI::UIBrush::UIBrush(BOOL isGamma1_0, FLOAT x, FLOAT y, FLOAT radiusX, FLOAT radiusY, UIColor crBegin, UIColor crEnd, BrushModeEx extendmode)
{
	UIColor arrStopPts[] = { crBegin ,crEnd };
	FLOAT arrINTPts[] = { 0.f, 1.f };
	br_CreatRadialBrush(x, y, radiusX, radiusY, arrStopPts, arrINTPts, 2, { 0,0 }, extendmode, isGamma1_0);
}
HHBUI::UIBrush::UIBrush(FLOAT x, FLOAT y, FLOAT radiusX, FLOAT radiusY, const UIColor* arrStopPts, const FLOAT* arrINT, INT cStopPts, POINT gradientOriginOffset, BrushModeEx extendmode, BOOL isGamma1_0)
{
	br_CreatRadialBrush(x, y, radiusX, radiusY, arrStopPts, arrINT, cStopPts, gradientOriginOffset, extendmode, isGamma1_0);
}
void HHBUI::UIBrush::SetRadialBoundsRect(float left, float top, float right, float bottom)
{
	if (m_brush)
	{
		ExRectF m_bounds = ExRectF(left, top, right, bottom).Normalize();
		((ID2D1RadialGradientBrush*)m_brush)->SetRadiusX(m_bounds.Width() / 2);
		((ID2D1RadialGradientBrush*)m_brush)->SetRadiusY(m_bounds.Height() / 2);
		((ID2D1RadialGradientBrush*)m_brush)->SetCenter(D2D1::Point2F(m_bounds.GetHorzCenter(), m_bounds.GetVertCenter()));
	}
}
void HHBUI::UIBrush::SetRadialCenter(FLOAT offsetX, FLOAT offsetY)
{
	if (m_brush)
		((ID2D1RadialGradientBrush*)m_brush)->SetCenter({ offsetX, offsetY });
}
void HHBUI::UIBrush::SetRadialCenterOffset(float horz, float vert)
{
	if (m_brush)
		((ID2D1RadialGradientBrush*)m_brush)->SetGradientOriginOffset(D2D1::Point2F(horz, vert));
}

void HHBUI::UIBrush::SetColor(UIColor argb)
{
	if (m_brush)
		((ID2D1SolidColorBrush*)m_brush)->SetColor(argb.GetDxObject());
}
void HHBUI::UIBrush::GetColor(D2D1_COLOR_F& lpColor)
{
	if (m_brush)
		lpColor = ((ID2D1SolidColorBrush*)m_brush)->GetColor();
}
void HHBUI::UIBrush::GetColor(UIColor& lpColor)
{
	if (m_brush)
		lpColor = UIColor(((ID2D1SolidColorBrush*)m_brush)->GetColor());
}
HHBUI::UIBrush::UIBrush(ID2D1Bitmap* pBitmapSource, BOOL tr, INT mode, BrushModeEx extendmode, DWORD alpha)
{
	//获取扩展模式
	auto extend_mode = GetD2DExtendMode(extendmode);

	D2D1_RECT_F m_src_rect{};
	m_src_rect.left = m_src_rect.top = 0;
	m_src_rect.right = pBitmapSource->GetSize().width;
	m_src_rect.bottom = pBitmapSource->GetSize().height;
	//创建图像画刷
	throw_if_failed(
		UIDrawContext::ToList.d2d_dc->CreateImageBrush(
			pBitmapSource, D2D1::ImageBrushProperties(
				m_src_rect,
				extend_mode, extend_mode,
				(D2D1_INTERPOLATION_MODE)mode
			), D2D1::BrushProperties(alpha / 255.0F), (ID2D1ImageBrush**)&m_brush
		), L"创建位图画刷失败");
}
void HHBUI::UIBrush::SetOpacity(FLOAT alpha)
{
	if (m_brush)
		((ID2D1SolidColorBrush*)m_brush)->SetOpacity(alpha);
}
void HHBUI::UIBrush::SetTransForm(ExMatrix matrix)
{
	if (m_brush)
	{
		D2D1_MATRIX_3X2_F mx = matrix.ToD2D();
		((ID2D1ImageBrush*)m_brush)->SetTransform(mx);
	}
}
void HHBUI::UIBrush::GetTransForm(ExMatrix* matrix)
{
	if (m_brush && matrix)
		((ID2D1ImageBrush*)m_brush)->GetTransform((D2D1_MATRIX_3X2_F*)matrix);
}

LPVOID HHBUI::UIBrush::GetContext()
{
	return m_brush;
}

HHBUI::UIBrush::~UIBrush()
{
	if (m_brush)
		((ID2D1Brush*)m_brush)->Release();
}

void HHBUI::UIBrush::br_CreateBrush(UIColor argb)
{
	throw_if_failed(
		UIDrawContext::ToList.d2d_dc->CreateSolidColorBrush(argb.GetDxObject(), (ID2D1SolidColorBrush**)&m_brush),
		L"纯色画刷创建失败"
	);
}

void HHBUI::UIBrush::br_CreatGradientBrush(FLOAT xStart, FLOAT yStart, FLOAT xEnd, FLOAT yEnd, const UIColor* arrStopPts, const FLOAT* arrINT, INT cStopPts, BrushModeEx extendmode, BOOL isGamma1_0)
{
	if (cStopPts < 2)
	{
		return;
	}
	ID2D1GradientStopCollection* gradientStopCollection = nullptr;
	std::vector<D2D1_GRADIENT_STOP> gradientStops(cStopPts);
	D2D1_LINEAR_GRADIENT_BRUSH_PROPERTIES gradientProperties{};
	for (INT i = 0; i < cStopPts; i++)
	{
		gradientStops[i].color = arrStopPts[i].GetDxObject();
		gradientStops[i].position = arrINT[i];
	}
	//获取扩展模式
	D2D1_EXTEND_MODE extend_mode = GetD2DExtendMode(extendmode);
	UIDrawContext::ToList.d2d_dc->CreateGradientStopCollection(gradientStops.data(), cStopPts, isGamma1_0 ? D2D1_GAMMA_1_0 : D2D1_GAMMA_2_2, extend_mode, &gradientStopCollection);

	gradientProperties.startPoint.x = xStart;
	gradientProperties.startPoint.y = yStart;
	gradientProperties.endPoint.x = xEnd;
	gradientProperties.endPoint.y = yEnd;
	if (gradientStopCollection)
	{
		UIDrawContext::ToList.d2d_dc->CreateLinearGradientBrush(&gradientProperties, NULL, gradientStopCollection, (ID2D1LinearGradientBrush**)&m_brush);
		gradientStopCollection->Release();
	}
}

void HHBUI::UIBrush::br_CreatRadialBrush(FLOAT x, FLOAT y, FLOAT radiusX, FLOAT radiusY, const UIColor* arrStopPts, const FLOAT* arrINT, INT cStopPts, POINT gradientOriginOffset, BrushModeEx extendmode, BOOL isGamma1_0)
{
	if (cStopPts < 2)
	{
		return;
	}
	ID2D1GradientStopCollection* gradientStopCollection = nullptr;
	std::vector<D2D1_GRADIENT_STOP> gradientStops(cStopPts);
	for (INT i = 0; i < cStopPts; i++)
	{
		gradientStops[i].color = arrStopPts[i].GetDxObject();
		gradientStops[i].position = arrINT[i];
	}
	//获取扩展模式
	D2D1_EXTEND_MODE extend_mode = GetD2DExtendMode(extendmode);
	UIDrawContext::ToList.d2d_dc->CreateGradientStopCollection(gradientStops.data(), cStopPts, isGamma1_0 ? D2D1_GAMMA_1_0 : D2D1_GAMMA_2_2, extend_mode, &gradientStopCollection);

	if (gradientStopCollection)
	{
		UIDrawContext::ToList.d2d_dc->CreateRadialGradientBrush(
			D2D1::RadialGradientBrushProperties(
				D2D1::Point2F(x, y),
				D2D1::Point2F(gradientOriginOffset.x, gradientOriginOffset.y),
				radiusX,
				radiusY),
			gradientStopCollection,
			(ID2D1RadialGradientBrush**)&m_brush
		);
		gradientStopCollection->Release();
	}
	
}

