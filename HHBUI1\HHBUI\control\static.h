﻿#pragma once
namespace HHBUI
{
	enum info_static_style
	{
		eos_static_dline = 16,                 //分割线
		eos_static_roll = 32,                  //滚动
		eos_static_blurtext = 64,              //模糊文字
		eos_static_pro = 128,                  //高性能文本
		eos_static_ex = 256,                   //组合标签
	};
	class TOAPI UIStatic : public UIControl
	{
	public:
		UIStatic() = default;
		UIStatic(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
	
		//置分割线颜色[eos_static_dline风格有效]
		void SetLineColor(UIColor lpLine);
		//置分割线宽 默认1[eos_static_dline风格有效]
		void SetLineWidth(FLOAT width);
		//置分割线风格 默认D2D1_DASH_STYLE_SOLID [eos_static_dline风格有效]
		void SetLineStyle(INT Style);
		//置分割线文本偏移 默认30[eos_static_dline风格有效]
		void SetLineOffset(FLOAT Offset);

		//置滚动方向 0左边、1左右来回[eos_static_roll风格有效]
		void SetRollStyle(INT Style);
		//置滚动自定义绘制尺寸
		void SetRollSize(INT sWidth);

		//置文本模糊 [eos_static_blurtext风格有效]
		void SetTextBlur(FLOAT fDeviation);

		//添加文本 [eos_static_ex风格有效]
		void AddText(LPCWSTR pwzText, UIColor color_normal = {}, UIColor color_backg = {}, LPCWSTR lpwzFontFace = 0, INT dwFontSize = 0, DWORD dwFontStyle = 0);

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		//自定义绘制
		EXMETHOD BOOL OnPsCustomDraw(ps_customdraw ps) { return FALSE; }
		void static_roll_to();

		struct static_s
		{
			UIImage* dstImg = nullptr; UICanvas* hCanvas = nullptr; ID2D1Effect* pEffect = nullptr;
			FLOAT nWidthText = 0.0f, p_Left = 0.0f, P_Width = 0.0f, P_Bool = 0.0f;
			INT sWidth = 0, type = 0, stimer = 0, time = 0, step = 0, delay = 0;
			BOOL direc = FALSE;
			UIarray* pArray = nullptr;
			UIColor clr[2] = { UIColor(208,211,217,150),UIColor(20,126,255,255) };
		}p_data;
	};
}
