﻿#pragma once
/*普通列表框*/
namespace HHBUI
{
	struct ListItem
	{
		LPCWSTR text = 0;
		UIImage* nImage = nullptr;
	};
	class TOAPI UIList : public UIListView
	{
	public:
		UIList() = default;
		UIList(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1)
			: UIListView(hParent, x, y, width, height, L"form-list", dwStyle, dwStyleEx, nID, dwTextFormat) {
			OnCreate();
		}
		//添加项目
		INT AddItem(LPCWSTR pwzText, UIImage *nImage = nullptr, INT index = -1, bool draw = false);
		INT AddItem(ListItem* item, INT index = -1, bool draw = false);
		//设置项目
		void SetItem(INT index, LPCWSTR pwzText, UIImage *nImage = nullptr, bool draw = false);
		/*删除一个列表项目
		* @param index - 列表索引 -1=尾部
		* @param draw - 是否立即绘制
		*
		* @return 当前列表项目总数
		*/
		INT DeleteItem(INT index, bool draw = true);

		/*删除所有列表项*/
		void DeleteAllItem(bool draw = true);
		//更新
		void Update();
		//设置项目热点颜色
		void SetCrHot(UIColor hover, UIColor checked, UIColor down);
		/*获取一个列表项目指针
		* @param index - 列表索引 -1=尾部
		*/
		BOOL GetItem(int index, ListItem** item);
		//获取总数
		INT GetItemCount();

	protected:
		EXMETHOD LRESULT OnPsProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		//自定义绘制
		EXMETHOD BOOL OnPsCustomDraw(INT iItem, ps_customdraw ps) override;
		//由于派生基类无法引导创建消息 只能手动指定
		void OnCreate();
		//索引检查 防止越界
		bool IndexCheck(INT index);
		struct list_s
		{
			UIarray* pArray = nullptr;
			UIBrush* hBrush = nullptr;
			bool isData = false;
			UIColor Color[3] = { UIColor(L"#e6e7e8"),UIColor(L"#c2c3c9"),UIColor(L"#cccedb") };
		}s_data;
		friend class UIComboBox;
	};
}
