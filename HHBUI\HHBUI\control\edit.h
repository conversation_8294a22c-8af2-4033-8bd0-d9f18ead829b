﻿#pragma once
#include "richedit.h"
namespace HHBUI
{
	enum EditFlags
	{
		eos_edit_disabledrag = 1,                   //允许拖拽
		eos_edit_usepassword = 1 << 1,              //密码输入
		eos_edit_hideselection = 1 << 2,            //允许选择文本 [只读模式可用于选择]
		eos_edit_richtext = 1 << 3,                 //富文本
		eos_edit_allowbeep = 1 << 4,                //允许鸣叫
		eos_edit_readonly = 1 << 5,                 //只读
		eos_edit_newline = 1 << 6,                  //回车换行
		eos_edit_numericinput = 1 << 7,             //数值输入
		eos_edit_autowordsel = 1 << 8,              //自动选择字符
		eos_edit_disablemenu = 1 << 9,              //禁用右键默认菜单
		eos_edit_parseurl = 1 << 10,                //解析URL
		eos_edit_allowtab = 1 << 11,                //允许TAB字符
		eos_edit_showtipsalways = 1 << 12,          //总是显示提示文本
		eos_edit_hiddencaret = 1 << 13,             //隐藏插入符
		eos_edit_disablectrl = 1 << 14,             //禁用ctrl快捷键 包括复制、粘贴、全选等
		eos_edit_fSaveSelection = 1 << 15,          //不活动时保存选择:当控件处于非活动状态时，应保存所选内容的边界
	};
	class TOAPI UIEdit : public UIControl
	{
	public:
		UIEdit() = default;
		UIEdit(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
		// 打开剪贴板并获取内容
		LPCWSTR GetClipboardText();
		//设置默认文本
		void SetCueBanner(LPCWSTR lpsztext, UIColor crText = {});
		//设置光标颜色
		void SetColorCaret(UIColor dwColor);
		//加载RTF
		void LoadRtf(LPCWSTR pszFileName);
		//保存RTF
		BOOL SaveRtf(LPCWSTR pszFileName);
		//设置输入方式 参考EditFlags
		void SetInputType(INT dwFlags);
		//获取输入方式
		INT GetInputType();
		//设置内容.
		void SetText(LPCWSTR lpString);
		//取内容
		LPCWSTR GetText();
		//取当前选中内容
		LPCWSTR GetCurSelText();
		//取内容长度
		size_t GetTextLength();
		//内容是否改变
		BOOL IsTextModify();
		//取行数
		size_t GetTextCount();
		/*
		* @brief 追加文本
		* @param dwColor 文本颜色 空为默认
		* @param bCanUndo 是否允许撤销
		*/
		void AppendText(LPCWSTR text, UIColor dwColor = {}, BOOL bCanUndo = true);
		//设置选中位置
		void SetSel(INT cpMin, INT cpMax, bool bNoScroll = false);
		//获取当前选中位置
		POINT GetSel();
		//全选内容
		void SelAll();
		/*
		* @brief 替换当前所选内容
		* @param bCanUndo 是否允许撤销
		*/
		void ReplaceSel(LPCWSTR text, bool bCanUndo = true);
		//设置最大文本长度
		BOOL SetLimitText(size_t nLength);
		/*
		 * @brief 寻找指定文本并选中
		 * @param  fGetSel      是否从当前选择位置开始
		 * @param  bRorder      是否逆向寻找,默认从头到尾
		 * @param  fAlef        默认情况下，具有不同重音的阿拉伯语和希伯来语都与 alef 字符匹配。 如果希望搜索区分具有不同重音的 alef，请开启。
		 * @param  bMatchcase   如果设置，则搜索操作区分大小写。 如果未设置，则搜索操作不区分大小写。
		 * @param  fWholeword   如果已设置，则操作仅搜索与搜索字符串匹配的整个单词。 如果未设置，该操作还会搜索与搜索字符串匹配的单词片段。
		 * @return 如果找到目标字符串，则返回值是匹配项的第一个字符的从零开始的位置。 如果未找到目标，则返回值为 -1
		 */
		size_t SetFindText(LPCWSTR text, BOOL fGetSel = true, BOOL bRorder = false, BOOL fAlef = false, BOOL bMatchcase = false, BOOL fWholeword = false);
		//恢复
		BOOL Redo();
		//撤销
		BOOL Undo();
		//清空
		void Clear();
		//复制
		void Copy();
		//剪切
		void Cut();
		//粘贴
		void Paste();
		/*
		* @brief 设置字符格式
		* @param dwMask 标志 CFM_开头 参考:https://learn.microsoft.com/zh-cn/windows/win32/api/richedit/ns-richedit-charformat2w_1
		* @param crText 文本颜色 需要拥有CFM_COLOR标志
		* @param wzFontFace 字体名称 需要拥有CFM_FACE标志
		* @param fontSize 字体大小 需要拥有CFM_SIZE标志
		* @param yOffset 与基线的字符偏移量 需要拥有CFM_OFFSET标志
		* @param bBold 字符为粗体 需要拥有CFE_BOLD标志
		* @param bItalic 字符为斜体 需要拥有CFE_ITALIC标志
		* @param bUnderLine 字符为下划线 需要拥有CFE_UNDERLINE标志
		* @param bStrikeOut 字符为删除线 需要拥有CFE_STRIKEOUT标志
		* @param bLink 字符为链接 需要拥有CFE_LINK标志
		*/
		LRESULT SetSelCharFormat(INT dwMask, UIColor crText = {}, LPCWSTR wzFontFace = {}, DWORD fontSize = NULL, INT yOffset = NULL, BOOL bBold = FALSE,
			BOOL bItalic = FALSE, BOOL bUnderLine = FALSE, BOOL bStrikeOut = FALSE, BOOL bLink = FALSE);
		/*
		* @brief 设置段落格式
		* @param dwMask 标志 PFM_开头 参考:https://learn.microsoft.com/zh-cn/windows/win32/api/richedit/ns-richedit-paraformat2_1
		* @param wNumbering 用于项目符号或编号段落的选项 需要拥有PFM_NUMBERING标志
		* @param dxStartIndent 段落第一行的缩进,以缇为单位 后续行的缩进取决于 dxOffset 成员 需要拥有PFM_STARTINDENT标志
		* @param dxRightIndent 段落右侧相对于右边距的缩进，以缇为单位 需要拥有PFM_RIGHTINDENT标志
		* @param dxOffset 相对于第一行的缩进，第二行和后续行的缩进，以缇为单位。 如果此成员为负数，则缩进第一行;如果此成员为正数，则缩进第一行. 需要拥有PFM_OFFSET标志
		* @param wAlignment 段落对齐方式;参考PFA_开头 需要拥有PFM_ALIGNMENT标志
		*/
		LRESULT SetSelParFormat(DWORD dwMask, WORD wNumbering = NULL, INT dxStartIndent = NULL, INT dxRightIndent = NULL, INT dxOffset = NULL, WORD wAlignment = NULL);


	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		void edit_size();
		LRESULT edit_sendmessage(INT uMsg, WPARAM wParam, LPARAM lParam, BOOL& sOK);
		void edit_contextmenu(HWND hWnd, WPARAM wParam, INT x, INT y);
		void edit_command(INT uMsg, WPARAM wParam, LPARAM lParam);
		void edit_setText(LPCWSTR lpsztext);
		static void CALLBACK edit_timer_caret(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime);
		static DWORD CALLBACK edit_StreamInCallback_FILE(DWORD_PTR dwCookie, LPBYTE pbBuff, LONG cb, LONG* pcb);
		void setppf(PARAFORMAT2* ppf);
		void setpcf(CHARFORMAT2W* pcf);
		
		struct edit_s
		{
			SIZEL sizelExtent = { 0,0 };
			LPVOID its = 0;
			LPVOID ith = 0;
			PARAFORMAT2 ppf{};
			CHARFORMAT2W cfDef{};   /**< Default character format  */
			ExRectF prctext = {}, prcinset = {};
			INT flags = 0;
			LPCWSTR pBanner;
			UIColor crBanner{};
			INT dwPropBits = 0;
			INT charPsw = 0;
			HDC mDc = 0;
			LPVOID hBmp = 0;
			LPVOID pBits = 0;
			UIBrush *crCaret = nullptr;
			RECT rcCaret{};
		}p_data;

		friend class TextHost;
	};
}
