﻿#include "pch.h"
#include "page.h"
#include <common/winapi.h>

HHBUI::UIPage::UIPage(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-page", lpszName, dwStyle, dwStyleEx, nID, dwTextFormat);
}
void HHBUI::UIPage::SetStyle(info_page_style style)
{
    p_data.mod = style;
}
void HHBUI::UIPage::SetCount(INT fCount, INT fShowNum)
{
    if (fCount < 0)
        return;
    p_data.count = fCount;
    p_data.shownum = fShowNum;
    p_data.pagenum = int(fCount / fShowNum);
    if (fCount % fShowNum != 0)
    {
        //因为不能被整除   所以有多出来的记录   单独增加一页
        p_data.pagenum = int(fCount / fShowNum) + 1;
    }
    p_data.current = 1;
    if (p_data.edit)
    {
        std::wstring dstw = std::to_wstring(p_data.pagenum);
        p_data.edit->SetLimitText(dstw.length());
    }
    Redraw();
}
void HHBUI::UIPage::SetCurrent(INT fCurrent)
{
    if (fCurrent > p_data.pagenum)
    {
        fCurrent = p_data.pagenum;
    }
    p_data.current = fCurrent;
    Redraw();
}
INT HHBUI::UIPage::GetCurrent()
{
    return p_data.current;
}
void HHBUI::UIPage::SetInterval(INT fInterval)
{
    p_data.interval = fInterval;
}
void HHBUI::UIPage::SetItemText(LPCWSTR prev, LPCWSTR next, LPCWSTR jump)
{
    if (prev)
    {
        //LocalFree((HLOCAL)p_data.itemtext_prev);
        p_data.itemtext_prev = prev;
    }
    if (next)
    {
        //LocalFree((HLOCAL)p_data.itemtext_next);
        p_data.itemtext_next = next;
    }
    if (jump)
    {
        //LocalFree((HLOCAL)p_data.itemtext_jump);
        p_data.itemtext_jump = jump;
    }
}
void HHBUI::UIPage::SetCrbkg(UIColor normal, UIColor hover, UIColor checked, UIColor ban)
{
    if (!normal.empty())
        p_data.crbkgnormal = normal;
    if (!hover.empty())
        p_data.crbkghover = hover;
    if (!checked.empty())
        p_data.crbkgchecked = checked;
    if (!ban.empty())
        p_data.crbkgban = ban;
}
void HHBUI::UIPage::SetCrBorder(UIColor normal, UIColor hover, UIColor checked, UIColor ban)
{
    if (!normal.empty())
        p_data.crbordernormal = normal;
    if (!hover.empty())
        p_data.crborderhover = hover;
    if (!checked.empty())
        p_data.crborderchecked = checked;
    if (!ban.empty())
        p_data.crborderban = ban;
}
void HHBUI::UIPage::SetCrText(UIColor normal, UIColor hover, UIColor checked, UIColor ban)
{
    if (!normal.empty())
        p_data.crtextnormal = normal;
    if (!hover.empty())
        p_data.crtexthover = hover;
    if (!checked.empty())
        p_data.crtextchecked = checked;
    if (!ban.empty())
        p_data.crtextban = ban;
}
void HHBUI::UIPage::SetCrEllipsis(UIColor normal, UIColor hover)
{
    if (!normal.empty())
        p_data.crcromitnormal = normal;
    if (!hover.empty())
        p_data.crcromithover = hover;
}
void HHBUI::UIPage::SetBtnRound(INT fRound)
{
    p_data.Round = fRound;
    ResetPageSize();
}
LRESULT HHBUI::UIPage::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_CREATE)
	{
		if ((m_data.dwStyle & eos_paging) == eos_paging)
		{
			p_data.pwidth = m_data.Frame.right - m_data.Frame.left;
			p_data.pheight = m_data.Frame.bottom - m_data.Frame.top;
			p_data.hotid = 0;
			p_data.current = 1;
			p_data.left_edit = 0;
			p_data.itemtext_prev = L"上一页";
			p_data.itemtext_next = L"下一页";
			p_data.itemtext_jump = L"跳转";
			p_data.interval = 8;
			p_data.count = 1;
			p_data.shownum = 1;
			p_data.pagenum = 1;
			p_data.crbkgnormal = UIColor(255, 255, 255, 255);
			p_data.crbkghover = UIColor(255, 255, 255, 255);
			p_data.crbkgchecked = UIColor(255, 255, 255, 255);
			p_data.crbkgban = UIColor(255, 255, 255, 255);
			p_data.crbordernormal = UIColor(217, 217, 217, 255);
			p_data.crborderhover = UIColor(24, 144, 255, 255);
			p_data.crborderchecked = UIColor(24, 144, 255, 255);
			p_data.crborderban = UIColor(217, 217, 217, 255);
			p_data.crtextnormal = UIColor(65, 65, 65, 255);
			if ((m_data.dwStyle & eos_paging_showjump) == eos_paging_showjump)
			{
				p_data.edit = new UIEdit(this, 0, 3, 50, p_data.pheight - 6, L"1", eos_edit_hideselection | eos_edit_numericinput | eos_edit_disablemenu);
				p_data.edit->SetTipsText(L"跳转", {}, p_data.crtextnormal);
				p_data.edit->SetLimitText(5);

			}
			p_data.crtexthover = UIColor(24, 144, 255, 255);
			p_data.crtextchecked = UIColor(24, 144, 255, 255);
			p_data.crtextban = UIColor(199, 199, 199, 255);
			p_data.crcromitnormal = UIColor(65, 65, 65, 200);
			p_data.crcromithover = UIColor(24, 144, 255, 255);

            ResetPageSize();
			p_data.lpbuttonleft = new int32_t[14]();
			p_data.lpbuttonwidth = new int32_t[14]();
			p_data.lpbuttonpage = new int32_t[14]();
		}
	}
	else if (uMsg == WM_DESTROY)
	{
		if ((m_data.dwStyle & eos_paging) == eos_paging)
		{
			if (p_data.edit)
				delete p_data.edit;
			delete[] p_data.lpbuttonleft;
			delete[] p_data.lpbuttonwidth;
			delete[] p_data.lpbuttonpage;
            if ((m_data.dwStyle & eos_paging_pnatext) != eos_paging_pnatext)
            {
                delete p_data.prev_normal;
                delete p_data.prev_light;
                delete p_data.prev_ban;
                delete p_data.next_normal;
                delete p_data.next_light;
                delete p_data.next_ban;
            }
            delete p_data.topfive_normal;
            delete p_data.topfive_light;
            delete p_data.lastfive_normal;
            delete p_data.lastfive_light;
		}
	}
    else if (uMsg == WM_SIZE)
    {
        if ((m_data.dwStyle & eos_paging) == eos_paging)
        {
            p_data.pwidth = GET_X_LPARAM(lParam);
            p_data.pheight = GET_Y_LPARAM(lParam);
            ResetPageSize();
            Redraw();
        }
    }
    else if (uMsg == WM_MOUSELEAVE)
    {
        if ((m_data.dwStyle & eos_paging) == eos_paging)
        {
            p_data.hotid = 0;
            Redraw();
        }
    }
    else if (uMsg == WM_LBUTTONDOWN)
    {
        if ((m_data.dwStyle & eos_paging) == eos_paging)
        {
            INT buttonID = 0;
            POINT pt = { GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam) };
            INT Num = 14;
            if ((m_data.dwStyle & eos_paging_showjump) != eos_paging_showjump)
                Num = 12;
            for (INT i = 1; i < Num; i++)
            {
                RECT rc{ p_data.lpbuttonleft[i], 0, p_data.lpbuttonleft[i] + p_data.lpbuttonwidth[i], p_data.pheight };
                if (PtInRect(&rc, pt))
                {
                    buttonID = p_data.lpbuttonpage[i];
                    continue;
                }
            }
            if (buttonID != 0)
            {
                if (DispatchNotify(WMM_PAGE_SELCHANGER, 0, buttonID) == 0)
                {
                    p_data.current = buttonID;
                    Redraw();
                }

            }
        }
    }
    else if (uMsg == WM_MOUSEMOVE)
    {
        if ((m_data.dwStyle & eos_paging) == eos_paging)
        {
            INT buttonID = 0;
            POINT pt = { GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam) };
            INT Num = 14;
            if ((m_data.dwStyle & eos_paging_showjump) != eos_paging_showjump)
                Num = 12;
            for (INT i = 1; i < Num; i++)
            {
                RECT rc{ p_data.lpbuttonleft[i], 0, p_data.lpbuttonleft[i] + p_data.lpbuttonwidth[i],  p_data.pheight };
                if (PtInRect(&rc, pt))
                {
                    buttonID = i;
                    continue;
                }
            }
            if (buttonID != p_data.hotid)
            {
                p_data.hotid = buttonID;
                Redraw();
                if (buttonID == 1)
                {
                    if (p_data.current == 1)
                    {
                        SetCursor(IDC_NO);
                    }
                    else
                    {
                        SetCursor(IDC_HAND);
                    }
                }
                else if (buttonID >= 2 && buttonID <= 10)
                {
                    SetCursor(IDC_HAND);
                }
                else if (buttonID == 11)
                {
                    if (p_data.current == p_data.pagenum)
                    {
                        SetCursor(IDC_NO);
                    }
                    else
                    {
                        SetCursor(IDC_HAND);
                    }
                }
                else if (buttonID == 13)
                {
                    SetCursor(IDC_HAND);
                }
                else
                {
                    SetCursor(IDC_ARROW);
                }
                if ((m_data.dwStyle & eos_paging_showtips) == eos_paging_showtips && buttonID != 0)
                {
                    if (p_data.lpbuttonwidth[buttonID] != 0)
                    {
                        if (buttonID == 1)
                        {
                            TooltipsPop(L"上一页");
                        }
                        else if (buttonID == 2)
                        {
                            TooltipsPop(L"第1页");
                        }
                        else if (buttonID == 3)
                        {
                            TooltipsPop(L"前5页");
                        }
                        else if (buttonID >= 4 && buttonID <= 8 || buttonID == 10)
                        {
                            auto str = L"第" + std::to_wstring(p_data.lpbuttonpage[buttonID]) + L"页";
                            TooltipsPop(str.c_str());
                        }
                        else if (buttonID == 9)
                        {
                            TooltipsPop(L"后5页");
                        }
                        else if (buttonID == 11)
                        {
                            TooltipsPop(L"下一页");
                        }
                        else if (buttonID == 13)
                        {
                            if ((m_data.dwStyle & eos_paging_showjump) == eos_paging_showjump)
                            {
                                auto text_length = p_data.edit->GetTextLength(); //取文本长度
                                if (text_length > 0)
                                {
                                    p_data.lpbuttonpage[13] = std::stoi(p_data.edit->GetText());
                                    if (p_data.lpbuttonpage[13] > p_data.pagenum)
                                        p_data.lpbuttonpage[13] = p_data.pagenum;
                                    if (p_data.lpbuttonpage[13] < 1)
                                        p_data.lpbuttonpage[13] = 1;
                                    auto pstr = L"跳转到第" + std::to_wstring(p_data.lpbuttonpage[13]) + L"页";
                                    TooltipsPop(pstr.c_str());
                                }
                            }

                        }
                    }
                }


            }
        }
    }
	return S_OK;
}
void HHBUI::UIPage::OnPaintProc(ps_context ps)
{
    if ((m_data.dwStyle & eos_paging) == eos_paging)
    {
        INT pCount = p_data.count;
        INT ShowNum = p_data.shownum;
        INT Current = p_data.current;
        INT hotID = p_data.hotid;
        INT PageNum = p_data.pagenum;
        FLOAT nRound = p_data.Round;
        UIColor textcr = p_data.crtextnormal;
        auto lpBrushfl = UIBrush(p_data.crbkgnormal);
        auto lpBrushbord = UIBrush(p_data.crbordernormal);
        FLOAT totalWidth = 0;
        INT offset = 0;
        if (p_data.mod != e_page_left)
        {
            for (INT i = 1; i <= 13; i++) {
                if (p_data.lpbuttonwidth[i] > 0) {
                    totalWidth += p_data.lpbuttonwidth[i] + p_data.interval;
                }
            }
            if (totalWidth != 0)
            {
                if (p_data.mod == e_page_center) {
                    offset = (ps.uWidth - totalWidth) / 2;
                }
                else if (p_data.mod == e_page_right) {
                    offset = ps.uWidth - totalWidth;
                }
            }
        }

        p_data.lpbuttonleft[1] = offset;
        p_data.lpbuttonpage[1] = Current - 1;
        if ((ps.dwStyle & eos_paging_pnatext) != eos_paging_pnatext)
        {
            p_data.lpbuttonwidth[1] = ps.uHeight;
            if (Current == 1)//已经是第一页 画 上一页 禁止
            {
                p_data.lpbuttonpage[1] = 0;
                ps.hCanvas->DrawImage(p_data.prev_ban, p_data.lpbuttonleft[1], 0);
            }
            else
            {
                if (hotID == 1)// 上一页 画 上一页 点燃
                {
                    ps.hCanvas->DrawImage(p_data.prev_light, p_data.lpbuttonleft[1], 0);
                }
                else
                {
                    ps.hCanvas->DrawImage(p_data.prev_normal, p_data.lpbuttonleft[1], 0);

                }
            }


        }
        else
        {
            FLOAT nTextWidth = 0, nTextHeight = 0;
            ps.hCanvas->CalcTextSize(ps.hFont, p_data.itemtext_prev, ps.dwTextFormat, 0, ps.uHeight, &nTextWidth, &nTextHeight);
            nTextWidth += 8.f;

            p_data.lpbuttonwidth[1] = (INT)nTextWidth;
            if (Current == 1)// 已经是第一页 画 上一页 禁止
            {
                p_data.lpbuttonpage[1] = 0;
                lpBrushfl.SetColor(p_data.crbkgban);
                lpBrushbord.SetColor(p_data.crborderban);
                textcr = p_data.crtextban;
            }
            else
            {
                if (hotID == 1)// 上一页 画 上一页 点燃
                {
                    lpBrushfl.SetColor(p_data.crbkghover);
                    lpBrushbord.SetColor(p_data.crborderhover);
                    textcr = p_data.crtexthover;
                }

            }
            ps.hCanvas->FillRoundRect(&lpBrushfl, p_data.lpbuttonleft[1], 1.f, p_data.lpbuttonwidth[1] + p_data.lpbuttonleft[1], ps.uHeight - 1.f, nRound);
            ps.hCanvas->DrawRoundRect(&lpBrushbord, p_data.lpbuttonleft[1], 1.f, p_data.lpbuttonwidth[1] + p_data.lpbuttonleft[1], ps.uHeight - 1.f, nRound, 1.f);
            ps.hCanvas->DrawTextByColor(ps.hFont, p_data.itemtext_prev, ps.dwTextFormat, p_data.lpbuttonleft[1], 0, p_data.lpbuttonwidth[1] + p_data.lpbuttonleft[1], ps.uHeight, textcr);
        }
        //画 第一页
        INT interval = p_data.interval;
        p_data.lpbuttonleft[2] = p_data.lpbuttonleft[1] + p_data.lpbuttonwidth[1] + interval;
        p_data.lpbuttonwidth[2] = ps.uHeight + 8;
        p_data.lpbuttonpage[2] = 1;
        lpBrushfl.SetColor(p_data.crbkgnormal);
        lpBrushbord.SetColor(p_data.crbordernormal);
        textcr = p_data.crtextnormal;
        if (Current == 1)//已经是第一页 画 第一页 选中
        {
            lpBrushfl.SetColor(p_data.crbkgchecked);
            lpBrushbord.SetColor(p_data.crborderchecked);
            textcr = p_data.crtextchecked;
        }
        else
        {
            if (hotID == 2)// 鼠标在 第一页 画 第一页 点燃
            {
                lpBrushfl.SetColor(p_data.crbkghover);
                lpBrushbord.SetColor(p_data.crborderhover);
                textcr = p_data.crtexthover;
            }

        }

        ps.hCanvas->FillRoundRect(&lpBrushfl, p_data.lpbuttonleft[2], 1.f, p_data.lpbuttonleft[2] + ps.uHeight + 8, ps.uHeight - 1.f, nRound);
        ps.hCanvas->DrawRoundRect(&lpBrushbord, p_data.lpbuttonleft[2], 1.f, p_data.lpbuttonleft[2] + ps.uHeight + 8, ps.uHeight - 1.f, nRound, 1.f);
        ps.hCanvas->DrawTextByColor(ps.hFont, L"1", ps.dwTextFormat, p_data.lpbuttonleft[2], 0, ps.uHeight + p_data.lpbuttonleft[2] + 8, ps.uHeight, textcr);
        //画 前五页

        p_data.lpbuttonleft[3] = p_data.lpbuttonleft[2] + p_data.lpbuttonwidth[2] + interval;
        if (Current > 4)
        {
            p_data.lpbuttonwidth[3] = ps.uHeight;
            p_data.lpbuttonpage[3] = Current - 5;
            if (p_data.lpbuttonpage[3] < 1)
                p_data.lpbuttonpage[3] = 1;
            if (hotID == 3)//画 前五页 点燃
            {
                ps.hCanvas->DrawImage(p_data.topfive_light, p_data.lpbuttonleft[3], 0);
            }
            else
            {
                ps.hCanvas->DrawImage(p_data.topfive_normal, p_data.lpbuttonleft[3], 0);
            }
        }
        else
        {
            p_data.lpbuttonwidth[3] = 0;
            p_data.lpbuttonpage[3] = 0;
        }
        //画 中间页
        INT init = 0;
        INT iterations = (PageNum > 7) ? 5 : (PageNum - 2);

        for (INT i = 1; i < iterations + 1; i++)
        {
            init = init + 1;

            if (Current < 4 && i == 5)
            {
                p_data.lpbuttonleft[3 + i] = p_data.lpbuttonleft[3 + i - 1] + p_data.lpbuttonwidth[3 + i - 1] + interval;
                p_data.lpbuttonwidth[3 + i] = 0;
                continue;
            }
            if (Current < 5)
            {
                p_data.lpbuttonpage[3 + i] = i + 1;
            }
            else
            {
                p_data.lpbuttonpage[3 + i] = Current - 3 + i;
                if (Current >= PageNum - 1)//是尾页 或 倒数第二页
                    p_data.lpbuttonpage[3 + i] = PageNum - 5 + i;  //页码不变
                if (p_data.lpbuttonpage[3 + i] == PageNum)  // 中间不画最后一个按钮
                {
                    p_data.lpbuttonleft[3 + i] = p_data.lpbuttonleft[3 + i - 1] + p_data.lpbuttonwidth[3 + i - 1] + interval;
                    p_data.lpbuttonwidth[3 + i] = 0;
                    break;
                }
            }
            FLOAT nTextWidth = 0, nTextHeight = 0;
            lpBrushfl.SetColor(p_data.crbkgnormal);
            lpBrushbord.SetColor(p_data.crbordernormal);
            textcr = p_data.crtextnormal;
            auto str = std::to_wstring(p_data.lpbuttonpage[3 + i]);

            ps.hCanvas->CalcTextSize(ps.hFont, str.c_str(), ps.dwTextFormat, 0, ps.uHeight, &nTextWidth, &nTextHeight);
            nTextWidth += 8.f;
            if (nTextWidth < ps.uHeight)
                nTextWidth = ps.uHeight + 8.f;

            if (p_data.lpbuttonwidth[3 + i - 1] != 0)
            {
                p_data.lpbuttonleft[3 + i] = p_data.lpbuttonleft[3 + i - 1] + p_data.lpbuttonwidth[3 + i - 1] + interval;
            }
            else
            {
                p_data.lpbuttonleft[3 + i] = p_data.lpbuttonleft[3 + i - 1];
            }
            p_data.lpbuttonwidth[3 + i] = nTextWidth;

            if (Current < 5)
            {
                if (Current == i + 1)//选中
                {
                    lpBrushfl.SetColor(p_data.crbkgchecked);
                    lpBrushbord.SetColor(p_data.crborderchecked);
                    textcr = p_data.crtextchecked;
                }
                else if (hotID == 3 + i)
                {
                    lpBrushfl.SetColor(p_data.crbkghover);
                    lpBrushbord.SetColor(p_data.crborderhover);
                    textcr = p_data.crtexthover;
                }
            }
            else
            {
                if (p_data.lpbuttonpage[3 + i] == Current)// ' 选中
                {
                    lpBrushfl.SetColor(p_data.crbkgchecked);
                    lpBrushbord.SetColor(p_data.crborderchecked);
                    textcr = p_data.crtextchecked;
                }
                else if (hotID == 3 + i)
                {
                    lpBrushfl.SetColor(p_data.crbkghover);
                    lpBrushbord.SetColor(p_data.crborderhover);
                    textcr = p_data.crtexthover;
                }
            }
            ps.hCanvas->FillRoundRect(&lpBrushfl, p_data.lpbuttonleft[3 + i], 1.f, p_data.lpbuttonleft[3 + i] + nTextWidth, ps.uHeight - 1.f, nRound);
            ps.hCanvas->DrawRoundRect(&lpBrushbord, p_data.lpbuttonleft[3 + i], 1.f, p_data.lpbuttonleft[3 + i] + nTextWidth, ps.uHeight - 1.f, nRound, 1.f);
            ps.hCanvas->DrawTextByColor(ps.hFont, str.c_str(), ps.dwTextFormat, p_data.lpbuttonleft[3 + i], 0, nTextWidth + p_data.lpbuttonleft[3 + i], ps.uHeight, textcr);
        }
        if (init < 5)
        {
            for (INT i = 1; i < 5 - init + 1; i++)
            {
                if (p_data.lpbuttonwidth[3 + init + i - 1] != 0)
                {
                    p_data.lpbuttonleft[3 + init + i] = p_data.lpbuttonleft[3 + init + i - 1] + p_data.lpbuttonwidth[3 + init + i - 1] + interval;
                }
                else
                {
                    p_data.lpbuttonleft[3 + init + i] = p_data.lpbuttonleft[3 + init + i - 1];
                }
                p_data.lpbuttonwidth[3 + init + i] = 0;
                p_data.lpbuttonpage[3 + init + i] = 0;
            }
        }
        //画 后五页
        if (PageNum > 7 && PageNum - Current > 3)
        {
            if (p_data.lpbuttonwidth[8] != 0)
            {
                p_data.lpbuttonleft[9] = p_data.lpbuttonleft[8] + p_data.lpbuttonwidth[8] + interval;
            }
            else
            {
                p_data.lpbuttonleft[9] = p_data.lpbuttonleft[8];
            }
            p_data.lpbuttonwidth[9] = ps.uHeight;
            p_data.lpbuttonpage[9] = Current + 5;
            if (p_data.lpbuttonpage[9] > PageNum)
                p_data.lpbuttonpage[9] = PageNum;
            if (hotID == 9)
            {
                ps.hCanvas->DrawImage(p_data.lastfive_light, p_data.lpbuttonleft[9], 0);
            }
            else
            {
                ps.hCanvas->DrawImage(p_data.lastfive_normal, p_data.lpbuttonleft[9], 0);
            }
        }
        else
        {
            if (p_data.lpbuttonwidth[8] != 0)
            {
                p_data.lpbuttonleft[9] = p_data.lpbuttonleft[8] + p_data.lpbuttonwidth[8] + interval;
            }
            else
            {
                p_data.lpbuttonleft[9] = p_data.lpbuttonleft[8] + p_data.lpbuttonwidth[8];
            }
            p_data.lpbuttonwidth[9] = 0;
            p_data.lpbuttonpage[9] = 0;

        }
        //画 尾页
        if (PageNum > 1)
        {
            FLOAT nTextWidth = 0, nTextHeight = 0;
            lpBrushfl.SetColor(p_data.crbkgnormal);
            lpBrushbord.SetColor(p_data.crbordernormal);
            textcr = p_data.crtextnormal;
            auto str = std::to_wstring(PageNum);
            ps.hCanvas->CalcTextSize(ps.hFont, str.c_str(), ps.dwTextFormat, 0, ps.uHeight, &nTextWidth, &nTextHeight);
            nTextWidth += 8;
            if (nTextWidth < ps.uHeight)
                nTextWidth = ps.uHeight + 8;

            if (p_data.lpbuttonwidth[9] != 0)
            {
                p_data.lpbuttonleft[10] = p_data.lpbuttonleft[9] + p_data.lpbuttonwidth[9] + interval;
            }
            else
            {
                p_data.lpbuttonleft[10] = p_data.lpbuttonleft[9];
            }
            p_data.lpbuttonwidth[10] = nTextWidth;
            p_data.lpbuttonpage[10] = PageNum;
            if (Current == PageNum)//已经是第一页 画 第一页 选中
            {
                lpBrushfl.SetColor(p_data.crbkgchecked);
                lpBrushbord.SetColor(p_data.crborderchecked);
                textcr = p_data.crtextchecked;
            }
            else
            {
                if (hotID == 10)
                {
                    lpBrushfl.SetColor(p_data.crbkghover);
                    lpBrushbord.SetColor(p_data.crborderhover);
                    textcr = p_data.crtexthover;
                }
            }
            ps.hCanvas->FillRoundRect(&lpBrushfl, p_data.lpbuttonleft[10], 1.f, p_data.lpbuttonleft[10] + nTextWidth, ps.uHeight - 1.f, nRound);
            ps.hCanvas->DrawRoundRect(&lpBrushbord, p_data.lpbuttonleft[10], 1.f, p_data.lpbuttonleft[10] + nTextWidth, ps.uHeight - 1.f, nRound, 1.f);
            ps.hCanvas->DrawTextByColor(ps.hFont, str.c_str(), ps.dwTextFormat, p_data.lpbuttonleft[10], 0, nTextWidth + p_data.lpbuttonleft[10], ps.uHeight, textcr);


        }
        else
        {
            p_data.lpbuttonleft[10] = p_data.lpbuttonleft[9];
            p_data.lpbuttonwidth[10] = 0;
            p_data.lpbuttonpage[10] = 0;

        }
        //画 下一页
        if (p_data.lpbuttonwidth[10] != 0)
        {
            p_data.lpbuttonleft[11] = p_data.lpbuttonleft[10] + p_data.lpbuttonwidth[10] + interval;
        }
        else
        {
            p_data.lpbuttonleft[11] = p_data.lpbuttonleft[10];

        }
        p_data.lpbuttonpage[11] = Current + 1;
        if ((ps.dwStyle & eos_paging_pnatext) != eos_paging_pnatext)
        {
            p_data.lpbuttonwidth[11] = ps.uHeight;
            if (Current == PageNum)//已经是最后一页 画 下一页 禁止
            {
                p_data.lpbuttonpage[11] = 0;
                ps.hCanvas->DrawImage(p_data.next_ban, p_data.lpbuttonleft[11], 0);
            }
            else
            {
                if (hotID == 11)//画 下一页 点燃
                {
                    ps.hCanvas->DrawImage(p_data.next_light, p_data.lpbuttonleft[11], 0);
                }
                else
                {
                    ps.hCanvas->DrawImage(p_data.next_normal, p_data.lpbuttonleft[11], 0);
                }
            }

        }
        else
        {
            FLOAT nTextWidth = 0, nTextHeight = 0;
            lpBrushfl.SetColor(p_data.crbkgnormal);
            lpBrushbord.SetColor(p_data.crbordernormal);
            textcr = p_data.crtextnormal;
            ps.hCanvas->CalcTextSize(ps.hFont, p_data.itemtext_next, ps.dwTextFormat, 0, ps.uHeight, &nTextWidth, &nTextHeight);
            nTextWidth += 8;
            if (nTextWidth < ps.uHeight)
                nTextWidth = ps.uHeight + 8;
            p_data.lpbuttonwidth[11] = (INT)nTextWidth;
            if (p_data.lpbuttonwidth[11] < (int32_t)ps.uHeight)
                p_data.lpbuttonwidth[11] = ps.uHeight;

            if (Current == PageNum)// 已经是最后一页 画 下一页 禁止
            {
                p_data.lpbuttonpage[11] = 0;
                lpBrushfl.SetColor(p_data.crbkgban);
                lpBrushbord.SetColor(p_data.crborderban);
                textcr = p_data.crtextban;
            }
            else
            {
                if (hotID == 11)// 画 下一页 点燃
                {
                    lpBrushfl.SetColor(p_data.crbkghover);
                    lpBrushbord.SetColor(p_data.crborderhover);
                    textcr = p_data.crtexthover;
                }

            }
            ps.hCanvas->FillRoundRect(&lpBrushfl, p_data.lpbuttonleft[11], 1.f, p_data.lpbuttonwidth[11] + p_data.lpbuttonleft[11], ps.uHeight - 1.f, nRound);
            ps.hCanvas->DrawRoundRect(&lpBrushbord, p_data.lpbuttonleft[11], 1.f, p_data.lpbuttonwidth[11] + p_data.lpbuttonleft[11], ps.uHeight - 1.f, nRound, 1.f);
            ps.hCanvas->DrawTextByColor(ps.hFont, p_data.itemtext_next, ps.dwTextFormat, p_data.lpbuttonleft[11], 0, p_data.lpbuttonwidth[11] + p_data.lpbuttonleft[11], ps.uHeight, textcr);
        }
        //画跳转
        if ((ps.dwStyle & eos_paging_showjump) == eos_paging_showjump)
        {
            INT left_edit = p_data.left_edit;
            p_data.lpbuttonleft[12] = p_data.lpbuttonleft[11] + p_data.lpbuttonwidth[11] + interval;
            p_data.lpbuttonwidth[12] = UIEngine::ScaleValue(52);
            if (left_edit != p_data.lpbuttonleft[12])
            {
                p_data.edit->Move(p_data.lpbuttonleft[12] / ps.dpi + UIEngine::ScaleValue(1), 3, p_data.lpbuttonwidth[12] / ps.dpi - UIEngine::ScaleValue(2), ps.uHeight / ps.dpi - 6, 0);
                p_data.left_edit = p_data.lpbuttonleft[12];
            }
            //画 编辑框背景
            lpBrushfl.SetColor(p_data.crbkgnormal);
            lpBrushbord.SetColor(p_data.crbordernormal);
            if (hotID == 12)
            {
                lpBrushfl.SetColor(p_data.crbkghover);
                lpBrushbord.SetColor(p_data.crborderhover);
            }

            ps.hCanvas->FillRoundRect(&lpBrushfl, p_data.lpbuttonleft[12], 1.f, p_data.lpbuttonleft[12] + p_data.lpbuttonwidth[12], ps.uHeight - 1.f, nRound);
            ps.hCanvas->DrawRoundRect(&lpBrushbord, p_data.lpbuttonleft[12], 1.f, p_data.lpbuttonleft[12] + p_data.lpbuttonwidth[12], ps.uHeight - 1.f, nRound, 1.f);

            p_data.lpbuttonleft[13] = p_data.lpbuttonleft[12] + p_data.lpbuttonwidth[12] + interval;
            p_data.lpbuttonwidth[13] = UIEngine::ScaleValue(60);


            lpBrushfl.SetColor(p_data.crbkgnormal);
            lpBrushbord.SetColor(p_data.crbordernormal);
            textcr = p_data.crtextnormal;
            if (hotID == 13)
            {
                lpBrushfl.SetColor(p_data.crbkghover);
                lpBrushbord.SetColor(p_data.crborderhover);
                textcr = p_data.crtexthover;
            }

            ps.hCanvas->FillRoundRect(&lpBrushfl, p_data.lpbuttonleft[13], 1.f, p_data.lpbuttonleft[13] + p_data.lpbuttonwidth[13], ps.uHeight - 1.f, nRound);
            ps.hCanvas->DrawRoundRect(&lpBrushbord, p_data.lpbuttonleft[13], 1.f, p_data.lpbuttonleft[13] + p_data.lpbuttonwidth[13], ps.uHeight - 1.f, nRound, 1.f);
            ps.hCanvas->DrawTextByColor(ps.hFont, p_data.itemtext_jump, ps.dwTextFormat, p_data.lpbuttonleft[13], 0, p_data.lpbuttonwidth[13] + p_data.lpbuttonleft[13], ps.uHeight, textcr);

        }
    }
}
void HHBUI::UIPage::ResetPageSize()
{
    float nSiez = p_data.pheight;
    float nleft = 1.f, ntop = 1.f;
    auto lpBrushfl = UIBrush(p_data.crbkgnormal);
    auto lpBrushbord = UIBrush(p_data.crbordernormal);
    auto hCanvas = new UICanvas(m_data.pWnd, nSiez, nSiez);
    if ((m_data.dwStyle & eos_paging_pnatext) != eos_paging_pnatext)
    {
        hCanvas->BeginDraw();
        //画 上一页 正常
        if (p_data.prev_normal)
            delete p_data.prev_normal;
        hCanvas->FillRoundRect(&lpBrushfl, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round);
        hCanvas->DrawRoundRect(&lpBrushbord, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round, 1.0f);
        nleft = (nSiez / 2) - (7.f / 2) + 1.f;
        ntop = (nSiez / 2) - (10.f / 2) + 4.f;

        lpBrushfl.SetColor(p_data.crtextnormal);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop + 1, nleft + 5, ntop - 3, 1.0f);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop + 2, nleft + 5, ntop + 6, 1.0f);
        hCanvas->EndDraw();
        hCanvas->ToImage(&p_data.prev_normal);

        hCanvas->BeginDraw();
        hCanvas->Clear();
        //画 上一页 点燃

        lpBrushfl.SetColor(p_data.crbkghover);
        lpBrushbord.SetColor(p_data.crborderhover);
        if (p_data.prev_light)
            delete p_data.prev_light;
        hCanvas->FillRoundRect(&lpBrushfl, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round);
        hCanvas->DrawRoundRect(&lpBrushbord, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round, 1.0f);
        nleft = (nSiez / 2) - (7.f / 2) + 1.f;
        ntop = (nSiez / 2) - (10.f / 2) + 4.f;

        lpBrushfl.SetColor(p_data.crtexthover);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop + 1, nleft + 5, ntop - 3, 1.0f);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop + 2, nleft + 5, ntop + 6, 1.0f);
        hCanvas->EndDraw();
        hCanvas->ToImage(&p_data.prev_light);

        hCanvas->BeginDraw();
        hCanvas->Clear();
        // 画 上一页 禁止

        lpBrushfl.SetColor(p_data.crbkgban);
        lpBrushbord.SetColor(p_data.crborderban);
        if (p_data.prev_ban)
            delete p_data.prev_ban;
        hCanvas->FillRoundRect(&lpBrushfl, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round);
        hCanvas->DrawRoundRect(&lpBrushbord, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round, 1.0f);
        nleft = (nSiez / 2) - (7.f / 2) + 1.f;
        ntop = (nSiez / 2) - (10.f / 2) + 4.f;

        lpBrushfl.SetColor(p_data.crtextban);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop + 1, nleft + 5, ntop - 3, 1.0f);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop + 2, nleft + 5, ntop + 6, 1.0f);
        hCanvas->EndDraw();
        hCanvas->ToImage(&p_data.prev_ban);

        hCanvas->BeginDraw();
        hCanvas->Clear();
        //画 下一页 正常

        lpBrushfl.SetColor(p_data.crbkgnormal);
        lpBrushbord.SetColor(p_data.crbordernormal);
        if (p_data.next_normal)
            delete p_data.next_normal;
        hCanvas->FillRoundRect(&lpBrushfl, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round);
        hCanvas->DrawRoundRect(&lpBrushbord, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round, 1.0f);
        nleft = (nSiez / 2) - (7.f / 2) + 1.f;
        ntop = (nSiez / 2) - (10.f / 2) + 4.f;

        lpBrushfl.SetColor(p_data.crtextnormal);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop - 3, nleft + 5, ntop + 1, 1.0f);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop + 6, nleft + 5, ntop + 2, 1.0f);

        hCanvas->EndDraw();
        hCanvas->ToImage(&p_data.next_normal);

        hCanvas->BeginDraw();
        hCanvas->Clear();
        //画 下一页 点燃

        lpBrushfl.SetColor(p_data.crbkghover);
        lpBrushbord.SetColor(p_data.crborderhover);
        if (p_data.next_light)
            delete p_data.next_light;
        hCanvas->FillRoundRect(&lpBrushfl, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round);
        hCanvas->DrawRoundRect(&lpBrushbord, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round, 1.0f);
        nleft = (nSiez / 2) - (7.f / 2) + 1.f;
        ntop = (nSiez / 2) - (10.f / 2) + 4.f;

        lpBrushfl.SetColor(p_data.crtexthover);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop - 3, nleft + 5, ntop + 1, 1.0f);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop + 6, nleft + 5, ntop + 2, 1.0f);
        hCanvas->EndDraw();
        hCanvas->ToImage(&p_data.next_light);

        hCanvas->BeginDraw();
        hCanvas->Clear();
        //画 下一页 禁止

        lpBrushfl.SetColor(p_data.crbkgban);
        lpBrushbord.SetColor(p_data.crborderban);
        if (p_data.next_ban)
            delete p_data.next_ban;
        hCanvas->FillRoundRect(&lpBrushfl, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round);
        hCanvas->DrawRoundRect(&lpBrushbord, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round, 1.0f);
        nleft = (nSiez / 2) - (7.f / 2) + 1.f;
        ntop = (nSiez / 2) - (10.f / 2) + 4.f;

        lpBrushfl.SetColor(p_data.crtextban);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop - 3, nleft + 5, ntop + 1, 1.0f);
        hCanvas->DrawLine(&lpBrushfl, nleft, ntop + 6, nleft + 5, ntop + 2, 1.0f);
        hCanvas->EndDraw();
        hCanvas->ToImage(&p_data.next_ban);
    }
    hCanvas->BeginDraw();
    hCanvas->Clear();
    //画 前五页 正常

    lpBrushfl.SetColor(p_data.crbkgnormal);
    lpBrushbord.SetColor(p_data.crcromitnormal);
    if (p_data.topfive_normal)
        delete p_data.topfive_normal;
    hCanvas->FillRoundRect(&lpBrushfl, 0, 0, nSiez, nSiez, p_data.Round);
    nleft = (nSiez / 2) - 8.f;
    ntop = (nSiez / 2) - 1.f;
    hCanvas->DrawLine(&lpBrushbord, nleft, ntop, nleft, ntop + 1, 2.0f);
    hCanvas->DrawLine(&lpBrushbord, nleft + 2 + 5, ntop, nleft + 2 + 5, ntop + 1, 2.0f);
    hCanvas->DrawLine(&lpBrushbord, nleft + 2 + 5 + 2 + 5, ntop, nleft + 2 + 5 + 2 + 5, ntop + 1, 2.0f);
    hCanvas->EndDraw();
    hCanvas->ToImage(&p_data.topfive_normal);

    hCanvas->BeginDraw();
    hCanvas->Clear();
    //画 前五页 点燃
    if (p_data.topfive_light)
        delete p_data.topfive_light;
    hCanvas->FillRoundRect(&lpBrushfl, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round);
    nleft = (nSiez / 2) - (7.f / 2) + 1.f;
    ntop = (nSiez / 2) - (10.f / 2) + 4.f;

    lpBrushfl.SetColor(p_data.crbkgnormal);
    lpBrushbord.SetColor(p_data.crcromithover);
    hCanvas->DrawLine(&lpBrushbord, nleft, ntop + 1, nleft + 3, ntop - 3, 1.0f);
    hCanvas->DrawLine(&lpBrushbord, nleft, ntop + 2, nleft + 3, ntop + 6, 1.0f);
    hCanvas->DrawLine(&lpBrushbord, nleft + 4, ntop + 1, nleft + 7, ntop - 3, 1.0f);
    hCanvas->DrawLine(&lpBrushbord, nleft + 4, ntop + 2, nleft + 7, ntop + 6, 1.0f);
    hCanvas->EndDraw();
    hCanvas->ToImage(&p_data.topfive_light);

    hCanvas->BeginDraw();
    hCanvas->Clear();
    //画 后五页 正常
    if (p_data.lastfive_normal)
        delete p_data.lastfive_normal;
    hCanvas->FillRoundRect(&lpBrushfl, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round);
    nleft = (nSiez / 2) - 8.f;
    ntop = (nSiez / 2) - 1.f;
    lpBrushbord.SetColor(p_data.crcromitnormal);
    hCanvas->DrawLine(&lpBrushbord, nleft, ntop, nleft, ntop + 1, 2.0f);
    hCanvas->DrawLine(&lpBrushbord, nleft + 2 + 5, ntop, nleft + 2 + 5, ntop + 1, 2.0f);
    hCanvas->DrawLine(&lpBrushbord, nleft + 2 + 5 + 2 + 5, ntop, nleft + 2 + 5 + 2 + 5, ntop + 1, 2.0f);
    hCanvas->EndDraw();
    hCanvas->ToImage(&p_data.lastfive_normal);

    hCanvas->BeginDraw();
    hCanvas->Clear();
    //画 后五页 点燃

    lpBrushfl.SetColor(p_data.crbkgnormal);
    lpBrushbord.SetColor(p_data.crcromithover);
    if (p_data.lastfive_light)
        delete p_data.lastfive_light;
    hCanvas->FillRoundRect(&lpBrushfl, 1.f, 1.f, nSiez - 1.f, nSiez - 1.f, p_data.Round);
    nleft = (nSiez / 2) - (7.f / 2) + 1.f;
    ntop = (nSiez / 2) - (10.f / 2) + 4.f;
    hCanvas->DrawLine(&lpBrushbord, nleft, ntop - 3, nleft + 3, ntop + 1, 1.0f);
    hCanvas->DrawLine(&lpBrushbord, nleft, ntop + 6, nleft + 3, ntop + 2, 1.0f);
    hCanvas->DrawLine(&lpBrushbord, nleft + 4, ntop - 3, nleft + 7, ntop + 1, 1.0f);
    hCanvas->DrawLine(&lpBrushbord, nleft + 4, ntop + 6, nleft + 7, ntop + 2, 1.0f);
    hCanvas->EndDraw();
    hCanvas->ToImage(&p_data.lastfive_light);

    delete hCanvas;
}

