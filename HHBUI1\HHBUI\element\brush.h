﻿#pragma once
namespace HHBUI
{
	/// 画刷扩展模式 指定颜色超出绘制范围时如何扩展
	EXENUM(BrushModeEx)
	{
		None = 0,								///< 画刷扩展模式：不扩展
		Tile = 1,								///< 画刷扩展模式：平铺
		Mirror = 2,								///< 画刷扩展模式：镜像平铺

		Default = BrushModeEx::None,	     	///< 画刷扩展模式：默认
	};
	class TOAPI UIBrush
	{
	public:
		UIBrush();
		//画刷创建自颜色
		UIBrush(UIColor argb);
		//画刷置颜色
		void SetColor(UIColor argb);
		//画刷取颜色
		void GetColor(D2D1_COLOR_F& lpColor);
		void GetColor(UIColor &lpColor);

		UIBrush(ID2D1Bitmap* pBitmapSource, BOOL tr, INT mode = D2D1_INTERPOLATION_MODE_LINEAR, BrushModeEx extendmode = BrushModeEx::Default, DWORD alpha = 255);
		//画刷创建自画布
		UIBrush(UICanvas* canvas_src, BrushModeEx extendmode = BrushModeEx::Default, DWORD alpha = 255);
		//画刷创建自图片句柄
		UIBrush(UIImage* hImage, INT mode = D2D1_INTERPOLATION_MODE_LINEAR, BrushModeEx extendmode = BrushModeEx::Default, DWORD alpha = 255);
		void SetImgTransformToRect(float Width, float Height, float left, float top, float right, float bottom);

		//画刷创建自线性渐变
		UIBrush(FLOAT xStart, FLOAT yStart, FLOAT xEnd, FLOAT yEnd, UIColor crBegin, UIColor crEnd, BrushModeEx extendmode = BrushModeEx::Default, BOOL isGamma1_0 = false);
		UIBrush(FLOAT xStart, FLOAT yStart, FLOAT xEnd, FLOAT yEnd, const UIColor* arrStopPts, const FLOAT* arrINT, INT cStopPts, BrushModeEx extendmode = BrushModeEx::Default, BOOL isGamma1_0 = false);
		void SetLinearBeginPoint(float x, float y);
		void SetLinearEndPoint(float x, float y);
		void SetLinearPoints(const ExPointF* begin_point, const ExPointF* end_point);

		/*
		* @brief 画刷创建自径向渐变
		* @param  isGamma1_0 如果为TRUE：内插在线性伽玛颜色空间中执行 否则内插在标准 RGB (sRGB) gamma 中执行
		* @param  x,y 坐标位置
		* @param  radiusX,radiusY 大小
		* @param  crBegin 开始颜色
		* @param  crEnd 结束颜色
		*/
		UIBrush(BOOL isGamma1_0, FLOAT x, FLOAT y, FLOAT radiusX, FLOAT radiusY, UIColor crBegin, UIColor crEnd, BrushModeEx extendmode = BrushModeEx::Default);
		/*
		* @brief 画刷创建自径向渐变数组
		* @param  x,y 坐标位置
		* @param  radiusX,radiusY 大小
		* @param  arrStopPts 开始到结束颜色的数组
		* @param  arrINT 颜色渐变位置数组 这个值得范围必须是[0.0, 1.0]
		* @param  gradientOriginOffset 起始点偏移，一般设置为(0,0)
		*/
		UIBrush(FLOAT x, FLOAT y, FLOAT radiusX, FLOAT radiusY, const UIColor* arrStopPts, const FLOAT* arrINT, INT cStopPts, POINT gradientOriginOffset = {}, BrushModeEx extendmode = BrushModeEx::Default, BOOL isGamma1_0 = false);
		void SetRadialBoundsRect(float left, float top, float right, float bottom);
		//指定画笔坐标空间中渐变椭圆的中心
		void SetRadialCenter(FLOAT offsetX, FLOAT offsetY);
		//指定渐变原点相对于渐变椭圆中心的偏移量。
		void SetRadialCenterOffset(float horz, float vert);

		/*设置画刷颜色不透明度(介于0和1之间的值，指示画刷的不透明度。该值是一个常数乘数，线性缩放画刷填充的所有像素的alpha值。不透明度值在相乘之前被限制在0–1的范围内)
		该操作会全局生效 就算设置新颜色值也会保持现有的透明度*/
		void SetOpacity(FLOAT alpha);
		//置图像画刷矩阵
		void SetTransForm(ExMatrix matrix);
		//取图像画刷矩阵
		void GetTransForm(ExMatrix* matrix);
		LPVOID GetContext();
		~UIBrush();
	protected:
		void br_CreateBrush(UIColor argb);
		void br_CreatGradientBrush(FLOAT xStart, FLOAT yStart, FLOAT xEnd, FLOAT yEnd, const UIColor* arrStopPts, const FLOAT* arrINT, INT cStopPts, BrushModeEx extendmode, BOOL isGamma1_0);
		void br_CreatRadialBrush(FLOAT x, FLOAT y, FLOAT radiusX, FLOAT radiusY, const UIColor* arrStopPts, const FLOAT* arrINT, INT cStopPts, POINT gradientOriginOffset, BrushModeEx extendmode, BOOL isGamma1_0);
		LPVOID m_brush = nullptr;
	};
}

