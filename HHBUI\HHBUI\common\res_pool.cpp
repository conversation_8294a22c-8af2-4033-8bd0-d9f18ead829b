﻿/**
 * @file res_pool.cpp
 * @brief 资源池对象实现文件
 */
#include "pch.h"
#include "res_pool.h"
#include "object_impl.hpp"
#include "mem_pool.hpp"
#include "lock.hpp"

namespace HHBUI
{
	class ExResPool : public ExObjectImpl<IExResPool>
	{
	public:
		EX_BEGIN_INTERFACE_MAP();
		EX_INTERFACE_ENTRY(IExResPool);
		EX_INTERFACE_ENTRY(IUnknown);
		EX_END_INTERFACE_MAP();

	public:
		ExResPool(size_t item_size, ExResPoolInitItemProc init_item_proc, ExResPoolFreeItemProc free_item_proc)
			: m_pool(sizeof(ExResPoolItemHeader) + item_size),
			m_init_item_proc(init_item_proc), m_free_item_proc(free_item_proc),
			m_creation_time(std::chrono::steady_clock::now())
		{
		}

		virtual ~ExResPool()
		{
			if (m_free_item_proc) {
				for (auto& item : m_res) {
					m_free_item_proc(this, item.first, item.second->flags, item.second + 1);
				}
			}
			m_res.clear();
			m_pool.Clear();
		}
		EXMETHOD uint32_t GetItemCount() const override
		{
			_Locked(m_lock);
			return (uint32_t)m_res.size();
		}

		EXMETHOD bool HasItem(EXATOM key) const override
		{
			_Locked(m_lock);
			return m_res.find(key) != m_res.end();
		}

		EXMETHOD HRESULT FindKeyByPtr(void* res, EXATOM* r_key) const override
		{
			CHECK_PARAM(res);
			CHECK_PARAM(r_key);

			//获得头部
			ExResPoolItemHeader* header = (ExResPoolItemHeader*)res;
			header--;

			//如果是可读区域,则尝试通过key找下
			if (!::IsBadReadPtr(header, sizeof(ExResPoolItemHeader))) {
				auto it = m_res.find(header->key);

				//找到了
				if (it != m_res.end()) {
					*r_key = it->first;
					return S_OK;
				}
			}

			//其他情况就只能遍历表了
			for (auto it = m_res.begin(); it != m_res.end(); ++it)
			{
				if (it->second == header) {
					*r_key = it->first;
					return S_OK;
				}
			}

			handle_ex(EE_NOEXISTS, L"该项目不存在");
		}

		EXMETHOD HRESULT UseOrCreateItem(EXATOM key, const void* data,
			WPARAM wparam, LPARAM lparam, DWORD flags, void** r_res) noexcept override
		{
			_Locked(m_lock);

			//先找找看
			auto it = m_res.find(key);

			//如果找到了就直接使用
			if (it != m_res.end()) {
				auto header = it->second;
				header->ref_count++;
				if (r_res) { *r_res = header + 1; }
				return S_OK;
			}

			ExResPoolItemHeader* header = nullptr;
			try
			{
				//如果没找到，就创建一个
				header = m_pool.Alloc<ExResPoolItemHeader>();

				//设置参数
				header->flags = flags;
				header->ref_count = 1;
				header->key = key;

				// 如果有初始化回调，则调用
				if (m_init_item_proc) {
					throw_if_notok(
						m_init_item_proc(this, key, data, wparam, lparam, flags, header + 1),
						L"初始化项目失败"
					);
				}

				//加入资源表
				m_res[key] = header;

				//返回
				if (r_res) { *r_res = header + 1; }
				return S_OK;
			}
			//如果创建失败，就释放内存
			catch_ignore({ if (header) { m_pool.Free(header); } return e.handle(true); });
		}

		EXMETHOD HRESULT UseItem(EXATOM key, void** r_res) override
		{
			_Locked(m_lock);

			auto it = m_res.find(key);
			//handle_if_false(it != m_res.end(), EE_NOEXISTS, L"该项目不存在");
			if (it == m_res.end())
				return S_FALSE;
			
			auto header = it->second;
			header->ref_count++;
			if (r_res) { *r_res = header + 1; }

			return S_OK;
		}

		EXMETHOD HRESULT UnUseItem(EXATOM key) override
		{
			_Locked(m_lock);

			auto it = m_res.find(key);
			//handle_if_false(it != m_res.end(), EE_NOEXISTS, L"该项目不存在");
			if (it == m_res.end())
				return S_FALSE;
			return _UnUseItemByIter(it);
		}

		EXMETHOD HRESULT UnUseItemByPtr(void* res) override
		{
			CHECK_PARAM(res);

			//获得头部
			ExResPoolItemHeader* header = (ExResPoolItemHeader*)res;
			header--;

			//如果是可读区域,则尝试通过key找下
			if (!::IsBadReadPtr(header, sizeof(ExResPoolItemHeader))) {
				auto it = m_res.find(header->key);

				//找到了
				if (it != m_res.end()) {
					if (SUCCEEDED(_UnUseItemByIter(it))) { return S_OK; }
				}
			}

			//其他情况就只能遍历表了
			for (auto it = m_res.begin(); it != m_res.end(); ++it)
			{
				if (it->second == header) { return _UnUseItemByIter(it); }
			}

			handle_ex(EE_NOEXISTS, L"该项目不存在");
		}

		EXMETHOD HRESULT EnumItems(ExResPoolEnumItemProc enum_proc, LPARAM lparam) override
		{
			CHECK_PARAM(enum_proc);
			_Locked(m_lock);

			for (auto& item : m_res) {
				if (!enum_proc(this, item.first, item.second->flags, item.second + 1, lparam)) {
					return S_FALSE;
				}
			}

			return S_OK;
		}

		// IObject接口实现
		EXMETHOD ObjectType GetObjectType() const override { return ObjectType::RESOURCE_OBJECT; }
		EXMETHOD ObjectState GetObjectState() const override { return ObjectState::INITIALIZED; }
		EXMETHOD std::wstring_view GetObjectName() const override { return L"ExResPool"; }
		EXMETHOD HRESULT SetObjectName(std::wstring_view name) override { return S_OK; }
		EXMETHOD std::wstring_view GetObjectDescription() const override { return L"Resource Pool Implementation"; }
		EXMETHOD HRESULT SetObjectDescription(std::wstring_view description) override { return S_OK; }
		EXMETHOD std::chrono::steady_clock::time_point GetCreationTime() const override { return m_creation_time; }
		EXMETHOD std::chrono::milliseconds GetLifetime() const override {
			return std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - m_creation_time);
		}
		EXMETHOD uint64_t GetMemoryUsage() const override { return m_pool.GetBlockSize() * GetItemCount(); }
		EXMETHOD bool IsValid() const override { return true; }
		EXMETHOD HRESULT Initialize(const ObjectCreateInfo* create_info = nullptr) override { return S_OK; }
		EXMETHOD HRESULT Finalize() override { return S_OK; }
		EXMETHOD HRESULT Clone(IObject** cloned_object) const override { return E_NOTIMPL; }
		EXMETHOD int32_t Compare(IObject* other) const override { return 0; }
		EXMETHOD HRESULT Serialize(void** data, uint32_t* size) const override { return E_NOTIMPL; }
		EXMETHOD HRESULT Deserialize(const void* data, uint32_t size) override { return E_NOTIMPL; }


	private:

		struct ExResPoolItemHeader;

		HRESULT _UnUseItemByIter(std::unordered_map<EXATOM, ExResPoolItemHeader*>::iterator& it)
		{
			auto header = it->second;
			try
			{
				// 减少引用计数,如果引用计数为0
				if (--header->ref_count == 0) {

					// 如果不是永久项目，则释放
					if ((header->flags & ExResPoolItemFlags::Eternal) == 0) {

						// 如果有释放回调，则调用
						if (m_free_item_proc) {
							throw_if_failed(
								m_free_item_proc(this, header->key, header->flags, header + 1),
								L"释放项目失败"
							);
						}

						// 从资源表和内存池中移除
						m_res.erase(it);
						m_pool.Free(header);
					}
				}

				return S_OK;
			}
			//如果发生异常，就恢复引用计数
			catch_default({ header->ref_count++; });
		}

		struct ExResPoolItemHeader
		{
			DWORD flags;
			int32_t ref_count;
			EXATOM key;
		};

		ExDynamicMemPool m_pool;
		ExLock m_lock;
		ExResPoolInitItemProc m_init_item_proc;
		ExResPoolFreeItemProc m_free_item_proc;
		std::unordered_map<EXATOM, ExResPoolItemHeader*> m_res;
		std::chrono::steady_clock::time_point m_creation_time;
	};

	////////////////////////


	HRESULT ExResPoolCreate(size_t item_size, ExResPoolInitItemProc init_item_proc,
		ExResPoolFreeItemProc free_item_proc, IExResPool** r_pool)
	{
		CHECK_PARAM(item_size > 0);
		CHECK_PARAM(r_pool);

		try
		{
			ExResPool *pool = new ExResPool(item_size, init_item_proc, free_item_proc);
			return pool->QueryInterface(__uuidof(IExResPool), (void**)r_pool);
		}
		catch_default({});
	}

}
