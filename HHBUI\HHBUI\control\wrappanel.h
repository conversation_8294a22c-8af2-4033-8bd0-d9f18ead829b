﻿#pragma once
namespace HHBUI
{
	class TOAPI UIWrapPanel : public UIControl
	{
	public:
		UIWrapPanel(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0);
	
		//设置布局类型,全局只可设置一次
		BOOL SetLayoutType(INT nType);
		//添加控件
		BOOL AddChild(UIControl* parent);
		//设置内间距 不需要设置的参数用-1
		void SetPadding(INT left, INT top = -1, INT right = -1, INT bottom = -1);
		//设置外间距 不需要设置的参数用-1
		void SetMargin(INT left, INT top = -1, INT right = -1, INT bottom = -1);
		//设置默认控件尺寸
		void SetSize(INT width, INT height);

		/*=================线性布局=============*/
		//设置排布方向 默认：水平
		void Setdirection(INT fVertical = elp_direction_h);
		//设置尺寸 [-1或未填写为组件当前尺寸]
		void SetSize(INT nSize);
		//设置另一个方向对齐
		void SetAlign(INT nFill = elcp_linear_algin_fill);
		//布局方向对齐方式
		void SetDalign(INT nDAlign = elp_linear_dalign_left_top);


	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		INT GetAllHeight(INT cWidth);
		struct wrappanel_s
		{
			UIStatic* BindObj = nullptr;
			BOOL uInit = false;
			INT nType = elt_null, fVertical = -1, nSize = -1, nFill = -1, nDAlign = -1;
			ExRect Padding = {};
			ExRect Margin = {};
			ExPoint AllSize = {};
		}p_data;

	};
}
