﻿#include "hhbui.h"
using namespace HHBUI;
LRESULT CALLBACK OnSliderMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARA<PERSON> wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UISlider*)UIView;
	if (uMsg == TBM_GETPOS)
	{
		output(obj->GetValue());
	}

	return S_OK;
}
void testslider(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 870, 550, L"hello Slider", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);
	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto slider0 = new UISlider(window, 30, 60, 300, 20);
	slider0->SetValue(60);
	slider0->SetBarSize(15);
	slider0->IsShwoText(FALSE);
	slider0->SetMsgProc(OnSliderMsgProc);

	auto slider1 = new UISlider(window, 360, 60, 300, 20);
	slider1->SetType(UISlider::slider_type::stroke);//无滑块类型
	slider1->SetSliderColor({}, UIColor(202, 81, 0, 255));
	slider1->SetValue(60);
	slider1->SetBarSize(15);
	slider1->IsShwoText(FALSE);

	auto slider2 = new UISlider(window, 30, 110, 300, 20);
	slider2->SetSliderColor({}, UIColor(245, 108, 108, 255));//自定义颜色
	slider2->SetValue(57);

	auto slider3 = new UISlider(window, 360, 110, 300, 20);
	slider3->SetPoint(UISlider::slider_point::right);
	slider3->SetValue(60);
	slider3->SetBarSize(15);
	slider3->IsShwoText(FALSE);

	auto slider4 = new UISlider(window, 30, 200, 20, 300);
	slider4->SetPoint(UISlider::slider_point::top);
	slider4->SetSliderColor({}, UIColor(230, 162, 60, 255));
	slider4->SetValue(35);
	
	auto slider5 = new UISlider(window, 60, 200, 20, 300);
	slider5->SetType(UISlider::slider_type::stroke);
	slider5->SetPoint(UISlider::slider_point::bottom);
	slider5->SetBarSize(15);
	slider5->SetValue(70);
	slider5->IsShwoText(FALSE);
	slider5->SetSliderColor({}, UIColor(103, 194, 58, 255));

	auto slider6 = new UISlider(window, 100, 200, 300, 20, L"{{value}}");
	slider6->SetType(UISlider::slider_type::stops);//步骤类型
	slider6->SetRange(0, 10);
	slider6->SetValue(5);

	/*
	auto slider7 = new UISlider(window, 100, 260, 300, 20);
	slider7->SetType(UISlider::slider_type::range);//范围类型
	slider7->SetRange(0, 100);
	slider7->SetValue(30, 70);
	slider7->IsShwoText(FALSE);
	*/
	window->Show();
}