﻿#pragma once

namespace HHBUI
{
	class TOAPI UISlider : public UIControl
	{
    public:
		enum slider_type {
			linear,		//常规（滑槽+滑块）
			stroke,    //填充（仅滑槽）
			range,     //范围（滑槽+双滑块）
			stops,     //分段（分段滑槽+滑块，分段数跟随最大值）
			slidBar    //滑块 （仅滑块）
		};
		enum slider_point {
			left,		//进度向左，横向（默认）
			top,		//进度向上，竖向
			right,		//进度向右，横向
			bottom		//进度向下，竖向
		};

		//title可自定义内容，{{value}}将被替换为进度值（中间不能有空格）
		UISlider(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCTSTR title = L"{{value}}%", INT nID = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT textFormat = DT_CENTER | DT_VCENTER | DT_SINGLELINE);
		//置滑块圆大小 最小5
		void SetBarSize(INT Size);
		//置滑块圆角
		void SetBarRadius(BOOL IsRadius);
		//置滑块条类型
		void SetType(slider_type type);
		//置滑块方向
		void SetPoint(slider_point point);
		//置滑块条颜色
		void SetSliderColor(UIColor nor, UIColor fill = {});

		//置进度位置
		void SetValue(float lVal);
		//取进度位置
		float GetValue();
		//置范围
		void SetRange(float lpMin, float lpMax = 100.f);
		//取最小范围
		float GetMin();
        //取最大范围
		float GetMax();

		//是否显示进度文字
		void IsShwoText(BOOL isshow);

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		void OnMouseMove(HWND hWnd, LPARAM lParam);
		void OnLButtonDown(HWND hWnd, LPARAM lParam);
		void OnLButtonUp();
		void OnGetRect(ExRectF fChannel, ExRectF& rc, float tw);
		void HitTest(LPARAM lParam);

		struct switch_s
		{
			BOOL bshowText = TRUE, bLDown = FALSE, bRad = TRUE;
			float min = 0.f, max = 100.f, lVal = 0.f, tw = 0.f;
			INT BarSize = 10;
			UIBrush* brush = nullptr;
			slider_type type = slider_type::linear;
			slider_point point = slider_point::left;
			UIColor clr[2] = { UIColor(208,211,217,150),UIColor(20,126,255,255)};
		}p_data;
	};
}
