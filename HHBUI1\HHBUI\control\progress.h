﻿#pragma once

namespace HHBUI
{
	class TOAPI UIProgress : public UIControl
	{
	public:
		enum progress_type {
			stroke,		//常规型，文字在进度条内，宽度跟随控件大小（默认）
			line,		//线型，文字在进度条外，宽度自定义
			ring,		//环型，文字在环中间，大小跟随控件大小
			ring_round,	//环型圆角
			dashboard,	//仪表盘型，文字在中间，大小跟随控件大小(!!未完成)
			wave        //水波
		};
		enum progress_point {
			left,		//进度向左，横向
			top,		//进度向上，竖向
			right,		//进度向右，横向（默认）
			bottom		//进度向下，竖向
		};

		//title可自定义内容，{{value}}将被替换为进度值（中间不能有空格）
		UIProgress(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCTSTR title = L"{{value}}%", INT nID = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT textFormat = DT_LEFT | DT_VCENTER | DT_SINGLELINE);

		//置圆角度（仅type=normal和line生效）
		void SetBarRadius(UINT radius);
		//置进度条类型（设置type=line时请同步修改圆角度）
		void SetType(progress_type type);
		//置进度方向（仅type=noemal和line生效）
		void SetPoint(progress_point point);
		//置进度条颜色 (建议fill的A通道不要＞200)
		void SetBarColor(UIColor nor = {}, UIColor fill = {});
		//置进度
		void SetValue(INT value);
		//置范围
		void SetRange(INT range);
		//取进度
		INT GetValue();
		//取范围
		INT GetRange();

		//置线宽（type=stroke不生效，type=ring和dashboard时建议为双数）
		void SetLineWidth(UINT width);
		//是否显示进度文字
		void IsShwoText(BOOL isshow);

		/*水波相关*/
		//置水波宽度
		void SetWaveWidth(INT fWidth);
		//置水波高度
		void SetWaveHeight(INT fHeight);
		//置水波气泡速度
		void SetWaveBubbleSpeed(INT fSpeed);

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		struct Bubble_s {
			std::string Key;
			ExPoint BubblePoint;
			int BubbleWidth = 0;
		};
		struct switch_s
		{
			BOOL bshowText = TRUE;
			UINT rad = 0, wid = 4;
			INT range = 100, value = 0;
			progress_type type = progress_type::stroke;
			progress_point point = progress_point::right;
			UIColor clr[2] = { UIColor(208,211,217,150),UIColor(20,126,255,255) };
			int waveWidth = 0;//波纹宽度
			int waveHeight = 0;//波纹高度
			int intLeftX = 0;
			int bubbleSpeed = 0;//气泡速度（0表示不启用）
			int lastBubbleTime = 0;//上次生成气泡的时间
			int bubbleIntervalTime = 0;//气泡生成间隔时间(根据波运行速度判断)
			std::vector<Bubble_s> lstBubble{ 0 };
		}p_data;
	};
}
