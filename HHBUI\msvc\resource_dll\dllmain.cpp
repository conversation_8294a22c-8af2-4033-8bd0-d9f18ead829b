﻿// dllmain.cpp : 定义 DLL 应用程序的入口点及资源访问功能。
#include "pch.h"
#include "resource.h"

// 模块句柄
HMODULE g_hModule = NULL;

// 导出获取资源的函数
extern "C" __declspec(dllexport) HRSRC GetResourceHandle(UINT resourceID)
{
    return FindResource(g_hModule, MAKEINTRESOURCE(resourceID), RT_RCDATA);
}

extern "C" __declspec(dllexport) HRSRC GetPngResourceHandle(UINT resourceID)
{
    return FindResource(g_hModule, MAKEINTRESOURCE(resourceID), L"PNG");
}

extern "C" __declspec(dllexport) HRSRC GetGifResourceHandle(UINT resourceID)
{
    return FindResource(g_hModule, MAKEINTRESOURCE(resourceID), L"GIF");
}

extern "C" __declspec(dllexport) HGLOBAL LoadResourceData(HRSRC hResInfo)
{
    if (hResInfo)
        return LoadResource(g_hModule, hResInfo);
    return NULL;
}

extern "C" __declspec(dllexport) LPVOID LockResourceData(HGLOBAL hResData)
{
    if (hResData)
        return LockResource(hResData);
    return NULL;
}

extern "C" __declspec(dllexport) DWORD GetResourceSize(HRSRC hResInfo)
{
    if (hResInfo)
        return SizeofResource(g_hModule, hResInfo);
    return 0;
}

BOOL APIENTRY DllMain(HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        g_hModule = hModule;
        DisableThreadLibraryCalls(hModule);
        break;
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}

