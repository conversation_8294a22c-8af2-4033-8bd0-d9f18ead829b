﻿#pragma once
namespace HHBUI
{
	enum TreeViewStyle
	{
		eos_tree_showaddandsub = 4096,             //显示加减号
		eos_tree_showcable = 8192,                 //显示连接线
		//事件
		WMM_TVN_DELETEITEMT = 391,                  //删除节点 wParam项目ID lParam 删除类型：0所有、1单删除时
		WMM_TVN_ITEMEXPANDED = 394,                  //节点展开 wParam状态 lParam项目ID
		WMM_TVN_ITEMEXPANDING = 395,                  //节点展开中 wParam状态 lParam项目ID
		WMM_TVN_DRAWITEM = 3099,                 //绘制节点 wParam绘制索引 lParam为ps_customdraw
		WMM_TVN_SELECTALL = 1011,                 //多选被选中 wParam状态 lParam项目ID
		WMM_TVN_SELECT = 1012,                 //被选中 lParam项目ID
	};
	struct info_tree_nodeitem
	{
		INT nID;
		LPCWSTR pwzText;
		LPARAM lParam;
		HHBUI::UIImage* nImageIndex;
		BOOL fExpand;
		INT nDepth;
		HHBUI::UIColor color;
		INT select;                      //选中 -1保持默认 0取消选中 1选中
		info_tree_nodeitem* pParent;
		info_tree_nodeitem* pPrev;
		info_tree_nodeitem* pNext;
		info_tree_nodeitem* pChildFirst;
		INT nCountChild;
		UINT imgWidth; //不需要指定
		UINT imgHeight;//不需要指定
	};
	class TOAPI UITreeView : public UIListView
	{
	public:
		UITreeView() = default;
		UITreeView(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1)
			: UIListView(hParent, x, y, width, height, L"form-treeview", dwStyle, dwStyleEx, nID, dwTextFormat) {
			OnCreate();
		}
		//插入节点
		LPVOID InsertItem(LPVOID itemParent, LPVOID itemInsertAfter, INT nID, LPCWSTR pwzText, LPARAM lParam = 0,
			UIImage* nImageIndex = 0, BOOL fExpand = FALSE, BOOL fUpdate = FALSE, UIColor color = {}, INT select = 0);
		void InsertItem(LPVOID itemParent, LPVOID itemInsertAfter, info_tree_nodeitem* item);
		//置节点
		void SetItem(INT Index, LPCWSTR pwzText, LPARAM lParam = 0, INT nID = 0, UIImage* nImageIndex = 0, UIColor color = {}, INT select = -1, BOOL fUpdate = FALSE);
		//更新
		void Update();
		//展开
		void Expand(INT Index, BOOL fExpand);
		//是否展开
		BOOL IsExpand(INT Index);
		//从ID获取节点
		void GetIdItem(INT nID, info_tree_nodeitem*& item);
		//从索引获取节点
		void GetIndexItem(INT Index, info_tree_nodeitem*& item);
		/*删除一个节点
		* @param Index - 索引
		* @param draw - 是否立即绘制
		*
		* @return 当前列表项目总数
		*/
		INT DeleteItem(INT Index, bool draw = true);

		/*删除所有节点
		* @param Index - 父索引 为0所有
		* @param draw - 是否立即绘制
		*/
		void DeleteAllItem(INT Index, bool draw = true);
		//获取节点总数 Index=0所有
		INT GetItemCount(INT Index = 0);
		//从索引取ID
		INT GetItemId(INT Index);
		//设置选择框颜色
		void SetCrSelect(UIColor cr);
		//设置加减号颜色
		void SetCrWaddAndsub(UIColor cr);
		//设置连线颜色
		void SetCrCable(UIColor cr);


	protected:
		EXMETHOD LRESULT OnPsProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD BOOL OnPsCustomDraw(INT iItem, ps_customdraw ps) override;
		BOOL inititem(info_tree_nodeitem* item, info_tree_nodeitem* parent, info_tree_nodeitem* insertAfter);
		BOOL generatelist(BOOL bForce);
		void updateitem();
		void calcitemmaxwidth(info_tree_nodeitem* item, INT& pWidth);
		info_tree_nodeitem* hittest(POINT pt, INT& pType);
		void OnCreate();
		bool IndexCheck(INT index);
		void fiExpand(info_tree_nodeitem* item, BOOL fExpand);

		struct tree_s
		{
			std::vector<info_tree_nodeitem*>* itemList = nullptr;
			UIBrush* hBrush = nullptr;
			bool isData = false;
			info_tree_nodeitem* nodeitem = nullptr;
			INT indent = 0;//留白宽度
			INT indexstart = 0;//可视项目起始索引
			INT indexend = 0;//可视项目结束索引
			BOOL expand = 0;//是否点击收缩
			INT setselect = 0;//现行选中ID
			UIColor select = { 0,108,190,255 };
			UIColor color_waddandsub = UIColor(L"#e6e7e8");//加减号颜色
			UIColor color_cable = UIColor(L"#c2c3c9");//连线颜色
			UIColor Color[3] = { UIColor(L"#e6e7e8"),UIColor(L"#c2c3c9"),UIColor(L"#cccedb") };
		}s_data;
	};
}
