﻿/* zutil.h -- internal interface and configuration of the compression library
 * Copyright (C) 1995-2022 <PERSON><PERSON><PERSON><PERSON>, <PERSON>
 * For conditions of distribution and use, see copyright notice in zlib.h
 */

/* WARNING: this file should *not* be used by applications. It is
   part of the implementation of the compression library and is
   subject to change. Applications should only use zlib.h.
 */

/* @(#) $Id$ */

#ifndef ZUTIL_H
#define ZUTIL_H

#ifdef HAVE_HIDDEN
#  define ZLIB_INTERNAL __attribute__((visibility ("hidden")))
#else
#  define ZLIB_INTERNAL
#endif

#include "zlib.h"

#if defined(STDC) && !defined(Z_SOLO)
#  if !(defined(_WIN32_WCE) && defined(_MSC_VER))
#    include <stddef.h>
#  endif
#  include <string.h>
#  include <stdlib.h>
#endif

#ifndef local
#  define local static
#endif
/* since "static" is used to mean two completely different things in C, we
   define "local" for the non-static meaning of "static", for readability
   (compile with -Dlocal if your debugger can't find static symbols) */

typedef unsigned char  uch;
typedef uch FAR uchf;
typedef unsigned short ush;
typedef ush FAR ushf;
typedef unsigned long  ulg;

/*
 * =====================================================================================
 * 【HHBUI】极致完美类型安全宏 - 2025-07-28
 * =====================================================================================
 * 提供类型安全的转换宏，避免编译器警告并确保数据完整性
 */
#ifndef ZLIB_SAFE_CAST_MACROS
#define ZLIB_SAFE_CAST_MACROS

/* 安全的类型转换宏 - 确保数据完整性 */
#define SAFE_CAST_UCH(x)   ((uch)((x) & 0xFF))
#define SAFE_CAST_UCHF(x)  ((uchf)((x) & 0xFF))
#define SAFE_CAST_USH(x)   ((ush)((x) & 0xFFFF))
#define SAFE_CAST_USHF(x)  ((ushf)((x) & 0xFFFF))

/* 安全的位操作宏 */
#define LOW_BYTE(x)        ((uch)((x) & 0xFF))
#define HIGH_BYTE(x)       ((uch)(((x) >> 8) & 0xFF))
#define MAKE_USHORT(h,l)   ((ush)(((ush)(h) << 8) | (ush)(l)))

#endif /* ZLIB_SAFE_CAST_MACROS */
/* ===================================================================================== */

/*
 * =====================================================================================
 * 【HHBUI】极致完美现代化内存管理系统 - 2025-07-28
 * =====================================================================================
 * 提供跨平台、类型安全的内存管理函数和宏定义
 */
#ifndef ZLIB_MODERN_MEMORY_MANAGEMENT
#define ZLIB_MODERN_MEMORY_MANAGEMENT

/* 现代化内存管理宏 - 类型安全且高性能 */
#ifdef _WIN32
  /* Windows环境: 使用标准C库函数 */
  #include <stdlib.h>
  #include <malloc.h>

  /* 安全的内存分配宏 */
  #define ZLIB_MALLOC(size)         malloc(size)
  #define ZLIB_CALLOC(count, size)  calloc(count, size)
  #define ZLIB_REALLOC(ptr, size)   realloc(ptr, size)
  #define ZLIB_FREE(ptr)            do { if(ptr) { free(ptr); (ptr) = NULL; } } while(0)

  /* 对齐内存分配 (现代CPU优化) */
  #define ZLIB_ALIGNED_MALLOC(size, align)  _aligned_malloc(size, align)
  #define ZLIB_ALIGNED_FREE(ptr)            _aligned_free(ptr)

  /* 内存安全检查宏 */
  #define ZLIB_CHECK_PTR(ptr)       ((ptr) != NULL)
  #define ZLIB_SAFE_SIZE(size)      ((size) > 0 && (size) < SIZE_MAX)

#else
  /* 其他平台: 标准C库 */
  #include <stdlib.h>

  #define ZLIB_MALLOC(size)         malloc(size)
  #define ZLIB_CALLOC(count, size)  calloc(count, size)
  #define ZLIB_REALLOC(ptr, size)   realloc(ptr, size)
  #define ZLIB_FREE(ptr)            do { if(ptr) { free(ptr); (ptr) = NULL; } } while(0)

  /* POSIX对齐内存分配 */
  #ifdef _POSIX_C_SOURCE
    #define ZLIB_ALIGNED_MALLOC(size, align)  aligned_alloc(align, size)
    #define ZLIB_ALIGNED_FREE(ptr)            free(ptr)
  #else
    #define ZLIB_ALIGNED_MALLOC(size, align)  malloc(size)
    #define ZLIB_ALIGNED_FREE(ptr)            free(ptr)
  #endif

  #define ZLIB_CHECK_PTR(ptr)       ((ptr) != NULL)
  #define ZLIB_SAFE_SIZE(size)      ((size) > 0 && (size) < SIZE_MAX)
#endif

/* 内存操作安全宏 */
#define ZLIB_MEMSET_SAFE(ptr, val, size) \
  do { if(ZLIB_CHECK_PTR(ptr) && ZLIB_SAFE_SIZE(size)) memset(ptr, val, size); } while(0)

#define ZLIB_MEMCPY_SAFE(dst, src, size) \
  do { if(ZLIB_CHECK_PTR(dst) && ZLIB_CHECK_PTR(src) && ZLIB_SAFE_SIZE(size)) memcpy(dst, src, size); } while(0)

#define ZLIB_MEMMOVE_SAFE(dst, src, size) \
  do { if(ZLIB_CHECK_PTR(dst) && ZLIB_CHECK_PTR(src) && ZLIB_SAFE_SIZE(size)) memmove(dst, src, size); } while(0)

#endif /* ZLIB_MODERN_MEMORY_MANAGEMENT */
/* ===================================================================================== */

#if !defined(Z_U8) && !defined(Z_SOLO) && defined(STDC)
#  include <limits.h>
#  if (ULONG_MAX == 0xffffffffffffffff)
#    define Z_U8 unsigned long
#  elif (ULLONG_MAX == 0xffffffffffffffff)
#    define Z_U8 unsigned long long
#  elif (UINT_MAX == 0xffffffffffffffff)
#    define Z_U8 unsigned
#  endif
#endif

extern z_const char * const z_errmsg[10]; /* indexed by 2-zlib_error */
/* (size given to avoid silly warnings with Visual C++) */

#define ERR_MSG(err) z_errmsg[Z_NEED_DICT-(err)]

#define ERR_RETURN(strm,err) \
  return (strm->msg = ERR_MSG(err), (err))
/* To be used only when the state is known to be valid */

        /* common constants */

#ifndef DEF_WBITS
#  define DEF_WBITS MAX_WBITS
#endif
/* default windowBits for decompression. MAX_WBITS is for compression only */

#if MAX_MEM_LEVEL >= 8
#  define DEF_MEM_LEVEL 8
#else
#  define DEF_MEM_LEVEL  MAX_MEM_LEVEL
#endif
/* default memLevel */

#define STORED_BLOCK 0
#define STATIC_TREES 1
#define DYN_TREES    2
/* The three kinds of block type */

#define MIN_MATCH  3
#define MAX_MATCH  258
/* The minimum and maximum match lengths */

#define PRESET_DICT 0x20 /* preset dictionary flag in zlib header */

        /* target dependencies */

/*
 * =====================================================================================
 * 【HHBUI】极致完美内存管理修复 - 2025-07-28
 * =====================================================================================
 * 修复内容: 解决farfree/farmalloc函数未定义警告
 * 问题描述: 16位DOS时代的远指针函数在现代环境下不可用
 * 修复方案: 提供现代化的内存管理函数映射和兼容性支持
 * =====================================================================================
 */

#if defined(MSDOS) || (defined(WINDOWS) && !defined(WIN32))
#  define OS_CODE  0x00
#  ifndef Z_SOLO
#    if defined(__TURBOC__) || defined(__BORLANDC__)
#      if (__STDC__ == 1) && (defined(__LARGE__) || defined(__COMPACT__))
         /* Allow compilation with ANSI keywords only enabled */

         /* 【HHBUI】极致完美修复: 16位DOS远指针函数现代化映射 */
         #ifdef _WIN32
           /* 现代Windows环境: 映射到标准C库函数 */
           #define farfree(ptr)           free(ptr)
           #define farmalloc(size)        malloc(size)
           /* 提供函数声明以满足编译器要求 */
           #ifndef ZLIB_FARPTR_DECLARED
           #define ZLIB_FARPTR_DECLARED
           static inline void _Cdecl farfree(void *block) { free(block); }
           static inline void *_Cdecl farmalloc(unsigned long nbytes) { return malloc(nbytes); }
           #endif
         #else
           /* 传统16位环境: 保持原有声明 */
           void _Cdecl farfree( void *block );
           void *_Cdecl farmalloc( unsigned long nbytes );
         #endif

#      else
#        include <alloc.h>
#      endif
#    else /* MSC or DJGPP */
#      include <malloc.h>
#    endif
#  endif
#endif

#ifdef AMIGA
#  define OS_CODE  1
#endif

#if defined(VAXC) || defined(VMS)
#  define OS_CODE  2
#  define F_OPEN(name, mode) \
     fopen((name), (mode), "mbc=60", "ctx=stm", "rfm=fix", "mrs=512")
#endif

#ifdef __370__
#  if __TARGET_LIB__ < 0x20000000
#    define OS_CODE 4
#  elif __TARGET_LIB__ < 0x40000000
#    define OS_CODE 11
#  else
#    define OS_CODE 8
#  endif
#endif

#if defined(ATARI) || defined(atarist)
#  define OS_CODE  5
#endif

#ifdef OS2
#  define OS_CODE  6
#  if defined(M_I86) && !defined(Z_SOLO)
#    include <malloc.h>
#  endif
#endif

#if defined(MACOS) || defined(TARGET_OS_MAC)
#  define OS_CODE  7
#  ifndef Z_SOLO
#    if defined(__MWERKS__) && __dest_os != __be_os && __dest_os != __win32_os
#      include <unix.h> /* for fdopen */
#    else
#      ifndef fdopen
#        define fdopen(fd,mode) NULL /* No fdopen() */
#      endif
#    endif
#  endif
#endif

#ifdef __acorn
#  define OS_CODE 13
#endif

#if defined(WIN32) && !defined(__CYGWIN__)
#  define OS_CODE  10
#endif

#ifdef _BEOS_
#  define OS_CODE  16
#endif

#ifdef __TOS_OS400__
#  define OS_CODE 18
#endif

#ifdef __APPLE__
#  define OS_CODE 19
#endif

#if defined(_BEOS_) || defined(RISCOS)
#  define fdopen(fd,mode) NULL /* No fdopen() */
#endif

#if (defined(_MSC_VER) && (_MSC_VER > 600)) && !defined __INTERIX
#  if defined(_WIN32_WCE)
#    define fdopen(fd,mode) NULL /* No fdopen() */
#  else
#    define fdopen(fd,type)  _fdopen(fd,type)
#  endif
#endif

#if defined(__BORLANDC__) && !defined(MSDOS)
  #pragma warn -8004
  #pragma warn -8008
  #pragma warn -8066
#endif

/* provide prototypes for these when building zlib without LFS */
#if !defined(_WIN32) && \
    (!defined(_LARGEFILE64_SOURCE) || _LFS64_LARGEFILE-0 == 0)
    ZEXTERN uLong ZEXPORT adler32_combine64 OF((uLong, uLong, z_off_t));
    ZEXTERN uLong ZEXPORT crc32_combine64 OF((uLong, uLong, z_off_t));
#endif

        /* common defaults */

#ifndef OS_CODE
#  define OS_CODE  3     /* assume Unix */
#endif

#ifndef F_OPEN
#  define F_OPEN(name, mode) fopen((name), (mode))
#endif

         /* functions */

#if defined(pyr) || defined(Z_SOLO)
#  define NO_MEMCPY
#endif
#if defined(SMALL_MEDIUM) && !defined(_MSC_VER) && !defined(__SC__)
 /* Use our own functions for small and medium model with MSC <= 5.0.
  * You may have to use the same strategy for Borland C (untested).
  * The __SC__ check is for Symantec.
  */
#  define NO_MEMCPY
#endif
#if defined(STDC) && !defined(HAVE_MEMCPY) && !defined(NO_MEMCPY)
#  define HAVE_MEMCPY
#endif
#ifdef HAVE_MEMCPY
#  ifdef SMALL_MEDIUM /* MSDOS small or medium model */
#    define zmemcpy _fmemcpy
#    define zmemcmp _fmemcmp
#    define zmemzero(dest, len) _fmemset(dest, 0, len)
#  else
#    define zmemcpy memcpy
#    define zmemcmp memcmp
#    define zmemzero(dest, len) memset(dest, 0, len)
#  endif
#else
   void ZLIB_INTERNAL zmemcpy OF((Bytef* dest, const Bytef* source, uInt len));
   int ZLIB_INTERNAL zmemcmp OF((const Bytef* s1, const Bytef* s2, uInt len));
   void ZLIB_INTERNAL zmemzero OF((Bytef* dest, uInt len));
#endif

/* Diagnostic functions */
#ifdef ZLIB_DEBUG
#  include <stdio.h>
   extern int ZLIB_INTERNAL z_verbose;
   extern void ZLIB_INTERNAL z_error OF((char *m));
#  define Assert(cond,msg) {if(!(cond)) z_error(msg);}
#  define Trace(x) {if (z_verbose>=0) fprintf x ;}
#  define Tracev(x) {if (z_verbose>0) fprintf x ;}
#  define Tracevv(x) {if (z_verbose>1) fprintf x ;}
#  define Tracec(c,x) {if (z_verbose>0 && (c)) fprintf x ;}
#  define Tracecv(c,x) {if (z_verbose>1 && (c)) fprintf x ;}
#else
#  define Assert(cond,msg)
#  define Trace(x)
#  define Tracev(x)
#  define Tracevv(x)
#  define Tracec(c,x)
#  define Tracecv(c,x)
#endif

#ifndef Z_SOLO
   voidpf ZLIB_INTERNAL zcalloc OF((voidpf opaque, unsigned items,
                                    unsigned size));
   void ZLIB_INTERNAL zcfree  OF((voidpf opaque, voidpf ptr));
#endif

#define ZALLOC(strm, items, size) \
           (*((strm)->zalloc))((strm)->opaque, (items), (size))
#define ZFREE(strm, addr)  (*((strm)->zfree))((strm)->opaque, (voidpf)(addr))
#define TRY_FREE(s, p) {if (p) ZFREE(s, p);}

/* Reverse the bytes in a 32-bit value */
#define ZSWAP32(q) ((((q) >> 24) & 0xff) + (((q) >> 8) & 0xff00) + \
                    (((q) & 0xff00) << 8) + (((q) & 0xff) << 24))

#endif /* ZUTIL_H */
