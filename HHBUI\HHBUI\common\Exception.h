﻿#pragma once
#include "common/status_handle.h"

#define _EX_MAKE_HRESULT(ERROR, MODULE, CODE) \
	MAKE_HRESULT(ERROR, FACILITY_ITF, (((MODULE & 0xFF) << 16) | (CODE)))
#define EX_DECLEAR_ERROR_STATUS(NAME, MODULE, CODE) \
	constexpr HRESULT NAME = _EX_MAKE_HRESULT(SEVERITY_ERROR, MODULE, CODE)
namespace HHBUI
{
	EX_DECLEAR_ERROR_STATUS(EE_LOST_NECESSARY, 0x00, 0x80);
	EX_DECLEAR_ERROR_STATUS(EE_NOREADY, 0x00, 0x01);
	EX_DECLEAR_ERROR_STATUS(EE_IO, 0x00, 0x0F);
	EX_DECLEAR_ERROR_STATUS(EE_NOEXISTS, 0x00, 0x02);
	EX_DECLEAR_ERROR_STATUS(EE_OUTOFBOUNDS, 0x00, 0x04);
	EX_DECLEAR_ERROR_STATUS(EE_UNMATCH, 0x00, 0x05);
	/// 异常
	class Exception
	{
	public:
		Exception(HRESULT status)
		{
			m_status = status;
			m_message = L"";
			m_file = L"";
			m_line = 0;
		}
		Exception(HRESULT status, LPCWSTR message)
		{
			m_status = status;
			m_message = message;
			m_file = L"";
			m_line = 0;
		}
		Exception(HRESULT status, LPCWSTR message, LPCWSTR file, int line)
		{
			m_status = status;
			m_message = message ? message : L"";
			m_file = file ? file : L"";
			m_line = line;
		}

		inline HRESULT status() const { return m_status; }
		inline const std::wstring& message() const { return m_message; }
		inline const std::wstring& file() const { return m_file; }
		inline int line() const { return m_line; }

		inline bool is_failed() const { return FAILED(m_status); }
		inline operator bool() const { return is_failed(); }

		inline HRESULT handle(bool only_failed = false) const
		{
			if (!only_failed || is_failed())
			{
				return ExStatusHandle(m_status,
					m_file.size() != 0 ? m_file.c_str() : nullptr, m_line,
					m_message.c_str()
				);
			}
			return m_status;
		}

	private:
		HRESULT m_status;
		std::wstring m_message;
		std::wstring m_file;
		int m_line;
	};

#define throw_ex(status,message)	throw Exception(status, message, __CALLINFO__)						// 抛出异常
#define throw_if_false(exp,status,message) if(!(exp)) { throw_ex(status,message); }							// 错误则抛出异常
#define throw_if_failed(exp,message) { HRESULT _HR_ = exp; if(FAILED(_HR_)){ throw_ex(_HR_, message); }}	// 失败则抛出异常
#define throw_if_notok(exp,message) { HRESULT _HR_ = exp; if(_HR_ != S_OK){ throw_ex(_HR_, message); }}		// 不成功则抛出异常

#define catch_return(todo)		catch(Exception& e) { todo; return e.handle(); }							// 捕获异常并返回
#define catch_continue(todo)	catch(Exception& e) { todo; e.handle(); }									// 捕获异常并继续执行
#define catch_ignore(todo)		catch(Exception& e) { UNREFERENCED_PARAMETER(e); todo; }					// 捕获异常但忽略
#define catch_throw(todo)		catch(Exception& e) { UNREFERENCED_PARAMETER(e); todo; throw; }			// 捕获异常并继续抛出
#define catch_default			catch_return																// 默认捕获异常

#define try_default(try_block,todo)		try{ try_block; } catch_default(todo);								// 简单异常捕获
#define try_continue(try_block,todo)	try{ try_block; } catch_continue(todo);								// 捕获异常并继续执行
#define try_ignore(try_block,todo)		try{ try_block; } catch_ignore(todo);								// 捕获异常但忽略

#define handle_ex(status,message)			return ExStatusHandle(status,__CALLINFO__,message)				// 处理异常
#define handle_continue(status,message)		ExStatusHandle(status,__CALLINFO__,message)						// 处理异常

	// 错误则处理异常
#define handle_if_false(exp,status,message,todo) \
	if(!(exp)) { todo; return ExStatusHandle(status,__CALLINFO__,message); }

	// 失败则处理异常
#define handle_if_failed(exp,message,todo) \
	{ HRESULT _HR_ = exp; if(FAILED(_HR_)){ todo; return ExStatusHandle(_HR_,__CALLINFO__, message); }}

	// 不成功则处理异常
#define handle_if_notok(exp,message,todo) \
	{ HRESULT _HR_ = exp; if(_HR_ != S_OK){ todo; return ExStatusHandle(_HR_,__CALLINFO__, message); }}

	//错误则返回
#define return_if_false(exp, todo, ret)	if(!(exp)){todo; return ret;}
	//存在则返回
#define return_if_true(exp, todo, ret)	if((exp)){todo; return ret;}
	//失败则返回
#define return_if_failed(exp, todo) { HRESULT _HR_ = exp; if(FAILED(_HR_)){ todo; return _HR_; }}

	//不成功则返回
#define return_if_notok(exp, todo) { HRESULT _HR_ = exp;  if(_HR_ != S_OK){ todo; return _HR_; }}


#pragma region 调试中断
#include <assert.h>

#ifdef EX_CFG_DEBUG_INTERRUPT

#define ExDbgBreak()				_CrtDbgBreak()		
#define ExAssert(exp_is_true)		assert(exp_is_true)
#define ExAssertMsg(exp_is_true,message)																	\
		(void)(																								\
            (!!(exp_is_true)) || !UIEngine::IsDebugMode() ||					                        			\
            (_wassert(message, __CALLINFO__), 0)															\
        )																									\


#define ExAssertFmt(exp_is_true, format, ...)																\
						(void) ((!!(exp_is_true)) || !UIEngine::IsDebugMode() ||		                        	\
						(1 != _CrtDbgReportW(_CRT_ASSERT, __CALLINFO__, nullptr, format, __VA_ARGS__)) ||	\
						(ExDbgBreak(), 0))

#define ExAssertFmtCallInfo(exp_is_true, file, line, format, ...)											\
						(void) ((!!(exp_is_true)) || !UIEngine::IsDebugMode() ||		                        	\
						(1 != _CrtDbgReportW(_CRT_ASSERT, file, line, nullptr, format, __VA_ARGS__)) ||		\
						(ExDbgBreak(), 0))

#else

#define ExDbgBreak()												(FALSE)
#define ExAssert(exp_is_true)										(exp_is_true)
#define ExAssertMsg(exp_is_true,message)							(exp_is_true)
#define ExAssertFmt(exp_is_true, format, ...)						(exp_is_true)
#define ExAssertFmtCallInfo(exp_is_true, file,line, format, ...)	(exp_is_true)

#endif // EX_CFG_DEBUG_INTERRUPT
#pragma endregion

#pragma region 参数检查

#ifdef EX_CFG_DEBUG_CHECK_PARAM
#define CHECK_PARAM(EXP)				{if(!(EXP)){ ExAssertMsg(EXP, L"InvalidParam: " #EXP); return E_INVALIDARG;}}
#define CHECK_PARAM_RET(EXP,RET)		{if(!(EXP)){ ExAssertMsg(EXP, L"InvalidParam: " #EXP); return RET;}}
#else
#define CHECK_PARAM(EXP)
#define CHECK_PARAM_RET(EXP,RET)
#endif // EX_CFG_DEBUG_CHECK_PARAM
#pragma endregion
}
