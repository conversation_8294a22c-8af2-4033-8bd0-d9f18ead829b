﻿#include "pch.h"
#include "layout.h"
#include <common/winapi.h>
#include <common/memory.h>

BOOL HHBUI::UILayout::layout_bind(INT nType, UIBase* hObjBind)
{
    if (l_data.lpLayoutInfo == nullptr)
    {
        l_data.hBind = hObjBind;
        l_data.nType = nType;
        l_data.fUpdateable = TRUE;
        switch (nType)
        {
        case elt_linear:
            l_data.lpfnProc = std::bind(&UILayout::layout_linear_proc, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
            break;
        case elt_flow:
            l_data.lpfnProc = std::bind(&UILayout::layout_flow_proc, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
            break;
        case elt_page:
            l_data.lpfnProc = std::bind(&UILayout::layout_page_proc, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
            break;
        case elt_table:
            l_data.lpfnProc = std::bind(&UILayout::layout_table_proc, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
            break;
        case elt_relative:
            l_data.lpfnProc = std::bind(&UILayout::layout_relative_proc, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
            break;
        case elt_absolute:
            l_data.lpfnProc = std::bind(&UILayout::layout_absolute_proc, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
            break;
        default:
            return FALSE;
        }

        INT nCount = l_data.lpfnProc(ELN_GETPROPSCOUNT, NULL, NULL);
        LPVOID pInfo = (LPVOID)((size_t)ExMemAlloc((nCount + (size_t)4) * sizeof(size_t)) + (size_t)4 * sizeof(size_t)); //padding
        l_data.lpLayoutInfo = pInfo;
        l_data.lpfnProc(ELN_INITPROPS, NULL, (size_t)pInfo);
        nCount = l_data.lpfnProc(ELN_GETCHILDPROPCOUNT, NULL, NULL);
        l_data.cbInfoLen = (nCount + 5) * sizeof(size_t); // Margin4/hObjChild1
        l_data.hArrChildrenInfo = new UIarray();
      
        return TRUE;
    }
    return FALSE;
}

BOOL HHBUI::UILayout::Layout_Init(INT nType)
{
    return layout_bind(nType, this);
}

HHBUI::UILayout::~UILayout()
{
    if (l_data.lpLayoutInfo)
    {
        l_data.lpfnProc(ELN_UNINITPROPS, 0, (size_t)l_data.lpLayoutInfo);
        size_t arraySize = l_data.hArrChildrenInfo->size();
        for (size_t i = 1; i <= arraySize; i++)
        {
            LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
            layout_free_info(pInfo);
        }
        delete l_data.hArrChildrenInfo;
        ExMemFree((LPVOID)((size_t)l_data.lpLayoutInfo - 4 * sizeof(size_t)));
    }
}

BOOL HHBUI::UILayout::Layout_Update()
{
    if (l_data.fUpdateable)
    {
        l_data.lpfnProc(ELN_UPDATE, 0, 0);
        return TRUE;
    }
    return FALSE;
}

INT HHBUI::UILayout::Layout_GetType()
{
    return l_data.nType;
}

void HHBUI::UILayout::Layout_SetEnableUpdate(BOOL fUpdateable)
{
    l_data.fUpdateable = fUpdateable;
}

LRESULT HHBUI::UILayout::Layout_Notify(INT nEvent, WPARAM wParam, LPARAM lParam)
{
    LRESULT ret = S_FALSE;
    if (l_data.fUpdateable)
    {
        ret = l_data.lpfnProc(nEvent, wParam, lParam);
    }
    return ret;
}
BOOL _layout_enum_find_obj(LPVOID hArr, INT nIndex, size_t pvItem, size_t pvParam)
{
    return (__get((LPVOID)pvItem, 0) == pvParam);
}
BOOL HHBUI::UILayout::Layout_Table_SetInfo(INT* aRowHeight, INT cRows, INT* aCellWidth, INT cCells)
{
    if (l_data.nType == elt_table)
    {
        UIarray* hArrRows = (UIarray*)__get(l_data.lpLayoutInfo, (elp_table_array_row - 1) * sizeof(LPVOID));
        hArrRows->redefine(cRows);
        for (INT i = 1; i <= cRows; i++)
        {
            hArrRows->set(i, aRowHeight[i - 1]);
        }
        UIarray* hArrcCells = (UIarray*)__get(l_data.lpLayoutInfo, (elp_table_array_cell - 1) * sizeof(LPVOID));
        hArrcCells->redefine(cCells);
        for (INT i = 1; i <= cCells; i++)
        {
            hArrcCells->set(i, aCellWidth[i - 1]);
        }
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

BOOL HHBUI::UILayout::Layout_SetChildProp(UIControl*parent, INT dwPropID, size_t pvValue)
{
    if (parent)
    {
        size_t nIndex = l_data.hArrChildrenInfo->emum(&_layout_enum_find_obj, (size_t)parent);
        LPVOID pInfo = nullptr;
        if (nIndex == 0)
        {
            pInfo = ExMemAlloc(l_data.cbInfoLen);
            if (pInfo != 0)
            {
                pInfo = (LPVOID)((size_t)pInfo + 4 * sizeof(size_t));
                __set(pInfo, 0, (size_t)parent);
                l_data.lpfnProc(ELN_INITCHILDPROPS, (size_t)parent, (size_t)pInfo);
                nIndex = l_data.hArrChildrenInfo->insert((size_t)pInfo);
            }
        }
        else
        {
            pInfo = (LPVOID)l_data.hArrChildrenInfo->get(nIndex);
        }
        if (pInfo != 0)
        {

            if (l_data.lpfnProc(ELN_CHECKCHILDPROPVALUE, MAKELONG(nIndex, dwPropID), pvValue) == 0)
            {

                __set(pInfo, dwPropID * sizeof(size_t), pvValue);
            }
        }
        else
        {
            return FALSE;
        }
        return TRUE;
    }
    return FALSE;
}

BOOL HHBUI::UILayout::Layout_GetChildProp(UIControl*parent, INT dwPropID, size_t* pvValue)
{
    if (parent)
    {
        size_t nIndex = l_data.hArrChildrenInfo->emum(&_layout_enum_find_obj, (size_t)parent);
        LPVOID pInfo = nullptr;
        if (nIndex != 0)
        {
            pInfo = (LPVOID)l_data.hArrChildrenInfo->get(nIndex);
            if (pInfo)
            {
                dwPropID = dwPropID * sizeof(LPVOID);
                if (dwPropID >= -16 && dwPropID < l_data.cbInfoLen)
                {
                    *pvValue = __get(pInfo, dwPropID);
                    return TRUE;
                }

            }
        }
       
    }
    return FALSE;
}

BOOL HHBUI::UILayout::Layout_GetChildPropList(UIControl*parent, LPVOID* lpProps)
{
    size_t nIndex = l_data.hArrChildrenInfo->emum(_layout_enum_find_obj, (size_t)parent);
    if (nIndex != 0)
    {
        *lpProps = (LPVOID)l_data.hArrChildrenInfo->get(nIndex);
        return TRUE;
    }
    return FALSE;
}

BOOL HHBUI::UILayout::Layout_SetProp(INT dwPropID, size_t pvValue)
{
    if (l_data.lpfnProc(ELN_CHECKCHILDPROPVALUE, dwPropID, pvValue) == 0)
    {
        if (dwPropID > 0)
        {
            dwPropID = dwPropID - 1;
        }
        __set(l_data.lpLayoutInfo, dwPropID * sizeof(size_t), pvValue);
        return TRUE;
    }
    return FALSE;
}

size_t HHBUI::UILayout::Layout_GetProp(INT dwPropID)
{
    size_t ret = 0;
    LPVOID pInfo = l_data.lpLayoutInfo;
    if (dwPropID > 0)
    {
        dwPropID = dwPropID - 1;
    }
    ret = __get(pInfo, dwPropID * sizeof(size_t));
    return ret;
}

LPVOID HHBUI::UILayout::Layout_GetPropList()
{
    return l_data.lpLayoutInfo;
}

BOOL HHBUI::UILayout::Layout_Absolute_Lock(UIControl*hObjChild, INT tLeft, INT tTop, INT tRight, INT tBottom, INT tWidth, INT tHeight)
{
    ExRectF rcClient{};
    if (hObjChild && l_data.nType == elt_absolute)
    {
        if (l_data.hBind->m_UIView)
        {
            auto objChild = (UIControl*)l_data.hBind->m_UIView;
            objChild->GetRect(rcClient, grt_client);
        }
        else
        {
            auto objChild = (UIWnd*)l_data.hBind->m_UIWindow;
            objChild->GetClientRect(rcClient);
        }
        LONG_PTR* pInfoPtr = reinterpret_cast<LONG_PTR*>(l_data.lpLayoutInfo);

        rcClient.left += *(pInfoPtr + elp_padding_left);
        rcClient.top += *(pInfoPtr + elp_padding_top);
        rcClient.right -= *(pInfoPtr + elp_padding_right);
        rcClient.bottom -= *(pInfoPtr + elp_padding_bottom);


        SIZE szClient{ 0 };
        szClient.cx = rcClient.right - rcClient.left;
        szClient.cy = rcClient.bottom - rcClient.top;
        ExRectF rcObj{};
        hObjChild->GetRect(rcObj);

        if (tLeft == 1) //数值锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_left, rcObj.left - rcClient.left);
            Layout_SetChildProp(hObjChild, elcp_absolute_left_type, elcp_absolute_type_px);
        }
        else if (tLeft == 2) //比例锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_left, (FLOAT)rcObj.left / szClient.cx * 100);
            Layout_SetChildProp(hObjChild, elcp_absolute_left_type, elcp_absolute_type_ps);
        }
        else
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_left, 0);
            Layout_SetChildProp(hObjChild, elcp_absolute_left_type, elcp_absolute_type_unknown);
        }

        if (tTop == 1) //数值锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_top, rcObj.top - rcClient.top);
            Layout_SetChildProp(hObjChild, elcp_absolute_top_type, elcp_absolute_type_px);
        }
        else if (tTop == 2) //比例锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_top, (FLOAT)rcObj.top / szClient.cy * 100);
            Layout_SetChildProp(hObjChild, elcp_absolute_top_type, elcp_absolute_type_ps);
        }
        else
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_top, 0);
            Layout_SetChildProp(hObjChild, elcp_absolute_top_type, elcp_absolute_type_unknown);
        }

        if (tRight == 1) //数值锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_right, rcClient.right - rcObj.right);
            Layout_SetChildProp(hObjChild, elcp_absolute_right_type, elcp_absolute_type_px);
        }
        else if (tRight == 2) //比例锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_right, (FLOAT)(rcClient.right - rcObj.right) / szClient.cx * 100);
            Layout_SetChildProp(hObjChild, elcp_absolute_right_type, elcp_absolute_type_ps);
        }
        else
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_right, 0);
            Layout_SetChildProp(hObjChild, elcp_absolute_right_type, elcp_absolute_type_unknown);
        }

        if (tBottom == 1) //数值锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_bottom, rcClient.bottom - rcObj.bottom);
            Layout_SetChildProp(hObjChild, elcp_absolute_bottom_type, elcp_absolute_type_px);
        }
        else if (tBottom == 2) //比例锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_bottom, (FLOAT)(rcClient.bottom - rcObj.bottom) / szClient.cy * 100);
            Layout_SetChildProp(hObjChild, elcp_absolute_bottom_type, elcp_absolute_type_ps);
        }
        else
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_bottom, 0);
            Layout_SetChildProp(hObjChild, elcp_absolute_bottom_type, elcp_absolute_type_unknown);
        }

        if (tWidth == 1) //数值锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_width, rcObj.right - rcObj.left);
            Layout_SetChildProp(hObjChild, elcp_absolute_width_type, elcp_absolute_type_px);
        }
        else if (tWidth == 2) //比例锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_width, (FLOAT)(rcObj.right - rcObj.left) / szClient.cx * 100);
            Layout_SetChildProp(hObjChild, elcp_absolute_width_type, elcp_absolute_type_ps);
        }
        else
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_width, 0);
            Layout_SetChildProp(hObjChild, elcp_absolute_width_type, elcp_absolute_type_unknown);
        }

        if (tHeight == 1) //数值锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_height, rcObj.bottom - rcObj.top);
            Layout_SetChildProp(hObjChild, elcp_absolute_height_type, elcp_absolute_type_px);
        }
        else if (tHeight == 2) //比例锁
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_height, (FLOAT)(rcObj.bottom - rcObj.top) / szClient.cy * 100);
            Layout_SetChildProp(hObjChild, elcp_absolute_height_type, elcp_absolute_type_ps);
        }
        else
        {
            Layout_SetChildProp(hObjChild, elcp_absolute_height, 0);
            Layout_SetChildProp(hObjChild, elcp_absolute_height_type, elcp_absolute_type_unknown);
        }
        return TRUE;
    }
    return FALSE;
}

BOOL HHBUI::UILayout::Layout_Absolute_Setedge(UIControl*hObjChild, INT dwEdge, INT dwType, size_t nValue)
{
    if (hObjChild && l_data.nType == elt_absolute)
    {
        dwEdge = (dwEdge + 1) / 2;
        if (dwEdge >= 1 && dwEdge <= 8)
        {
            INT offsetof = 0;
            if (l_data.hBind->m_UIWindow)
            {
                auto objChild = (UIWnd*)l_data.hBind->m_UIWindow;
                offsetof = objChild->m_data.size_tmp;
            }

            Layout_SetChildProp(hObjChild, dwEdge * 2, dwType);
            Layout_SetChildProp(hObjChild, dwEdge * 2 - 1, nValue + offsetof);
            return TRUE;
        }
    }
    return FALSE;
}

BOOL HHBUI::UILayout::Layout_AddChild(UIControl*parent)
{
    if (parent && layout_get_child(parent) == 0)
    {
        auto nIndex = l_data.hArrChildrenInfo->emum(&_layout_enum_find_obj, (size_t)parent);
        if (nIndex == 0)
        {
            LPVOID pInfo = ExMemAlloc(l_data.cbInfoLen);
            if (pInfo != 0)
            {
                pInfo = (LPVOID)((size_t)pInfo + 4 * sizeof(size_t));
                __set(pInfo, 0, (size_t)parent->m_UIView);
                l_data.lpfnProc(ELN_INITCHILDPROPS, (size_t)parent->m_UIView, (size_t)pInfo);
                nIndex = l_data.hArrChildrenInfo->insert((size_t)pInfo);
                return TRUE;
            }
        }
    }
    return FALSE;
}

BOOL HHBUI::UILayout::Layout_AddChildren(BOOL fDesc, LPCWSTR dwObjClass, INT* nCount)
{
    INT nError = 0;
    *nCount = 0;
    LPVOID parent = nullptr;
    if (l_data.hBind->m_UIView)
    {
        parent = fDesc ? ((UIControl*)l_data.hBind->m_UIView)->m_objChildLast : ((UIControl*)l_data.hBind->m_UIView)->m_objChildFirst;
    }
    else
    {
        parent = fDesc ? ((UIWnd*)l_data.hBind->m_UIWindow)->m_objChildLast : ((UIWnd*)l_data.hBind->m_UIWindow)->m_objChildFirst;
    }
    while (parent)
    {
        auto pObj2 = (UIControl*)parent;
        std::wstring clsAtom = pObj2->GetlClassName();
        if ((dwObjClass == 0 || dwObjClass == clsAtom) && clsAtom != L"form-sysbutton")
        {
            if (layout_get_child(parent) == 0)
            {
                LPVOID pInfo = ExMemAlloc(l_data.cbInfoLen);
                if (pInfo)
                {
                    pInfo = (LPVOID)((size_t)pInfo + 4 * sizeof(size_t));
                    __set(pInfo, 0, (size_t)parent);
                   l_data.lpfnProc(ELN_INITCHILDPROPS, (size_t)parent, (size_t)pInfo);
                   l_data.hArrChildrenInfo->insert((size_t)pInfo);
                    *nCount = *nCount + 1;
                }
                else
                {
                    nError = 1;
                }
            }
        }
        parent = fDesc ? pObj2->GetNode(GW_HWNDPREV) : pObj2->GetNode(GW_HWNDNEXT);
    }
    return nError == 0;
}

BOOL HHBUI::UILayout::Layout_DeleteChild(UIControl*parent)
{
    if (l_data.hArrChildrenInfo)
    {
        auto nIndex = l_data.hArrChildrenInfo->emum(_layout_enum_find_obj, (size_t)parent);
        if (nIndex != -1)
        {
            l_data.hArrChildrenInfo->erase(nIndex);
            return TRUE;
        }
    }
    return FALSE;
}

BOOL HHBUI::UILayout::Layout_DeleteChildren(LPCWSTR dwObjClass)
{
    if (l_data.hArrChildrenInfo)
    {
        if (dwObjClass != 0)
        {
            auto arraySize = l_data.hArrChildrenInfo->size();
            for (size_t i = arraySize; i >= 1; i--)
            {
                LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
                auto UIView = (UIControl*)__get(pInfo, 0);
                if (UIView)
                {
                    if (dwObjClass == UIView->GetlClassName())
                    {
                        layout_free_info(pInfo);
                        l_data.hArrChildrenInfo->erase(i);
                        return TRUE;
                    }
                }
            }
        }
        else
        {
            auto arraySize = l_data.hArrChildrenInfo->size();
            for (size_t i = 1; i <= arraySize; i++)
            {
                LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
                layout_free_info(pInfo);
                l_data.hArrChildrenInfo->get(i);
            }
            l_data.hArrChildrenInfo->clear();
            return TRUE;
        }
    }
    return FALSE;
}

size_t HHBUI::UILayout::Layout_GetChildCount()
{
    if (l_data.hArrChildrenInfo)
        return l_data.hArrChildrenInfo->size();
    return 0;
}

LRESULT HHBUI::UILayout::layout_linear_proc(INT nEvent, WPARAM wParam, LPARAM lParam)
{
    if (nEvent == ELN_GETPROPSCOUNT)
    {
        return 2;
    }
    else if (nEvent == ELN_GETCHILDPROPCOUNT)
    {
        return 2;
    }
    else if (nEvent == ELN_INITCHILDPROPS)
    {
        __set((LPVOID)lParam, elcp_linear_size * sizeof(size_t), -1);
    }
    else if (nEvent == ELN_CHECKCHILDPROPVALUE)
    {
        INT nSize = GET_Y_LPARAM(wParam);
        if (nSize == ELN_CHECKCHILDPROPVALUE)
        {
            return (lParam < elcp_linear_algin_fill || lParam > elcp_linear_align_right_bottom);
        }
    }
    else if (nEvent == ELN_UPDATE)
    {
        ExRectF rcClient{};
        if (l_data.hBind->m_UIView)
        {
            auto objChild = (UIControl*)l_data.hBind->m_UIView;
            objChild->GetRect(rcClient, grt_client);
        }
        else
        {
            auto objChild = (UIWnd*)l_data.hBind->m_UIWindow;
            objChild->GetClientRect(rcClient);
        }
        INT nDAlign = __get(l_data.lpLayoutInfo, (elp_linear_dalign - 1) * sizeof(size_t));
        BOOL fVertical = __get(l_data.lpLayoutInfo, (elp_linear_direction - 1) * sizeof(size_t)) == elp_direction_v;


        LONG_PTR* pInfoPtr = reinterpret_cast<LONG_PTR*>(l_data.lpLayoutInfo);
        RECT lpRctmp{ (LONG)*(pInfoPtr + elp_padding_left), 
            (LONG)*(pInfoPtr + elp_padding_top), 
            (LONG)*(pInfoPtr + elp_padding_right), 
            (LONG)*(pInfoPtr + elp_padding_bottom) };

        rcClient.left += lpRctmp.left;
        rcClient.top += lpRctmp.top;
        rcClient.right -= lpRctmp.right;
        rcClient.bottom -= lpRctmp.bottom;

        SIZE szClient{};
        szClient.cx = rcClient.right - rcClient.left;
        szClient.cy = rcClient.bottom - rcClient.top;
        ExRectF rcObj{};
        rcObj.left = rcClient.left;
        rcObj.top = rcClient.top;
        std::vector<ExRectF> arrRect;
        std::vector<INT> arrOrg;
        size_t arraySize = l_data.hArrChildrenInfo->size();
        if (nDAlign != 0 && arraySize > 0)
        {
            arrRect.resize(arraySize);
            arrOrg.resize(arraySize);
        }
        for (size_t i = 1; i <= arraySize; i++)
        {
            INT orgFlags = 0;
            auto pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
            auto objChild = (UIControl*)__get(pInfo, 0);
            if (!objChild)
                continue;
            INT nSize = __get(pInfo, elcp_linear_size * sizeof(size_t));
            ExRectF rcTmp{};
            objChild->GetRect(rcTmp);
            INT w = rcTmp.right - rcTmp.left;
            INT h = rcTmp.bottom - rcTmp.top;
            if (nSize < 0)
            {
                if (fVertical)
                {
                    nSize = h;
                }
                else
                {
                    nSize = w;
                }
            }
            INT nFill = __get(pInfo, elcp_linear_align * sizeof(size_t));
            orgFlags = 0;
            if (fVertical)
            {
                if (nFill == elcp_linear_align_left_top)
                {
                    rcObj.left = rcClient.left;
                    rcObj.right = rcObj.left + w;
                }
                else if (nFill == elcp_linear_align_center)
                {
                    rcObj.left = rcClient.left + (szClient.cx - w) / 2;
                    rcObj.right = rcObj.left + w;
                    orgFlags = 4;
                }
                else if (nFill == elcp_linear_align_right_top || nFill == elcp_linear_align_right_bottom)
                {
                    rcObj.right = rcClient.right;
                    rcObj.left = rcClient.right - w;
                }
                else
                {
                    if (lpRctmp.left != 0)
                        rcObj.left = rcClient.left;
                    else
                        rcObj.left = rcTmp.left;
                    if (lpRctmp.right != 0)
                        rcObj.right = rcClient.right;
                    else
                        rcObj.right = rcTmp.right;
                }

                if (nFill == elcp_linear_align_right_bottom)
                {
                    rcObj.top = rcClient.top + rcClient.bottom - h;
                    rcObj.bottom = rcObj.top + nSize;

                    auto pInfo1 = (LPVOID)l_data.hArrChildrenInfo->get(i - 1);
                    if (pInfo1)
                    {
                        auto objChild1 = (UIControl*)__get(pInfo1, 0);
                        if (objChild1)
                        {
                            ExRectF rcTmp1{};
                            objChild1->GetRect(rcTmp1);
                            rcObj.top = rcClient.top + rcClient.bottom - (rcTmp1.bottom - rcTmp1.top) * ((LONG)i - 1) - h;
                            rcObj.bottom = rcObj.top + (rcTmp1.bottom - rcTmp1.top);

                        }
                    }

                }
                else
                {
                    rcObj.bottom = rcObj.top + nSize;
                }

            }
            else
            {
                if (nFill == elcp_linear_align_left_top)
                {
                    rcObj.top = rcClient.top;
                    rcObj.bottom = rcObj.top + rcObj.bottom - rcTmp.top;
                }
                else if (nFill == elcp_linear_align_center)
                {
                    rcObj.top = rcClient.top + (szClient.cy - h) / 2;
                    rcObj.bottom = rcObj.top + h;
                    orgFlags = 8;
                }
                else if (nFill == elcp_linear_align_right_top)
                {
                    rcObj.bottom = rcClient.bottom;
                    rcObj.top = rcClient.bottom - h;
                }
                else
                {
                    if (lpRctmp.top != 0)
                        rcObj.top = rcClient.top;
                    else
                        rcObj.top = rcTmp.top;
                    if (lpRctmp.bottom != 0)
                        rcObj.bottom = rcClient.bottom;
                    else
                        rcObj.bottom = rcTmp.bottom;
                }
                rcObj.right = rcObj.left + nSize;
            }
            if (nDAlign == 0)
            {
                layout_move_margin(objChild, rcObj, (LPVOID)((size_t)pInfo - 4 * sizeof(size_t)), fVertical ? 5 : 10, orgFlags);
            }
            else
            {
                LONG_PTR* pInfoPtr = reinterpret_cast<LONG_PTR*>(pInfo);
                rcObj.left += *(pInfoPtr + elcp_margin_left);
                rcObj.top += *(pInfoPtr + elcp_margin_top);
                rcObj.right -= *(pInfoPtr + elcp_margin_right);
                rcObj.bottom -= *(pInfoPtr + elcp_margin_bottom);
                arrRect[i - 1] = rcObj;
                arrOrg[i - 1] = orgFlags;
            }
            if (fVertical)
            {
                rcObj.top = rcObj.bottom;
            }
            else
            {
                rcObj.left = rcObj.right;
            }
        }

        if (arraySize > 0 && arrRect.size() > 0)
        {
            LPVOID pInfo = l_data.lpLayoutInfo;
            INT nDAlign = __get(pInfo, (elp_linear_dalign - 1) * sizeof(size_t));
            INT w = 0;
            INT h = 0;
            if (fVertical)
            {
                INT nSize = arrRect[arrRect.size() - 1].bottom - arrRect[0].top;
                h = 5;
                if (nDAlign == 2) //bottom
                {
                    w = rcClient.bottom - nSize - arrRect[0].top;
                }
                else if (nDAlign == 1) //CENTER
                {
                    w = rcClient.top + (rcClient.bottom - rcClient.top - nSize) / 2 - arrRect[0].top;
                }
                else
                {
                    w = 0;
                }
            }
            else
            {
                INT nSize = arrRect[arrRect.size() - 1].right - arrRect[0].left;
                h = 10;
                if (nDAlign == 2) //right
                {
                    w = rcClient.right - nSize - arrRect[0].left;
                }
                else if (nDAlign == 1) //center
                {
                    w = rcClient.left + (rcClient.right - rcClient.top - nSize) / 2 - arrRect[0].left;
                }
                else
                {
                    w = 0;
                }
            }
            for (size_t i = 1; i <= arraySize; i++)
            {
                LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
                ExRectF rcObj = arrRect[i - 1];
                if (fVertical)
                {
                    rcObj.Offset(0, w);
                    //OffsetRect(&rcObj, 0, w);
                }
                else
                {
                    rcObj.Offset(w, 0);
                    //OffsetRect(&rcObj, w, 0);
                }
                layout_move_margin((LPVOID)__get(pInfo, 0), rcObj, (LPVOID)((size_t)pInfo - 4 * sizeof(size_t)), 15, arrOrg[i - 1]);
            }
        }
    }
    return 0;
}

LRESULT HHBUI::UILayout::layout_flow_proc(INT nEvent, WPARAM wParam, LPARAM lParam)
{
    if (nEvent == ELN_GETPROPSCOUNT)
    {
        return 1;
    }
    else if (nEvent == ELN_GETCHILDPROPCOUNT)
    {
        return 2;
    }
    else if (nEvent == ELN_INITCHILDPROPS)
    {
        __set((LPVOID)lParam, elcp_flow_size * sizeof(size_t), -1);
    }
    else if (nEvent == ELN_UPDATE)
    {
        ExRectF rcClient{};
        if (l_data.hBind->m_UIView)
        {
            auto objChild = (UIControl*)l_data.hBind->m_UIView;
            objChild->GetRect(rcClient, grt_client);
        }
        else
        {
            auto objChild = (UIWnd*)l_data.hBind->m_UIWindow;
            objChild->GetClientRect(rcClient);
        }
        BOOL fVertical = __get(l_data.lpLayoutInfo, (elp_flow_direction - 1) * sizeof(size_t)) == elp_direction_v;

        LONG_PTR* pInfoPtr = reinterpret_cast<LONG_PTR*>(l_data.lpLayoutInfo);
        rcClient.left += *(pInfoPtr + elp_padding_left);
        rcClient.top += *(pInfoPtr + elp_padding_top);
        rcClient.right -= *(pInfoPtr + elp_padding_right);
        rcClient.bottom -= *(pInfoPtr + elp_padding_bottom);
        ExRectF rcObj{};
        rcObj.left = rcClient.left;
        rcObj.top = rcClient.top;

        INT nMaxSize = 0;
        size_t arraySize = l_data.hArrChildrenInfo->size();
        for (size_t i = 1; i <= arraySize; i++)
        {
            LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
            auto objChild = (UIControl*)__get(pInfo, 0);
            if (!objChild || !objChild->IsVisible())
                continue;
            INT nSize = __get(pInfo, elcp_flow_size * sizeof(size_t));
            ExRectF rcTmp{};
            objChild->GetRect(rcTmp);
            if (nSize < 0)
            {
                if (fVertical)
                {
                    nSize = rcTmp.bottom - rcTmp.top;
                }
                else
                {
                    nSize = rcTmp.right - rcTmp.left;
                }
            }
            if (fVertical)
            {
                rcObj.right = rcObj.left + (rcTmp.right - rcTmp.left);
                rcObj.bottom = rcObj.top + nSize;
                if (rcObj.bottom > rcClient.bottom || __get(pInfo, elcp_flow_new_line * sizeof(size_t)) != 0)
                {
                    rcObj.top = rcClient.top;
                    rcObj.bottom = rcObj.top + nSize;
                    rcObj.left = rcObj.left + nMaxSize;
                    rcObj.right = rcObj.left + (rcTmp.right - rcTmp.left);
                    nMaxSize = 0;
                }
            }
            else
            {
                rcObj.right = rcObj.left + nSize;
                rcObj.bottom = rcObj.top + (rcTmp.bottom - rcTmp.top);
                if (rcObj.right > rcClient.right || __get(pInfo, elcp_flow_new_line * sizeof(size_t)) != 0)
                {
                    rcObj.left = rcClient.left;
                    rcObj.right = rcObj.left + nSize;
                    rcObj.top = rcObj.top + nMaxSize;
                    rcObj.bottom = rcObj.top + (rcTmp.bottom - rcTmp.top);
                    nMaxSize = 0;
                }
            }
            layout_move_margin(objChild, rcObj, (LPVOID)((size_t)pInfo - 4 * sizeof(size_t)), 0, 0);
            if (fVertical)
            {
                if (rcObj.right - rcObj.left > nMaxSize)
                {
                    nMaxSize = rcObj.right - rcObj.left;
                }
                rcObj.top = rcObj.bottom;
            }
            else
            {
                if (rcObj.bottom - rcObj.top > nMaxSize)
                {
                    nMaxSize = rcObj.bottom - rcObj.top;
                }
                rcObj.left = rcObj.right;
            }
        }
    }
    return 0;
}

LRESULT HHBUI::UILayout::layout_page_proc(INT nEvent, WPARAM wParam, LPARAM lParam)
{
    if (nEvent == ELN_GETPROPSCOUNT)
    {
        return 1;
    }
    else if (nEvent == ELN_GETCHILDPROPCOUNT)
    {
        return 1;
    }
    else if (nEvent == ELN_UPDATE)
    {
        ExRectF rcClient{};
        if (l_data.hBind->m_UIView)
        {
            auto objChild = (UIControl*)l_data.hBind->m_UIView;
            objChild->GetRect(rcClient, grt_client);
        }
        else
        {
            auto objChild = (UIWnd*)l_data.hBind->m_UIWindow;
            objChild->GetClientRect(rcClient);
        }
        BOOL fVertical = __get(l_data.lpLayoutInfo, (elp_linear_direction - 1) * sizeof(size_t)) == elp_direction_v;

        LONG_PTR* pInfoPtr = reinterpret_cast<LONG_PTR*>(l_data.lpLayoutInfo);
        rcClient.left += *(pInfoPtr + elp_padding_left);
        rcClient.top += *(pInfoPtr + elp_padding_top);
        rcClient.right -= *(pInfoPtr + elp_padding_right);
        rcClient.bottom -= *(pInfoPtr + elp_padding_bottom);
        size_t nCurrentPage = __get(l_data.lpLayoutInfo, (elp_page_current - 1) * sizeof(size_t));
        size_t arraySize = l_data.hArrChildrenInfo->size();
        for (size_t i = 1; i <= arraySize; i++)
        {
            LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
            auto objChild = (UIControl*)__get(pInfo, 0);
            if (!objChild)
                continue;
            if (i == nCurrentPage)
            {
                if (__get(pInfo, elcp_page_fill * sizeof(size_t)) != 0)
                {
                    layout_move_margin(objChild, rcClient, (LPVOID)((size_t)pInfo - 4 * sizeof(size_t)), 15, 0);
                }
            }
            objChild->Show(i == nCurrentPage);
        }
    }
    return 0;
}

LRESULT HHBUI::UILayout::layout_table_proc(INT nEvent, WPARAM wParam, LPARAM lParam)
{
    if (nEvent == ELN_GETPROPSCOUNT)
    {
        //' layout_rows = 行数 = 类似 "100,200,50"
        //' layout_cells = 列数 = 类似 "100,200,50"
        //' LAYOUT_ROW_ARRAY = 正数为像素,负数为百分比
        //' LAYOUT_CELL_ARRAY
        return 2;
    }
    else if (nEvent == ELN_GETCHILDPROPCOUNT)
    {
        /*' layout_row = 行号
        ' layout_cell = 列号
        ' layout_rowspan = 跨行数
        ' layout_cellspan = 跨列数
        ' layout_fill = 是否填充,否则放左上角*/
        return 5;
    }
    else if (nEvent == ELN_INITPROPS)
    {
        __set((LPVOID)lParam, (elp_table_array_row - 1) * sizeof(size_t), (size_t)new UIarray());
        __set((LPVOID)lParam, (elp_table_array_cell - 1) * sizeof(size_t), (size_t)new UIarray());
    }
    else if (nEvent == ELN_INITCHILDPROPS)
    {
        __set((LPVOID)lParam, elcp_table_row_span * sizeof(size_t), 1);
        __set((LPVOID)lParam, elcp_table_cell_span * sizeof(size_t), 1);
    }
    else if (nEvent == ELN_UNINITPROPS)
    {
        UIarray* hArrRows = (UIarray*)__get((LPVOID)lParam, (elp_table_array_row - 1) * sizeof(size_t));
        UIarray* hArrCells = (UIarray*)__get((LPVOID)lParam, (elp_table_array_cell - 1) * sizeof(size_t));
        delete hArrRows;
        delete hArrCells;
    }
    else if (nEvent == ELN_UPDATE)
    {
        ExRectF rcClient{};
        if (l_data.hBind->m_UIView)
        {
            auto objChild = (UIControl*)l_data.hBind->m_UIView;
            objChild->GetRect(rcClient, grt_client);
        }
        else
        {
            auto objChild = (UIWnd*)l_data.hBind->m_UIWindow;
            objChild->GetClientRect(rcClient);
        }
        UIarray* hArrRows = (UIarray*)__get(l_data.lpLayoutInfo, (elp_table_array_row - 1) * sizeof(size_t));
        UIarray* hArrCells = (UIarray*)__get(l_data.lpLayoutInfo, (elp_table_array_cell - 1) * sizeof(size_t));
        LONG_PTR* pInfoPtr = reinterpret_cast<LONG_PTR*>(l_data.lpLayoutInfo);
        rcClient.left += *(pInfoPtr + elp_padding_left);
        rcClient.top += *(pInfoPtr + elp_padding_top);
        rcClient.right -= *(pInfoPtr + elp_padding_right);
        rcClient.bottom -= *(pInfoPtr + elp_padding_bottom);
        std::vector<std::vector<ExRectF>> aRects;
        ExRectF rcTmp{};
        size_t arrayCellsSize = hArrCells->size();
        size_t arrayRowsSize = hArrRows->size();
        if (arrayRowsSize > 0 && arrayCellsSize > 0)
        {
            aRects.resize(arrayRowsSize);
            for (size_t index = 0; index < aRects.size(); index++)
            {
                aRects[index].resize(arrayCellsSize);
            }
            rcTmp.top = rcClient.top;
            for (size_t i = 1; i <= arrayRowsSize; i++)
            {
                rcTmp.left = rcClient.left;
                rcTmp.bottom = (LONG)hArrRows->get(i);
                if (rcTmp.bottom < 0)
                {
                    rcTmp.bottom = (rcClient.bottom - rcClient.top) * (FLOAT)abs(rcTmp.bottom) / 100;
                }
                for (size_t j = 1; j <= arrayCellsSize; j++)
                {
                    aRects[i - 1][j - 1].left = rcTmp.left;
                    aRects[i - 1][j - 1].top = rcTmp.top;
                    rcTmp.right = (LONG)hArrCells->get(j);
                    if (rcTmp.right < 0)
                    {
                        rcTmp.right = (rcClient.right - rcClient.left) * (FLOAT)abs(rcTmp.right) / 100;
                    }
                    aRects[i - 1][j - 1].right = aRects[i - 1][j - 1].left + rcTmp.right;
                    aRects[i - 1][j - 1].bottom = aRects[i - 1][j - 1].top + rcTmp.bottom;
                    rcTmp.left = rcTmp.left + rcTmp.right;
                }
                rcTmp.top = rcTmp.top + rcTmp.bottom;
            }
        }
        else
        {
            return -1;
        }
        size_t arraySize = l_data.hArrChildrenInfo->size();
        for (size_t i = 1; i <= arraySize; i++)
        {
            LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
            auto objChild = (UIControl*)__get(pInfo, 0);
            if (!objChild)
                continue;
            rcTmp.left = __get(pInfo, elcp_table_cell * sizeof(size_t));
            rcTmp.top = __get(pInfo, elcp_table_row * sizeof(size_t));
            rcTmp.right = __get(pInfo, elcp_table_cell_span * sizeof(size_t)) - 1;
            if (rcTmp.right <= 0)
            {
                rcTmp.right = rcTmp.left;
            }
            else
            {
                rcTmp.right = rcTmp.left + rcTmp.right;
            }
            rcTmp.bottom = __get(pInfo, elcp_table_row_span * sizeof(size_t)) - 1;
            if (rcTmp.bottom <= 0)
            {
                rcTmp.bottom = rcTmp.top;
            }
            else
            {
                rcTmp.bottom = rcTmp.top + rcTmp.bottom;
            }
            if (rcTmp.left <= 0)
            {
                rcTmp.left = 1;
            }
            if (rcTmp.top <= 0)
            {
                rcTmp.top = 1;
            }
            if (rcTmp.left > (LONG)arrayCellsSize)
            {
                rcTmp.left = (LONG)arrayCellsSize;
            }
            if (rcTmp.top > (LONG)arrayRowsSize)
            {
                rcTmp.top = (LONG)arrayRowsSize;
            }
            if (rcTmp.right < rcTmp.left)
            {
                rcTmp.right = rcTmp.left;
            }
            if (rcTmp.bottom < rcTmp.top)
            {
                rcTmp.bottom = rcTmp.top;
            }
            if (rcTmp.right > (LONG)arrayCellsSize)
            {
                rcTmp.right = (LONG)arrayCellsSize;
            }
            if (rcTmp.bottom > (LONG)arrayRowsSize)
            {
                rcTmp.bottom = (LONG)arrayRowsSize;
            }
            rcClient.left = aRects[rcTmp.top - 1][rcTmp.left - 1].left;
            rcClient.top = aRects[rcTmp.top - 1][rcTmp.left - 1].top;
            rcClient.right = aRects[rcTmp.bottom - 1][rcTmp.right - 1].right;
            rcClient.bottom = aRects[rcTmp.bottom - 1][rcTmp.right - 1].bottom;
            layout_move_margin(objChild, rcClient, (LPVOID)((size_t)pInfo - 4 * sizeof(size_t)), 15, 0);
        }
    }
    return 0;
}

LRESULT HHBUI::UILayout::layout_relative_proc(INT nEvent, WPARAM wParam, LPARAM lParam)
{
    if (nEvent == ELN_GETPROPSCOUNT)
    {
        return 0;
    }
    else if (nEvent == ELN_GETCHILDPROPCOUNT)
    {
        return 10;
    }
    else if (nEvent == ELN_CHECKCHILDPROPVALUE)
    {
        auto objChild = (UIControl*)lParam;
        if (objChild && GET_X_LPARAM(wParam) > 0)
        {
            LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(GET_X_LPARAM(wParam));
            auto parent = __get(pInfo, 0);
            INT dwPropID = GET_Y_LPARAM(wParam);
            LPVOID pInfoOther = layout_get_child(objChild);
            BOOL nRet = FALSE;

            if (parent && dwPropID > 0 && pInfoOther != 0)
            {
                if (dwPropID == elcp_relative_left_of)
                {
                    nRet = __get(pInfoOther, elcp_relative_right_of * sizeof(size_t)) != parent;
                }
                else if (dwPropID == elcp_relative_right_of)
                {
                    nRet = __get(pInfoOther, elcp_relative_left_of * sizeof(size_t)) != parent;
                }
                else if (dwPropID == elcp_relative_top_of)
                {
                    nRet = __get(pInfoOther, elcp_relative_bottom_of * sizeof(size_t)) != parent;
                }
                else if (dwPropID == elcp_relative_bottom_of)
                {
                    nRet = __get(pInfoOther, elcp_relative_top_of * sizeof(size_t)) != parent;
                }
                else
                {

                    nRet = __get(pInfoOther, dwPropID * sizeof(size_t)) != parent;
                }
            }
            return nRet == TRUE ? FALSE : TRUE;
        }
    }
    else if (nEvent == ELN_UPDATE)
    {
        layout_relative_update(lParam);
    }
    return 0;
}

LRESULT HHBUI::UILayout::layout_absolute_proc(INT nEvent, WPARAM wParam, LPARAM lParam)
{
    if (nEvent == ELN_GETPROPSCOUNT)
    {
        return 0;
    }
    else if (nEvent == ELN_GETCHILDPROPCOUNT)
    {
        /*layout_left="10/10%"
            layout_top="10/10%"
            layout_right="10/10%"
            layout_bottom="10/10%"
            layout_width="10/10%"
            layout_height="10/10%"
            layout_offsetH="10/10%/10*"
            layout_offsetV="10/10%/10*"*/
        return 16;
    }
    else if (nEvent == ELN_UPDATE)
    {
        INT offsetof = 0;
        ExRectF rcClient{};
        if (l_data.hBind->m_UIView)
        {
            auto objChild = (UIControl*)l_data.hBind->m_UIView;
            objChild->GetRect(rcClient, grt_client);
        }
        else
        {
            auto objChild = (UIWnd*)l_data.hBind->m_UIWindow;
            objChild->GetClientRect(rcClient);
            if (objChild->m_data.size_tmp == 0)
                offsetof = 10;
        }
        LONG_PTR* pInfoPtr = reinterpret_cast<LONG_PTR*>(l_data.lpLayoutInfo);
        rcClient.left += *(pInfoPtr + elp_padding_left);
        rcClient.top += *(pInfoPtr + elp_padding_top);
        rcClient.right -= *(pInfoPtr + elp_padding_right);
        rcClient.bottom -= *(pInfoPtr + elp_padding_bottom);

        SIZE szClient{ 0 };
        szClient.cx = rcClient.right - rcClient.left;
        szClient.cy = rcClient.bottom - rcClient.top;
        size_t arraySize = l_data.hArrChildrenInfo->size();
        for (size_t i = 1; i <= arraySize; i++)
        {
            LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
            auto parent = (UIControl*)__get(pInfo, 0);
            if (!parent)
                continue;
            ExRectF rcTmp{};
            parent->GetRect(rcTmp);
            INT ary1 = 0;
            INT ary2 = 0;
            INT ary3 = 0;
            INT ary4 = 0;
            INT nType = __get(pInfo, elcp_absolute_width_type * sizeof(size_t));
            INT tmp = __get(pInfo, elcp_absolute_width * sizeof(size_t));
            SIZE szObj{ 0 };
            if (nType == elcp_absolute_type_ps)
            {
                szObj.cx = (FLOAT)tmp / 100 * szClient.cx;
            }
            else if (nType == elcp_absolute_type_px)
            {
                szObj.cx = tmp;
            }
            else
            {
                szObj.cx = rcTmp.right - rcTmp.left;
            }
            nType = __get(pInfo, elcp_absolute_height_type * sizeof(size_t));
            tmp = __get(pInfo, elcp_absolute_height * sizeof(size_t));
            if (nType == elcp_absolute_type_ps)
            {
                szObj.cy = (FLOAT)tmp / 100 * szClient.cy;
            }
            else if (nType == elcp_absolute_type_px)
            {
                szObj.cy = tmp;
            }
            else
            {
                szObj.cy = rcTmp.bottom - rcTmp.top;
            }

            ExRectF rcObj{};
            for (INT index = 0; index < 5; index++)
            {
                if (ary1 == 0)
                {
                    ary1 = 1;
                    nType = __get(pInfo, elcp_absolute_left_type * sizeof(size_t));
                    tmp = __get(pInfo, elcp_absolute_left * sizeof(size_t)) - offsetof;
                    if (nType == elcp_absolute_type_ps)
                    {
                        rcObj.left = rcClient.left + (FLOAT)tmp / 100 * szClient.cx;
                    }
                    else if (nType == elcp_absolute_type_px)
                    {
                        rcObj.left = rcClient.left + tmp;
                    }
                    else if (ary3 == 1)
                    {
                        rcObj.left = rcObj.right - szObj.cx;
                    }
                    else
                    {
                        ary1 = 0;
                    }
                }
                if (ary2 == 0)
                {
                    ary2 = 1;
                    nType = __get(pInfo, elcp_absolute_top_type * sizeof(size_t));
                    tmp = __get(pInfo, elcp_absolute_top * sizeof(size_t)) - offsetof;
                    if (nType == elcp_absolute_type_ps)
                    {
                        rcObj.top = rcClient.top + (FLOAT)tmp / 100 * szClient.cy;
                    }
                    else if (nType == elcp_absolute_type_px)
                    {
                        rcObj.top = rcClient.top + tmp;
                    }
                    else if (ary4 == 1)
                    {
                        rcObj.top = rcObj.bottom - szObj.cy;
                    }
                    else
                    {
                        ary2 = 0;
                    }
                }
                if (ary3 == 0)
                {
                    ary3 = 1;
                    nType = __get(pInfo, elcp_absolute_right_type * sizeof(size_t));
                    tmp = __get(pInfo, elcp_absolute_right * sizeof(size_t)) - offsetof;
                    if (nType == elcp_absolute_type_ps)
                    {
                        rcObj.right = rcClient.right - (FLOAT)tmp / 100 * szClient.cx;
                    }
                    else if (nType == elcp_absolute_type_px)
                    {
                        rcObj.right = rcClient.right - tmp;
                    }
                    else if (ary1 == 1)
                    {
                        rcObj.right = rcObj.left + szObj.cx;
                    }
                    else
                    {
                        ary3 = 0;
                    }
                }
                if (ary4 == 0)
                {
                    ary4 = 1;
                    nType = __get(pInfo, elcp_absolute_bottom_type * sizeof(size_t));
                    tmp = __get(pInfo, elcp_absolute_bottom * sizeof(size_t)) - offsetof;
                    if (nType == elcp_absolute_type_ps)
                    {
                        rcObj.bottom = rcClient.bottom - (FLOAT)tmp / 100 * szClient.cy;
                    }
                    else if (nType == elcp_absolute_type_px)
                    {
                        rcObj.bottom = rcClient.bottom - tmp;
                    }
                    else if (ary2 == 1)
                    {
                        rcObj.bottom = rcObj.top + szObj.cy;
                    }
                    else
                    {
                        ary4 = 0;
                    }
                }
                if (ary1 == 1 && ary2 == 1 && ary3 == 1 && ary4 == 1)
                {
                    break;
                }
            }
            if (ary1 == 0)
            {
                rcObj.left = rcTmp.left;
            }
            if (ary2 == 0)
            {
                rcObj.top = rcTmp.top;
            }
            if (ary3 == 0)
            {
                rcObj.right = rcTmp.right;
            }
            if (ary4 == 0)
            {
                rcObj.bottom = rcTmp.bottom;
            }
            nType = __get(pInfo, elcp_absolute_offset_h_type * sizeof(size_t));
            tmp = __get(pInfo, elcp_absolute_offset_h * sizeof(size_t));
            if (nType == elcp_absolute_type_ps)
            {
                rcObj.left = rcObj.left + (FLOAT)tmp / 100 * szClient.cx;
                rcObj.right = rcObj.right + (FLOAT)tmp / 100 * szClient.cx;
            }
            else if (nType == elcp_absolute_type_px)
            {
                rcObj.left = rcObj.left + tmp;
                rcObj.right = rcObj.right + tmp;
            }
            else if (nType == elcp_absolute_type_objps)
            {
                rcObj.left = rcObj.left + (FLOAT)tmp / 100 * szObj.cx;
                rcObj.right = rcObj.right + (FLOAT)tmp / 100 * szObj.cx;
            }

            nType = __get(pInfo, elcp_absolute_offset_v_type * sizeof(size_t));
            tmp = __get(pInfo, elcp_absolute_offset_v * sizeof(size_t));
            if (nType == elcp_absolute_type_ps)
            {
                rcObj.top = rcObj.top + (FLOAT)tmp / 100 * szClient.cy;
                rcObj.bottom = rcObj.bottom + (FLOAT)tmp / 100 * szClient.cy;
            }
            else if (nType == elcp_absolute_type_px)
            {
                rcObj.top = rcObj.top + tmp;
                rcObj.bottom = rcObj.bottom + tmp;
            }
            else if (nType == elcp_absolute_type_objps)
            {
                rcObj.top = rcObj.top + (FLOAT)tmp / 100 * szObj.cy;
                rcObj.bottom = rcObj.bottom + (FLOAT)tmp / 100 * szObj.cy;
            }
          
            layout_move_margin(parent, rcObj, (LPVOID)((size_t)pInfo - 4 * sizeof(size_t)), 15, 0);
        }
    }
    else if (nEvent == ELN_CHECKCHILDPROPVALUE)
    {
        LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(GET_X_LPARAM(wParam));
        INT nType = GET_Y_LPARAM(wParam);
        if (nType % 2 == 1 && nType >= elcp_absolute_left && nType <= elcp_absolute_offset_v)
        {
            if (__get(pInfo, (nType + 1) * sizeof(size_t)) == elcp_absolute_type_unknown)
            {
                __set(pInfo, (nType + 1) * sizeof(size_t), elcp_absolute_type_px);
            }
        }
    }
    return 0;
}

void HHBUI::UILayout::layout_move_margin(LPVOID parent, ExRectF& lpObjRc, LPVOID lpMargin, INT dwLockFlags, INT dwOrgFlags)
{
    auto objChild = (UIControl*)parent;
    if (!objChild)
        return;
    ExRectF rcObj = lpObjRc;
    RECT rcMargin{0};
    rcMargin.left = __get(lpMargin, 3 * sizeof(size_t));
    rcMargin.top = __get(lpMargin, 2 * sizeof(size_t));
    rcMargin.right = __get(lpMargin, sizeof(size_t));
    rcMargin.bottom = __get(lpMargin, 0);

    rcObj.left += rcMargin.left;
    if ((dwLockFlags & 1) == 0)
    {
        rcObj.right += rcMargin.left;
        lpObjRc.right += rcMargin.left;
    }

    rcObj.top += rcMargin.top;

    if ((dwLockFlags & 2) == 0)
    {
        rcObj.bottom += rcMargin.top;
        lpObjRc.bottom += rcMargin.top;
    }

    if ((dwLockFlags & 4) == 4)
    {
        rcObj.right -= rcMargin.right;
    }
    else
    {
        lpObjRc.right += rcMargin.right;
    }

    if ((dwLockFlags & 8) == 8)
    {
        rcObj.bottom -= rcMargin.bottom;
    }
    else
    {
        lpObjRc.bottom += rcMargin.bottom;
    }

    if (dwOrgFlags == 15)
    {
        return;
    }

    rcObj.right -= rcObj.left;
    rcObj.bottom -= rcObj.top;
    if ((dwOrgFlags & 1) != 0)
    {
        rcObj.left = CW_USEDEFAULT;
    }
    if ((dwOrgFlags & 2) != 0)
    {
        rcObj.top = CW_USEDEFAULT;
    }
    if ((dwOrgFlags & 4) != 0)
    {
        rcObj.right = CW_USEDEFAULT;
    }
    if ((dwOrgFlags & 8) != 0)
    {
        rcObj.bottom = CW_USEDEFAULT;
    }
    objChild->Move(rcObj.left, rcObj.top, rcObj.right, rcObj.bottom);
}

void HHBUI::UILayout::layout_relative_update(LPARAM lParam)
{
    std::map<LPVOID, LPVOID> hHashPosInfos;
    ExRectF rcClient{};
    if (l_data.hBind->m_UIView)
    {
        auto objChild = (UIControl*)l_data.hBind->m_UIView;
        objChild->GetRect(rcClient, grt_client);
    }
    else
    {
        auto objChild = (UIWnd*)l_data.hBind->m_UIWindow;
        objChild->GetClientRect(rcClient);
    }

    LONG_PTR* pInfoPtr = reinterpret_cast<LONG_PTR*>(l_data.lpLayoutInfo);
    rcClient.left += *(pInfoPtr + elp_padding_left);
    rcClient.top += *(pInfoPtr + elp_padding_top);
    rcClient.right -= *(pInfoPtr + elp_padding_right);
    rcClient.bottom -= *(pInfoPtr + elp_padding_bottom);
    std::vector<size_t> Infos(3);
    size_t arraySize = l_data.hArrChildrenInfo->size();
    for (size_t i = 1; i <= arraySize; i++)
    {
        LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
        auto parent = (UIControl*)__get(pInfo, 0);
        if (!parent)
            continue;
        ExRectF rcObj{};
        parent->GetRect(rcObj);

        LPVOID pPosInfo = ExMemAlloc(4 * 3 * sizeof(size_t) + sizeof(size_t) + sizeof(size_t)); //多一个放pInfo和orgFlags
        //[是否确定/类型/句柄或坐标]*4个边界,确定以后一定是坐标
        //处理无法确定四边的情况
        BOOL fNoPosInfoH = TRUE;
        for (INT j = 1; j <= 10; j += 2)
        {
            if (__get(pInfo, j * sizeof(size_t)) != 0)
            {
                fNoPosInfoH = FALSE;
                break;
            }
        }
        BOOL fNoPosInfoV = TRUE;
        for (INT j = 2; j <= 10; j += 2)
        {
            if (__get(pInfo, j * sizeof(size_t)) != 0)
            {
                fNoPosInfoV = FALSE;
                break;
            }
        }

        if (fNoPosInfoH)
        {
            __set(pPosInfo, 0, 1);
            __set(pPosInfo, sizeof(size_t), 0);
            __set(pPosInfo, 2 * sizeof(size_t), rcObj.left + __gets(pInfo, elcp_margin_left));
            __set((LPVOID)((size_t)pPosInfo + 6 * sizeof(size_t)), 0, 1);
            __set((LPVOID)((size_t)pPosInfo + 6 * sizeof(size_t)), sizeof(size_t), 0);
            __set((LPVOID)((size_t)pPosInfo + 6 * sizeof(size_t)), 2 * sizeof(size_t), rcObj.right + __gets(pInfo, elcp_margin_left) + __gets(pInfo, elcp_margin_right));
        }
        if (fNoPosInfoV)
        {

            __set((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t)), 0, 1);
            __set((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t)), sizeof(size_t), 0);
            __set((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t)), 2 * sizeof(size_t), rcObj.top + __gets(pInfo, elcp_margin_top));

            __set((LPVOID)((size_t)pPosInfo + 9 * sizeof(size_t)), 0, 1);
            __set((LPVOID)((size_t)pPosInfo + 9 * sizeof(size_t)), sizeof(size_t), 0);
            __set((LPVOID)((size_t)pPosInfo + 9 * sizeof(size_t)), 2 * sizeof(size_t), rcObj.bottom + __gets(pInfo, elcp_margin_top) + __gets(pInfo, elcp_margin_bottom));
        }
        //rcObj暂时按左顶宽高处理
        rcObj.right -= rcObj.left;
        rcObj.bottom -= rcObj.top;
        INT orgFlags = 0;
        //预处理边界定位信息

        if (fNoPosInfoH == FALSE)
        { //左边界处理
            Infos[0] = 1;
            if (__get(pInfo, elcp_relative_left_align_of * sizeof(size_t)) != 0)
            {
                size_t dwValue = __get(pInfo, elcp_relative_left_align_of * sizeof(size_t));
                Infos[1] = elcp_relative_left_align_of;
                if (dwValue == -1) //相对父
                {
                    Infos[2] = rcClient.left + __gets(pInfo, elcp_margin_left);
                }
                else if (dwValue != 0 && ((UIControl*)dwValue)->GetParent()->m_UIView == parent || l_data.hBind->m_UIWindow == ((UIControl*)dwValue)->GetUIWnd()) //同层组件
                {
                    Infos[0] = 0;
                    Infos[2] = dwValue;
                }
                else
                {
                    Infos[0] = 0;
                    Infos[1] = 0;
                    Infos[2] = rcObj.right;
                }
            }
            else if (__get(pInfo, elcp_relative_center_parent_h * sizeof(size_t)) != 0)
            {
                Infos[1] = elcp_relative_center_parent_h;
                Infos[2] = rcClient.left + abs(rcClient.right - rcClient.left - rcObj.right) / 2 + __gets(pInfo, elcp_margin_left);
                orgFlags = orgFlags | 4;
            }
            else
            {
                size_t dwValue = __get(pInfo, elcp_relative_right_of * sizeof(size_t));
                if (dwValue != 0 && ((UIControl*)dwValue)->GetParent()->m_UIView == parent || l_data.hBind->m_UIWindow == ((UIControl*)dwValue)->GetUIWnd()) //同层组件
                {
                    Infos[0] = 0;
                    Infos[1] = elcp_relative_right_of;
                    Infos[2] = dwValue;
                }
                else
                {
                    Infos[0] = 0;
                    Infos[1] = 0;
                    Infos[2] = rcObj.right;
                }
            }
            __set(pPosInfo, 0, Infos[0]);
            __set(pPosInfo, sizeof(size_t), Infos[1]);
            __set(pPosInfo, 2 * sizeof(size_t), Infos[2]);
            //右边界处理
            Infos[0] = 1;

            if (__get(pInfo, elcp_relative_right_align_of * sizeof(size_t)) != 0)
            {
                size_t dwValue = __get(pInfo, elcp_relative_right_align_of * sizeof(size_t));
                Infos[1] = elcp_relative_right_align_of;
                if (dwValue == -1) //相对父
                {
                    Infos[2] = rcClient.right + __gets(pInfo, elcp_margin_left);
                }
                else if (dwValue != 0 && ((UIControl*)dwValue)->GetParent()->m_UIView == parent || l_data.hBind->m_UIWindow == ((UIControl*)dwValue)->GetUIWnd())//同层组件
                {
                    Infos[0] = 0;
                    Infos[2] = dwValue;
                }
                else
                {
                    Infos[0] = 0;
                    Infos[1] = 0;
                    Infos[2] = rcObj.right;
                }
            }
            else if (__get(pInfo, elcp_relative_center_parent_h * sizeof(size_t)) != 0)
            {
                Infos[1] = elcp_relative_center_parent_h;
                Infos[2] = rcClient.left + abs(rcClient.right - rcClient.left + rcObj.right) / 2 + __gets(pInfo, elcp_margin_left) + __gets(pInfo, elcp_margin_right);
                orgFlags = orgFlags | 4;
            }
            else
            {
                size_t dwValue = __get(pInfo, elcp_relative_left_of * sizeof(size_t));
                if (dwValue != 0 && ((UIControl*)dwValue)->GetParent()->m_UIView == parent || l_data.hBind->m_UIWindow == ((UIControl*)dwValue)->GetUIWnd())//同层组件
                {
                    Infos[0] = 0;
                    Infos[1] = elcp_relative_left_of;
                    Infos[2] = dwValue;
                }
                else
                {
                    Infos[0] = 0;
                    Infos[1] = 0;
                    Infos[2] = rcObj.right;
                }
            }
            __set((LPVOID)((size_t)pPosInfo + 2 * 3 * sizeof(size_t)), 0, Infos[0]);
            __set((LPVOID)((size_t)pPosInfo + 2 * 3 * sizeof(size_t)), sizeof(size_t), Infos[1]);
            __set((LPVOID)((size_t)pPosInfo + 2 * 3 * sizeof(size_t)), 2 * sizeof(size_t), Infos[2]);
        }
        if (fNoPosInfoV == FALSE)
        {
            //上边界处理
            Infos[0] = 1;
            if (__get(pInfo, elcp_relative_top_align_of * sizeof(size_t)) != 0)
            {
                size_t dwValue = __get(pInfo, elcp_relative_top_align_of * sizeof(size_t));
                Infos[1] = elcp_relative_top_align_of;
                if (dwValue == -1) //相对父
                {
                    Infos[2] = rcClient.top;
                }
                else if (dwValue != 0 && ((UIControl*)dwValue)->GetParent()->m_UIView == parent || l_data.hBind->m_UIWindow == ((UIControl*)dwValue)->GetUIWnd()) //同层组件
                {
                    Infos[0] = 0;
                    Infos[2] = dwValue;
                }
                else
                {
                    Infos[0] = 0;
                    Infos[1] = 0;
                    Infos[2] = rcObj.bottom;
                }
            }
            else if (__get(pInfo, elcp_relative_center_parent_v * sizeof(size_t)) != 0)
            {
                Infos[1] = elcp_relative_center_parent_v;
                Infos[2] = rcClient.top + abs(rcClient.bottom - rcClient.top - rcObj.bottom) / 2 + __gets(pInfo, elcp_margin_top);
                orgFlags = orgFlags | 8;
            }
            else
            {
                size_t dwValue = __get(pInfo, elcp_relative_bottom_of * sizeof(size_t));
                if (dwValue != 0 && ((UIControl*)dwValue)->GetParent()->m_UIView == parent || l_data.hBind->m_UIWindow == ((UIControl*)dwValue)->GetUIWnd()) //同层组件
                {

                    Infos[0] = 0;
                    Infos[1] = elcp_relative_bottom_of;
                    Infos[2] = dwValue;
                }
                else
                {
                    Infos[0] = 0;
                    Infos[1] = 0;
                    Infos[2] = rcObj.bottom;
                }
            }
            __set((LPVOID)((size_t)pPosInfo + 1 * 3 * sizeof(size_t)), 0, Infos[0]);
            __set((LPVOID)((size_t)pPosInfo + 1 * 3 * sizeof(size_t)), sizeof(size_t), Infos[1]);
            __set((LPVOID)((size_t)pPosInfo + 1 * 3 * sizeof(size_t)), 2 * sizeof(size_t), Infos[2]);

            //下边界处理
            Infos[0] = 1;
            if (__get(pInfo, elcp_relative_bottom_align_of * sizeof(size_t)) != 0)
            {
                size_t dwValue = __get(pInfo, elcp_relative_bottom_align_of * sizeof(size_t));
                Infos[1] = elcp_relative_bottom_align_of;
                if (dwValue == -1) //相对父
                {
                    Infos[2] = rcClient.bottom + __gets(pInfo, elcp_margin_top) - __gets(pInfo, elcp_margin_bottom);
                }
                else if (dwValue != 0 && ((UIControl*)dwValue)->GetParent()->m_UIView == parent || l_data.hBind->m_UIWindow == ((UIControl*)dwValue)->GetUIWnd())//同层组件
                {
                    Infos[0] = 0;
                    Infos[2] = dwValue;
                }
                else
                {
                    Infos[0] = 0;
                    Infos[1] = 0;
                    Infos[2] = rcObj.bottom;
                }
            }
            else if (__get(pInfo, elcp_relative_center_parent_v * sizeof(size_t)) != 0)
            {
                Infos[1] = elcp_relative_center_parent_v;
                Infos[2] = rcClient.top + abs(rcClient.bottom - rcClient.top + rcObj.bottom) / 2 + __gets(pInfo, elcp_margin_top) + __gets(pInfo, elcp_margin_bottom);
                orgFlags = orgFlags | 8;
            }
            else
            {
                size_t dwValue = __get(pInfo, elcp_relative_top_of * sizeof(size_t));
                if (dwValue != 0 && ((UIControl*)dwValue)->GetParent()->m_UIView == parent || l_data.hBind->m_UIWindow == ((UIControl*)dwValue)->GetUIWnd())//同层组件
                {
                    Infos[0] = 0;
                    Infos[1] = elcp_relative_top_of;
                    Infos[2] = dwValue;
                }
                else
                {
                    Infos[0] = 0;
                    Infos[1] = 0;
                    Infos[2] = rcObj.bottom;
                }
            }
            __set((LPVOID)((size_t)pPosInfo + 3 * 3 * sizeof(size_t)), 0, Infos[0]);
            __set((LPVOID)((size_t)pPosInfo + 3 * 3 * sizeof(size_t)), sizeof(size_t), Infos[1]);
            __set((LPVOID)((size_t)pPosInfo + 3 * 3 * sizeof(size_t)), 2 * sizeof(size_t), Infos[2]);
        }
        __set(pPosInfo, 12 * sizeof(size_t), (size_t)pInfo);
        __set(pPosInfo, 12 * sizeof(size_t) + sizeof(size_t), orgFlags);
        hHashPosInfos.insert(std::make_pair(parent, pPosInfo));
    }

    for (INT index = 0; index < 5; index++)
    {
        size_t cNoLockObj = hHashPosInfos.size();
        for (size_t i = 1; i <= arraySize; i++)
        {
            LPVOID pInfo = (LPVOID)l_data.hArrChildrenInfo->get(i);
            LPVOID pPosInfo = nullptr;
            auto p = hHashPosInfos.find((LPVOID)__get(pInfo, 0));
            if (p != hHashPosInfos.end()) {
                pPosInfo = p->second;
            }
            if (pPosInfo)
            {
                //找能确定的点
                if (__get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t) * 0), 0) == 0)
                {
                    Infos[0] = __get(pPosInfo, 0);
                    Infos[1] = __get(pPosInfo, sizeof(size_t));
                    Infos[2] = __get(pPosInfo, 2 * sizeof(size_t));

                    if (Infos[1] == elcp_relative_right_of)
                    {
                        LPVOID pInfoOther = nullptr;
                        auto ss = hHashPosInfos.find((LPVOID)Infos[2]);
                        if (ss != hHashPosInfos.end()) {
                            pInfoOther = ss->second;
                        }
                        if (pInfoOther)
                        {
                            if (__get(pInfoOther, 2 * 3 * sizeof(size_t)) != 0) //已经锁定了
                            {
                                Infos[2] = __get(pInfoOther, 2 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) + __gets(pInfo, elcp_margin_left);
                                Infos[0] = 1;
                            }
                        }
                    }
                    else if (Infos[1] == elcp_relative_left_align_of)
                    {
                        LPVOID pInfoOther = nullptr;
                        auto ss = hHashPosInfos.find((LPVOID)Infos[2]);
                        if (ss != hHashPosInfos.end()) {
                            pInfoOther = ss->second;
                        }
                        if (pInfoOther)
                        {
                            if (__get(pInfoOther, 0 * 3 * sizeof(size_t)) != 0) //已经锁定了
                            {
                                Infos[2] = __get(pInfoOther, 0 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) + __gets(pInfo, elcp_margin_left);
                                Infos[0] = 1;
                            }
                        }
                    }
                    else if (Infos[1] == 0)
                    {
                        if (__get(pPosInfo, 2 * 3 * sizeof(size_t)) != 0) //已经锁定了
                        {
                            Infos[2] = __get(pPosInfo, 2 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) - Infos[2] - __gets(pInfo, elcp_margin_right) - __gets(pInfo, elcp_margin_left);
                            Infos[0] = 1;
                        }
                    }
                    __set(pPosInfo, 0, Infos[0]);
                    __set(pPosInfo, sizeof(size_t), Infos[1]);
                    __set(pPosInfo, 2 * sizeof(size_t), Infos[2]);
                }
                if (__get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t) * 1), 0) == 0)
                {
                    Infos[0] = __get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t)), 0);
                    Infos[1] = __get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t)), sizeof(size_t));
                    Infos[2] = __get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t)), 2 * sizeof(size_t));

                    if (Infos[1] == elcp_relative_bottom_of)
                    {
                        LPVOID pInfoOther = nullptr;
                        auto ss = hHashPosInfos.find((LPVOID)Infos[2]);
                        if (ss != hHashPosInfos.end()) {
                            pInfoOther = ss->second;
                        }
                        if (pInfoOther)
                        {
                            if (__get(pInfoOther, 3 * 3 * sizeof(size_t)) != 0) //已经锁定了
                            {
                                Infos[2] = __get(pInfoOther, 3 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) + __gets(pInfo, elcp_margin_top);
                                Infos[0] = 1;
                            }
                        }
                    }
                    else if (Infos[1] == elcp_relative_top_align_of)
                    {
                        LPVOID pInfoOther = nullptr;
                        auto ss = hHashPosInfos.find((LPVOID)Infos[2]);
                        if (ss != hHashPosInfos.end()) {
                            pInfoOther = ss->second;
                        }
                        if (pInfoOther)
                        {
                            if (__get(pInfoOther, 1 * 3 * sizeof(size_t)) != 0) //已经锁定了
                            {
                                Infos[2] = __get(pInfoOther, 1 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) + __gets(pInfo, elcp_margin_top);
                                Infos[0] = 1;
                            }
                        }
                    }
                    else if (Infos[1] == 0)
                    {
                        if (__get(pPosInfo, 3 * 3 * sizeof(size_t)) != 0) //已经锁定了
                        {
                            Infos[2] = __get(pPosInfo, 3 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) - Infos[2] - __gets(pInfo, elcp_margin_bottom) - __gets(pInfo, elcp_margin_top);
                            Infos[0] = 1;
                        }
                    }
                    __set((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t)), 0, Infos[0]);
                    __set((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t)), sizeof(size_t), Infos[1]);
                    __set((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t)), 2 * sizeof(size_t), Infos[2]);
                }
                if (__get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t) * 2), 0) == 0)
                {
                    Infos[0] = __get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t) * 2), 0);
                    Infos[1] = __get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t) * 2), sizeof(size_t));
                    Infos[2] = __get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t) * 2), 2 * sizeof(size_t));

                    if (Infos[1] == elcp_relative_left_of)
                    {
                        LPVOID pInfoOther = nullptr;
                        auto ss = hHashPosInfos.find((LPVOID)Infos[2]);
                        if (ss != hHashPosInfos.end()) {
                            pInfoOther = ss->second;
                        }
                        if (pInfoOther)
                        {
                            if (__get(pInfoOther, 0 * 3 * sizeof(size_t)) != 0) //已经锁定了
                            {
                                Infos[2] = __get(pInfoOther, 0 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) + __gets(pInfo, elcp_margin_right);
                                Infos[0] = 1;
                            }
                        }
                    }
                    else if (Infos[1] == elcp_relative_right_align_of)
                    {

                        LPVOID pInfoOther = nullptr;
                        auto ss = hHashPosInfos.find((LPVOID)Infos[2]);
                        if (ss != hHashPosInfos.end()) {
                            pInfoOther = ss->second;
                        }
                        if (pInfoOther)
                        {
                            if (__get(pInfoOther, 2 * 3 * sizeof(size_t)) != 0) //已经锁定了
                            {
                                Infos[2] = __get(pInfoOther, 2 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) + __gets(pInfo, elcp_margin_right);
                                Infos[0] = 1;
                            }
                        }
                    }
                    else if (Infos[1] == 0)
                    {
                        if (__get(pPosInfo, 0 * 3 * sizeof(size_t)) != 0) //已经锁定了
                        {
                            Infos[2] = __get(pPosInfo, 0 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) + Infos[2] + __gets(pInfo, elcp_margin_left) + __gets(pInfo, elcp_margin_right);
                            Infos[0] = 1;
                        }
                    }
                    __set((LPVOID)((size_t)pPosInfo + 2 * 3 * sizeof(size_t)), 0, Infos[0]);
                    __set((LPVOID)((size_t)pPosInfo + 2 * 3 * sizeof(size_t)), sizeof(size_t), Infos[1]);
                    __set((LPVOID)((size_t)pPosInfo + 2 * 3 * sizeof(size_t)), 2 * sizeof(size_t), Infos[2]);
                }
                if (__get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t) * 3), 0) == 0)
                {
                    Infos[0] = __get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t) * 3), 0);
                    Infos[1] = __get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t) * 3), sizeof(size_t));
                    Infos[2] = __get((LPVOID)((size_t)pPosInfo + 3 * sizeof(size_t) * 3), 2 * sizeof(size_t));

                    if (Infos[1] == elcp_relative_top_of)
                    {
                        LPVOID pInfoOther = nullptr;
                        auto ss = hHashPosInfos.find((LPVOID)Infos[2]);
                        if (ss != hHashPosInfos.end()) {
                            pInfoOther = ss->second;
                        }
                        if (pInfoOther)
                        {
                            if (__get(pInfoOther, 1 * 3 * sizeof(size_t)) != 0) //已经锁定了
                            {
                                Infos[2] = __get(pInfoOther, 1 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) + __gets(pInfo, elcp_margin_bottom);
                                Infos[0] = 1;
                            }
                        }
                    }
                    else if (Infos[1] == elcp_relative_bottom_align_of)
                    {
                        LPVOID pInfoOther = nullptr;
                        auto ss = hHashPosInfos.find((LPVOID)Infos[2]);
                        if (ss != hHashPosInfos.end()) {
                            pInfoOther = ss->second;
                        }
                        if (pInfoOther)
                        {
                            if (__get(pInfoOther, 3 * 3 * sizeof(size_t)) != 0) //已经锁定了
                            {
                                Infos[2] = __get(pInfoOther, 3 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) + __gets(pInfo, elcp_margin_bottom);
                                Infos[0] = 1;
                            }
                        }
                    }
                    else if (Infos[1] == 0)
                    {
                        if (__get(pPosInfo, 1 * 3 * sizeof(size_t)) != 0) //已经锁定了
                        {
                            Infos[2] = __get(pPosInfo, 1 * 3 * sizeof(size_t) + 2 * sizeof(size_t)) + Infos[2] + __gets(pInfo, elcp_margin_top) + __gets(pInfo, elcp_margin_bottom);
                            Infos[0] = 1;
                        }
                    }
                    __set((LPVOID)((size_t)pPosInfo + 3 * 3 * sizeof(size_t)), 0, Infos[0]);
                    __set((LPVOID)((size_t)pPosInfo + 3 * 3 * sizeof(size_t)), sizeof(size_t), Infos[1]);
                    __set((LPVOID)((size_t)pPosInfo + 3 * 3 * sizeof(size_t)), 2 * sizeof(size_t), Infos[2]);
                }
                if (__get(pInfo, 0) != 0 && __get(pInfo, 3 * sizeof(size_t)) != 0 && __get(pInfo, 6 * sizeof(size_t)) != 0 && __get(pInfo, 9 * sizeof(size_t)) != 0) //已经确定整个组件了
                {
                    cNoLockObj = cNoLockObj - 1;
                }
            }
            else
            {
                cNoLockObj = cNoLockObj - 1;
            }
        }
        if (cNoLockObj <= 0)
        {
            break;
        }
    }

    for (const auto& pair : hHashPosInfos) {
        auto parent = (UIControl*)pair.first;

        LPVOID pPosInfo = pair.second;
        LPVOID pInfo = (LPVOID)__get(pPosInfo, 12 * sizeof(size_t));
        INT orgFlags = __get(pPosInfo, 12 * sizeof(size_t) + sizeof(size_t));
        ExRectF rcObj{};
        parent->GetRect(rcObj);

        ExRectF rcTmp{};
        if (__get((LPVOID)((size_t)pPosInfo + 0 * 3 * sizeof(size_t)), 0) == 0)
        {
            rcTmp.left = rcObj.left;
        }
        else
        {
            rcTmp.left = __get((LPVOID)((size_t)pPosInfo + 0 * 3 * sizeof(size_t)), 2 * sizeof(size_t));
        }
        if (__get((LPVOID)((size_t)pPosInfo + 1 * 3 * sizeof(size_t)), 0) == 0)
        {
            rcTmp.top = rcObj.top;
        }
        else
        {
            rcTmp.top = __get((LPVOID)((size_t)pPosInfo + 1 * 3 * sizeof(size_t)), 2 * sizeof(size_t));
        }
        if (__get((LPVOID)((size_t)pPosInfo + 2 * 3 * sizeof(size_t)), 0) == 0)
        {
            rcTmp.right = rcTmp.left + rcObj.right - rcObj.left;
        }
        else
        {
            rcTmp.right = __get((LPVOID)((size_t)pPosInfo + 2 * 3 * sizeof(size_t)), 2 * sizeof(size_t));
        }
        if (__get((LPVOID)((size_t)pPosInfo + 3 * 3 * sizeof(size_t)), 0) == 0)
        {
            rcTmp.bottom = rcTmp.top + rcObj.bottom - rcObj.top;
        }
        else
        {
            rcTmp.bottom = __get((LPVOID)((size_t)pPosInfo + 3 * 3 * sizeof(size_t)), 2 * sizeof(size_t));
        }

        layout_move_margin(parent, rcTmp, (LPVOID)((size_t)pInfo - 4 * sizeof(size_t)), 15, orgFlags);
    }
 
    hHashPosInfos.clear();
}

LPVOID HHBUI::UILayout::layout_get_child(LPVOID parent)
{
    auto objChild = (UIControl*)parent;
    LPVOID pInfo = nullptr;
    if (objChild->GetParent()->m_UIView == parent || l_data.hBind->m_UIWindow == objChild->GetUIWnd())
    {
        size_t nIndex = l_data.hArrChildrenInfo->emum(&_layout_enum_find_obj, (size_t)parent);
        if (nIndex != 0)
        {
            pInfo = (LPVOID)l_data.hArrChildrenInfo->get(nIndex);
        }
    }
    return pInfo;
}

void HHBUI::UILayout::layout_free_info(LPVOID pvItem)
{
    l_data.lpfnProc(ELN_UNINITCHILDPROPS, __get(pvItem, 0), (size_t)pvItem);
    ExMemFree((LPVOID)((size_t)pvItem - 4 * sizeof(size_t)));
}

