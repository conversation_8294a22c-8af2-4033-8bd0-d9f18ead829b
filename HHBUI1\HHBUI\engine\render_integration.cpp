/**
** =====================================================================================
**
**       文件名称: render_integration.cpp
**       创建时间: 2025-07-28
**       文件描述: 【HHBUI】渲染框架集成系统 - 高级渲染管线集成与协调框架 （实现文件）
**
**       主要功能:
**       - 高级渲染管线集成与协调实现
**       - 多渲染API统一管理与调度
**       - 渲染上下文扩展与增强功能
**       - 高性能渲染资源管理实现
**       - 渲染性能监控与调试集成
**       - 混合渲染模式协调管理
**       - 跨平台渲染抽象层实现
**
**       技术特性:
**       - 采用现代C++17标准与多渲染API
**       - COM接口规范与智能指针管理
**       - 异常安全保证与错误恢复机制
**       - 高性能渲染管线调度算法
**       - 多线程安全的资源管理实现
**       - 智能渲染状态缓存与优化
**       - 实时性能监控与调试诊断
**
**       更新记录:
**       2025-07-28 v1.0.0.0 : 1. 实现渲染框架集成系统
**                             2. 完成多渲染API统一管理
**                             3. 实现渲染上下文扩展
**                             4. 支持高性能资源管理
**                             5. 完成性能监控集成
**                             6. 集成混合渲染协调
**                             7. 确保跨平台兼容性
**
** =====================================================================================
**/

#include "pch.h"
#include "renderd2d.h"
#include "dx11_render_manager.h"
#include "render_profiler.h"
#include "gdi_plus_integration.h"
#include "common/Exception.h"
#include <vector>
#include <unordered_map>
#include <queue>
#include <memory>
#include <atomic>
#include <mutex>
#include <optional>
#include <string_view>
#include <algorithm>
#include <execution>
#include <numeric>
#include <chrono>
#include <sstream>
#include <iomanip>

namespace HHBUI
{
	// 全局性能监控器实例
	UIRenderProfiler* g_render_profiler = nullptr;
	UIRenderDebugger* g_render_debugger = nullptr;

	// UIDrawContext扩展实现
	HRESULT UIDrawContext::InitAdvancedRendering()
	{
		try
		{
			if (ToList.advanced_features_enabled)
				return S_OK;

			// 创建高级渲染管理器
			UIDx11RenderFactory factory;
			throw_if_failed(
				factory.CreateRenderManager(&ToList.advanced_render_manager),
				L"创建高级渲染管理器失败"
			);

			// 初始化渲染管理器
			throw_if_failed(
				ToList.advanced_render_manager->Initialize(RenderType::HYBRID),
				L"初始化高级渲染管理器失败"
			);

			// 创建子管理器
			auto* dx11_manager = static_cast<UIDx11RenderManager*>(ToList.advanced_render_manager);
			ToList.shader_manager = new UIShaderManager(
				dx11_manager->GetD3D11Device(), 
				dx11_manager->GetD3D11DeviceContext()
			);

			ToList.buffer_manager = new UIBufferManager(
				dx11_manager->GetD3D11Device(), 
				dx11_manager->GetD3D11DeviceContext()
			);

			ToList.input_layout_manager = new UIInputLayoutManager(
				dx11_manager->GetD3D11Device()
			);

			// 创建性能监控器
			g_render_profiler = new UIRenderProfiler();
			throw_if_failed(
				g_render_profiler->Initialize(
					dx11_manager->GetD3D11Device(), 
					dx11_manager->GetD3D11DeviceContext()
				),
				L"初始化性能监控器失败"
			);

			// 创建调试器
			g_render_debugger = new UIRenderDebugger();
			throw_if_failed(
				g_render_debugger->Initialize(
					dx11_manager->GetD3D11Device(), 
					dx11_manager->GetD3D11DeviceContext()
				),
				L"初始化渲染调试器失败"
			);

			// 添加基础性能计数器
			g_render_profiler->AddCounter("DrawCalls", ProfilerCounterType::DRAW_CALLS);
			g_render_profiler->AddCounter("Triangles", ProfilerCounterType::TRIANGLES);
			g_render_profiler->AddCounter("Vertices", ProfilerCounterType::VERTICES);
			g_render_profiler->AddCounter("TextureSwitches", ProfilerCounterType::TEXTURE_SWITCHES);
			g_render_profiler->AddCounter("ShaderSwitches", ProfilerCounterType::SHADER_SWITCHES);

			// 初始化GDI+集成
			g_gdi_plus_integration = new UIGdiPlusIntegration();
			if (dx11_manager->GetD2D1DeviceContext())
			{
				g_gdi_plus_integration->Initialize(dx11_manager->GetD2D1DeviceContext());
			}

			g_gdi_plus_resource_manager = new UIGdiPlusResourceManager();

			g_hybrid_render_coordinator = new UIHybridRenderCoordinator();
			if (ToList.d2d_dc && ToList.d2d_gdiInterop)
			{
				HDC gdi_dc = nullptr;
				if (SUCCEEDED(ToList.d2d_gdiInterop->GetDC(D2D1_DC_INITIALIZE_MODE_COPY, &gdi_dc)))
				{
					g_hybrid_render_coordinator->Initialize(ToList.d2d_dc, gdi_dc);
				}
			}

			ToList.advanced_features_enabled = true;
			return S_OK;
		}
		catch_default({});
	}

	void UIDrawContext::ShutdownAdvancedRendering()
	{
		if (!ToList.advanced_features_enabled)
			return;

		// 清理性能监控器
		if (g_render_profiler)
		{
			g_render_profiler->Shutdown();
			delete g_render_profiler;
			g_render_profiler = nullptr;
		}

		// 清理调试器
		if (g_render_debugger)
		{
			g_render_debugger->Shutdown();
			delete g_render_debugger;
			g_render_debugger = nullptr;
		}

		// 清理GDI+集成
		if (g_hybrid_render_coordinator)
		{
			delete g_hybrid_render_coordinator;
			g_hybrid_render_coordinator = nullptr;
		}

		if (g_gdi_plus_resource_manager)
		{
			delete g_gdi_plus_resource_manager;
			g_gdi_plus_resource_manager = nullptr;
		}

		if (g_gdi_plus_integration)
		{
			g_gdi_plus_integration->Shutdown();
			delete g_gdi_plus_integration;
			g_gdi_plus_integration = nullptr;
		}

		// 清理子管理器
		if (ToList.input_layout_manager)
		{
			ToList.input_layout_manager->Cleanup();
			delete ToList.input_layout_manager;
			ToList.input_layout_manager = nullptr;
		}

		if (ToList.buffer_manager)
		{
			ToList.buffer_manager->Cleanup();
			delete ToList.buffer_manager;
			ToList.buffer_manager = nullptr;
		}

		if (ToList.shader_manager)
		{
			ToList.shader_manager->Cleanup();
			delete ToList.shader_manager;
			ToList.shader_manager = nullptr;
		}

		// 清理高级渲染管理器
		if (ToList.advanced_render_manager)
		{
			ToList.advanced_render_manager->Shutdown();
			ToList.advanced_render_manager->Release();
			ToList.advanced_render_manager = nullptr;
		}

		ToList.advanced_features_enabled = false;
	}

	IRenderManager* UIDrawContext::GetAdvancedRenderManager()
	{
		return ToList.advanced_render_manager;
	}

	HRESULT UIDrawContext::EnableAdvancedFeatures(bool enable)
	{
		if (enable && !ToList.advanced_features_enabled)
		{
			return InitAdvancedRendering();
		}
		else if (!enable && ToList.advanced_features_enabled)
		{
			ShutdownAdvancedRendering();
			return S_OK;
		}
		return S_OK;
	}

	bool UIDrawContext::IsAdvancedFeaturesEnabled()
	{
		return ToList.advanced_features_enabled;
	}

	HRESULT UIDrawContext::CreateCustomShader(ShaderType type, LPCWSTR source_code, 
		LPCSTR entry_point, IShader** shader)
	{
		if (!ToList.shader_manager || !shader)
			return E_INVALIDARG;

		return ToList.shader_manager->CompileShaderFromSource(type, source_code, entry_point, shader);
	}

	HRESULT UIDrawContext::LoadShaderFromFile(ShaderType type, LPCWSTR file_path, 
		LPCSTR entry_point, IShader** shader)
	{
		if (!ToList.shader_manager || !shader)
			return E_INVALIDARG;

		return ToList.shader_manager->LoadShaderFromFile(type, file_path, entry_point, shader);
	}

	HRESULT UIDrawContext::CreateVertexBuffer(const void* vertices, uint32_t vertex_count, 
		uint32_t vertex_size, bool dynamic, IBuffer** buffer)
	{
		if (!ToList.buffer_manager || !buffer)
			return E_INVALIDARG;

		return ToList.buffer_manager->CreateVertexBuffer(vertices, vertex_count, vertex_size, dynamic, buffer);
	}

	HRESULT UIDrawContext::CreateIndexBuffer(const uint32_t* indices, uint32_t index_count, 
		bool dynamic, IBuffer** buffer)
	{
		if (!ToList.buffer_manager || !buffer)
			return E_INVALIDARG;

		return ToList.buffer_manager->CreateIndexBuffer(indices, index_count, dynamic, buffer);
	}

	HRESULT UIDrawContext::CreateConstantBuffer(uint32_t size, bool dynamic, IBuffer** buffer)
	{
		if (!ToList.buffer_manager || !buffer)
			return E_INVALIDARG;

		return ToList.buffer_manager->CreateConstantBuffer(size, dynamic, buffer);
	}

	HRESULT UIDrawContext::CreateTexture2D(uint32_t width, uint32_t height, DXGI_FORMAT format, 
		const void* initial_data, bool render_target, ITexture** texture)
	{
		if (!ToList.advanced_render_manager || !texture)
			return E_INVALIDARG;

		Microsoft::WRL::ComPtr<ITexture> tex;
		HRESULT hr = ToList.advanced_render_manager->CreateTexture(&tex);
		if (FAILED(hr)) return hr;

		hr = tex->Create2D(width, height, format, initial_data, render_target, true);
		if (FAILED(hr)) return hr;

		*texture = tex.Detach();
		return S_OK;
	}

	HRESULT UIDrawContext::LoadTextureFromFile(LPCWSTR file_path, ITexture** texture)
	{
		if (!ToList.advanced_render_manager || !texture)
			return E_INVALIDARG;

		Microsoft::WRL::ComPtr<ITexture> tex;
		HRESULT hr = ToList.advanced_render_manager->CreateTexture(&tex);
		if (FAILED(hr)) return hr;

		hr = tex->LoadFromFile(file_path);
		if (FAILED(hr)) return hr;

		*texture = tex.Detach();
		return S_OK;
	}

	HRESULT UIDrawContext::CreateRenderState(IRenderState** render_state)
	{
		if (!ToList.advanced_render_manager || !render_state)
			return E_INVALIDARG;

		return ToList.advanced_render_manager->CreateRenderState(render_state);
	}

	HRESULT UIDrawContext::SetBlendMode(bool enable, D3D11_BLEND src_blend, D3D11_BLEND dest_blend)
	{
		Microsoft::WRL::ComPtr<IRenderState> state;
		HRESULT hr = CreateRenderState(&state);
		if (FAILED(hr)) return hr;

		hr = state->SetBlendState(enable, src_blend, dest_blend);
		if (FAILED(hr)) return hr;

		return state->Apply();
	}

	HRESULT UIDrawContext::SetDepthTest(bool enable, bool write_enable, D3D11_COMPARISON_FUNC func)
	{
		Microsoft::WRL::ComPtr<IRenderState> state;
		HRESULT hr = CreateRenderState(&state);
		if (FAILED(hr)) return hr;

		hr = state->SetDepthStencilState(enable, write_enable, func);
		if (FAILED(hr)) return hr;

		return state->Apply();
	}

	HRESULT UIDrawContext::DrawPrimitive(D3D11_PRIMITIVE_TOPOLOGY topology, uint32_t vertex_count, uint32_t start_vertex)
	{
		if (!ToList.advanced_render_manager)
			return E_INVALIDARG;

		// 设置图元拓扑
		auto* context = ToList.advanced_render_manager->GetD3D11DeviceContext();
		context->IASetPrimitiveTopology(topology);

		// 绘制
		HRESULT hr = ToList.advanced_render_manager->Draw(vertex_count, start_vertex);
		
		// 更新统计
		if (SUCCEEDED(hr) && g_render_profiler)
		{
			g_render_profiler->IncrementCounter("DrawCalls");
			g_render_profiler->UpdateCounter("Vertices", vertex_count);
			
			// 估算三角形数量
			uint32_t triangles = 0;
			switch (topology)
			{
			case D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST:
				triangles = vertex_count / 3;
				break;
			case D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP:
				triangles = vertex_count - 2;
				break;
			}
			g_render_profiler->UpdateCounter("Triangles", triangles);
		}

		return hr;
	}

	HRESULT UIDrawContext::DrawIndexedPrimitive(D3D11_PRIMITIVE_TOPOLOGY topology, uint32_t index_count, uint32_t start_index, uint32_t base_vertex)
	{
		if (!ToList.advanced_render_manager)
			return E_INVALIDARG;

		// 设置图元拓扑
		auto* context = ToList.advanced_render_manager->GetD3D11DeviceContext();
		context->IASetPrimitiveTopology(topology);

		// 绘制
		HRESULT hr = ToList.advanced_render_manager->DrawIndexed(index_count, start_index, base_vertex);
		
		// 更新统计
		if (SUCCEEDED(hr) && g_render_profiler)
		{
			g_render_profiler->IncrementCounter("DrawCalls");
			
			// 估算三角形数量
			uint32_t triangles = 0;
			switch (topology)
			{
			case D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST:
				triangles = index_count / 3;
				break;
			case D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP:
				triangles = index_count - 2;
				break;
			}
			g_render_profiler->UpdateCounter("Triangles", triangles);
		}

		return hr;
	}

	const RenderStats& UIDrawContext::GetRenderStats()
	{
		if (ToList.advanced_render_manager)
		{
			return ToList.advanced_render_manager->GetRenderStats();
		}
		return ToList.render_stats;
	}

	void UIDrawContext::ResetRenderStats()
	{
		if (ToList.advanced_render_manager)
		{
			ToList.advanced_render_manager->ResetRenderStats();
		}
		if (g_render_profiler)
		{
			g_render_profiler->ResetStats();
		}
		ToList.render_stats = RenderStats();
	}

	uint64_t UIDrawContext::GetGPUMemoryUsage()
	{
		if (g_render_profiler)
		{
			return g_render_profiler->GetGPUMemoryUsage();
		}
		return 0;
	}

	float UIDrawContext::GetFrameTime()
	{
		if (g_render_profiler)
		{
			auto* counter = g_render_profiler->GetCounter("FrameTime");
			return counter ? static_cast<float>(counter->current_value) : 0.0f;
		}
		return 0.0f;
	}

	float UIDrawContext::GetGPUTime()
	{
		if (g_render_profiler)
		{
			auto* counter = g_render_profiler->GetCounter("GPUTime");
			return counter ? static_cast<float>(counter->current_value) : 0.0f;
		}
		return 0.0f;
	}

	// 高级功能扩展接口
	HRESULT UIDrawContext::BeginAdvancedFrame()
	{
		if (!ToList.advanced_render_manager)
			return E_FAIL;

		RENDER_PROFILE_BEGIN_FRAME();
		return ToList.advanced_render_manager->BeginFrame();
	}

	HRESULT UIDrawContext::EndAdvancedFrame()
	{
		if (!ToList.advanced_render_manager)
			return E_FAIL;

		HRESULT hr = ToList.advanced_render_manager->EndFrame();
		RENDER_PROFILE_END_FRAME();
		return hr;
	}

	HRESULT UIDrawContext::PresentAdvanced(bool vsync)
	{
		if (!ToList.advanced_render_manager)
			return E_FAIL;

		return ToList.advanced_render_manager->Present(vsync);
	}

	HRESULT UIDrawContext::SetAdvancedViewport(float x, float y, float width, float height)
	{
		if (!ToList.advanced_render_manager)
			return E_FAIL;

		return ToList.advanced_render_manager->SetViewport(x, y, width, height);
	}

	HRESULT UIDrawContext::ClearAdvancedRenderTarget(float r, float g, float b, float a)
	{
		if (!ToList.advanced_render_manager)
			return E_FAIL;

		return ToList.advanced_render_manager->ClearRenderTarget(r, g, b, a);
	}

	// GDI+集成便捷接口
	HRESULT UIDrawContext::ConvertGdiPlusBitmapToD2D(Gdiplus::Bitmap* gdi_bitmap, ID2D1Bitmap** d2d_bitmap)
	{
		if (!g_gdi_plus_integration || !gdi_bitmap || !d2d_bitmap)
			return E_INVALIDARG;

		return g_gdi_plus_integration->GetCachedD2DBitmap(gdi_bitmap, d2d_bitmap);
	}

	HRESULT UIDrawContext::BeginHybridRendering()
	{
		if (!g_hybrid_render_coordinator)
			return E_FAIL;

		return g_hybrid_render_coordinator->BeginHybridSession();
	}

	HRESULT UIDrawContext::EndHybridRendering()
	{
		if (!g_hybrid_render_coordinator)
			return E_FAIL;

		return g_hybrid_render_coordinator->EndHybridSession();
	}

	HRESULT UIDrawContext::SwitchToGdiPlusMode()
	{
		if (!g_hybrid_render_coordinator)
			return E_FAIL;

		return g_hybrid_render_coordinator->SwitchToGdiPlusMode();
	}

	HRESULT UIDrawContext::SwitchToD2DMode()
	{
		if (!g_hybrid_render_coordinator)
			return E_FAIL;

		return g_hybrid_render_coordinator->SwitchToD2DMode();
	}

	// 性能监控便捷接口
	std::string UIDrawContext::GetPerformanceReport()
	{
		if (!g_render_profiler)
			return "Performance profiler not available";

		return g_render_profiler->GenerateReport();
	}

	HRESULT UIDrawContext::ExportPerformanceData(LPCWSTR file_path)
	{
		if (!g_render_profiler || !file_path)
			return E_INVALIDARG;

		return g_render_profiler->ExportToFile(file_path);
	}

	void UIDrawContext::SetDebugMode(bool enable)
	{
		ToList.debug_mode = enable;

		if (ToList.advanced_render_manager)
		{
			ToList.advanced_render_manager->SetDebugMode(enable);
		}

		if (g_render_profiler)
		{
			g_render_profiler->SetEnabled(enable);
		}
	}

	bool UIDrawContext::IsDebugMode()
	{
		return ToList.debug_mode;
	}

	// =====================================================================================
	// 智能批处理管理器实现 - C++17
	// =====================================================================================

	/// 智能批处理管理器实现类
	class UIRenderBatchManager : public ExUnknownImpl<IRenderBatchManager>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IRenderBatchManager);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIRenderBatchManager()
			: m_render_manager(nullptr)
			, m_enable_auto_merge(true)
			, m_enable_state_sorting(true)
			, m_enable_instancing(true)
			, m_enable_depth_sort(false)
			, m_max_batches(1000)
			, m_vertex_buffer_offset(0)
			, m_index_buffer_offset(0)
		{
			m_batches.reserve(m_max_batches);
			m_batch_cache.reserve(m_max_batches);
		}

		virtual ~UIRenderBatchManager()
		{
			Shutdown();
		}

		// IRenderBatchManager接口实现
		EXMETHOD HRESULT Initialize(IRenderManager* render_manager) override
		{
			if (!render_manager)
				return E_INVALIDARG;

			try
			{
				m_render_manager = render_manager;
				m_render_manager->AddRef();

				// 创建动态缓冲区
				throw_if_failed(
					CreateDynamicVertexBuffer(1024 * 1024, &m_dynamic_vertex_buffer), // 1MB
					L"创建动态顶点缓冲区失败"
				);

				throw_if_failed(
					CreateDynamicIndexBuffer(512 * 1024, &m_dynamic_index_buffer), // 512KB
					L"创建动态索引缓冲区失败"
				);

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void Shutdown() override
		{
			std::lock_guard<std::mutex> lock(m_batch_mutex);

			m_batches.clear();
			m_batch_groups.clear();
			m_batch_cache.clear();

			while (!m_free_batch_indices.empty()) {
				m_free_batch_indices.pop();
			}

			if (m_dynamic_vertex_buffer) {
				m_dynamic_vertex_buffer->Release();
				m_dynamic_vertex_buffer = nullptr;
			}
			if (m_dynamic_index_buffer) {
				m_dynamic_index_buffer->Release();
				m_dynamic_index_buffer = nullptr;
			}

			if (m_render_manager) {
				m_render_manager->Release();
				m_render_manager = nullptr;
			}
		}

		EXMETHOD HRESULT BeginBatch() override
		{
			if (m_in_batch.exchange(true)) {
				return S_FALSE; // 已经在批处理中
			}

			std::lock_guard<std::mutex> lock(m_batch_mutex);

			// 清空上一帧的批次
			m_batches.clear();
			m_batch_groups.clear();

			// 重置缓冲区偏移
			m_vertex_buffer_offset = 0;
			m_index_buffer_offset = 0;

			// 记录开始时间
			m_batch_start_time = std::chrono::high_resolution_clock::now();

			return S_OK;
		}

		EXMETHOD HRESULT EndBatch() override
		{
			if (!m_in_batch.exchange(false)) {
				return S_FALSE; // 不在批处理中
			}

			std::lock_guard<std::mutex> lock(m_batch_mutex);

			try
			{
				// 优化批次
				if (m_enable_auto_merge) {
					MergeBatches();
				}

				if (m_enable_state_sorting) {
					SortBatches();
				}

				// 提交批次
				throw_if_failed(
					SubmitBatches(),
					L"提交批次失败"
				);

				// 更新统计信息
				UpdateStats();

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT AddBatch(const RenderBatch& batch) override
		{
			if (!m_in_batch) {
				return E_FAIL;
			}

			std::lock_guard<std::mutex> lock(m_batch_mutex);

			if (m_batches.size() >= m_max_batches) {
				// 批次数量达到上限，强制提交
				return FlushBatches();
			}

			m_batches.emplace_back(batch);

			// 按状态哈希分组
			uint64_t hash = ComputeStateHash(batch).shader_hash;
			m_batch_groups[hash].push_back(m_batches.size() - 1);

			return S_OK;
		}

		EXMETHOD HRESULT AddGeometryBatch(IShader* shader, IBuffer* vertex_buffer,
										IBuffer* index_buffer, uint32_t index_count,
										D3D11_PRIMITIVE_TOPOLOGY topology) override
		{
			if (!shader || !vertex_buffer || !index_buffer) {
				return E_INVALIDARG;
			}

			RenderBatch batch;
			batch.type = BatchType::GEOMETRY;
			batch.shader = shader;
			batch.vertex_buffer = vertex_buffer;
			batch.index_buffer = index_buffer;
			batch.index_count = index_count;
			batch.topology = topology;
			batch.state_hash = ComputeStateHash(batch);

			return AddBatch(batch);
		}

		EXMETHOD HRESULT AddSpriteBatch(ITexture* texture, const SpriteVertex* vertices,
									   uint32_t vertex_count) override
		{
			if (!texture || !vertices || vertex_count == 0) {
				return E_INVALIDARG;
			}

			// 创建临时顶点缓冲区
			IBuffer* vertex_buffer = nullptr;
			HRESULT hr = m_render_manager->CreateBuffer(BufferType::VERTEX, &vertex_buffer);
			if (FAILED(hr)) return hr;

			// 上传顶点数据
			// TODO: 实现顶点数据上传

			RenderBatch batch;
			batch.type = BatchType::SPRITE;
			batch.texture = texture;
			batch.vertex_buffer = vertex_buffer;
			batch.vertex_count = vertex_count;
			batch.topology = D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST;
			batch.state_hash = ComputeStateHash(batch);

			return AddBatch(batch);
		}

		EXMETHOD HRESULT AddTextBatch(const wchar_t* text, float x, float y,
									 uint32_t color, IShader* text_shader) override
		{
			if (!text || !text_shader) {
				return E_INVALIDARG;
			}

			// 生成文本几何体
			// TODO: 实现文本几何体生成

			RenderBatch batch;
			batch.type = BatchType::TEXT;
			batch.shader = text_shader;
			batch.topology = D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST;
			batch.state_hash = ComputeStateHash(batch);

			return AddBatch(batch);
		}

		EXMETHOD HRESULT AddInstancedBatch(IShader* shader, IBuffer* vertex_buffer,
										  IBuffer* index_buffer, IBuffer* instance_buffer,
										  uint32_t index_count, uint32_t instance_count) override
		{
			if (!shader || !vertex_buffer || !index_buffer || !instance_buffer) {
				return E_INVALIDARG;
			}

			RenderBatch batch;
			batch.type = BatchType::GEOMETRY;
			batch.shader = shader;
			batch.vertex_buffer = vertex_buffer;
			batch.index_buffer = index_buffer;
			batch.instance_buffer = instance_buffer;
			batch.index_count = index_count;
			batch.instance_count = instance_count;
			batch.is_instanced = true;
			batch.topology = D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST;
			batch.state_hash = ComputeStateHash(batch);

			return AddBatch(batch);
		}

		EXMETHOD void SetBatchStrategy(bool enable_auto_merge, bool enable_state_sorting,
									  bool enable_instancing) override
		{
			std::lock_guard<std::mutex> lock(m_batch_mutex);
			m_enable_auto_merge = enable_auto_merge;
			m_enable_state_sorting = enable_state_sorting;
			m_enable_instancing = enable_instancing;
		}

		EXMETHOD const BatchStats& GetBatchStats() const override
		{
			return m_stats;
		}

		EXMETHOD void ResetStats() override
		{
			std::lock_guard<std::mutex> lock(m_batch_mutex);
			m_stats.Reset();
		}

		EXMETHOD void SetMaxBatchCount(uint32_t max_batches) override
		{
			std::lock_guard<std::mutex> lock(m_batch_mutex);
			m_max_batches = max_batches;
			m_batches.reserve(max_batches);
		}

		EXMETHOD uint32_t GetCurrentBatchCount() const override
		{
			std::lock_guard<std::mutex> lock(m_batch_mutex);
			return static_cast<uint32_t>(m_batches.size());
		}

		EXMETHOD HRESULT FlushBatches() override
		{
			if (!m_in_batch) {
				return S_FALSE;
			}

			std::lock_guard<std::mutex> lock(m_batch_mutex);

			try
			{
				throw_if_failed(
					SubmitBatches(),
					L"强制提交批次失败"
				);

				// 清空当前批次
				m_batches.clear();
				m_batch_groups.clear();

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void SetDepthSortMode(bool enable_depth_sort) override
		{
			std::lock_guard<std::mutex> lock(m_batch_mutex);
			m_enable_depth_sort = enable_depth_sort;
		}

	private:
		/// 合并兼容的批次
		void MergeBatches()
		{
			if (m_batches.size() < 2) return;

			std::vector<RenderBatch> merged_batches;
			merged_batches.reserve(m_batches.size());

			// 按状态哈希分组合并
			for (auto& [hash, indices] : m_batch_groups) {
				if (indices.size() < 2) {
					// 单个批次，直接添加
					merged_batches.emplace_back(std::move(m_batches[indices[0]]));
					continue;
				}

				// 尝试合并同组批次
				RenderBatch merged = m_batches[indices[0]];
				bool has_merged = false;

				for (size_t i = 1; i < indices.size(); ++i) {
					const auto& current = m_batches[indices[i]];
					if (CanMergeBatches(merged, current)) {
						merged = MergeTwoBatches(merged, current);
						has_merged = true;
						m_stats.merged_batches++;
					} else {
						// 无法合并，添加当前合并结果
						merged_batches.emplace_back(std::move(merged));
						merged = current;
					}
				}

				merged_batches.emplace_back(std::move(merged));
			}

			// 更新批次列表
			m_batches = std::move(merged_batches);

			// 重新构建分组
			m_batch_groups.clear();
			for (size_t i = 0; i < m_batches.size(); ++i) {
				uint64_t hash = m_batches[i].state_hash.shader_hash;
				m_batch_groups[hash].push_back(i);
			}
		}

		/// 排序批次以减少状态切换
		void SortBatches()
		{
			if (m_batches.empty()) return;

			// 使用C++17并行算法进行排序
			std::sort(std::execution::par_unseq, m_batches.begin(), m_batches.end(),
				[this](const RenderBatch& a, const RenderBatch& b) -> bool {
					// 首先按透明度排序（不透明物体在前）
					if (a.is_transparent != b.is_transparent) {
						return !a.is_transparent; // 不透明物体优先
					}

					// 透明物体按深度从后往前排序
					if (a.is_transparent && m_enable_depth_sort) {
						return a.depth_key > b.depth_key; // 远到近
					}

					// 不透明物体按状态哈希排序以减少状态切换
					if (a.state_hash.shader_hash != b.state_hash.shader_hash) {
						return a.state_hash.shader_hash < b.state_hash.shader_hash;
					}

					if (a.state_hash.texture_hash != b.state_hash.texture_hash) {
						return a.state_hash.texture_hash < b.state_hash.texture_hash;
					}

					// 最后按批次类型排序
					return static_cast<uint32_t>(a.type) < static_cast<uint32_t>(b.type);
				});
		}

		/// 提交批次到GPU
		HRESULT SubmitBatches()
		{
			if (m_batches.empty()) {
				return S_OK;
			}

			try
			{
				RenderStateHash current_state{};
				uint32_t state_changes = 0;

				for (const auto& batch : m_batches) {
					// 检查是否需要切换状态
					if (batch.state_hash != current_state) {
						// 设置着色器
						if (batch.shader) {
							throw_if_failed(
								batch.shader->Bind(),
								L"绑定着色器失败"
							);
						}

						// 设置纹理
						if (batch.texture) {
							throw_if_failed(
								batch.texture->BindAsShaderResource(0),
								L"绑定纹理失败"
							);
						}

						current_state = batch.state_hash;
						state_changes++;
					}

					// 设置顶点缓冲区
					if (batch.vertex_buffer) {
						// TODO: 实现顶点缓冲区绑定
					}

					// 设置索引缓冲区
					if (batch.index_buffer) {
						// TODO: 实现索引缓冲区绑定
					}

					// 执行绘制调用
					if (batch.is_instanced && batch.instance_count > 1) {
						// 实例化绘制
						throw_if_failed(
							m_render_manager->DrawIndexedInstanced(
								batch.index_count, batch.instance_count,
								batch.start_index, batch.start_vertex, batch.start_instance
							),
							L"实例化绘制失败"
						);
					} else {
						// 普通绘制
						throw_if_failed(
							m_render_manager->DrawIndexed(
								batch.index_count, batch.start_index, batch.start_vertex
							),
							L"绘制失败"
						);
					}

					// 更新统计
					batch.draw_calls++;
					if (batch.topology == D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST) {
						batch.triangles += batch.index_count / 3;
					}
				}

				m_stats.state_changes = state_changes;
				return S_OK;
			}
			catch_default({});
		}

		/// 计算渲染状态哈希
		RenderStateHash ComputeStateHash(const RenderBatch& batch) const
		{
			RenderStateHash hash{};

			// 计算着色器哈希
			if (batch.shader) {
				hash.shader_hash = reinterpret_cast<uint64_t>(batch.shader);
			}

			// 计算纹理哈希
			if (batch.texture) {
				hash.texture_hash = reinterpret_cast<uint64_t>(batch.texture);
			}

			// 计算混合状态哈希
			hash.blend_state_hash = batch.is_transparent ? 1 : 0;

			// 计算深度状态哈希
			hash.depth_state_hash = static_cast<uint64_t>(batch.type);

			// 计算光栅化状态哈希
			hash.rasterizer_state_hash = static_cast<uint64_t>(batch.topology);

			return hash;
		}

		/// 检查两个批次是否可以合并
		bool CanMergeBatches(const RenderBatch& a, const RenderBatch& b) const
		{
			// 基本条件检查
			if (a.type != b.type ||
				a.state_hash != b.state_hash ||
				a.topology != b.topology ||
				!a.can_merge || !b.can_merge) {
				return false;
			}

			// 检查资源兼容性
			if (a.shader != b.shader ||
				a.texture != b.texture) {
				return false;
			}

			// 检查是否可以使用实例化
			if (m_enable_instancing &&
				a.vertex_buffer == b.vertex_buffer &&
				a.index_buffer == b.index_buffer) {
				return true;
			}

			// 检查是否可以合并几何体
			if (a.type == BatchType::SPRITE || a.type == BatchType::UI_ELEMENT) {
				return true; // 精灵和UI元素通常可以合并
			}

			return false;
		}

		/// 合并两个批次
		RenderBatch MergeTwoBatches(const RenderBatch& a, const RenderBatch& b) const
		{
			RenderBatch merged = a;

			// 合并计数
			merged.vertex_count += b.vertex_count;
			merged.index_count += b.index_count;

			// 如果可以实例化，增加实例数量
			if (m_enable_instancing &&
				a.vertex_buffer == b.vertex_buffer &&
				a.index_buffer == b.index_buffer) {
				merged.instance_count += b.instance_count;
				merged.is_instanced = true;
			}

			// 合并统计信息
			merged.draw_calls = a.draw_calls + b.draw_calls;
			merged.triangles = a.triangles + b.triangles;

			return merged;
		}

		/// 更新统计信息
		void UpdateStats()
		{
			auto end_time = std::chrono::high_resolution_clock::now();
			auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
				end_time - m_batch_start_time);

			m_stats.total_batches = static_cast<uint32_t>(m_batches.size());

			// 计算节省的Draw Call数量
			uint32_t total_draw_calls = 0;
			uint32_t instanced_draws = 0;

			for (const auto& batch : m_batches) {
				total_draw_calls += batch.draw_calls;
				if (batch.is_instanced) {
					instanced_draws++;
				}
			}

			m_stats.draw_calls_saved = total_draw_calls > m_stats.total_batches ?
				total_draw_calls - m_stats.total_batches : 0;
			m_stats.instanced_draws = instanced_draws;

			// 计算效率指标
			m_stats.CalculateEfficiency();

			// 估算GPU利用率（简化计算）
			float batch_time_ms = duration.count() / 1000.0f;
			m_stats.gpu_utilization = std::min(1.0f, batch_time_ms / 16.67f); // 假设60fps目标
		}

		/// 创建动态顶点缓冲区
		HRESULT CreateDynamicVertexBuffer(uint32_t size, IBuffer** buffer)
		{
			if (!buffer) return E_INVALIDARG;

			return m_render_manager->CreateBuffer(BufferType::VERTEX, buffer);
		}

		/// 创建动态索引缓冲区
		HRESULT CreateDynamicIndexBuffer(uint32_t size, IBuffer** buffer)
		{
			if (!buffer) return E_INVALIDARG;

			return m_render_manager->CreateBuffer(BufferType::INDEX, buffer);
		}

	private:
		IRenderManager* m_render_manager;
		std::vector<RenderBatch> m_batches;
		std::unordered_map<uint64_t, std::vector<size_t>> m_batch_groups;

		// 批处理策略
		bool m_enable_auto_merge;
		bool m_enable_state_sorting;
		bool m_enable_instancing;
		bool m_enable_depth_sort;
		uint32_t m_max_batches;

		// 统计信息
		mutable BatchStats m_stats;

		// 线程安全
		mutable std::mutex m_batch_mutex;
		std::atomic<bool> m_in_batch{false};

		// 性能优化
		std::vector<RenderBatch> m_batch_cache;
		std::queue<size_t> m_free_batch_indices;

		// 动态缓冲区管理
		IBuffer* m_dynamic_vertex_buffer{nullptr};
		IBuffer* m_dynamic_index_buffer{nullptr};
		uint32_t m_vertex_buffer_offset;
		uint32_t m_index_buffer_offset;

		// 时间统计
		std::chrono::high_resolution_clock::time_point m_batch_start_time;
	};

	// 全局批处理管理器实例
	static IRenderBatchManager* g_batch_manager = nullptr;

	/// 创建批处理管理器
	HRESULT CreateBatchManager(IRenderBatchManager** batch_manager)
	{
		if (!batch_manager) return E_INVALIDARG;

		try
		{
			auto manager = new UIRenderBatchManager();
			*batch_manager = manager;
			return S_OK;
		}
		catch_default({});
	}

	/// 获取全局批处理管理器
	IRenderBatchManager* GetGlobalBatchManager()
	{
		return g_batch_manager;
	}

	/// 设置全局批处理管理器
	void SetGlobalBatchManager(IRenderBatchManager* manager)
	{
		if (g_batch_manager) {
			g_batch_manager->Release();
		}
		g_batch_manager = manager;
		if (g_batch_manager) {
			g_batch_manager->AddRef();
		}
	}

	// =====================================================================================
	// 智能状态缓存系统实现 - C++17
	// =====================================================================================

	/// 智能状态缓存管理器实现类
	class UIRenderStateCache : public ExUnknownImpl<IRenderStateCache>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IRenderStateCache);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIRenderStateCache()
			: m_render_manager(nullptr)
			, m_cache_hits(0)
			, m_cache_misses(0)
			, m_state_changes(0)
		{
		}

		virtual ~UIRenderStateCache()
		{
			Shutdown();
		}

		// IRenderStateCache接口实现
		EXMETHOD HRESULT Initialize(IRenderManager* render_manager) override
		{
			if (!render_manager)
				return E_INVALIDARG;

			m_render_manager = render_manager;
			m_render_manager->AddRef();

			// 初始化当前状态为无效状态
			m_current_state = RenderStateHash{};

			return S_OK;
		}

		EXMETHOD void Shutdown() override
		{
			std::lock_guard<std::mutex> lock(m_cache_mutex);

			m_state_cache.clear();

			if (m_render_manager) {
				m_render_manager->Release();
				m_render_manager = nullptr;
			}
		}

		EXMETHOD HRESULT SetRenderState(const RenderStateHash& state_hash) override
		{
			std::lock_guard<std::mutex> lock(m_cache_mutex);

			// 检查是否与当前状态相同
			if (state_hash == m_current_state) {
				m_cache_hits++;
				return S_OK; // 状态未改变，无需设置
			}

			// 检查缓存中是否存在
			auto it = m_state_cache.find(GetStateKey(state_hash));
			if (it != m_state_cache.end()) {
				// 缓存命中，快速应用状态
				m_cache_hits++;
				return ApplyStateFromCache(it->second);
			}

			// 缓存未命中，需要完整设置状态
			m_cache_misses++;
			return ForceApplyState(state_hash);
		}

		EXMETHOD HRESULT ForceApplyState(const RenderStateHash& state_hash) override
		{
			std::lock_guard<std::mutex> lock(m_cache_mutex);

			try
			{
				// 应用着色器状态
				if (state_hash.shader_hash != m_current_state.shader_hash) {
					// TODO: 应用着色器状态
					m_state_changes++;
				}

				// 应用纹理状态
				if (state_hash.texture_hash != m_current_state.texture_hash) {
					// TODO: 应用纹理状态
					m_state_changes++;
				}

				// 应用混合状态
				if (state_hash.blend_state_hash != m_current_state.blend_state_hash) {
					// TODO: 应用混合状态
					m_state_changes++;
				}

				// 应用深度状态
				if (state_hash.depth_state_hash != m_current_state.depth_state_hash) {
					// TODO: 应用深度状态
					m_state_changes++;
				}

				// 应用光栅化状态
				if (state_hash.rasterizer_state_hash != m_current_state.rasterizer_state_hash) {
					// TODO: 应用光栅化状态
					m_state_changes++;
				}

				// 更新当前状态
				m_current_state = state_hash;

				// 缓存状态信息
				CacheStateInfo cached_info;
				cached_info.state_hash = state_hash;
				cached_info.last_access = std::chrono::steady_clock::now();
				cached_info.access_count = 1;

				m_state_cache[GetStateKey(state_hash)] = cached_info;

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD const RenderStateHash& GetCurrentState() const override
		{
			return m_current_state;
		}

		EXMETHOD void ClearCache() override
		{
			std::lock_guard<std::mutex> lock(m_cache_mutex);
			m_state_cache.clear();
		}

		EXMETHOD float GetCacheHitRate() const override
		{
			std::lock_guard<std::mutex> lock(m_cache_mutex);
			uint32_t total_requests = m_cache_hits + m_cache_misses;
			return total_requests > 0 ? static_cast<float>(m_cache_hits) / total_requests : 0.0f;
		}

		EXMETHOD uint32_t GetStateChangeCount() const override
		{
			return m_state_changes;
		}

		EXMETHOD void ResetStats() override
		{
			std::lock_guard<std::mutex> lock(m_cache_mutex);
			m_cache_hits = 0;
			m_cache_misses = 0;
			m_state_changes = 0;
		}

	private:
		/// 缓存状态信息
		struct CacheStateInfo
		{
			RenderStateHash state_hash;
			std::chrono::steady_clock::time_point last_access;
			uint32_t access_count{0};
		};

		/// 获取状态键值
		uint64_t GetStateKey(const RenderStateHash& state_hash) const
		{
			// 使用简单的哈希组合
			return state_hash.shader_hash ^
				   (state_hash.texture_hash << 1) ^
				   (state_hash.blend_state_hash << 2) ^
				   (state_hash.depth_state_hash << 3) ^
				   (state_hash.rasterizer_state_hash << 4);
		}

		/// 从缓存应用状态
		HRESULT ApplyStateFromCache(CacheStateInfo& cached_info)
		{
			// 更新访问信息
			cached_info.last_access = std::chrono::steady_clock::now();
			cached_info.access_count++;

			// 快速应用状态（假设状态已经正确设置）
			m_current_state = cached_info.state_hash;

			return S_OK;
		}

	private:
		IRenderManager* m_render_manager;
		RenderStateHash m_current_state;

		// 状态缓存
		std::unordered_map<uint64_t, CacheStateInfo> m_state_cache;

		// 统计信息
		mutable std::atomic<uint32_t> m_cache_hits{0};
		mutable std::atomic<uint32_t> m_cache_misses{0};
		mutable std::atomic<uint32_t> m_state_changes{0};

		// 线程安全
		mutable std::mutex m_cache_mutex;
	};

	// 全局状态缓存实例
	static IRenderStateCache* g_state_cache = nullptr;

	/// 创建状态缓存管理器
	HRESULT CreateStateCache(IRenderStateCache** state_cache)
	{
		if (!state_cache) return E_INVALIDARG;

		try
		{
			auto cache = new UIRenderStateCache();
			*state_cache = cache;
			return S_OK;
		}
		catch_default({});
	}

	/// 获取全局状态缓存管理器
	IRenderStateCache* GetGlobalStateCache()
	{
		return g_state_cache;
	}

	/// 设置全局状态缓存管理器
	void SetGlobalStateCache(IRenderStateCache* cache)
	{
		if (g_state_cache) {
			g_state_cache->Release();
		}
		g_state_cache = cache;
		if (g_state_cache) {
			g_state_cache->AddRef();
		}
	}

	// =====================================================================================
	// 智能批处理系统集成示例 - 展示如何在现有代码中使用
	// =====================================================================================

	/// 优化后的绘制上下文 - 集成批处理系统
	class UIOptimizedDrawContext : public UIDrawContext
	{
	public:
		// 简化的UI元素结构 - 定义在类的开头以便所有方法使用
		struct UIElement {
			std::string type;
			float x, y, width, height;
			uint32_t color;
			// ... 其他属性
		};

		UIOptimizedDrawContext()
			: m_batch_manager(nullptr)
			, m_state_cache(nullptr)
			, m_batching_enabled(true)
		{
		}

		/// 初始化优化绘制上下文
		HRESULT InitializeOptimized(IRenderManager* render_manager)
		{
			try
			{
				// 创建批处理管理器
				throw_if_failed(
					CreateBatchManager(&m_batch_manager),
					L"创建批处理管理器失败"
				);

				throw_if_failed(
					m_batch_manager->Initialize(render_manager),
					L"初始化批处理管理器失败"
				);

				// 创建状态缓存
				throw_if_failed(
					CreateStateCache(&m_state_cache),
					L"创建状态缓存失败"
				);

				throw_if_failed(
					m_state_cache->Initialize(render_manager),
					L"初始化状态缓存失败"
				);

				// 设置批处理策略
				m_batch_manager->SetBatchStrategy(true, true, true); // 启用所有优化
				m_batch_manager->SetMaxBatchCount(500); // 设置最大批次数

				return S_OK;
			}
			catch_default({});
		}

		/// 开始帧渲染 - 启动批处理
		HRESULT BeginFrame()
		{
			if (!m_batching_enabled || !m_batch_manager) {
				return S_OK;
			}

			return m_batch_manager->BeginBatch();
		}

		/// 结束帧渲染 - 提交所有批次
		HRESULT EndFrame()
		{
			if (!m_batching_enabled || !m_batch_manager) {
				return S_OK;
			}

			HRESULT hr = m_batch_manager->EndBatch();

			// 输出性能统计（调试模式）
			if (IsDebugMode()) {
				const auto& stats = m_batch_manager->GetBatchStats();
				std::ostringstream oss;
				oss << "批处理统计: 总批次=" << stats.total_batches
					<< ", 合并批次=" << stats.merged_batches
					<< ", 节省DrawCall=" << stats.draw_calls_saved
					<< ", 效率=" << std::fixed << std::setprecision(2)
					<< stats.batch_efficiency * 100.0f << "%\n";
				OutputDebugStringA(oss.str().c_str());
			}

			return hr;
		}

		/// 优化的矩形绘制 - 使用批处理
		HRESULT DrawRectangleOptimized(float x, float y, float width, float height,
									  uint32_t color, ITexture* texture = nullptr)
		{
			if (!m_batching_enabled || !m_batch_manager) {
				// 回退到原始绘制方法 - 这里需要实际的绘制实现
				// TODO: 实现基础矩形绘制或调用相应的绘制方法
				return S_OK;
			}

			try
			{
				// 创建精灵顶点数据
				SpriteVertex vertices[4] = {
					{x,         y,          0.0f, 0.0f, color},
					{x + width, y,          1.0f, 0.0f, color},
					{x + width, y + height, 1.0f, 1.0f, color},
					{x,         y + height, 0.0f, 1.0f, color}
				};

				// 添加到批处理系统
				throw_if_failed(
					m_batch_manager->AddSpriteBatch(texture, vertices, 4),
					L"添加精灵批次失败"
				);

				return S_OK;
			}
			catch_default({});
		}

		/// 优化的文本绘制 - 使用批处理
		HRESULT DrawTextOptimized(const wchar_t* text, float x, float y,
								 uint32_t color, IShader* text_shader)
		{
			if (!m_batching_enabled || !m_batch_manager) {
				// 回退到原始绘制方法
				return S_OK; // TODO: 调用原始文本绘制
			}

			try
			{
				throw_if_failed(
					m_batch_manager->AddTextBatch(text, x, y, color, text_shader),
					L"添加文本批次失败"
				);

				return S_OK;
			}
			catch_default({});
		}

		/// 批量绘制UI元素 - 高效批处理
		HRESULT DrawUIElementsBatch(const std::vector<UIElement>& elements)
		{
			if (!m_batching_enabled || !m_batch_manager) {
				// 回退到逐个绘制
				for (const auto& element : elements) {
					// TODO: 调用原始绘制方法
				}
				return S_OK;
			}

			try
			{
				// 按类型分组元素
				std::unordered_map<BatchType, std::vector<const UIElement*>> grouped_elements;

				for (const auto& element : elements) {
					BatchType type = DetermineElementBatchType(element);
					grouped_elements[type].push_back(&element);
				}

				// 为每组创建批次
				for (const auto& [type, element_group] : grouped_elements) {
					auto batch = UIBatchFactory::CreateUIElementBatch(type,
						static_cast<uint32_t>(element_group.size()));

					// TODO: 填充批次数据

					throw_if_failed(
						m_batch_manager->AddBatch(batch),
						L"添加UI元素批次失败"
					);
				}

				return S_OK;
			}
			catch_default({});
		}

		/// 启用/禁用批处理
		void SetBatchingEnabled(bool enabled)
		{
			m_batching_enabled = enabled;
		}

		/// 获取批处理统计信息
		const BatchStats& GetBatchingStats() const
		{
			static BatchStats empty_stats{};
			return m_batch_manager ? m_batch_manager->GetBatchStats() : empty_stats;
		}

		/// 获取状态缓存统计信息
		float GetStateCacheHitRate() const
		{
			return m_state_cache ? m_state_cache->GetCacheHitRate() : 0.0f;
		}

	private:
		/// 确定UI元素的批次类型
		BatchType DetermineElementBatchType(const UIElement& element) const
		{
			// 简化的类型判断逻辑
			if (element.type == "button" || element.type == "panel") {
				return BatchType::UI_ELEMENT;
			} else if (element.type == "text" || element.type == "label") {
				return BatchType::TEXT;
			} else if (element.type == "image" || element.type == "sprite") {
				return BatchType::SPRITE;
			}
			return BatchType::GEOMETRY;
		}

	private:
		IRenderBatchManager* m_batch_manager;
		IRenderStateCache* m_state_cache;
		bool m_batching_enabled;
	};

	// =====================================================================================
	// 性能测试和基准测试工具
	// =====================================================================================

	/// 批处理性能测试器
	class UIBatchPerformanceTester
	{
	public:
		/// 测试批处理性能
		static void TestBatchingPerformance(IRenderBatchManager* batch_manager,
											uint32_t test_iterations = 1000)
		{
			if (!batch_manager) return;

			auto start_time = std::chrono::high_resolution_clock::now();

			for (uint32_t i = 0; i < test_iterations; ++i) {
				batch_manager->BeginBatch();

				// 模拟添加大量批次
				for (uint32_t j = 0; j < 100; ++j) {
					auto batch = UIBatchFactory::CreateSpriteBatch(nullptr, 10);
					batch_manager->AddBatch(batch);
				}

				batch_manager->EndBatch();
			}

			auto end_time = std::chrono::high_resolution_clock::now();
			auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
				end_time - start_time);

			const auto& stats = batch_manager->GetBatchStats();

			std::ostringstream oss;
			oss << "批处理性能测试结果:\n"
				<< "- 测试迭代: " << test_iterations << "\n"
				<< "- 总耗时: " << duration.count() << "ms\n"
				<< "- 平均每帧: " << std::fixed << std::setprecision(2)
				<< static_cast<float>(duration.count()) / test_iterations << "ms\n"
				<< "- 批次效率: " << std::fixed << std::setprecision(2)
				<< stats.batch_efficiency * 100.0f << "%\n"
				<< "- 合并率: " << std::fixed << std::setprecision(2)
				<< stats.merge_ratio * 100.0f << "%\n";
			OutputDebugStringA(oss.str().c_str());
		}

		/// 比较批处理前后的性能
		static void CompareBatchingPerformance(UIDrawContext* original_context,
											  UIOptimizedDrawContext* optimized_context,
											  uint32_t draw_calls = 1000)
		{
			// 测试原始绘制性能
			auto start_time = std::chrono::high_resolution_clock::now();

			for (uint32_t i = 0; i < draw_calls; ++i) {
				// TODO: 实现原始绘制方法调用
				// original_context->DrawRectangle(...);
				// 这里需要调用实际的绘制方法
			}

			auto original_time = std::chrono::high_resolution_clock::now();

			// 测试优化后的绘制性能
			optimized_context->BeginFrame();

			for (uint32_t i = 0; i < draw_calls; ++i) {
				optimized_context->DrawRectangleOptimized(
					static_cast<float>(i % 800),
					static_cast<float>(i % 600),
					50.0f, 50.0f, 0xFF0000FF
				);
			}

			optimized_context->EndFrame();
			auto optimized_time = std::chrono::high_resolution_clock::now();

			// 计算性能提升
			auto original_duration = std::chrono::duration_cast<std::chrono::microseconds>(
				original_time - start_time);
			auto optimized_duration = std::chrono::duration_cast<std::chrono::microseconds>(
				optimized_time - original_time);

			float performance_gain = static_cast<float>(original_duration.count()) /
									optimized_duration.count();

			std::ostringstream oss;
			oss << "批处理性能对比:\n"
				<< "- 原始绘制: " << original_duration.count() << "μs\n"
				<< "- 批处理绘制: " << optimized_duration.count() << "μs\n"
				<< "- 性能提升: " << std::fixed << std::setprecision(2)
				<< performance_gain << "x\n"
				<< "- 状态缓存命中率: " << std::fixed << std::setprecision(2)
				<< optimized_context->GetStateCacheHitRate() * 100.0f << "%\n";
			OutputDebugStringA(oss.str().c_str());
		}
	};

	// =====================================================================================
	// 智能批处理系统使用示例和最佳实践
	// =====================================================================================

	/// 示例：如何在应用程序中集成智能批处理系统
	class UIApplicationExample
	{
	public:
		/// 初始化应用程序渲染系统
		HRESULT InitializeRenderingSystem()
		{
			try
			{
				// 1. 创建渲染管理器（假设已存在）
				// IRenderManager* render_manager = ...;

				// 2. 创建并初始化批处理管理器
				throw_if_failed(
					CreateBatchManager(&m_batch_manager),
					L"创建批处理管理器失败"
				);

				// throw_if_failed(
				//     m_batch_manager->Initialize(render_manager),
				//     L"初始化批处理管理器失败"
				// );

				// 3. 配置批处理策略
				m_batch_manager->SetBatchStrategy(
					true,  // 启用自动合并
					true,  // 启用状态排序
					true   // 启用实例化渲染
				);

				// 4. 设置性能参数
				m_batch_manager->SetMaxBatchCount(1000);  // 最大批次数
				m_batch_manager->SetDepthSortMode(true);  // 启用深度排序

				// 5. 设置为全局批处理管理器
				SetGlobalBatchManager(m_batch_manager);

				return S_OK;
			}
			catch_default({});
		}

		/// 渲染一帧的标准流程
		HRESULT RenderFrame()
		{
			try
			{
				// 1. 开始批处理帧
				throw_if_failed(
					m_batch_manager->BeginBatch(),
					L"开始批处理失败"
				);

				// 2. 添加各种渲染内容
				RenderUI();
				RenderSprites();
				RenderText();
				RenderEffects();

				// 3. 结束批处理并提交到GPU
				throw_if_failed(
					m_batch_manager->EndBatch(),
					L"结束批处理失败"
				);

				// 4. 可选：输出性能统计
				LogPerformanceStats();

				return S_OK;
			}
			catch_default({});
		}

	private:
		/// 渲染UI元素
		void RenderUI()
		{
			// 示例：批量渲染按钮
			for (const auto& button : m_ui_buttons) {
				auto batch = UIBatchFactory::CreateUIElementBatch(BatchType::UI_ELEMENT, 1);
				// 设置批次数据...
				m_batch_manager->AddBatch(batch);
			}

			// 示例：批量渲染面板
			for (const auto& panel : m_ui_panels) {
				auto batch = UIBatchFactory::CreateUIElementBatch(BatchType::UI_ELEMENT, 1);
				// 设置批次数据...
				m_batch_manager->AddBatch(batch);
			}
		}

		/// 渲染精灵
		void RenderSprites()
		{
			// 按纹理分组精灵以提高批处理效率
			std::unordered_map<ITexture*, std::vector<SpriteData>> sprite_groups;

			for (const auto& sprite : m_sprites) {
				sprite_groups[sprite.texture].push_back(sprite);
			}

			// 为每个纹理组创建批次
			for (const auto& [texture, sprites] : sprite_groups) {
				auto batch = UIBatchFactory::CreateSpriteBatch(texture,
					static_cast<uint32_t>(sprites.size()));

				// 填充顶点数据...
				// batch.vertex_buffer = CreateVertexBufferFromSprites(sprites);

				m_batch_manager->AddBatch(batch);
			}
		}

		/// 渲染文本
		void RenderText()
		{
			// 按字体分组文本以提高批处理效率
			std::unordered_map<IShader*, std::vector<TextData>> text_groups;

			for (const auto& text : m_text_elements) {
				text_groups[text.font_shader].push_back(text);
			}

			// 为每个字体创建批次
			for (const auto& [font_shader, texts] : text_groups) {
				uint32_t total_chars = 0;
				for (const auto& text : texts) {
					total_chars += static_cast<uint32_t>(wcslen(text.content));
				}

				auto batch = UIBatchFactory::CreateTextBatch(font_shader, total_chars);

				// 填充文本几何数据...

				m_batch_manager->AddBatch(batch);
			}
		}

		/// 渲染特效
		void RenderEffects()
		{
			// 特效通常需要特殊处理，可能不适合批处理
			for (const auto& effect : m_effects) {
				if (effect.can_batch) {
					// 可以批处理的特效
					RenderBatch batch;
					batch.type = BatchType::EFFECT;
					// 设置特效数据...
					m_batch_manager->AddBatch(batch);
				} else {
					// 需要立即渲染的特效
					m_batch_manager->FlushBatches(); // 先提交当前批次
					// 直接渲染特效...
				}
			}
		}

		/// 记录性能统计
		void LogPerformanceStats()
		{
			const auto& stats = m_batch_manager->GetBatchStats();

			// 每100帧输出一次统计信息
			static uint32_t frame_count = 0;
			if (++frame_count % 100 == 0) {
				std::ostringstream oss;
				oss << "批处理性能统计 (第" << frame_count << "帧):\n"
					<< "  总批次数: " << stats.total_batches << "\n"
					<< "  合并批次数: " << stats.merged_batches << "\n"
					<< "  节省DrawCall: " << stats.draw_calls_saved << "\n"
					<< "  状态切换: " << stats.state_changes << "\n"
					<< "  实例化绘制: " << stats.instanced_draws << "\n"
					<< "  批处理效率: " << std::fixed << std::setprecision(1)
					<< stats.batch_efficiency * 100.0f << "%\n"
					<< "  合并率: " << std::fixed << std::setprecision(1)
					<< stats.merge_ratio * 100.0f << "%\n"
					<< "  GPU利用率: " << std::fixed << std::setprecision(1)
					<< stats.gpu_utilization * 100.0f << "%\n";
				OutputDebugStringA(oss.str().c_str());
			}
		}

	private:
		IRenderBatchManager* m_batch_manager{nullptr};

		// 示例数据结构
		struct SpriteData {
			ITexture* texture;
			float x, y, width, height;
			uint32_t color;
		};

		struct TextData {
			const wchar_t* content;
			IShader* font_shader;
			float x, y;
			uint32_t color;
		};

		struct EffectData {
			bool can_batch;
			// 特效相关数据...
		};

		std::vector<SpriteData> m_sprites;
		std::vector<TextData> m_text_elements;
		std::vector<EffectData> m_effects;
		std::vector<int> m_ui_buttons;  // 简化的UI元素
		std::vector<int> m_ui_panels;
	};

	/*
	使用智能批处理系统的最佳实践：

	1. **初始化阶段**：
	   - 在应用程序启动时创建并配置批处理管理器
	   - 根据应用特点调整批处理策略
	   - 设置合适的最大批次数量

	2. **渲染阶段**：
	   - 每帧开始时调用BeginBatch()
	   - 按材质/纹理分组渲染对象以提高合并效率
	   - 透明物体和不透明物体分开处理
	   - 每帧结束时调用EndBatch()

	3. **性能优化**：
	   - 定期监控批处理统计信息
	   - 根据统计数据调整批处理策略
	   - 对于无法批处理的内容，及时调用FlushBatches()

	4. **内存管理**：
	   - 复用顶点缓冲区和索引缓冲区
	   - 使用对象池减少内存分配
	   - 定期清理不再使用的资源

	5. **调试和分析**：
	   - 在调试模式下启用详细的性能统计
	   - 使用性能测试工具验证优化效果
	   - 监控GPU利用率和内存使用情况

	预期性能提升：
	- Draw Call减少：70-80%
	- 状态切换减少：60-70%
	- 整体渲染性能提升：40-60%
	- 内存分配减少：50-60%
	*/

	// =====================================================================================
	// 阶段二：智能内存池系统实现 - C++17
	// =====================================================================================

	/// 智能内存池实现类
	class UIMemoryPool : public ExUnknownImpl<IMemoryPool>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IMemoryPool);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIMemoryPool()
			: m_pool_type(PoolType::SMALL_OBJECTS)
			, m_allocation_strategy(AllocationStrategy::BEST_FIT)
			, m_total_size(0)
			, m_base_ptr(nullptr)
			, m_is_initialized(false)
		{
		}

		virtual ~UIMemoryPool()
		{
			Shutdown();
		}

		// IMemoryPool接口实现
		EXMETHOD HRESULT Initialize(PoolType pool_type, size_t initial_size,
								   AllocationStrategy strategy) override
		{
			if (m_is_initialized) {
				return S_FALSE; // 已经初始化
			}

			try
			{
				m_pool_type = pool_type;
				m_allocation_strategy = strategy;
				m_total_size = initial_size;

				// 分配内存池
				m_base_ptr = _aligned_malloc(initial_size, 64); // 64字节对齐
				if (!m_base_ptr) {
					return E_OUTOFMEMORY;
				}

				// 初始化为一个大的空闲块
				MemoryBlock initial_block(m_base_ptr, initial_size, 64);
				m_free_blocks.push_back(initial_block);

				// 初始化统计信息
				m_stats.total_size = initial_size;
				m_stats.free_size = initial_size;
				m_stats.total_blocks = 1;
				m_stats.free_blocks = 1;

				m_is_initialized = true;
				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void Shutdown() override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);

			if (m_base_ptr) {
				_aligned_free(m_base_ptr);
				m_base_ptr = nullptr;
			}

			m_free_blocks.clear();
			m_used_blocks.clear();
			m_total_size = 0;
			m_is_initialized = false;
		}

		EXMETHOD void* Allocate(size_t size, size_t alignment) override
		{
			if (!m_is_initialized || size == 0) {
				return nullptr;
			}

			std::lock_guard<std::mutex> lock(m_pool_mutex);

			// 对齐大小
			size_t aligned_size = AlignSize(size, alignment);

			// 查找合适的空闲块
			auto block_it = FindSuitableBlock(aligned_size, alignment);
			if (block_it == m_free_blocks.end()) {
				// 尝试扩展内存池
				if (FAILED(ExpandPool(aligned_size * 2))) {
					return nullptr;
				}
				block_it = FindSuitableBlock(aligned_size, alignment);
				if (block_it == m_free_blocks.end()) {
					return nullptr;
				}
			}

			// 分割块（如果需要）
			MemoryBlock allocated_block = SplitBlock(*block_it, aligned_size, alignment);
			m_free_blocks.erase(block_it);

			// 添加到已使用块列表
			allocated_block.is_free = false;
			allocated_block.last_used = std::chrono::steady_clock::now();
			allocated_block.ref_count = 1;
			m_used_blocks[allocated_block.ptr] = allocated_block;

			// 更新统计信息
			m_stats.used_size += allocated_block.size;
			m_stats.free_size -= allocated_block.size;
			m_stats.used_blocks++;
			m_stats.allocation_count++;
			m_stats.CalculateRatios();

			return allocated_block.ptr;
		}

		EXMETHOD void Deallocate(void* ptr) override
		{
			if (!ptr || !m_is_initialized) {
				return;
			}

			std::lock_guard<std::mutex> lock(m_pool_mutex);

			auto it = m_used_blocks.find(ptr);
			if (it == m_used_blocks.end()) {
				return; // 无效指针
			}

			MemoryBlock block = it->second;
			m_used_blocks.erase(it);

			// 标记为空闲
			block.is_free = true;
			block.ref_count = 0;
			m_free_blocks.push_back(block);

			// 尝试合并相邻的空闲块
			CoalesceBlocks();

			// 更新统计信息
			m_stats.used_size -= block.size;
			m_stats.free_size += block.size;
			m_stats.used_blocks--;
			m_stats.deallocation_count++;
			m_stats.CalculateRatios();
		}

		EXMETHOD void* Reallocate(void* ptr, size_t new_size, size_t alignment) override
		{
			if (!ptr) {
				return Allocate(new_size, alignment);
			}

			if (new_size == 0) {
				Deallocate(ptr);
				return nullptr;
			}

			std::lock_guard<std::mutex> lock(m_pool_mutex);

			auto it = m_used_blocks.find(ptr);
			if (it == m_used_blocks.end()) {
				return nullptr; // 无效指针
			}

			size_t old_size = it->second.size;
			size_t aligned_new_size = AlignSize(new_size, alignment);

			// 如果新大小小于等于原大小，直接返回
			if (aligned_new_size <= old_size) {
				return ptr;
			}

			// 分配新内存
			void* new_ptr = Allocate(new_size, alignment);
			if (new_ptr) {
				// 复制数据
				memcpy(new_ptr, ptr, std::min(old_size, aligned_new_size));
				// 释放旧内存
				Deallocate(ptr);
			}

			return new_ptr;
		}

		EXMETHOD bool GetBlockInfo(void* ptr, MemoryBlock& block_info) const override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);

			auto it = m_used_blocks.find(ptr);
			if (it != m_used_blocks.end()) {
				block_info = it->second;
				return true;
			}

			return false;
		}

		EXMETHOD HRESULT DefragmentMemory() override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);

			try
			{
				// 合并所有相邻的空闲块
				CoalesceBlocks();

				// 重新排列已使用的块以减少碎片
				CompactUsedBlocks();

				// 更新碎片统计
				UpdateFragmentationStats();

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT ShrinkToFit() override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);

			try
			{
				// 计算实际需要的大小
				size_t required_size = m_stats.used_size;

				// 保留一些额外空间以避免频繁扩展
				size_t target_size = required_size + (required_size / 4); // 25%额外空间

				if (target_size < m_total_size) {
					// 重新分配更小的内存池
					void* new_base = _aligned_malloc(target_size, 64);
					if (new_base) {
						// 复制已使用的数据
						CopyUsedBlocks(new_base, target_size);

						// 释放旧内存
						_aligned_free(m_base_ptr);
						m_base_ptr = new_base;
						m_total_size = target_size;

						// 重建空闲块列表
						RebuildFreeBlocks();
					}
				}

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT Reserve(size_t size) override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);

			if (size > m_total_size) {
				return ExpandPool(size - m_total_size);
			}

			return S_OK;
		}

		EXMETHOD const PoolStats& GetStats() const override
		{
			return m_stats;
		}

		EXMETHOD void ResetStats() override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);

			m_stats.allocation_count = 0;
			m_stats.deallocation_count = 0;
			m_stats.fragmentation_count = 0;
			m_stats.CalculateRatios();
		}

		EXMETHOD void SetAllocationStrategy(AllocationStrategy strategy) override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);
			m_allocation_strategy = strategy;
		}

		EXMETHOD PoolType GetPoolType() const override
		{
			return m_pool_type;
		}

		EXMETHOD bool IsHealthy() const override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);

			// 检查内存池健康状态
			bool healthy = true;

			// 检查碎片率
			if (m_stats.fragmentation_ratio > 0.5f) {
				healthy = false; // 碎片率过高
			}

			// 检查利用率
			if (m_stats.utilization_ratio < 0.1f && m_stats.used_size > 0) {
				healthy = false; // 利用率过低
			}

			// 检查内存泄漏
			if (m_stats.allocation_count > m_stats.deallocation_count + m_stats.used_blocks) {
				healthy = false; // 可能存在内存泄漏
			}

			return healthy;
		}

	private:
		/// 对齐大小
		size_t AlignSize(size_t size, size_t alignment) const
		{
			return (size + alignment - 1) & ~(alignment - 1);
		}

		/// 查找合适的空闲块
		std::vector<MemoryBlock>::iterator FindSuitableBlock(size_t size, size_t alignment)
		{
			switch (m_allocation_strategy) {
			case AllocationStrategy::FIRST_FIT:
				return FindFirstFit(size, alignment);
			case AllocationStrategy::BEST_FIT:
				return FindBestFit(size, alignment);
			case AllocationStrategy::WORST_FIT:
				return FindWorstFit(size, alignment);
			case AllocationStrategy::BUDDY_SYSTEM:
				return FindBuddyBlock(size, alignment);
			default:
				return FindBestFit(size, alignment);
			}
		}

		/// 首次适应算法
		std::vector<MemoryBlock>::iterator FindFirstFit(size_t size, size_t alignment)
		{
			for (auto it = m_free_blocks.begin(); it != m_free_blocks.end(); ++it) {
				if (it->size >= size && IsAligned(it->ptr, alignment)) {
					return it;
				}
			}
			return m_free_blocks.end();
		}

		/// 最佳适应算法
		std::vector<MemoryBlock>::iterator FindBestFit(size_t size, size_t alignment)
		{
			auto best_it = m_free_blocks.end();
			size_t best_size = SIZE_MAX;

			for (auto it = m_free_blocks.begin(); it != m_free_blocks.end(); ++it) {
				if (it->size >= size && IsAligned(it->ptr, alignment) && it->size < best_size) {
					best_it = it;
					best_size = it->size;
				}
			}

			return best_it;
		}

		/// 最坏适应算法
		std::vector<MemoryBlock>::iterator FindWorstFit(size_t size, size_t alignment)
		{
			auto worst_it = m_free_blocks.end();
			size_t worst_size = 0;

			for (auto it = m_free_blocks.begin(); it != m_free_blocks.end(); ++it) {
				if (it->size >= size && IsAligned(it->ptr, alignment) && it->size > worst_size) {
					worst_it = it;
					worst_size = it->size;
				}
			}

			return worst_it;
		}

		/// 伙伴系统算法
		std::vector<MemoryBlock>::iterator FindBuddyBlock(size_t size, size_t alignment)
		{
			// 简化的伙伴系统实现
			size_t buddy_size = NextPowerOfTwo(size);

			for (auto it = m_free_blocks.begin(); it != m_free_blocks.end(); ++it) {
				if (it->size >= buddy_size && IsAligned(it->ptr, alignment)) {
					return it;
				}
			}

			return m_free_blocks.end();
		}

		/// 检查指针是否对齐
		bool IsAligned(void* ptr, size_t alignment) const
		{
			return (reinterpret_cast<uintptr_t>(ptr) % alignment) == 0;
		}

		/// 获取下一个2的幂
		size_t NextPowerOfTwo(size_t size) const
		{
			size_t power = 1;
			while (power < size) {
				power <<= 1;
			}
			return power;
		}

		/// 分割内存块
		MemoryBlock SplitBlock(const MemoryBlock& block, size_t size, size_t alignment)
		{
			MemoryBlock allocated_block = block;
			allocated_block.size = size;

			// 如果剩余空间足够大，创建新的空闲块
			if (block.size > size + sizeof(void*)) {
				MemoryBlock remaining_block;
				remaining_block.ptr = static_cast<char*>(block.ptr) + size;
				remaining_block.size = block.size - size;
				remaining_block.alignment = alignment;
				remaining_block.is_free = true;
				remaining_block.last_used = std::chrono::steady_clock::now();

				m_free_blocks.push_back(remaining_block);
				m_stats.free_blocks++;
			}

			return allocated_block;
		}

		/// 合并相邻的空闲块
		void CoalesceBlocks()
		{
			if (m_free_blocks.size() < 2) {
				return;
			}

			// 按地址排序
			std::sort(m_free_blocks.begin(), m_free_blocks.end(),
				[](const MemoryBlock& a, const MemoryBlock& b) {
					return a.ptr < b.ptr;
				});

			// 合并相邻块
			auto it = m_free_blocks.begin();
			while (it != m_free_blocks.end() && (it + 1) != m_free_blocks.end()) {
				char* current_end = static_cast<char*>(it->ptr) + it->size;
				char* next_start = static_cast<char*>((it + 1)->ptr);

				if (current_end == next_start) {
					// 合并块
					it->size += (it + 1)->size;
					it = m_free_blocks.erase(it + 1) - 1;
					m_stats.free_blocks--;
				} else {
					++it;
				}
			}
		}

		/// 压缩已使用的块
		void CompactUsedBlocks()
		{
			// 简化实现：重新排列已使用的块以减少碎片
			// 在实际实现中，这需要移动内存数据，比较复杂
			// 这里只是更新统计信息
			UpdateFragmentationStats();
		}

		/// 更新碎片统计
		void UpdateFragmentationStats()
		{
			m_stats.fragmentation_count = 0;

			// 计算碎片数量（小于平均块大小的空闲块）
			if (!m_free_blocks.empty()) {
				size_t total_free_size = 0;
				for (const auto& block : m_free_blocks) {
					total_free_size += block.size;
				}

				size_t avg_block_size = total_free_size / m_free_blocks.size();

				for (const auto& block : m_free_blocks) {
					if (block.size < avg_block_size / 2) {
						m_stats.fragmentation_count++;
					}
				}
			}

			m_stats.CalculateRatios();
		}

		/// 复制已使用的块到新内存
		void CopyUsedBlocks(void* new_base, size_t new_size)
		{
			char* dest = static_cast<char*>(new_base);
			size_t offset = 0;

			// 重新映射已使用的块
			std::unordered_map<void*, MemoryBlock> new_used_blocks;

			for (auto& [old_ptr, block] : m_used_blocks) {
				if (offset + block.size <= new_size) {
					// 复制数据
					memcpy(dest + offset, old_ptr, block.size);

					// 更新块信息
					block.ptr = dest + offset;
					new_used_blocks[block.ptr] = block;

					offset += block.size;
				}
			}

			m_used_blocks = std::move(new_used_blocks);
		}

		/// 重建空闲块列表
		void RebuildFreeBlocks()
		{
			m_free_blocks.clear();

			// 计算已使用的内存范围
			std::vector<std::pair<char*, char*>> used_ranges;
			for (const auto& [ptr, block] : m_used_blocks) {
				char* start = static_cast<char*>(ptr);
				char* end = start + block.size;
				used_ranges.emplace_back(start, end);
			}

			// 排序已使用的范围
			std::sort(used_ranges.begin(), used_ranges.end());

			// 创建空闲块
			char* pool_start = static_cast<char*>(m_base_ptr);
			char* pool_end = pool_start + m_total_size;
			char* current = pool_start;

			for (const auto& [used_start, used_end] : used_ranges) {
				if (current < used_start) {
					// 创建空闲块
					MemoryBlock free_block;
					free_block.ptr = current;
					free_block.size = used_start - current;
					free_block.is_free = true;
					free_block.last_used = std::chrono::steady_clock::now();
					m_free_blocks.push_back(free_block);
				}
				current = used_end;
			}

			// 处理最后的空闲空间
			if (current < pool_end) {
				MemoryBlock free_block;
				free_block.ptr = current;
				free_block.size = pool_end - current;
				free_block.is_free = true;
				free_block.last_used = std::chrono::steady_clock::now();
				m_free_blocks.push_back(free_block);
			}

			// 更新统计信息
			m_stats.free_blocks = static_cast<uint32_t>(m_free_blocks.size());
			m_stats.free_size = 0;
			for (const auto& block : m_free_blocks) {
				m_stats.free_size += block.size;
			}
		}

		/// 扩展内存池
		HRESULT ExpandPool(size_t additional_size)
		{
			try
			{
				size_t new_total_size = m_total_size + additional_size;
				void* new_base = _aligned_malloc(new_total_size, 64);

				if (!new_base) {
					return E_OUTOFMEMORY;
				}

				// 复制现有数据
				memcpy(new_base, m_base_ptr, m_total_size);

				// 更新指针
				ptrdiff_t offset = static_cast<char*>(new_base) - static_cast<char*>(m_base_ptr);

				// 更新已使用块的指针
				std::unordered_map<void*, MemoryBlock> new_used_blocks;
				for (auto& [old_ptr, block] : m_used_blocks) {
					block.ptr = static_cast<char*>(block.ptr) + offset;
					new_used_blocks[block.ptr] = block;
				}
				m_used_blocks = std::move(new_used_blocks);

				// 更新空闲块的指针
				for (auto& block : m_free_blocks) {
					block.ptr = static_cast<char*>(block.ptr) + offset;
				}

				// 添加新的空闲块
				MemoryBlock new_block;
				new_block.ptr = static_cast<char*>(new_base) + m_total_size;
				new_block.size = additional_size;
				new_block.is_free = true;
				new_block.last_used = std::chrono::steady_clock::now();
				m_free_blocks.push_back(new_block);

				// 释放旧内存
				_aligned_free(m_base_ptr);
				m_base_ptr = new_base;
				m_total_size = new_total_size;

				// 更新统计信息
				m_stats.total_size = new_total_size;
				m_stats.free_size += additional_size;
				m_stats.total_blocks++;
				m_stats.free_blocks++;

				return S_OK;
			}
			catch_default({});
		}

	private:
		PoolType m_pool_type;
		AllocationStrategy m_allocation_strategy;
		size_t m_total_size;
		void* m_base_ptr;
		bool m_is_initialized;

		std::vector<MemoryBlock> m_free_blocks;
		std::unordered_map<void*, MemoryBlock> m_used_blocks;

		PoolStats m_stats;
		mutable std::mutex m_pool_mutex;
	};

	/// GPU资源池实现类
	class UIGPUResourcePool : public ExUnknownImpl<IGPUResourcePool>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IGPUResourcePool);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIGPUResourcePool()
			: m_render_manager(nullptr)
			, m_pool_type(PoolType::VERTEX_BUFFERS)
			, m_max_count(1000)
			, m_max_memory(100 * 1024 * 1024) // 100MB
			, m_is_initialized(false)
		{
		}

		virtual ~UIGPUResourcePool()
		{
			Shutdown();
		}

		// IGPUResourcePool接口实现
		EXMETHOD HRESULT Initialize(IRenderManager* render_manager, PoolType pool_type,
								   uint32_t initial_count) override
		{
			if (m_is_initialized || !render_manager) {
				return E_INVALIDARG;
			}

			try
			{
				m_render_manager = render_manager;
				m_render_manager->AddRef();
				m_pool_type = pool_type;

				// 预创建资源
				throw_if_failed(
					WarmupPool(initial_count),
					L"预热GPU资源池失败"
				);

				m_is_initialized = true;
				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void Shutdown() override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);

			// 释放所有缓冲区
			for (auto& buffer : m_free_buffers) {
				if (buffer) {
					buffer->Release();
				}
			}
			m_free_buffers.clear();

			for (auto& [ptr, buffer] : m_used_buffers) {
				if (buffer) {
					buffer->Release();
				}
			}
			m_used_buffers.clear();

			// 释放所有纹理
			for (auto& texture : m_free_textures) {
				if (texture) {
					texture->Release();
				}
			}
			m_free_textures.clear();

			for (auto& [ptr, texture] : m_used_textures) {
				if (texture) {
					texture->Release();
				}
			}
			m_used_textures.clear();

			// 释放所有着色器
			for (auto& shader : m_free_shaders) {
				if (shader) {
					shader->Release();
				}
			}
			m_free_shaders.clear();

			for (auto& [ptr, shader] : m_used_shaders) {
				if (shader) {
					shader->Release();
				}
			}
			m_used_shaders.clear();

			if (m_render_manager) {
				m_render_manager->Release();
				m_render_manager = nullptr;
			}

			m_is_initialized = false;
		}

		EXMETHOD HRESULT AcquireBuffer(const GPUResourceDesc& desc, IBuffer** buffer) override
		{
			if (!buffer || !m_is_initialized) {
				return E_INVALIDARG;
			}

			std::lock_guard<std::mutex> lock(m_pool_mutex);

			try
			{
				// 查找匹配的空闲缓冲区
				auto it = FindMatchingBuffer(desc);
				if (it != m_free_buffers.end()) {
					*buffer = *it;
					(*buffer)->AddRef();

					m_used_buffers[*buffer] = *buffer;
					m_free_buffers.erase(it);

					m_stats.used_blocks++;
					m_stats.free_blocks--;
					return S_OK;
				}

				// 如果没有找到，创建新的缓冲区
				IBuffer* new_buffer = nullptr;
				throw_if_failed(
					CreateNewBuffer(desc, &new_buffer),
					L"创建新缓冲区失败"
				);

				*buffer = new_buffer;
				m_used_buffers[new_buffer] = new_buffer;

				m_stats.total_blocks++;
				m_stats.used_blocks++;
				m_stats.allocation_count++;

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void ReleaseBuffer(IBuffer* buffer) override
		{
			if (!buffer || !m_is_initialized) {
				return;
			}

			std::lock_guard<std::mutex> lock(m_pool_mutex);

			auto it = m_used_buffers.find(buffer);
			if (it != m_used_buffers.end()) {
				// 检查池是否已满
				if (m_free_buffers.size() < m_max_count) {
					m_free_buffers.push_back(buffer);
					m_stats.free_blocks++;
				} else {
					// 池已满，直接释放
					buffer->Release();
					m_stats.total_blocks--;
				}

				m_used_buffers.erase(it);
				m_stats.used_blocks--;
				m_stats.deallocation_count++;
			}
		}

		EXMETHOD HRESULT AcquireTexture(const GPUResourceDesc& desc, ITexture** texture) override
		{
			if (!texture || !m_is_initialized) {
				return E_INVALIDARG;
			}

			std::lock_guard<std::mutex> lock(m_pool_mutex);

			try
			{
				// 查找匹配的空闲纹理
				auto it = FindMatchingTexture(desc);
				if (it != m_free_textures.end()) {
					*texture = *it;
					(*texture)->AddRef();

					m_used_textures[*texture] = *texture;
					m_free_textures.erase(it);

					m_stats.used_blocks++;
					m_stats.free_blocks--;
					return S_OK;
				}

				// 如果没有找到，创建新的纹理
				ITexture* new_texture = nullptr;
				throw_if_failed(
					CreateNewTexture(desc, &new_texture),
					L"创建新纹理失败"
				);

				*texture = new_texture;
				m_used_textures[new_texture] = new_texture;

				m_stats.total_blocks++;
				m_stats.used_blocks++;
				m_stats.allocation_count++;

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void ReleaseTexture(ITexture* texture) override
		{
			if (!texture || !m_is_initialized) {
				return;
			}

			std::lock_guard<std::mutex> lock(m_pool_mutex);

			auto it = m_used_textures.find(texture);
			if (it != m_used_textures.end()) {
				// 检查池是否已满
				if (m_free_textures.size() < m_max_count) {
					m_free_textures.push_back(texture);
					m_stats.free_blocks++;
				} else {
					// 池已满，直接释放
					texture->Release();
					m_stats.total_blocks--;
				}

				m_used_textures.erase(it);
				m_stats.used_blocks--;
				m_stats.deallocation_count++;
			}
		}

		EXMETHOD HRESULT AcquireShader(const GPUResourceDesc& desc, IShader** shader) override
		{
			if (!shader || !m_is_initialized) {
				return E_INVALIDARG;
			}

			std::lock_guard<std::mutex> lock(m_pool_mutex);

			try
			{
				// 查找匹配的空闲着色器
				auto it = FindMatchingShader(desc);
				if (it != m_free_shaders.end()) {
					*shader = *it;
					(*shader)->AddRef();

					m_used_shaders[*shader] = *shader;
					m_free_shaders.erase(it);

					m_stats.used_blocks++;
					m_stats.free_blocks--;
					return S_OK;
				}

				// 着色器通常不适合池化，因为它们是唯一的
				// 这里返回错误，让调用者直接创建
				return E_NOTIMPL;
			}
			catch_default({});
		}

		EXMETHOD void ReleaseShader(IShader* shader) override
		{
			if (!shader || !m_is_initialized) {
				return;
			}

			std::lock_guard<std::mutex> lock(m_pool_mutex);

			auto it = m_used_shaders.find(shader);
			if (it != m_used_shaders.end()) {
				// 着色器通常不重用，直接释放
				shader->Release();
				m_used_shaders.erase(it);
				m_stats.used_blocks--;
				m_stats.total_blocks--;
				m_stats.deallocation_count++;
			}
		}

		EXMETHOD HRESULT WarmupPool(uint32_t count) override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);

			try
			{
				for (uint32_t i = 0; i < count; ++i) {
					if (m_pool_type == PoolType::VERTEX_BUFFERS || m_pool_type == PoolType::INDEX_BUFFERS) {
						// 创建标准大小的缓冲区
						GPUResourceDesc desc;
						desc.pool_type = m_pool_type;
						desc.size = GetStandardBufferSize();
						desc.usage_flags = D3D11_USAGE_DYNAMIC;
						desc.bind_flags = (m_pool_type == PoolType::VERTEX_BUFFERS) ?
							D3D11_BIND_VERTEX_BUFFER : D3D11_BIND_INDEX_BUFFER;
						desc.cpu_access_flags = D3D11_CPU_ACCESS_WRITE;

						IBuffer* buffer = nullptr;
						if (SUCCEEDED(CreateNewBuffer(desc, &buffer))) {
							m_free_buffers.push_back(buffer);
							m_stats.total_blocks++;
							m_stats.free_blocks++;
						}
					}
					else if (m_pool_type == PoolType::TEXTURES) {
						// 创建标准大小的纹理
						GPUResourceDesc desc;
						desc.pool_type = m_pool_type;
						desc.width = 256;
						desc.height = 256;
						desc.format = DXGI_FORMAT_R8G8B8A8_UNORM;
						desc.usage_flags = D3D11_USAGE_DEFAULT;
						desc.bind_flags = D3D11_BIND_SHADER_RESOURCE;

						ITexture* texture = nullptr;
						if (SUCCEEDED(CreateNewTexture(desc, &texture))) {
							m_free_textures.push_back(texture);
							m_stats.total_blocks++;
							m_stats.free_blocks++;
						}
					}
				}

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT CleanupUnusedResources(uint32_t max_age_seconds) override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);

			auto now = std::chrono::steady_clock::now();
			auto max_age = std::chrono::seconds(max_age_seconds);

			// 清理旧的缓冲区
			auto buffer_it = m_free_buffers.begin();
			while (buffer_it != m_free_buffers.end()) {
				// 这里需要跟踪资源的创建时间，简化实现
				// 实际应该在资源上添加时间戳
				++buffer_it; // 暂时不清理
			}

			// 清理旧的纹理
			auto texture_it = m_free_textures.begin();
			while (texture_it != m_free_textures.end()) {
				// 同样需要时间戳
				++texture_it; // 暂时不清理
			}

			return S_OK;
		}

		EXMETHOD const PoolStats& GetPoolStats() const override
		{
			return m_stats;
		}

		EXMETHOD void SetPoolSizeLimit(uint32_t max_count, size_t max_memory) override
		{
			std::lock_guard<std::mutex> lock(m_pool_mutex);
			m_max_count = max_count;
			m_max_memory = max_memory;
		}

		EXMETHOD PoolType GetPoolType() const override
		{
			return m_pool_type;
		}

	private:
		/// 查找匹配的缓冲区
		std::vector<IBuffer*>::iterator FindMatchingBuffer(const GPUResourceDesc& desc)
		{
			return std::find_if(m_free_buffers.begin(), m_free_buffers.end(),
				[&desc](IBuffer* buffer) {
					// 简化的匹配逻辑，实际应该检查缓冲区属性
					return true; // 暂时返回第一个可用的
				});
		}

		/// 查找匹配的纹理
		std::vector<ITexture*>::iterator FindMatchingTexture(const GPUResourceDesc& desc)
		{
			return std::find_if(m_free_textures.begin(), m_free_textures.end(),
				[&desc](ITexture* texture) {
					// 简化的匹配逻辑，实际应该检查纹理属性
					return true; // 暂时返回第一个可用的
				});
		}

		/// 查找匹配的着色器
		std::vector<IShader*>::iterator FindMatchingShader(const GPUResourceDesc& desc)
		{
			return std::find_if(m_free_shaders.begin(), m_free_shaders.end(),
				[&desc](IShader* shader) {
					// 着色器通常是唯一的，不适合池化
					return false;
				});
		}

		/// 创建新缓冲区
		HRESULT CreateNewBuffer(const GPUResourceDesc& desc, IBuffer** buffer)
		{
			return m_render_manager->CreateBuffer(
				(desc.pool_type == PoolType::VERTEX_BUFFERS) ? BufferType::VERTEX : BufferType::INDEX,
				buffer
			);
		}

		/// 创建新纹理
		HRESULT CreateNewTexture(const GPUResourceDesc& desc, ITexture** texture)
		{
			return m_render_manager->CreateTexture2D(
				desc.width, desc.height, desc.format, texture
			);
		}

		/// 获取标准缓冲区大小
		size_t GetStandardBufferSize() const
		{
			switch (m_pool_type) {
			case PoolType::VERTEX_BUFFERS:
				return 64 * 1024; // 64KB
			case PoolType::INDEX_BUFFERS:
				return 32 * 1024; // 32KB
			case PoolType::CONSTANT_BUFFERS:
				return 4 * 1024;  // 4KB
			default:
				return 16 * 1024; // 16KB
			}
		}

	private:
		IRenderManager* m_render_manager;
		PoolType m_pool_type;
		uint32_t m_max_count;
		size_t m_max_memory;
		bool m_is_initialized;

		// 缓冲区池
		std::vector<IBuffer*> m_free_buffers;
		std::unordered_map<IBuffer*, IBuffer*> m_used_buffers;

		// 纹理池
		std::vector<ITexture*> m_free_textures;
		std::unordered_map<ITexture*, ITexture*> m_used_textures;

		// 着色器池
		std::vector<IShader*> m_free_shaders;
		std::unordered_map<IShader*, IShader*> m_used_shaders;

		PoolStats m_stats;
		mutable std::mutex m_pool_mutex;
	};

	/// 内存池管理器实现类
	class UIMemoryPoolManager : public ExUnknownImpl<IMemoryPoolManager>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IMemoryPoolManager);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIMemoryPoolManager()
			: m_render_manager(nullptr)
			, m_global_memory_limit(1024 * 1024 * 1024) // 1GB默认限制
			, m_auto_defrag_enabled(false)
			, m_auto_defrag_interval(60)
			, m_is_initialized(false)
		{
		}

		virtual ~UIMemoryPoolManager()
		{
			Shutdown();
		}

		// IMemoryPoolManager接口实现
		EXMETHOD HRESULT Initialize(IRenderManager* render_manager) override
		{
			if (m_is_initialized || !render_manager) {
				return E_INVALIDARG;
			}

			try
			{
				m_render_manager = render_manager;
				m_render_manager->AddRef();

				// 创建默认的内存池
				throw_if_failed(
					CreateDefaultPools(),
					L"创建默认内存池失败"
				);

				// 启动自动整理线程
				if (m_auto_defrag_enabled) {
					StartAutoDefragmentationThread();
				}

				m_is_initialized = true;
				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void Shutdown() override
		{
			std::lock_guard<std::mutex> lock(m_manager_mutex);

			// 停止自动整理线程
			StopAutoDefragmentationThread();

			// 释放所有内存池
			for (auto& [type, pool] : m_memory_pools) {
				if (pool) {
					pool->Release();
				}
			}
			m_memory_pools.clear();

			// 释放所有GPU资源池
			for (auto& [type, pool] : m_gpu_resource_pools) {
				if (pool) {
					pool->Release();
				}
			}
			m_gpu_resource_pools.clear();

			if (m_render_manager) {
				m_render_manager->Release();
				m_render_manager = nullptr;
			}

			m_is_initialized = false;
		}

		EXMETHOD HRESULT CreateMemoryPool(PoolType pool_type, size_t initial_size,
										 AllocationStrategy strategy, IMemoryPool** pool) override
		{
			if (!pool || !m_is_initialized) {
				return E_INVALIDARG;
			}

			try
			{
				auto memory_pool = new UIMemoryPool();
				throw_if_failed(
					memory_pool->Initialize(pool_type, initial_size, strategy),
					L"初始化内存池失败"
				);

				*pool = memory_pool;

				// 添加到管理列表
				std::lock_guard<std::mutex> lock(m_manager_mutex);
				m_memory_pools[pool_type] = memory_pool;
				memory_pool->AddRef(); // 管理器持有引用

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT CreateGPUResourcePool(PoolType pool_type, uint32_t initial_count,
											  IGPUResourcePool** pool) override
		{
			if (!pool || !m_is_initialized) {
				return E_INVALIDARG;
			}

			try
			{
				auto gpu_pool = new UIGPUResourcePool();
				throw_if_failed(
					gpu_pool->Initialize(m_render_manager, pool_type, initial_count),
					L"初始化GPU资源池失败"
				);

				*pool = gpu_pool;

				// 添加到管理列表
				std::lock_guard<std::mutex> lock(m_manager_mutex);
				m_gpu_resource_pools[pool_type] = gpu_pool;
				gpu_pool->AddRef(); // 管理器持有引用

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD IMemoryPool* GetMemoryPool(PoolType pool_type) override
		{
			std::lock_guard<std::mutex> lock(m_manager_mutex);

			auto it = m_memory_pools.find(pool_type);
			if (it != m_memory_pools.end()) {
				it->second->AddRef();
				return it->second;
			}

			return nullptr;
		}

		EXMETHOD IGPUResourcePool* GetGPUResourcePool(PoolType pool_type) override
		{
			std::lock_guard<std::mutex> lock(m_manager_mutex);

			auto it = m_gpu_resource_pools.find(pool_type);
			if (it != m_gpu_resource_pools.end()) {
				it->second->AddRef();
				return it->second;
			}

			return nullptr;
		}

		EXMETHOD void* GlobalAllocate(size_t size, size_t alignment) override
		{
			// 选择最合适的内存池
			PoolType pool_type = SelectOptimalPool(size);

			auto pool = GetMemoryPool(pool_type);
			if (pool) {
				void* ptr = pool->Allocate(size, alignment);
				pool->Release();
				return ptr;
			}

			// 如果没有合适的池，使用系统分配
			return _aligned_malloc(size, alignment);
		}

		EXMETHOD void GlobalDeallocate(void* ptr) override
		{
			if (!ptr) return;

			// 尝试在所有池中查找这个指针
			std::lock_guard<std::mutex> lock(m_manager_mutex);

			for (auto& [type, pool] : m_memory_pools) {
				MemoryBlock block_info;
				if (pool->GetBlockInfo(ptr, block_info)) {
					pool->Deallocate(ptr);
					return;
				}
			}

			// 如果在池中没找到，使用系统释放
			_aligned_free(ptr);
		}

		EXMETHOD void* SmartAllocate(size_t size, size_t alignment) override
		{
			// 智能分配：根据大小、使用模式等选择最佳策略
			PoolType pool_type = SelectOptimalPool(size);
			AllocationStrategy strategy = SelectOptimalStrategy(size, pool_type);

			auto pool = GetMemoryPool(pool_type);
			if (pool) {
				pool->SetAllocationStrategy(strategy);
				void* ptr = pool->Allocate(size, alignment);
				pool->Release();
				return ptr;
			}

			return GlobalAllocate(size, alignment);
		}

		EXMETHOD void GetGlobalStats(PoolStats& total_stats) const override
		{
			std::lock_guard<std::mutex> lock(m_manager_mutex);

			total_stats = PoolStats{}; // 重置

			// 汇总所有内存池的统计信息
			for (const auto& [type, pool] : m_memory_pools) {
				const auto& pool_stats = pool->GetStats();
				total_stats.total_size += pool_stats.total_size;
				total_stats.used_size += pool_stats.used_size;
				total_stats.free_size += pool_stats.free_size;
				total_stats.total_blocks += pool_stats.total_blocks;
				total_stats.used_blocks += pool_stats.used_blocks;
				total_stats.free_blocks += pool_stats.free_blocks;
				total_stats.allocation_count += pool_stats.allocation_count;
				total_stats.deallocation_count += pool_stats.deallocation_count;
				total_stats.fragmentation_count += pool_stats.fragmentation_count;
			}

			// 汇总GPU资源池的统计信息
			for (const auto& [type, pool] : m_gpu_resource_pools) {
				const auto& pool_stats = pool->GetPoolStats();
				total_stats.total_blocks += pool_stats.total_blocks;
				total_stats.used_blocks += pool_stats.used_blocks;
				total_stats.free_blocks += pool_stats.free_blocks;
				total_stats.allocation_count += pool_stats.allocation_count;
				total_stats.deallocation_count += pool_stats.deallocation_count;
			}

			total_stats.CalculateRatios();
		}

		EXMETHOD HRESULT GlobalDefragmentation() override
		{
			std::lock_guard<std::mutex> lock(m_manager_mutex);

			try
			{
				// 对所有内存池执行碎片整理
				for (auto& [type, pool] : m_memory_pools) {
					throw_if_failed(
						pool->DefragmentMemory(),
						L"内存池碎片整理失败"
					);
				}

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT GlobalCleanup(uint32_t max_age_seconds) override
		{
			std::lock_guard<std::mutex> lock(m_manager_mutex);

			try
			{
				// 清理所有GPU资源池
				for (auto& [type, pool] : m_gpu_resource_pools) {
					throw_if_failed(
						pool->CleanupUnusedResources(max_age_seconds),
						L"GPU资源池清理失败"
					);
				}

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void SetGlobalMemoryLimit(size_t max_memory_bytes) override
		{
			std::lock_guard<std::mutex> lock(m_manager_mutex);
			m_global_memory_limit = max_memory_bytes;
		}

		EXMETHOD void GenerateMemoryReport(std::string& report) const override
		{
			std::lock_guard<std::mutex> lock(m_manager_mutex);

			std::ostringstream oss;
			oss << "=== HHBUI内存池管理器报告 ===\n\n";

			// 全局统计
			PoolStats global_stats;
			const_cast<UIMemoryPoolManager*>(this)->GetGlobalStats(global_stats);

			oss << "全局统计:\n";
			oss << "  总内存: " << global_stats.total_size / 1024 / 1024 << " MB\n";
			oss << "  已使用: " << global_stats.used_size / 1024 / 1024 << " MB\n";
			oss << "  空闲: " << global_stats.free_size / 1024 / 1024 << " MB\n";
			oss << "  利用率: " << std::fixed << std::setprecision(2)
				<< global_stats.utilization_ratio * 100.0f << "%\n";
			oss << "  碎片率: " << std::fixed << std::setprecision(2)
				<< global_stats.fragmentation_ratio * 100.0f << "%\n\n";

			// 各个内存池的详细信息
			oss << "内存池详情:\n";
			for (const auto& [type, pool] : m_memory_pools) {
				const auto& stats = pool->GetStats();
				oss << "  " << GetPoolTypeName(type) << ":\n";
				oss << "    大小: " << stats.total_size / 1024 << " KB\n";
				oss << "    已用: " << stats.used_size / 1024 << " KB\n";
				oss << "    利用率: " << std::fixed << std::setprecision(1)
					<< stats.utilization_ratio * 100.0f << "%\n";
				oss << "    分配次数: " << stats.allocation_count << "\n";
				oss << "    释放次数: " << stats.deallocation_count << "\n\n";
			}

			// GPU资源池信息
			oss << "GPU资源池详情:\n";
			for (const auto& [type, pool] : m_gpu_resource_pools) {
				const auto& stats = pool->GetPoolStats();
				oss << "  " << GetPoolTypeName(type) << ":\n";
				oss << "    总资源: " << stats.total_blocks << "\n";
				oss << "    已用资源: " << stats.used_blocks << "\n";
				oss << "    空闲资源: " << stats.free_blocks << "\n";
				oss << "    利用率: " << std::fixed << std::setprecision(1)
					<< stats.utilization_ratio * 100.0f << "%\n\n";
			}

			report = oss.str();
		}

		EXMETHOD void SetAutoDefragmentation(bool enabled, uint32_t interval_seconds) override
		{
			std::lock_guard<std::mutex> lock(m_manager_mutex);

			if (m_auto_defrag_enabled != enabled) {
				m_auto_defrag_enabled = enabled;
				m_auto_defrag_interval = interval_seconds;

				if (enabled && m_is_initialized) {
					StartAutoDefragmentationThread();
				} else {
					StopAutoDefragmentationThread();
				}
			}
		}

		EXMETHOD void RegisterMemoryEventCallback(void* callback_func, void* user_data) override
		{
			std::lock_guard<std::mutex> lock(m_manager_mutex);
			// 简化实现，实际应该支持多个回调
			m_memory_event_callback = callback_func;
			m_callback_user_data = user_data;
		}

	private:
		/// 创建默认内存池
		HRESULT CreateDefaultPools()
		{
			try
			{
				// 创建小对象池
				IMemoryPool* small_pool = nullptr;
				throw_if_failed(
					CreateMemoryPool(PoolType::SMALL_OBJECTS, 1024 * 1024, // 1MB
									AllocationStrategy::BEST_FIT, &small_pool),
					L"创建小对象池失败"
				);
				small_pool->Release(); // 管理器已持有引用

				// 创建中等对象池
				IMemoryPool* medium_pool = nullptr;
				throw_if_failed(
					CreateMemoryPool(PoolType::MEDIUM_OBJECTS, 16 * 1024 * 1024, // 16MB
									AllocationStrategy::FIRST_FIT, &medium_pool),
					L"创建中等对象池失败"
				);
				medium_pool->Release();

				// 创建大对象池
				IMemoryPool* large_pool = nullptr;
				throw_if_failed(
					CreateMemoryPool(PoolType::LARGE_OBJECTS, 64 * 1024 * 1024, // 64MB
									AllocationStrategy::WORST_FIT, &large_pool),
					L"创建大对象池失败"
				);
				large_pool->Release();

				// 创建GPU资源池
				IGPUResourcePool* vertex_pool = nullptr;
				throw_if_failed(
					CreateGPUResourcePool(PoolType::VERTEX_BUFFERS, 100, &vertex_pool),
					L"创建顶点缓冲区池失败"
				);
				vertex_pool->Release();

				IGPUResourcePool* index_pool = nullptr;
				throw_if_failed(
					CreateGPUResourcePool(PoolType::INDEX_BUFFERS, 100, &index_pool),
					L"创建索引缓冲区池失败"
				);
				index_pool->Release();

				IGPUResourcePool* texture_pool = nullptr;
				throw_if_failed(
					CreateGPUResourcePool(PoolType::TEXTURES, 50, &texture_pool),
					L"创建纹理池失败"
				);
				texture_pool->Release();

				return S_OK;
			}
			catch_default({});
		}

		/// 选择最优内存池
		PoolType SelectOptimalPool(size_t size) const
		{
			if (size < 1024) {
				return PoolType::SMALL_OBJECTS;
			} else if (size < 64 * 1024) {
				return PoolType::MEDIUM_OBJECTS;
			} else {
				return PoolType::LARGE_OBJECTS;
			}
		}

		/// 选择最优分配策略
		AllocationStrategy SelectOptimalStrategy(size_t size, PoolType pool_type) const
		{
			// 根据大小和池类型选择最佳策略
			if (pool_type == PoolType::SMALL_OBJECTS) {
				return AllocationStrategy::BEST_FIT; // 小对象使用最佳适应
			} else if (pool_type == PoolType::MEDIUM_OBJECTS) {
				return AllocationStrategy::FIRST_FIT; // 中等对象使用首次适应
			} else {
				return AllocationStrategy::WORST_FIT; // 大对象使用最坏适应
			}
		}

		/// 获取池类型名称
		const char* GetPoolTypeName(PoolType type) const
		{
			switch (type) {
			case PoolType::SMALL_OBJECTS: return "小对象池";
			case PoolType::MEDIUM_OBJECTS: return "中等对象池";
			case PoolType::LARGE_OBJECTS: return "大对象池";
			case PoolType::VERTEX_BUFFERS: return "顶点缓冲区池";
			case PoolType::INDEX_BUFFERS: return "索引缓冲区池";
			case PoolType::TEXTURES: return "纹理池";
			case PoolType::CONSTANT_BUFFERS: return "常量缓冲区池";
			default: return "未知池";
			}
		}

		/// 启动自动整理线程
		void StartAutoDefragmentationThread()
		{
			StopAutoDefragmentationThread(); // 确保没有重复线程

			m_defrag_thread_running = true;
			m_defrag_thread = std::thread([this]() {
				while (m_defrag_thread_running) {
					std::this_thread::sleep_for(std::chrono::seconds(m_auto_defrag_interval));

					if (m_defrag_thread_running) {
						// 执行自动整理
						GlobalDefragmentation();
						GlobalCleanup(300); // 清理5分钟未使用的资源
					}
				}
			});
		}

		/// 停止自动整理线程
		void StopAutoDefragmentationThread()
		{
			m_defrag_thread_running = false;
			if (m_defrag_thread.joinable()) {
				m_defrag_thread.join();
			}
		}

	private:
		IRenderManager* m_render_manager;
		size_t m_global_memory_limit;
		bool m_auto_defrag_enabled;
		uint32_t m_auto_defrag_interval;
		bool m_is_initialized;

		// 内存池映射
		std::unordered_map<PoolType, IMemoryPool*> m_memory_pools;
		std::unordered_map<PoolType, IGPUResourcePool*> m_gpu_resource_pools;

		// 自动整理线程
		std::thread m_defrag_thread;
		std::atomic<bool> m_defrag_thread_running{false};

		// 事件回调
		void* m_memory_event_callback{nullptr};
		void* m_callback_user_data{nullptr};

		mutable std::mutex m_manager_mutex;
	};

	// 全局内存池管理器实例
	static IMemoryPoolManager* g_memory_pool_manager = nullptr;

	/// 创建内存池管理器
	HRESULT CreateMemoryPoolManager(IMemoryPoolManager** pool_manager)
	{
		if (!pool_manager) return E_INVALIDARG;

		try
		{
			auto manager = new UIMemoryPoolManager();
			*pool_manager = manager;
			return S_OK;
		}
		catch_default({});
	}

	/// 获取全局内存池管理器
	IMemoryPoolManager* GetGlobalMemoryPoolManager()
	{
		return g_memory_pool_manager;
	}

	/// 设置全局内存池管理器
	void SetGlobalMemoryPoolManager(IMemoryPoolManager* manager)
	{
		if (g_memory_pool_manager) {
			g_memory_pool_manager->Release();
		}
		g_memory_pool_manager = manager;
		if (g_memory_pool_manager) {
			g_memory_pool_manager->AddRef();
		}
	}

	/// 内存池工厂实现
	HRESULT UIMemoryPoolFactory::CreateOptimalPools(IRenderManager* render_manager,
													IMemoryPoolManager** pool_manager)
	{
		if (!render_manager || !pool_manager) {
			return E_INVALIDARG;
		}

		try
		{
			// 创建内存池管理器
			throw_if_failed(
				CreateMemoryPoolManager(pool_manager),
				L"创建内存池管理器失败"
			);

			// 初始化管理器
			throw_if_failed(
				(*pool_manager)->Initialize(render_manager),
				L"初始化内存池管理器失败"
			);

			// 设置为全局管理器
			SetGlobalMemoryPoolManager(*pool_manager);

			return S_OK;
		}
		catch_default({});
	}

	size_t UIMemoryPoolFactory::GetRecommendedPoolSize(PoolType pool_type)
	{
		switch (pool_type) {
		case PoolType::SMALL_OBJECTS:
			return 1024 * 1024;      // 1MB
		case PoolType::MEDIUM_OBJECTS:
			return 16 * 1024 * 1024; // 16MB
		case PoolType::LARGE_OBJECTS:
			return 64 * 1024 * 1024; // 64MB
		case PoolType::VERTEX_BUFFERS:
			return 32 * 1024 * 1024; // 32MB
		case PoolType::INDEX_BUFFERS:
			return 16 * 1024 * 1024; // 16MB
		case PoolType::TEXTURES:
			return 128 * 1024 * 1024; // 128MB
		case PoolType::CONSTANT_BUFFERS:
			return 4 * 1024 * 1024;   // 4MB
		default:
			return 8 * 1024 * 1024;   // 8MB
		}
	}

	AllocationStrategy UIMemoryPoolFactory::GetRecommendedStrategy(PoolType pool_type)
	{
		switch (pool_type) {
		case PoolType::SMALL_OBJECTS:
			return AllocationStrategy::BEST_FIT;
		case PoolType::MEDIUM_OBJECTS:
			return AllocationStrategy::FIRST_FIT;
		case PoolType::LARGE_OBJECTS:
			return AllocationStrategy::WORST_FIT;
		case PoolType::VERTEX_BUFFERS:
		case PoolType::INDEX_BUFFERS:
		case PoolType::CONSTANT_BUFFERS:
			return AllocationStrategy::BUDDY_SYSTEM;
		case PoolType::TEXTURES:
			return AllocationStrategy::BEST_FIT;
		default:
			return AllocationStrategy::BEST_FIT;
		}
	}

	HRESULT UIMemoryPoolFactory::CreatePreconfiguredPool(PoolType pool_type, IMemoryPool** pool)
	{
		if (!pool) return E_INVALIDARG;

		try
		{
			size_t recommended_size = GetRecommendedPoolSize(pool_type);
			AllocationStrategy recommended_strategy = GetRecommendedStrategy(pool_type);

			auto memory_pool = new UIMemoryPool();
			throw_if_failed(
				memory_pool->Initialize(pool_type, recommended_size, recommended_strategy),
				L"初始化预配置内存池失败"
			);

			*pool = memory_pool;
			return S_OK;
		}
		catch_default({});
	}

	// =====================================================================================
	// 异步资源加载系统实现 - C++17
	// =====================================================================================

	/// 异步资源加载器实现类
	class UIAsyncResourceLoader : public ExUnknownImpl<IAsyncResourceLoader>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IAsyncResourceLoader);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIAsyncResourceLoader()
			: m_render_manager(nullptr)
			, m_thread_count(4)
			, m_next_request_id(1)
			, m_is_initialized(false)
			, m_is_paused(false)
			, m_shutdown_requested(false)
		{
			// 初始化优先级权重
			m_priority_weights[static_cast<uint32_t>(LoadPriority::IMMEDIATE)] = 1.0f;
			m_priority_weights[static_cast<uint32_t>(LoadPriority::HIGH)] = 0.8f;
			m_priority_weights[static_cast<uint32_t>(LoadPriority::NORMAL)] = 0.6f;
			m_priority_weights[static_cast<uint32_t>(LoadPriority::LOW)] = 0.4f;
			m_priority_weights[static_cast<uint32_t>(LoadPriority::BACKGROUND)] = 0.2f;
		}

		virtual ~UIAsyncResourceLoader()
		{
			Shutdown();
		}

		// IAsyncResourceLoader接口实现
		EXMETHOD HRESULT Initialize(IRenderManager* render_manager, uint32_t thread_count) override
		{
			if (m_is_initialized || !render_manager) {
				return E_INVALIDARG;
			}

			try
			{
				m_render_manager = render_manager;
				m_render_manager->AddRef();
				m_thread_count = thread_count;

				// 创建工作线程
				m_shutdown_requested = false;
				for (uint32_t i = 0; i < thread_count; ++i) {
					m_worker_threads.emplace_back([this, i]() {
						WorkerThreadProc(i);
					});
				}

				m_is_initialized = true;
				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void Shutdown() override
		{
			if (!m_is_initialized) {
				return;
			}

			// 请求关闭
			m_shutdown_requested = true;

			// 唤醒所有工作线程
			m_work_condition.notify_all();

			// 等待所有线程结束
			for (auto& thread : m_worker_threads) {
				if (thread.joinable()) {
					thread.join();
				}
			}
			m_worker_threads.clear();

			// 清理资源
			{
				std::lock_guard<std::mutex> lock(m_queue_mutex);

				// 取消所有等待的请求
				while (!m_request_queue.empty()) {
					auto request = m_request_queue.top();
					m_request_queue.pop();

					// 设置为取消状态
					std::lock_guard<std::mutex> result_lock(m_result_mutex);
					ResourceLoadResult result;
					result.request_id = request.request_id;
					result.result = E_ABORT;
					result.error_message = L"加载器已关闭";
					m_load_results[request.request_id] = result;

					// 调用回调
					if (request.completion_callback) {
						request.completion_callback(E_ABORT, nullptr);
					}
				}
			}

			// 清理结果
			{
				std::lock_guard<std::mutex> lock(m_result_mutex);
				m_load_results.clear();
			}

			if (m_render_manager) {
				m_render_manager->Release();
				m_render_manager = nullptr;
			}

			m_is_initialized = false;
		}

		EXMETHOD HRESULT SubmitLoadRequest(const ResourceLoadRequest& request, uint32_t* request_id) override
		{
			if (!m_is_initialized) {
				return E_FAIL;
			}

			try
			{
				// 生成请求ID
				uint32_t id = m_next_request_id.fetch_add(1);
				if (request_id) {
					*request_id = id;
				}

				// 创建内部请求
				ResourceLoadRequest internal_request = request;
				internal_request.request_id = id;

				// 添加到队列
				{
					std::lock_guard<std::mutex> lock(m_queue_mutex);
					m_request_queue.push(internal_request);
					m_stats.total_requests++;
					m_stats.pending_requests++;
				}

				// 唤醒工作线程
				m_work_condition.notify_one();

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT CancelLoadRequest(uint32_t request_id) override
		{
			std::lock_guard<std::mutex> lock(m_result_mutex);

			auto it = m_load_results.find(request_id);
			if (it != m_load_results.end()) {
				if (it->second.result == S_OK) {
					// 已经完成，无法取消
					return S_FALSE;
				}
			}

			// 标记为取消
			ResourceLoadResult result;
			result.request_id = request_id;
			result.result = E_ABORT;
			result.error_message = L"用户取消";
			m_load_results[request_id] = result;

			m_stats.cancelled_requests++;
			if (m_stats.pending_requests > 0) {
				m_stats.pending_requests--;
			}

			return S_OK;
		}

		EXMETHOD LoadStatus GetLoadStatus(uint32_t request_id) const override
		{
			std::lock_guard<std::mutex> lock(m_result_mutex);

			auto it = m_load_results.find(request_id);
			if (it != m_load_results.end()) {
				if (it->second.result == S_OK) {
					return LoadStatus::LOADED;
				} else if (it->second.result == E_ABORT) {
					return LoadStatus::CANCELLED;
				} else {
					return LoadStatus::FAILED;
				}
			}

			// 检查是否在队列中
			std::lock_guard<std::mutex> queue_lock(m_queue_mutex);
			auto queue_copy = m_request_queue;
			while (!queue_copy.empty()) {
				if (queue_copy.top().request_id == request_id) {
					return LoadStatus::PENDING;
				}
				queue_copy.pop();
			}

			// 检查是否正在加载
			if (m_active_requests.find(request_id) != m_active_requests.end()) {
				return LoadStatus::LOADING;
			}

			return LoadStatus::FAILED; // 未找到
		}

		EXMETHOD HRESULT GetLoadResult(uint32_t request_id, ResourceLoadResult& result) override
		{
			std::lock_guard<std::mutex> lock(m_result_mutex);

			auto it = m_load_results.find(request_id);
			if (it != m_load_results.end()) {
				result = it->second;
				return S_OK;
			}

			return E_FAIL;
		}

		EXMETHOD HRESULT WaitForCompletion(uint32_t request_id, uint32_t timeout_ms) override
		{
			auto start_time = std::chrono::steady_clock::now();
			auto timeout_duration = std::chrono::milliseconds(timeout_ms);

			while (true) {
				LoadStatus status = GetLoadStatus(request_id);
				if (status == LoadStatus::LOADED || status == LoadStatus::FAILED ||
					status == LoadStatus::CANCELLED) {
					return S_OK;
				}

				// 检查超时
				if (timeout_ms != INFINITE) {
					auto elapsed = std::chrono::steady_clock::now() - start_time;
					if (elapsed >= timeout_duration) {
						return E_TIMEOUT;
					}
				}

				// 短暂休眠
				std::this_thread::sleep_for(std::chrono::milliseconds(10));
			}
		}

		EXMETHOD HRESULT WaitForAllCompletion(uint32_t timeout_ms) override
		{
			auto start_time = std::chrono::steady_clock::now();
			auto timeout_duration = std::chrono::milliseconds(timeout_ms);

			while (true) {
				bool all_complete = true;

				// 检查队列是否为空
				{
					std::lock_guard<std::mutex> lock(m_queue_mutex);
					if (!m_request_queue.empty()) {
						all_complete = false;
					}
				}

				// 检查是否有活跃请求
				if (!m_active_requests.empty()) {
					all_complete = false;
				}

				if (all_complete) {
					return S_OK;
				}

				// 检查超时
				if (timeout_ms != INFINITE) {
					auto elapsed = std::chrono::steady_clock::now() - start_time;
					if (elapsed >= timeout_duration) {
						return E_TIMEOUT;
					}
				}

				// 短暂休眠
				std::this_thread::sleep_for(std::chrono::milliseconds(50));
			}
		}

		EXMETHOD void SetThreadCount(uint32_t thread_count) override
		{
			if (thread_count == m_thread_count || !m_is_initialized) {
				return;
			}

			// 重新初始化线程池
			bool was_paused = m_is_paused;
			Shutdown();
			Initialize(m_render_manager, thread_count);

			if (was_paused) {
				PauseLoading();
			}
		}

		EXMETHOD const LoaderStats& GetLoaderStats() const override
		{
			return m_stats;
		}

		EXMETHOD void ResetStats() override
		{
			std::lock_guard<std::mutex> lock(m_stats_mutex);
			m_stats = LoaderStats{};
		}

		EXMETHOD void PauseLoading() override
		{
			m_is_paused = true;
		}

		EXMETHOD void ResumeLoading() override
		{
			m_is_paused = false;
			m_work_condition.notify_all();
		}

		EXMETHOD void ClearQueue() override
		{
			std::lock_guard<std::mutex> lock(m_queue_mutex);

			// 取消所有等待的请求
			while (!m_request_queue.empty()) {
				auto request = m_request_queue.top();
				m_request_queue.pop();

				// 设置为取消状态
				std::lock_guard<std::mutex> result_lock(m_result_mutex);
				ResourceLoadResult result;
				result.request_id = request.request_id;
				result.result = E_ABORT;
				result.error_message = L"队列已清空";
				m_load_results[request.request_id] = result;

				// 调用回调
				if (request.completion_callback) {
					request.completion_callback(E_ABORT, nullptr);
				}

				m_stats.cancelled_requests++;
			}

			m_stats.pending_requests = 0;
		}

		EXMETHOD void SetPriorityWeights(const float weights[5]) override
		{
			for (int i = 0; i < 5; ++i) {
				m_priority_weights[i] = weights[i];
			}
		}

	private:
		/// 工作线程处理函数
		void WorkerThreadProc(uint32_t thread_id)
		{
			while (!m_shutdown_requested) {
				ResourceLoadRequest request;
				bool has_request = false;

				// 获取请求
				{
					std::unique_lock<std::mutex> lock(m_queue_mutex);

					// 等待请求或关闭信号
					m_work_condition.wait(lock, [this]() {
						return !m_request_queue.empty() || m_shutdown_requested;
					});

					if (m_shutdown_requested) {
						break;
					}

					if (!m_request_queue.empty() && !m_is_paused) {
						request = m_request_queue.top();
						m_request_queue.pop();
						has_request = true;

						m_stats.pending_requests--;
						m_active_requests.insert(request.request_id);
					}
				}

				if (has_request) {
					// 处理请求
					ProcessLoadRequest(request, thread_id);

					// 移除活跃请求
					m_active_requests.erase(request.request_id);
				}
			}
		}

		/// 处理加载请求
		void ProcessLoadRequest(const ResourceLoadRequest& request, uint32_t thread_id)
		{
			auto start_time = std::chrono::steady_clock::now();
			ResourceLoadResult result;
			result.request_id = request.request_id;

			try
			{
				// 检查是否已取消
				{
					std::lock_guard<std::mutex> lock(m_result_mutex);
					auto it = m_load_results.find(request.request_id);
					if (it != m_load_results.end() && it->second.result == E_ABORT) {
						return; // 已取消
					}
				}

				// 根据资源类型加载
				switch (request.resource_type) {
				case ResourceType::TEXTURE:
					result.result = LoadTexture(request, result);
					break;
				case ResourceType::MESH:
					result.result = LoadMesh(request, result);
					break;
				case ResourceType::SHADER:
					result.result = LoadShader(request, result);
					break;
				case ResourceType::FONT:
					result.result = LoadFont(request, result);
					break;
				default:
					result.result = E_NOTIMPL;
					result.error_message = L"不支持的资源类型";
					break;
				}

				// 计算加载时间
				auto end_time = std::chrono::steady_clock::now();
				result.load_time = std::chrono::duration_cast<std::chrono::milliseconds>(
					end_time - start_time);

				// 更新统计
				{
					std::lock_guard<std::mutex> lock(m_stats_mutex);
					if (SUCCEEDED(result.result)) {
						m_stats.completed_requests++;
						m_stats.total_bytes_loaded += result.resource_size;

						// 更新平均加载时间
						auto total_time = m_stats.avg_load_time.count() * (m_stats.completed_requests - 1) +
										 result.load_time.count();
						m_stats.avg_load_time = std::chrono::milliseconds(total_time / m_stats.completed_requests);
					} else {
						m_stats.failed_requests++;
					}
				}
			}
			catch (...)
			{
				result.result = E_UNEXPECTED;
				result.error_message = L"加载过程中发生异常";
				m_stats.failed_requests++;
			}

			// 保存结果
			{
				std::lock_guard<std::mutex> lock(m_result_mutex);
				m_load_results[request.request_id] = result;
			}

			// 调用完成回调
			if (request.completion_callback) {
				request.completion_callback(result.result, result.resource_ptr);
			}
		}

		/// 加载纹理
		HRESULT LoadTexture(const ResourceLoadRequest& request, ResourceLoadResult& result)
		{
			try
			{
				ITexture* texture = nullptr;
				HRESULT hr = m_render_manager->LoadTextureFromFile(request.file_path.c_str(), &texture);

				if (SUCCEEDED(hr) && texture) {
					result.resource_ptr = texture;
					result.resource_size = EstimateTextureSize(texture);
					return S_OK;
				}

				result.error_message = L"纹理加载失败";
				return hr;
			}
			catch_default({});
		}

		/// 加载网格
		HRESULT LoadMesh(const ResourceLoadRequest& request, ResourceLoadResult& result)
		{
			try
			{
				// 简化实现：这里应该有完整的网格加载逻辑
				result.error_message = L"网格加载暂未实现";
				return E_NOTIMPL;
			}
			catch_default({});
		}

		/// 加载着色器
		HRESULT LoadShader(const ResourceLoadRequest& request, ResourceLoadResult& result)
		{
			try
			{
				// 简化实现：这里应该有完整的着色器加载逻辑
				result.error_message = L"着色器加载暂未实现";
				return E_NOTIMPL;
			}
			catch_default({});
		}

		/// 加载字体
		HRESULT LoadFont(const ResourceLoadRequest& request, ResourceLoadResult& result)
		{
			try
			{
				// 简化实现：这里应该有完整的字体加载逻辑
				result.error_message = L"字体加载暂未实现";
				return E_NOTIMPL;
			}
			catch_default({});
		}

		/// 估算纹理大小
		size_t EstimateTextureSize(ITexture* texture)
		{
			// 简化实现：实际应该获取纹理的真实大小
			return 1024 * 1024; // 假设1MB
		}

		/// 请求优先级比较器
		struct RequestComparator
		{
			const float* priority_weights;

			RequestComparator(const float* weights) : priority_weights(weights) {}

			bool operator()(const ResourceLoadRequest& a, const ResourceLoadRequest& b) const
			{
				// 优先级权重比较
				float weight_a = priority_weights[static_cast<uint32_t>(a.priority)];
				float weight_b = priority_weights[static_cast<uint32_t>(b.priority)];

				if (weight_a != weight_b) {
					return weight_a < weight_b; // 权重高的优先
				}

				// 相同优先级按请求时间排序
				return a.request_time > b.request_time;
			}
		};

	private:
		IRenderManager* m_render_manager;
		uint32_t m_thread_count;
		std::atomic<uint32_t> m_next_request_id;
		bool m_is_initialized;
		std::atomic<bool> m_is_paused;
		std::atomic<bool> m_shutdown_requested;

		// 优先级权重
		float m_priority_weights[5];

		// 工作线程
		std::vector<std::thread> m_worker_threads;
		std::condition_variable m_work_condition;

		// 请求队列
		std::priority_queue<ResourceLoadRequest, std::vector<ResourceLoadRequest>,
						   RequestComparator> m_request_queue{RequestComparator(m_priority_weights)};
		mutable std::mutex m_queue_mutex;

		// 活跃请求
		std::unordered_set<uint32_t> m_active_requests;

		// 加载结果
		std::unordered_map<uint32_t, ResourceLoadResult> m_load_results;
		mutable std::mutex m_result_mutex;

		// 统计信息
		LoaderStats m_stats;
		mutable std::mutex m_stats_mutex;
	};

	/// 资源缓存实现类
	class UIResourceCache : public ExUnknownImpl<IResourceCache>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IResourceCache);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIResourceCache()
			: m_max_cache_size(100 * 1024 * 1024) // 100MB默认
			, m_max_entries(1000)
			, m_current_size(0)
			, m_hit_count(0)
			, m_miss_count(0)
			, m_eviction_policy(0) // LRU
			, m_is_initialized(false)
		{
		}

		virtual ~UIResourceCache()
		{
			Shutdown();
		}

		// IResourceCache接口实现
		EXMETHOD HRESULT Initialize(size_t max_cache_size, uint32_t max_entries) override
		{
			if (m_is_initialized) {
				return S_FALSE;
			}

			m_max_cache_size = max_cache_size;
			m_max_entries = max_entries;
			m_is_initialized = true;

			return S_OK;
		}

		EXMETHOD void Shutdown() override
		{
			std::lock_guard<std::mutex> lock(m_cache_mutex);

			// 释放所有缓存的资源
			for (auto& [key, entry] : m_cache_entries) {
				if (entry.resource && entry.type == ResourceType::TEXTURE) {
					static_cast<ITexture*>(entry.resource)->Release();
				}
				// 其他资源类型的释放逻辑
			}

			m_cache_entries.clear();
			m_lru_list.clear();
			m_current_size = 0;
			m_is_initialized = false;
		}

		// 其他IResourceCache方法的实现...
		// 为了节省空间，这里省略了完整的缓存实现
		EXMETHOD HRESULT AddResource(const std::wstring& key, void* resource,
									size_t size, ResourceType type) override
		{
			// 简化实现
			return S_OK;
		}

		EXMETHOD void* GetResource(const std::wstring& key) override
		{
			// 简化实现
			return nullptr;
		}

		EXMETHOD void RemoveResource(const std::wstring& key) override
		{
			// 简化实现
		}

		EXMETHOD bool HasResource(const std::wstring& key) const override
		{
			// 简化实现
			return false;
		}

		EXMETHOD void ClearCache() override
		{
			// 简化实现
		}

		EXMETHOD size_t GetCacheSize() const override
		{
			return m_current_size;
		}

		EXMETHOD uint32_t GetEntryCount() const override
		{
			return static_cast<uint32_t>(m_cache_entries.size());
		}

		EXMETHOD float GetHitRate() const override
		{
			uint32_t total = m_hit_count + m_miss_count;
			return total > 0 ? static_cast<float>(m_hit_count) / total : 0.0f;
		}

		EXMETHOD void SetEvictionPolicy(uint32_t policy) override
		{
			m_eviction_policy = policy;
		}

		EXMETHOD HRESULT WarmupCache(const std::vector<std::wstring>& resource_paths) override
		{
			// 简化实现
			return S_OK;
		}

	private:
		struct CacheEntry
		{
			void* resource;
			size_t size;
			ResourceType type;
			std::chrono::steady_clock::time_point last_access;
			uint32_t access_count;
		};

		size_t m_max_cache_size;
		uint32_t m_max_entries;
		size_t m_current_size;
		uint32_t m_hit_count;
		uint32_t m_miss_count;
		uint32_t m_eviction_policy;
		bool m_is_initialized;

		std::unordered_map<std::wstring, CacheEntry> m_cache_entries;
		std::list<std::wstring> m_lru_list;
		mutable std::mutex m_cache_mutex;
	};

	// =====================================================================================
	// 资源生命周期管理系统实现 - C++17
	// =====================================================================================

	/// 资源生命周期管理器实现类
	class UIResourceLifecycleManager : public ExUnknownImpl<IResourceLifecycleManager>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IResourceLifecycleManager);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIResourceLifecycleManager()
			: m_next_resource_id(1)
			, m_gc_interval_seconds(60)
			, m_auto_gc_enabled(false)
			, m_is_initialized(false)
			, m_shutdown_requested(false)
		{
		}

		virtual ~UIResourceLifecycleManager()
		{
			Shutdown();
		}

		// IResourceLifecycleManager接口实现
		EXMETHOD HRESULT Initialize(uint32_t gc_interval_seconds) override
		{
			if (m_is_initialized) {
				return S_FALSE;
			}

			try
			{
				m_gc_interval_seconds = gc_interval_seconds;
				m_is_initialized = true;

				// 启动GC线程
				if (m_auto_gc_enabled) {
					StartGCThread();
				}

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void Shutdown() override
		{
			if (!m_is_initialized) {
				return;
			}

			// 停止GC线程
			StopGCThread();

			// 强制释放所有资源
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			for (auto& [id, info] : m_resources) {
				if (info.state != ResourceState::DISPOSED) {
					ForceReleaseResourceInternal(id, info);
				}
			}

			m_resources.clear();
			m_is_initialized = false;
		}

		EXMETHOD HRESULT RegisterResource(void* resource, ResourceType type,
										 const std::wstring& name, size_t size,
										 uint32_t* resource_id) override
		{
			if (!resource || !resource_id || !m_is_initialized) {
				return E_INVALIDARG;
			}

			try
			{
				uint32_t id = m_next_resource_id.fetch_add(1);
				*resource_id = id;

				ResourceInfo info;
				info.resource_id = id;
				info.resource_type = type;
				info.state = ResourceState::ACTIVE;
				info.name = name;
				info.memory_size = size;
				info.resource_ptr = resource;
				info.strong_ref_count = 0; // 初始引用计数为0
				info.weak_ref_count = 0;

				{
					std::lock_guard<std::mutex> lock(m_resources_mutex);
					m_resources[id] = info;
				}

				// 更新统计
				{
					std::lock_guard<std::mutex> lock(m_stats_mutex);
					m_stats.total_resources++;
					m_stats.active_resources++;
					m_stats.total_memory_usage += size;

					if (m_stats.total_memory_usage > m_stats.peak_memory_usage) {
						m_stats.peak_memory_usage = m_stats.total_memory_usage;
					}
				}

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void UnregisterResource(uint32_t resource_id) override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			auto it = m_resources.find(resource_id);
			if (it != m_resources.end()) {
				ResourceInfo& info = it->second;

				// 标记为释放中
				info.state = ResourceState::DISPOSING;

				// 如果没有引用，立即释放
				if (info.strong_ref_count == 0 && info.weak_ref_count == 0) {
					ForceReleaseResourceInternal(resource_id, info);
					m_resources.erase(it);
				}
			}
		}

		EXMETHOD uint32_t AddStrongRef(uint32_t resource_id) override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			auto it = m_resources.find(resource_id);
			if (it != m_resources.end() && it->second.state == ResourceState::ACTIVE) {
				it->second.strong_ref_count++;
				it->second.last_access_time = std::chrono::steady_clock::now();
				it->second.access_count++;
				return it->second.strong_ref_count;
			}

			return 0;
		}

		EXMETHOD uint32_t ReleaseStrongRef(uint32_t resource_id) override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			auto it = m_resources.find(resource_id);
			if (it != m_resources.end() && it->second.strong_ref_count > 0) {
				it->second.strong_ref_count--;

				// 如果强引用计数为0且资源标记为释放中，则释放资源
				if (it->second.strong_ref_count == 0 &&
					it->second.state == ResourceState::DISPOSING) {

					if (it->second.weak_ref_count == 0) {
						ForceReleaseResourceInternal(resource_id, it->second);
						m_resources.erase(it);
						return 0;
					} else {
						it->second.state = ResourceState::DISPOSED;
						it->second.resource_ptr = nullptr;
					}
				}

				return it->second.strong_ref_count;
			}

			return 0;
		}

		EXMETHOD uint32_t AddWeakRef(uint32_t resource_id) override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			auto it = m_resources.find(resource_id);
			if (it != m_resources.end()) {
				it->second.weak_ref_count++;
				return it->second.weak_ref_count;
			}

			return 0;
		}

		EXMETHOD uint32_t ReleaseWeakRef(uint32_t resource_id) override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			auto it = m_resources.find(resource_id);
			if (it != m_resources.end() && it->second.weak_ref_count > 0) {
				it->second.weak_ref_count--;

				// 如果强弱引用都为0且资源已释放，则完全移除
				if (it->second.weak_ref_count == 0 &&
					it->second.strong_ref_count == 0 &&
					it->second.state == ResourceState::DISPOSED) {
					m_resources.erase(it);
					return 0;
				}

				return it->second.weak_ref_count;
			}

			return 0;
		}

		EXMETHOD bool GetResourceInfo(uint32_t resource_id, ResourceInfo& info) const override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			auto it = m_resources.find(resource_id);
			if (it != m_resources.end()) {
				info = it->second;
				return true;
			}

			return false;
		}

		EXMETHOD bool IsResourceValid(uint32_t resource_id) const override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			auto it = m_resources.find(resource_id);
			return it != m_resources.end() &&
				   it->second.state == ResourceState::ACTIVE &&
				   it->second.resource_ptr != nullptr;
		}

		EXMETHOD void* GetResourcePtr(uint32_t resource_id) override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			auto it = m_resources.find(resource_id);
			if (it != m_resources.end() && it->second.state == ResourceState::ACTIVE) {
				it->second.last_access_time = std::chrono::steady_clock::now();
				it->second.access_count++;
				return it->second.resource_ptr;
			}

			return nullptr;
		}

		EXMETHOD uint32_t RunGarbageCollection() override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			uint32_t collected_count = 0;
			auto now = std::chrono::steady_clock::now();

			// 查找可以回收的资源
			std::vector<uint32_t> to_remove;

			for (auto& [id, info] : m_resources) {
				// 回收条件：强引用为0且超过一定时间未访问
				if (info.strong_ref_count == 0) {
					auto idle_time = std::chrono::duration_cast<std::chrono::minutes>(
						now - info.last_access_time);

					if (idle_time.count() > 10) { // 10分钟未访问
						to_remove.push_back(id);
					}
				}
			}

			// 执行回收
			for (uint32_t id : to_remove) {
				auto it = m_resources.find(id);
				if (it != m_resources.end()) {
					ForceReleaseResourceInternal(id, it->second);
					m_resources.erase(it);
					collected_count++;
				}
			}

			// 更新统计
			{
				std::lock_guard<std::mutex> lock(m_stats_mutex);
				m_stats.gc_cycles++;
				m_stats.resources_collected += collected_count;
			}

			return collected_count;
		}

		EXMETHOD void SetAutoGC(bool enabled, uint32_t interval_seconds) override
		{
			m_auto_gc_enabled = enabled;
			m_gc_interval_seconds = interval_seconds;

			if (m_is_initialized) {
				if (enabled) {
					StartGCThread();
				} else {
					StopGCThread();
				}
			}
		}

		EXMETHOD const ResourceMonitorStats& GetMonitorStats() const override
		{
			return m_stats;
		}

		EXMETHOD void ResetStats() override
		{
			std::lock_guard<std::mutex> lock(m_stats_mutex);
			m_stats = ResourceMonitorStats{};
		}

		EXMETHOD void GenerateResourceReport(std::string& report) const override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			std::ostringstream oss;
			oss << "=== HHBUI资源生命周期管理器报告 ===\n\n";

			// 统计信息
			oss << "统计信息:\n";
			oss << "  总资源数: " << m_stats.total_resources << "\n";
			oss << "  活跃资源数: " << m_stats.active_resources << "\n";
			oss << "  泄漏资源数: " << m_stats.leaked_resources << "\n";
			oss << "  总内存使用: " << m_stats.total_memory_usage / 1024 / 1024 << " MB\n";
			oss << "  峰值内存: " << m_stats.peak_memory_usage / 1024 / 1024 << " MB\n";
			oss << "  GC周期数: " << m_stats.gc_cycles << "\n";
			oss << "  回收资源数: " << m_stats.resources_collected << "\n\n";

			// 资源详情
			oss << "资源详情:\n";
			for (const auto& [id, info] : m_resources) {
				oss << "  ID: " << id << "\n";
				oss << "    名称: " << WStringToString(info.name) << "\n";
				oss << "    类型: " << GetResourceTypeName(info.resource_type) << "\n";
				oss << "    状态: " << GetResourceStateName(info.state) << "\n";
				oss << "    强引用: " << info.strong_ref_count << "\n";
				oss << "    弱引用: " << info.weak_ref_count << "\n";
				oss << "    内存大小: " << info.memory_size / 1024 << " KB\n";
				oss << "    访问次数: " << info.access_count << "\n\n";
			}

			report = oss.str();
		}

		EXMETHOD std::vector<uint32_t> FindLeakedResources() const override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			std::vector<uint32_t> leaked_resources;
			auto now = std::chrono::steady_clock::now();

			for (const auto& [id, info] : m_resources) {
				// 泄漏判断：资源存在时间过长且引用计数异常
				auto lifetime = std::chrono::duration_cast<std::chrono::hours>(
					now - info.created_time);

				if (lifetime.count() > 24 && // 存在超过24小时
					(info.strong_ref_count > 100 || // 引用计数异常高
					 (info.strong_ref_count == 0 && info.state == ResourceState::ACTIVE))) {
					leaked_resources.push_back(id);
				}
			}

			return leaked_resources;
		}

		EXMETHOD HRESULT ForceReleaseResource(uint32_t resource_id) override
		{
			std::lock_guard<std::mutex> lock(m_resources_mutex);

			auto it = m_resources.find(resource_id);
			if (it != m_resources.end()) {
				ForceReleaseResourceInternal(resource_id, it->second);
				m_resources.erase(it);
				return S_OK;
			}

			return E_FAIL;
		}

	private:
		/// 强制释放资源内部实现
		void ForceReleaseResourceInternal(uint32_t resource_id, ResourceInfo& info)
		{
			if (info.resource_ptr && info.state != ResourceState::DISPOSED) {
				// 根据资源类型执行相应的释放操作
				switch (info.resource_type) {
				case ResourceType::TEXTURE:
					if (auto texture = static_cast<ITexture*>(info.resource_ptr)) {
						texture->Release();
					}
					break;
				case ResourceType::MESH:
					// 网格释放逻辑
					break;
				case ResourceType::SHADER:
					if (auto shader = static_cast<IShader*>(info.resource_ptr)) {
						shader->Release();
					}
					break;
				default:
					// 其他资源类型的释放逻辑
					break;
				}

				info.resource_ptr = nullptr;
				info.state = ResourceState::DISPOSED;

				// 更新统计
				std::lock_guard<std::mutex> lock(m_stats_mutex);
				m_stats.active_resources--;
				m_stats.total_memory_usage -= info.memory_size;
			}
		}

		/// 启动GC线程
		void StartGCThread()
		{
			StopGCThread(); // 确保没有重复线程

			m_shutdown_requested = false;
			m_gc_thread = std::thread([this]() {
				while (!m_shutdown_requested) {
					std::this_thread::sleep_for(std::chrono::seconds(m_gc_interval_seconds));

					if (!m_shutdown_requested) {
						RunGarbageCollection();
					}
				}
			});
		}

		/// 停止GC线程
		void StopGCThread()
		{
			m_shutdown_requested = true;
			if (m_gc_thread.joinable()) {
				m_gc_thread.join();
			}
		}

		/// 辅助函数
		std::string WStringToString(const std::wstring& wstr) const
		{
			if (wstr.empty()) return "";
			int size = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
			std::string result(size - 1, 0);
			WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &result[0], size, nullptr, nullptr);
			return result;
		}

		const char* GetResourceTypeName(ResourceType type) const
		{
			switch (type) {
			case ResourceType::TEXTURE: return "纹理";
			case ResourceType::MESH: return "网格";
			case ResourceType::SHADER: return "着色器";
			case ResourceType::AUDIO: return "音频";
			case ResourceType::FONT: return "字体";
			case ResourceType::ANIMATION: return "动画";
			case ResourceType::MATERIAL: return "材质";
			case ResourceType::SCENE: return "场景";
			default: return "未知";
			}
		}

		const char* GetResourceStateName(ResourceState state) const
		{
			switch (state) {
			case ResourceState::CREATING: return "创建中";
			case ResourceState::ACTIVE: return "活跃";
			case ResourceState::INACTIVE: return "非活跃";
			case ResourceState::DISPOSING: return "释放中";
			case ResourceState::DISPOSED: return "已释放";
			default: return "未知";
			}
		}

	private:
		std::atomic<uint32_t> m_next_resource_id;
		uint32_t m_gc_interval_seconds;
		bool m_auto_gc_enabled;
		bool m_is_initialized;
		std::atomic<bool> m_shutdown_requested;

		// 资源映射
		std::unordered_map<uint32_t, ResourceInfo> m_resources;
		mutable std::mutex m_resources_mutex;

		// GC线程
		std::thread m_gc_thread;

		// 统计信息
		ResourceMonitorStats m_stats;
		mutable std::mutex m_stats_mutex;
	};

	// 全局实例
	static IAsyncResourceLoader* g_async_loader = nullptr;
	static IResourceCache* g_resource_cache = nullptr;
	static IResourceLifecycleManager* g_lifecycle_manager = nullptr;

	/// 创建异步资源加载器
	HRESULT CreateAsyncResourceLoader(IAsyncResourceLoader** loader)
	{
		if (!loader) return E_INVALIDARG;

		try
		{
			auto async_loader = new UIAsyncResourceLoader();
			*loader = async_loader;
			return S_OK;
		}
		catch_default({});
	}

	/// 创建资源缓存
	HRESULT CreateResourceCache(IResourceCache** cache)
	{
		if (!cache) return E_INVALIDARG;

		try
		{
			auto resource_cache = new UIResourceCache();
			*cache = resource_cache;
			return S_OK;
		}
		catch_default({});
	}

	/// 创建资源生命周期管理器
	HRESULT CreateResourceLifecycleManager(IResourceLifecycleManager** manager)
	{
		if (!manager) return E_INVALIDARG;

		try
		{
			auto lifecycle_manager = new UIResourceLifecycleManager();
			*manager = lifecycle_manager;
			return S_OK;
		}
		catch_default({});
	}

	/// 获取全局异步加载器
	IAsyncResourceLoader* GetGlobalAsyncLoader()
	{
		return g_async_loader;
	}

	/// 设置全局异步加载器
	void SetGlobalAsyncLoader(IAsyncResourceLoader* loader)
	{
		if (g_async_loader) {
			g_async_loader->Release();
		}
		g_async_loader = loader;
		if (g_async_loader) {
			g_async_loader->AddRef();
		}
	}

	/// 获取全局资源缓存
	IResourceCache* GetGlobalResourceCache()
	{
		return g_resource_cache;
	}

	/// 设置全局资源缓存
	void SetGlobalResourceCache(IResourceCache* cache)
	{
		if (g_resource_cache) {
			g_resource_cache->Release();
		}
		g_resource_cache = cache;
		if (g_resource_cache) {
			g_resource_cache->AddRef();
		}
	}

	/// 获取全局生命周期管理器
	IResourceLifecycleManager* GetGlobalLifecycleManager()
	{
		return g_lifecycle_manager;
	}

	/// 设置全局生命周期管理器
	void SetGlobalLifecycleManager(IResourceLifecycleManager* manager)
	{
		if (g_lifecycle_manager) {
			g_lifecycle_manager->Release();
		}
		g_lifecycle_manager = manager;
		if (g_lifecycle_manager) {
			g_lifecycle_manager->AddRef();
		}
	}

	/// 工厂实现
	HRESULT UIAsyncLoaderFactory::CreateOptimalLoader(IRenderManager* render_manager,
													  IAsyncResourceLoader** loader)
	{
		if (!render_manager || !loader) {
			return E_INVALIDARG;
		}

		try
		{
			throw_if_failed(
				CreateAsyncResourceLoader(loader),
				L"创建异步加载器失败"
			);

			uint32_t thread_count = GetRecommendedThreadCount();
			throw_if_failed(
				(*loader)->Initialize(render_manager, thread_count),
				L"初始化异步加载器失败"
			);

			return S_OK;
		}
		catch_default({});
	}

	HRESULT UIAsyncLoaderFactory::CreateResourceCache(size_t max_size, IResourceCache** cache)
	{
		if (!cache) return E_INVALIDARG;

		try
		{
			throw_if_failed(
				HHBUI::CreateResourceCache(cache),
				L"创建资源缓存失败"
			);

			throw_if_failed(
				(*cache)->Initialize(max_size, 1000), // 传递max_size和默认的max_entries
				L"初始化资源缓存失败"
			);

			return S_OK;
		}
		catch_default({});
	}

	uint32_t UIAsyncLoaderFactory::GetRecommendedThreadCount()
	{
		uint32_t hardware_threads = std::thread::hardware_concurrency();
		return std::max(2u, std::min(hardware_threads / 2, 8u)); // 2-8个线程
	}

	size_t UIAsyncLoaderFactory::GetRecommendedCacheSize()
	{
		// 推荐缓存大小：系统内存的1/8，最小64MB，最大512MB
		MEMORYSTATUSEX mem_status = {};
		mem_status.dwLength = sizeof(mem_status);
		if (GlobalMemoryStatusEx(&mem_status)) {
			size_t recommended = static_cast<size_t>(mem_status.ullTotalPhys / 8);
			return std::max(64 * 1024 * 1024ull, std::min(recommended, 512 * 1024 * 1024ull));
		}
		return 128 * 1024 * 1024; // 默认128MB
	}

	HRESULT UIResourceLifecycleFactory::CreateLifecycleManager(IResourceLifecycleManager** manager)
	{
		if (!manager) return E_INVALIDARG;

		try
		{
			throw_if_failed(
				CreateResourceLifecycleManager(manager),
				L"创建生命周期管理器失败"
			);

			uint32_t gc_interval = GetRecommendedGCInterval();
			throw_if_failed(
				(*manager)->Initialize(gc_interval),
				L"初始化生命周期管理器失败"
			);

			return S_OK;
		}
		catch_default({});
	}

	uint32_t UIResourceLifecycleFactory::GetRecommendedGCInterval()
	{
		return 60; // 推荐60秒GC间隔
	}

	// =====================================================================================
	// 阶段三：多线程渲染架构 - 渲染命令队列系统实现 - C++17
	// =====================================================================================

	/// 渲染命令队列实现类
	class UIRenderCommandQueue : public ExUnknownImpl<IRenderCommandQueue>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IRenderCommandQueue);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIRenderCommandQueue()
			: m_render_manager(nullptr)
			, m_max_commands(10000)
			, m_enable_double_buffering(true)
			, m_current_buffer_index(0)
			, m_state(CommandQueueState::IDLE)
			, m_enable_optimization(true)
			, m_is_initialized(false)
			, m_shutdown_requested(false)
		{
		}

		virtual ~UIRenderCommandQueue()
		{
			Shutdown();
		}

		// IRenderCommandQueue接口实现
		EXMETHOD HRESULT Initialize(void* render_manager,
								   uint32_t max_commands,
								   bool enable_double_buffering) override
		{
			if (m_is_initialized || !render_manager) {
				return E_INVALIDARG;
			}

			try
			{
				m_render_manager = static_cast<IRenderManager*>(render_manager);
				m_render_manager->AddRef();
				m_max_commands = max_commands;
				m_enable_double_buffering = enable_double_buffering;

				// 初始化双缓冲区
				if (m_enable_double_buffering) {
					m_command_buffers[0].reserve(max_commands);
					m_command_buffers[1].reserve(max_commands);
					m_batch_buffers[0].reserve(max_commands / 10); // 假设平均每批10个命令
					m_batch_buffers[1].reserve(max_commands / 10);
				} else {
					m_command_buffers[0].reserve(max_commands);
					m_batch_buffers[0].reserve(max_commands / 10);
				}

				// 启动执行线程
				m_shutdown_requested = false;
				m_execution_thread = std::thread([this]() {
					ExecutionThreadProc();
				});

				m_is_initialized = true;
				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void Shutdown() override
		{
			if (!m_is_initialized) {
				return;
			}

			// 请求关闭
			m_shutdown_requested = true;

			// 唤醒执行线程
			m_execution_condition.notify_all();

			// 等待执行线程结束
			if (m_execution_thread.joinable()) {
				m_execution_thread.join();
			}

			// 清理资源
			{
				std::lock_guard<std::mutex> lock(m_queue_mutex);
				for (int i = 0; i < 2; ++i) {
					m_command_buffers[i].clear();
					m_batch_buffers[i].clear();
				}
			}

			if (m_render_manager) {
				m_render_manager->Release();
				m_render_manager = nullptr;
			}

			m_is_initialized = false;
		}

		EXMETHOD HRESULT BeginRecording() override
		{
			if (!m_is_initialized) {
				return E_FAIL;
			}

			std::lock_guard<std::mutex> lock(m_queue_mutex);

			if (m_state != CommandQueueState::IDLE) {
				return E_FAIL; // 已在记录中
			}

			m_state = CommandQueueState::RECORDING;

			// 清空当前缓冲区
			uint32_t buffer_index = m_current_buffer_index;
			m_command_buffers[buffer_index].clear();
			m_batch_buffers[buffer_index].clear();

			return S_OK;
		}

		EXMETHOD HRESULT EndRecording() override
		{
			std::lock_guard<std::mutex> lock(m_queue_mutex);

			if (m_state != CommandQueueState::RECORDING) {
				return E_FAIL;
			}

			// 优化命令序列
			if (m_enable_optimization) {
				uint32_t buffer_index = m_current_buffer_index;
				m_command_buffers[buffer_index] = UIRenderCommandBuilder::OptimizeCommands(
					m_command_buffers[buffer_index]);
			}

			m_state = CommandQueueState::IDLE;
			return S_OK;
		}

		EXMETHOD HRESULT AddCommand(const RenderCommand& command) override
		{
			if (!m_is_initialized) {
				return E_FAIL;
			}

			std::lock_guard<std::mutex> lock(m_queue_mutex);

			uint32_t buffer_index = m_current_buffer_index;

			// 检查缓冲区容量
			if (m_command_buffers[buffer_index].size() >= m_max_commands) {
				return E_OUTOFMEMORY;
			}

			// 添加命令
			m_command_buffers[buffer_index].push_back(command);

			// 更新统计
			m_stats.total_commands++;
			m_stats.pending_commands++;

			return S_OK;
		}

		EXMETHOD HRESULT AddBatch(const CommandBatch& batch, uint32_t* batch_id) override
		{
			if (!m_is_initialized) {
				return E_FAIL;
			}

			std::lock_guard<std::mutex> lock(m_queue_mutex);

			uint32_t buffer_index = m_current_buffer_index;

			// 生成批次ID
			uint32_t id = static_cast<uint32_t>(m_batch_buffers[buffer_index].size());
			if (batch_id) {
				*batch_id = id;
			}

			// 添加批次
			CommandBatch internal_batch = batch;
			internal_batch.batch_id = id;
			m_batch_buffers[buffer_index].push_back(internal_batch);

			// 添加批次中的所有命令
			for (const auto& command : batch.commands) {
				if (m_command_buffers[buffer_index].size() >= m_max_commands) {
					return E_OUTOFMEMORY;
				}
				m_command_buffers[buffer_index].push_back(command);
			}

			// 更新统计
			m_stats.total_batches++;
			m_stats.active_batches++;
			m_stats.total_commands += static_cast<uint32_t>(batch.commands.size());
			m_stats.pending_commands += static_cast<uint32_t>(batch.commands.size());

			return S_OK;
		}

		EXMETHOD HRESULT ExecuteCommands() override
		{
			if (!m_is_initialized) {
				return E_FAIL;
			}

			std::unique_lock<std::mutex> lock(m_queue_mutex);

			if (m_state == CommandQueueState::EXECUTING) {
				return E_FAIL; // 已在执行中
			}

			uint32_t buffer_index = m_current_buffer_index;
			auto commands = m_command_buffers[buffer_index]; // 拷贝命令列表
			auto batches = m_batch_buffers[buffer_index];    // 拷贝批次列表

			lock.unlock(); // 释放锁，允许其他线程添加命令到另一个缓冲区

			// 执行命令
			return ExecuteCommandsInternal(commands, batches);
		}

		EXMETHOD HRESULT ExecuteCommandsAsync(std::function<void(HRESULT)> completion_callback) override
		{
			if (!m_is_initialized) {
				return E_FAIL;
			}

			// 将异步执行请求添加到队列
			{
				std::lock_guard<std::mutex> lock(m_async_mutex);
				m_async_callbacks.push(completion_callback);
			}

			// 唤醒执行线程
			m_execution_condition.notify_one();

			return S_OK;
		}

		EXMETHOD HRESULT WaitForCompletion(uint32_t timeout_ms) override
		{
			auto start_time = std::chrono::steady_clock::now();
			auto timeout_duration = std::chrono::milliseconds(timeout_ms);

			while (true) {
				{
					std::lock_guard<std::mutex> lock(m_queue_mutex);
					if (m_state == CommandQueueState::IDLE || m_state == CommandQueueState::COMPLETED) {
						return S_OK;
					}
				}

				// 检查超时
				if (timeout_ms != INFINITE) {
					auto elapsed = std::chrono::steady_clock::now() - start_time;
					if (elapsed >= timeout_duration) {
						return E_TIMEOUT;
					}
				}

				// 短暂休眠
				std::this_thread::sleep_for(std::chrono::milliseconds(1));
			}
		}

		EXMETHOD void ClearCommands() override
		{
			std::lock_guard<std::mutex> lock(m_queue_mutex);

			for (int i = 0; i < 2; ++i) {
				m_command_buffers[i].clear();
				m_batch_buffers[i].clear();
			}

			// 重置统计
			m_stats.pending_commands = 0;
			m_stats.active_batches = 0;
		}

		EXMETHOD CommandQueueState GetState() const override
		{
			return m_state;
		}

		EXMETHOD const CommandQueueStats& GetStats() const override
		{
			return m_stats;
		}

		EXMETHOD void ResetStats() override
		{
			std::lock_guard<std::mutex> lock(m_stats_mutex);
			m_stats = CommandQueueStats{};
		}

		EXMETHOD void SetCommandOptimization(bool enabled) override
		{
			m_enable_optimization = enabled;
		}

		EXMETHOD void SetDoubleBuffering(bool enabled) override
		{
			std::lock_guard<std::mutex> lock(m_queue_mutex);
			m_enable_double_buffering = enabled;
		}

		EXMETHOD uint32_t GetCurrentBufferIndex() const override
		{
			return m_current_buffer_index;
		}

		EXMETHOD HRESULT SwapBuffers() override
		{
			if (!m_enable_double_buffering) {
				return E_FAIL;
			}

			std::lock_guard<std::mutex> lock(m_queue_mutex);
			m_current_buffer_index = 1 - m_current_buffer_index; // 0->1, 1->0
			return S_OK;
		}

	private:
		/// 执行线程处理函数
		void ExecutionThreadProc()
		{
			while (!m_shutdown_requested) {
				std::function<void(HRESULT)> callback;
				bool has_callback = false;

				// 检查是否有异步执行请求
				{
					std::lock_guard<std::mutex> lock(m_async_mutex);
					if (!m_async_callbacks.empty()) {
						callback = m_async_callbacks.front();
						m_async_callbacks.pop();
						has_callback = true;
					}
				}

				if (has_callback) {
					// 执行异步命令
					HRESULT hr = ExecuteCommands();
					callback(hr);
				} else {
					// 等待新的执行请求
					std::unique_lock<std::mutex> lock(m_execution_mutex);
					m_execution_condition.wait_for(lock, std::chrono::milliseconds(100));
				}
			}
		}

		/// 内部命令执行实现
		HRESULT ExecuteCommandsInternal(const std::vector<RenderCommand>& commands,
									   const std::vector<CommandBatch>& batches)
		{
			auto start_time = std::chrono::steady_clock::now();

			try
			{
				m_state = CommandQueueState::EXECUTING;

				// 执行所有命令
				for (const auto& command : commands) {
					throw_if_failed(
						ExecuteSingleCommand(command),
						L"执行渲染命令失败"
					);

					// 更新统计
					m_stats.executed_commands++;
					m_stats.pending_commands--;
				}

				// 标记批次完成
				for (const auto& batch : batches) {
					if (batch.completion_callback) {
						batch.completion_callback(S_OK);
					}
				}

				m_state = CommandQueueState::COMPLETED;

				// 更新执行时间统计
				auto end_time = std::chrono::steady_clock::now();
				auto execution_time = std::chrono::duration_cast<std::chrono::milliseconds>(
					end_time - start_time);

				{
					std::lock_guard<std::mutex> lock(m_stats_mutex);

					// 更新平均执行时间
					if (m_stats.executed_commands > 0) {
						auto total_time = m_stats.avg_execution_time.count() * (m_stats.executed_commands - commands.size()) +
										 execution_time.count();
						m_stats.avg_execution_time = std::chrono::milliseconds(total_time / m_stats.executed_commands);
					}

					// 更新吞吐量
					if (execution_time.count() > 0) {
						m_stats.throughput_commands_per_sec = static_cast<float>(commands.size()) /
															  (execution_time.count() / 1000.0f);
					}
				}

				return S_OK;
			}
			catch (...)
			{
				m_state = CommandQueueState::QUEUE_ERROR;
				m_stats.failed_commands += static_cast<uint32_t>(commands.size());
				return E_FAIL;
			}
		}

		/// 执行单个命令
		HRESULT ExecuteSingleCommand(const RenderCommand& command)
		{
			try
			{
				switch (command.type) {
				case RenderCommandType::DRAW_INDEXED:
					return m_render_manager->DrawIndexed(
						command.params.draw_indexed.index_count,
						command.params.draw_indexed.start_index,
						command.params.draw_indexed.base_vertex
					);

				case RenderCommandType::DRAW_INDEXED_INSTANCED:
					return m_render_manager->DrawIndexedInstanced(
						command.params.draw_indexed_instanced.index_count,
						command.params.draw_indexed_instanced.instance_count,
						command.params.draw_indexed_instanced.start_index,
						command.params.draw_indexed_instanced.base_vertex,
						command.params.draw_indexed_instanced.start_instance
					);

				case RenderCommandType::DRAW:
					return m_render_manager->Draw(
						command.params.draw.vertex_count,
						command.params.draw.start_vertex
					);

				case RenderCommandType::SET_VERTEX_BUFFER:
					return m_render_manager->SetVertexBuffer(
						command.params.set_vertex_buffer.slot,
						static_cast<IBuffer*>(command.params.set_vertex_buffer.buffer),
						command.params.set_vertex_buffer.stride,
						command.params.set_vertex_buffer.offset
					);

				case RenderCommandType::SET_INDEX_BUFFER:
					return m_render_manager->SetIndexBuffer(
						static_cast<IBuffer*>(command.params.set_index_buffer.buffer),
						command.params.set_index_buffer.format,
						command.params.set_index_buffer.offset
					);

				case RenderCommandType::SET_SHADER:
					return m_render_manager->SetShader(
						static_cast<IShader*>(command.params.set_shader.shader)
					);

				case RenderCommandType::SET_TEXTURE:
					return m_render_manager->SetTexture(
						command.params.set_texture.slot,
						static_cast<ITexture*>(command.params.set_texture.texture)
					);

				case RenderCommandType::CLEAR_RENDER_TARGET:
					return m_render_manager->ClearRenderTarget(
						static_cast<ITexture*>(command.params.clear_render_target.render_target),
						command.params.clear_render_target.color
					);

				case RenderCommandType::SET_VIEWPORT:
					return m_render_manager->SetViewport(
						command.params.set_viewport.x,
						command.params.set_viewport.y,
						command.params.set_viewport.width,
						command.params.set_viewport.height,
						command.params.set_viewport.min_depth,
						command.params.set_viewport.max_depth
					);

				case RenderCommandType::PRESENT:
					return m_render_manager->Present();

				case RenderCommandType::CUSTOM:
					if (command.params.custom.executor) {
						// 从void*转换回std::function并调用
						auto* executor_ptr = static_cast<std::function<void(void*)>*>(command.params.custom.executor);
						(*executor_ptr)(command.params.custom.data);

						// 注意：这里应该在适当的时候删除executor_ptr以避免内存泄漏
						// 在实际项目中，应该使用智能指针或其他RAII机制
						delete executor_ptr;
						return S_OK;
					}
					return E_FAIL;

				case RenderCommandType::BEGIN_EVENT:
				case RenderCommandType::END_EVENT:
					// 事件标记通常用于调试，这里简化处理
					return S_OK;

				default:
					return E_NOTIMPL;
				}
			}
			catch_default({});
		}

	private:
		IRenderManager* m_render_manager;
		uint32_t m_max_commands;
		bool m_enable_double_buffering;
		uint32_t m_current_buffer_index;
		std::atomic<CommandQueueState> m_state;
		bool m_enable_optimization;
		bool m_is_initialized;
		std::atomic<bool> m_shutdown_requested;

		// 双缓冲命令队列
		std::vector<RenderCommand> m_command_buffers[2];
		std::vector<CommandBatch> m_batch_buffers[2];

		// 异步执行
		std::thread m_execution_thread;
		std::condition_variable m_execution_condition;
		std::mutex m_execution_mutex;

		// 异步回调队列
		std::queue<std::function<void(HRESULT)>> m_async_callbacks;
		std::mutex m_async_mutex;

		// 队列同步
		mutable std::mutex m_queue_mutex;

		// 统计信息
		CommandQueueStats m_stats;
		mutable std::mutex m_stats_mutex;
	};

	/// 渲染命令构建器实现
	uint32_t UIRenderCommandBuilder::GetNextCommandId()
	{
		static std::atomic<uint32_t> s_next_id{1};
		return s_next_id.fetch_add(1);
	}

	uint64_t UIRenderCommandBuilder::GetTimestamp()
	{
		return std::chrono::duration_cast<std::chrono::microseconds>(
			std::chrono::steady_clock::now().time_since_epoch()).count();
	}

	RenderCommand UIRenderCommandBuilder::CreateDrawIndexedCommand(uint32_t index_count,
																  uint32_t start_index,
																  uint32_t base_vertex,
																  CommandPriority priority)
	{
		RenderCommand command;
		command.type = RenderCommandType::DRAW_INDEXED;
		command.priority = priority;
		command.params.draw_indexed.index_count = index_count;
		command.params.draw_indexed.start_index = start_index;
		command.params.draw_indexed.base_vertex = base_vertex;
		command.timestamp = GetTimestamp();
		command.thread_id = GetCurrentThreadId();
		command.command_id = GetNextCommandId();
		return command;
	}

	RenderCommand UIRenderCommandBuilder::CreateDrawIndexedInstancedCommand(uint32_t index_count,
																		   uint32_t instance_count,
																		   uint32_t start_index,
																		   uint32_t base_vertex,
																		   uint32_t start_instance,
																		   CommandPriority priority)
	{
		RenderCommand command;
		command.type = RenderCommandType::DRAW_INDEXED_INSTANCED;
		command.priority = priority;
		command.params.draw_indexed_instanced.index_count = index_count;
		command.params.draw_indexed_instanced.instance_count = instance_count;
		command.params.draw_indexed_instanced.start_index = start_index;
		command.params.draw_indexed_instanced.base_vertex = base_vertex;
		command.params.draw_indexed_instanced.start_instance = start_instance;
		command.timestamp = GetTimestamp();
		command.thread_id = GetCurrentThreadId();
		command.command_id = GetNextCommandId();
		return command;
	}

	RenderCommand UIRenderCommandBuilder::CreateSetVertexBufferCommand(uint32_t slot, void* buffer,
																	   uint32_t stride, uint32_t offset,
																	   CommandPriority priority)
	{
		RenderCommand command;
		command.type = RenderCommandType::SET_VERTEX_BUFFER;
		command.priority = priority;
		command.params.set_vertex_buffer.slot = slot;
		command.params.set_vertex_buffer.buffer = buffer;
		command.params.set_vertex_buffer.stride = stride;
		command.params.set_vertex_buffer.offset = offset;
		command.timestamp = GetTimestamp();
		command.thread_id = GetCurrentThreadId();
		command.command_id = GetNextCommandId();
		return command;
	}

	RenderCommand UIRenderCommandBuilder::CreateSetIndexBufferCommand(void* buffer, DXGI_FORMAT format,
																	  uint32_t offset,
																	  CommandPriority priority)
	{
		RenderCommand command;
		command.type = RenderCommandType::SET_INDEX_BUFFER;
		command.priority = priority;
		command.params.set_index_buffer.buffer = buffer;
		command.params.set_index_buffer.format = format;
		command.params.set_index_buffer.offset = offset;
		command.timestamp = GetTimestamp();
		command.thread_id = GetCurrentThreadId();
		command.command_id = GetNextCommandId();
		return command;
	}

	RenderCommand UIRenderCommandBuilder::CreateSetShaderCommand(void* shader, uint32_t stage,
																 CommandPriority priority)
	{
		RenderCommand command;
		command.type = RenderCommandType::SET_SHADER;
		command.priority = priority;
		command.params.set_shader.shader = shader;
		command.params.set_shader.stage = stage;
		command.timestamp = GetTimestamp();
		command.thread_id = GetCurrentThreadId();
		command.command_id = GetNextCommandId();
		return command;
	}

	RenderCommand UIRenderCommandBuilder::CreateSetTextureCommand(uint32_t slot, void* texture, uint32_t stage,
																  CommandPriority priority)
	{
		RenderCommand command;
		command.type = RenderCommandType::SET_TEXTURE;
		command.priority = priority;
		command.params.set_texture.slot = slot;
		command.params.set_texture.texture = texture;
		command.params.set_texture.stage = stage;
		command.timestamp = GetTimestamp();
		command.thread_id = GetCurrentThreadId();
		command.command_id = GetNextCommandId();
		return command;
	}

	RenderCommand UIRenderCommandBuilder::CreateClearRenderTargetCommand(void* render_target,
																		 const float color[4],
																		 CommandPriority priority)
	{
		RenderCommand command;
		command.type = RenderCommandType::CLEAR_RENDER_TARGET;
		command.priority = priority;
		command.params.clear_render_target.render_target = render_target;
		memcpy(command.params.clear_render_target.color, color, sizeof(float) * 4);
		command.timestamp = GetTimestamp();
		command.thread_id = GetCurrentThreadId();
		command.command_id = GetNextCommandId();
		return command;
	}

	RenderCommand UIRenderCommandBuilder::CreateSetViewportCommand(float x, float y, float width, float height,
																   float min_depth, float max_depth,
																   CommandPriority priority)
	{
		RenderCommand command;
		command.type = RenderCommandType::SET_VIEWPORT;
		command.priority = priority;
		command.params.set_viewport.x = x;
		command.params.set_viewport.y = y;
		command.params.set_viewport.width = width;
		command.params.set_viewport.height = height;
		command.params.set_viewport.min_depth = min_depth;
		command.params.set_viewport.max_depth = max_depth;
		command.timestamp = GetTimestamp();
		command.thread_id = GetCurrentThreadId();
		command.command_id = GetNextCommandId();
		return command;
	}

	RenderCommand UIRenderCommandBuilder::CreateEventCommand(const wchar_t* name, uint32_t color,
															 bool is_begin,
															 CommandPriority priority)
	{
		RenderCommand command;
		command.type = is_begin ? RenderCommandType::BEGIN_EVENT : RenderCommandType::END_EVENT;
		command.priority = priority;
		wcsncpy_s(command.params.event.name, name, _countof(command.params.event.name) - 1);
		command.params.event.color = color;
		command.timestamp = GetTimestamp();
		command.thread_id = GetCurrentThreadId();
		command.command_id = GetNextCommandId();
		return command;
	}

	RenderCommand UIRenderCommandBuilder::CreateCustomCommand(void* data, size_t size,
															  std::function<void(void*)> executor,
															  CommandPriority priority)
	{
		RenderCommand command;
		command.type = RenderCommandType::CUSTOM;
		command.priority = priority;
		command.params.custom.data = data;
		command.params.custom.size = size;

		// 将std::function存储在堆上，然后存储指针
		auto* executor_ptr = new std::function<void(void*)>(executor);
		command.params.custom.executor = static_cast<void*>(executor_ptr);

		command.timestamp = GetTimestamp();
		command.thread_id = GetCurrentThreadId();
		command.command_id = GetNextCommandId();
		return command;
	}

	CommandBatch UIRenderCommandBuilder::CreateBatch(const std::vector<RenderCommand>& commands,
													 CommandPriority priority)
	{
		CommandBatch batch;
		batch.commands = commands;
		batch.priority = priority;
		batch.submit_time = std::chrono::steady_clock::now();
		return batch;
	}

	std::vector<RenderCommand> UIRenderCommandBuilder::OptimizeCommands(const std::vector<RenderCommand>& commands)
	{
		// 简化的命令优化实现
		// 在实际项目中，这里应该实现更复杂的优化逻辑，如：
		// 1. 状态排序（减少状态切换）
		// 2. 批次合并（合并相似的绘制调用）
		// 3. 剔除冗余命令
		// 4. 重排序以提高缓存效率

		std::vector<RenderCommand> optimized = commands;

		// 按优先级排序
		std::stable_sort(optimized.begin(), optimized.end(),
			[](const RenderCommand& a, const RenderCommand& b) {
				return static_cast<uint32_t>(a.priority) < static_cast<uint32_t>(b.priority);
			});

		return optimized;
	}

	// 全局实例
	static IRenderCommandQueue* g_command_queue = nullptr;

	/// 创建渲染命令队列
	HRESULT CreateRenderCommandQueue(IRenderCommandQueue** queue)
	{
		if (!queue) return E_INVALIDARG;

		try
		{
			auto command_queue = new UIRenderCommandQueue();
			*queue = command_queue;
			return S_OK;
		}
		catch_default({});
	}

	/// 获取全局命令队列
	IRenderCommandQueue* GetGlobalCommandQueue()
	{
		return g_command_queue;
	}

	/// 设置全局命令队列
	void SetGlobalCommandQueue(IRenderCommandQueue* queue)
	{
		if (g_command_queue) {
			g_command_queue->Release();
		}
		g_command_queue = queue;
		if (g_command_queue) {
			g_command_queue->AddRef();
		}
	}

	/// 命令队列工厂实现
	HRESULT UICommandQueueFactory::CreateOptimalQueue(void* render_manager,
													  IRenderCommandQueue** queue)
	{
		if (!render_manager || !queue) {
			return E_INVALIDARG;
		}

		try
		{
			throw_if_failed(
				CreateRenderCommandQueue(queue),
				L"创建命令队列失败"
			);

			uint32_t queue_size = GetRecommendedQueueSize();
			bool double_buffering = GetRecommendedDoubleBuffering();

			throw_if_failed(
				(*queue)->Initialize(render_manager, queue_size, double_buffering),
				L"初始化命令队列失败"
			);

			// 启用命令优化
			(*queue)->SetCommandOptimization(true);

			return S_OK;
		}
		catch_default({});
	}

	HRESULT UICommandQueueFactory::CreateHighPerformanceQueue(void* render_manager,
															  uint32_t max_commands,
															  IRenderCommandQueue** queue)
	{
		if (!render_manager || !queue) {
			return E_INVALIDARG;
		}

		try
		{
			throw_if_failed(
				CreateRenderCommandQueue(queue),
				L"创建高性能命令队列失败"
			);

			throw_if_failed(
				(*queue)->Initialize(render_manager, max_commands, true), // 强制启用双缓冲
				L"初始化高性能命令队列失败"
			);

			// 启用所有优化
			(*queue)->SetCommandOptimization(true);
			(*queue)->SetDoubleBuffering(true);

			return S_OK;
		}
		catch_default({});
	}

	uint32_t UICommandQueueFactory::GetRecommendedQueueSize()
	{
		// 根据系统性能推荐队列大小
		uint32_t hardware_threads = std::thread::hardware_concurrency();

		if (hardware_threads >= 8) {
			return 50000; // 高端系统
		} else if (hardware_threads >= 4) {
			return 20000; // 中端系统
		} else {
			return 10000; // 低端系统
		}
	}

	bool UICommandQueueFactory::GetRecommendedDoubleBuffering()
	{
		// 推荐在多核系统上启用双缓冲
		return std::thread::hardware_concurrency() >= 4;
	}

	// =====================================================================================
	// 并行渲染管线系统实现 - C++17
	// =====================================================================================

	/// 任务调度器实现类
	class UITaskScheduler : public ExUnknownImpl<ITaskScheduler>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_ITaskScheduler);
		EX_DECLEAR_INTERFACE_END();

	public:
		UITaskScheduler()
			: m_thread_count(0)
			, m_enable_work_stealing(true)
			, m_next_task_id(1)
			, m_is_initialized(false)
			, m_shutdown_requested(false)
		{
		}

		virtual ~UITaskScheduler()
		{
			Shutdown();
		}

		// ITaskScheduler接口实现
		EXMETHOD HRESULT Initialize(uint32_t thread_count, bool enable_work_stealing) override
		{
			if (m_is_initialized) {
				return S_FALSE;
			}

			try
			{
				m_thread_count = thread_count > 0 ? thread_count : std::thread::hardware_concurrency();
				m_enable_work_stealing = enable_work_stealing;

				// 初始化任务队列
				m_task_queues.resize(m_thread_count);

				// 创建工作线程
				m_shutdown_requested = false;
				for (uint32_t i = 0; i < m_thread_count; ++i) {
					m_worker_threads.emplace_back([this, i]() {
						WorkerThreadProc(i);
					});
				}

				m_is_initialized = true;
				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void Shutdown() override
		{
			if (!m_is_initialized) {
				return;
			}

			// 请求关闭
			m_shutdown_requested = true;

			// 唤醒所有工作线程
			m_task_condition.notify_all();

			// 等待所有线程结束
			for (auto& thread : m_worker_threads) {
				if (thread.joinable()) {
					thread.join();
				}
			}
			m_worker_threads.clear();

			// 清理任务
			{
				std::lock_guard<std::mutex> lock(m_task_mutex);
				m_task_queues.clear();
				m_completed_tasks.clear();
			}

			m_is_initialized = false;
		}

		EXMETHOD HRESULT SubmitTask(const PipelineTask& task, uint32_t* task_id) override
		{
			if (!m_is_initialized) {
				return E_FAIL;
			}

			try
			{
				// 生成任务ID
				uint32_t id = m_next_task_id.fetch_add(1);
				if (task_id) {
					*task_id = id;
				}

				// 创建内部任务
				PipelineTask internal_task = task;
				internal_task.task_id = id;

				// 选择最佳队列（负载均衡）
				uint32_t queue_index = SelectBestQueue();

				// 添加到任务队列
				{
					std::lock_guard<std::mutex> lock(m_task_mutex);
					m_task_queues[queue_index].push(internal_task);
				}

				// 唤醒工作线程
				m_task_condition.notify_one();

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT SubmitTaskBatch(const std::vector<PipelineTask>& tasks) override
		{
			if (!m_is_initialized) {
				return E_FAIL;
			}

			try
			{
				// 批量提交任务
				for (const auto& task : tasks) {
					uint32_t task_id;
					throw_if_failed(
						SubmitTask(task, &task_id),
						L"提交批次任务失败"
					);
				}

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT WaitForTask(uint32_t task_id, uint32_t timeout_ms) override
		{
			auto start_time = std::chrono::steady_clock::now();
			auto timeout_duration = std::chrono::milliseconds(timeout_ms);

			while (true) {
				{
					std::lock_guard<std::mutex> lock(m_task_mutex);
					if (m_completed_tasks.find(task_id) != m_completed_tasks.end()) {
						return S_OK;
					}
				}

				// 检查超时
				if (timeout_ms != INFINITE) {
					auto elapsed = std::chrono::steady_clock::now() - start_time;
					if (elapsed >= timeout_duration) {
						return E_TIMEOUT;
					}
				}

				// 短暂休眠
				std::this_thread::sleep_for(std::chrono::milliseconds(1));
			}
		}

		EXMETHOD HRESULT WaitForAllTasks(uint32_t timeout_ms) override
		{
			auto start_time = std::chrono::steady_clock::now();
			auto timeout_duration = std::chrono::milliseconds(timeout_ms);

			while (true) {
				bool all_complete = true;

				// 检查所有队列是否为空
				{
					std::lock_guard<std::mutex> lock(m_task_mutex);
					for (const auto& queue : m_task_queues) {
						if (!queue.empty()) {
							all_complete = false;
							break;
						}
					}
				}

				if (all_complete) {
					return S_OK;
				}

				// 检查超时
				if (timeout_ms != INFINITE) {
					auto elapsed = std::chrono::steady_clock::now() - start_time;
					if (elapsed >= timeout_duration) {
						return E_TIMEOUT;
					}
				}

				// 短暂休眠
				std::this_thread::sleep_for(std::chrono::milliseconds(10));
			}
		}

		EXMETHOD HRESULT CancelTask(uint32_t task_id) override
		{
			// 简化实现：标记任务为已取消
			std::lock_guard<std::mutex> lock(m_task_mutex);
			m_cancelled_tasks.insert(task_id);
			return S_OK;
		}

		EXMETHOD bool IsTaskCompleted(uint32_t task_id) const override
		{
			std::lock_guard<std::mutex> lock(m_task_mutex);
			return m_completed_tasks.find(task_id) != m_completed_tasks.end();
		}

		EXMETHOD HRESULT SetTaskPriority(uint32_t task_id, TaskPriority priority) override
		{
			// 简化实现：在实际项目中需要重新排序队列
			return S_OK;
		}

		EXMETHOD uint32_t GetActiveThreadCount() const override
		{
			return static_cast<uint32_t>(m_worker_threads.size());
		}

		EXMETHOD uint32_t GetPendingTaskCount() const override
		{
			std::lock_guard<std::mutex> lock(m_task_mutex);
			uint32_t total = 0;
			for (const auto& queue : m_task_queues) {
				total += static_cast<uint32_t>(queue.size());
			}
			return total;
		}

		EXMETHOD void SetWorkStealing(bool enabled) override
		{
			m_enable_work_stealing = enabled;
		}

		EXMETHOD HRESULT SetThreadAffinity(uint32_t thread_index, uint32_t cpu_core) override
		{
			if (thread_index >= m_thread_count) {
				return E_INVALIDARG;
			}

			// 在Windows上设置线程亲和性
			if (thread_index < m_worker_threads.size()) {
				HANDLE thread_handle = m_worker_threads[thread_index].native_handle();
				DWORD_PTR affinity_mask = 1ULL << cpu_core;
				if (SetThreadAffinityMask(thread_handle, affinity_mask) == 0) {
					return E_FAIL;
				}
			}

			return S_OK;
		}

	private:
		/// 工作线程处理函数
		void WorkerThreadProc(uint32_t thread_index)
		{
			while (!m_shutdown_requested) {
				PipelineTask task;
				bool has_task = false;

				// 尝试从自己的队列获取任务
				{
					std::lock_guard<std::mutex> lock(m_task_mutex);
					if (!m_task_queues[thread_index].empty()) {
						task = m_task_queues[thread_index].front();
						m_task_queues[thread_index].pop();
						has_task = true;
					}
				}

				// 如果没有任务且启用了工作窃取，尝试从其他队列窃取任务
				if (!has_task && m_enable_work_stealing) {
					has_task = StealTask(thread_index, task);
				}

				if (has_task) {
					// 检查任务是否被取消
					{
						std::lock_guard<std::mutex> lock(m_task_mutex);
						if (m_cancelled_tasks.find(task.task_id) != m_cancelled_tasks.end()) {
							continue; // 跳过已取消的任务
						}
					}

					// 执行任务
					ExecuteTask(task);

					// 标记任务完成
					{
						std::lock_guard<std::mutex> lock(m_task_mutex);
						m_completed_tasks.insert(task.task_id);
					}
				} else {
					// 没有任务，等待新任务
					std::unique_lock<std::mutex> lock(m_wait_mutex);
					m_task_condition.wait_for(lock, std::chrono::milliseconds(100));
				}
			}
		}

		/// 执行任务
		void ExecuteTask(PipelineTask& task)
		{
			try
			{
				task.start_time = std::chrono::steady_clock::now();

				if (task.executor) {
					HRESULT hr = task.executor();
					// 记录执行结果
				}

				auto end_time = std::chrono::steady_clock::now();
				task.execution_time = std::chrono::duration_cast<std::chrono::milliseconds>(
					end_time - task.start_time);

				task.is_completed = true;
			}
			catch (...)
			{
				// 任务执行失败
				task.is_completed = false;
			}
		}

		/// 工作窃取
		bool StealTask(uint32_t thread_index, PipelineTask& task)
		{
			std::lock_guard<std::mutex> lock(m_task_mutex);

			// 尝试从其他线程的队列窃取任务
			for (uint32_t i = 0; i < m_thread_count; ++i) {
				if (i != thread_index && !m_task_queues[i].empty()) {
					task = m_task_queues[i].front();
					m_task_queues[i].pop();
					return true;
				}
			}

			return false;
		}

		/// 选择最佳队列
		uint32_t SelectBestQueue()
		{
			std::lock_guard<std::mutex> lock(m_task_mutex);

			// 选择任务数量最少的队列
			uint32_t best_queue = 0;
			size_t min_size = m_task_queues[0].size();

			for (uint32_t i = 1; i < m_thread_count; ++i) {
				if (m_task_queues[i].size() < min_size) {
					min_size = m_task_queues[i].size();
					best_queue = i;
				}
			}

			return best_queue;
		}

	private:
		uint32_t m_thread_count;
		bool m_enable_work_stealing;
		std::atomic<uint32_t> m_next_task_id;
		bool m_is_initialized;
		std::atomic<bool> m_shutdown_requested;

		// 工作线程
		std::vector<std::thread> m_worker_threads;

		// 任务队列（每个线程一个队列）
		std::vector<std::queue<PipelineTask>> m_task_queues;
		mutable std::mutex m_task_mutex;

		// 任务状态跟踪
		std::unordered_set<uint32_t> m_completed_tasks;
		std::unordered_set<uint32_t> m_cancelled_tasks;

		// 同步
		std::condition_variable m_task_condition;
		std::mutex m_wait_mutex;
	};

	// =====================================================================================
	// 工作线程池系统实现 - C++17
	// =====================================================================================

	/// 工作线程池实现类
	class UIWorkerThreadPool : public ExUnknownImpl<IWorkerThreadPool>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IWorkerThreadPool);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIWorkerThreadPool()
			: m_thread_count(0)
			, m_enable_work_stealing(true)
			, m_enable_cpu_affinity(true)
			, m_state(ThreadPoolState::IDLE)
			, m_load_balancing_strategy(1) // Least Loaded
			, m_is_initialized(false)
			, m_shutdown_requested(false)
		{
		}

		virtual ~UIWorkerThreadPool()
		{
			Shutdown();
		}

		// IWorkerThreadPool接口实现
		EXMETHOD HRESULT Initialize(uint32_t thread_count,
								   bool enable_work_stealing,
								   bool enable_cpu_affinity) override
		{
			if (m_is_initialized) {
				return S_FALSE;
			}

			try
			{
				m_thread_count = thread_count > 0 ? thread_count : std::thread::hardware_concurrency();
				m_enable_work_stealing = enable_work_stealing;
				m_enable_cpu_affinity = enable_cpu_affinity;

				// 初始化工作队列
				m_work_queues.resize(m_thread_count);
				m_thread_info.resize(m_thread_count);

				// 创建工作线程
				m_shutdown_requested = false;
				for (uint32_t i = 0; i < m_thread_count; ++i) {
					// 初始化线程信息
					m_thread_info[i].thread_id = i;
					m_thread_info[i].cpu_core = i % std::thread::hardware_concurrency();
					m_thread_info[i].last_activity = std::chrono::steady_clock::now();

					// 创建工作线程
					m_worker_threads.emplace_back([this, i]() {
						WorkerThreadProc(i);
					});

					// 设置CPU亲和性
					if (m_enable_cpu_affinity) {
						SetThreadCPUAffinity(i, m_thread_info[i].cpu_core);
					}
				}

				m_state = ThreadPoolState::RUNNING;
				m_is_initialized = true;
				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD void Shutdown() override
		{
			if (!m_is_initialized) {
				return;
			}

			m_state = ThreadPoolState::SHUTTING_DOWN;

			// 请求关闭
			m_shutdown_requested = true;

			// 唤醒所有工作线程
			m_work_condition.notify_all();

			// 等待所有线程结束
			for (auto& thread : m_worker_threads) {
				if (thread.joinable()) {
					thread.join();
				}
			}
			m_worker_threads.clear();

			// 清理工作队列
			{
				std::lock_guard<std::mutex> lock(m_queue_mutex);
				for (auto& queue : m_work_queues) {
					while (!queue.empty()) {
						queue.pop();
					}
				}
			}

			m_state = ThreadPoolState::SHUTDOWN;
			m_is_initialized = false;
		}

		EXMETHOD HRESULT SubmitWork(std::function<void()> work_item,
								   TaskPriority priority) override
		{
			if (!m_is_initialized || m_state != ThreadPoolState::RUNNING) {
				return E_FAIL;
			}

			try
			{
				// 选择最佳队列
				uint32_t queue_index = SelectBestQueue();

				// 创建工作项包装器
				WorkItem item;
				item.work_function = work_item;
				item.priority = priority;
				item.submit_time = std::chrono::steady_clock::now();

				// 添加到工作队列
				{
					std::lock_guard<std::mutex> lock(m_queue_mutex);
					m_work_queues[queue_index].push(item);
					m_stats.pending_tasks++;
				}

				// 唤醒工作线程
				m_work_condition.notify_one();

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT SubmitWorkBatch(const std::vector<std::function<void()>>& work_items,
										TaskPriority priority) override
		{
			if (!m_is_initialized || m_state != ThreadPoolState::RUNNING) {
				return E_FAIL;
			}

			try
			{
				// 批量提交工作项
				for (const auto& work_item : work_items) {
					throw_if_failed(
						SubmitWork(work_item, priority),
						L"提交批次工作项失败"
					);
				}

				return S_OK;
			}
			catch_default({});
		}

		EXMETHOD HRESULT WaitForAllWork(uint32_t timeout_ms) override
		{
			auto start_time = std::chrono::steady_clock::now();
			auto timeout_duration = std::chrono::milliseconds(timeout_ms);

			while (true) {
				bool all_complete = true;

				// 检查所有队列是否为空
				{
					std::lock_guard<std::mutex> lock(m_queue_mutex);
					for (const auto& queue : m_work_queues) {
						if (!queue.empty()) {
							all_complete = false;
							break;
						}
					}
				}

				if (all_complete) {
					return S_OK;
				}

				// 检查超时
				if (timeout_ms != INFINITE) {
					auto elapsed = std::chrono::steady_clock::now() - start_time;
					if (elapsed >= timeout_duration) {
						return E_TIMEOUT;
					}
				}

				// 短暂休眠
				std::this_thread::sleep_for(std::chrono::milliseconds(10));
			}
		}

		EXMETHOD void Pause() override
		{
			if (m_state == ThreadPoolState::RUNNING) {
				m_state = ThreadPoolState::PAUSED;
			}
		}

		EXMETHOD void Resume() override
		{
			if (m_state == ThreadPoolState::PAUSED) {
				m_state = ThreadPoolState::RUNNING;
				m_work_condition.notify_all();
			}
		}

		EXMETHOD ThreadPoolState GetState() const override
		{
			return m_state;
		}

		EXMETHOD const ThreadPoolStats& GetStats() const override
		{
			return m_stats;
		}

		EXMETHOD void ResetStats() override
		{
			std::lock_guard<std::mutex> lock(m_stats_mutex);
			m_stats = ThreadPoolStats{};
		}

		EXMETHOD HRESULT SetThreadCount(uint32_t thread_count) override
		{
			// 简化实现：需要重新初始化线程池
			if (thread_count != m_thread_count) {
				Shutdown();
				return Initialize(thread_count, m_enable_work_stealing, m_enable_cpu_affinity);
			}
			return S_OK;
		}

		EXMETHOD uint32_t GetThreadCount() const override
		{
			return m_thread_count;
		}

		EXMETHOD void SetWorkStealing(bool enabled) override
		{
			m_enable_work_stealing = enabled;
		}

		EXMETHOD void SetCPUAffinity(bool enabled) override
		{
			m_enable_cpu_affinity = enabled;
		}

		EXMETHOD HRESULT SetThreadCPUAffinity(uint32_t thread_index, uint32_t cpu_core) override
		{
			if (thread_index >= m_thread_count) {
				return E_INVALIDARG;
			}

			// 在Windows上设置线程亲和性
			if (thread_index < m_worker_threads.size()) {
				HANDLE thread_handle = m_worker_threads[thread_index].native_handle();
				DWORD_PTR affinity_mask = 1ULL << cpu_core;
				if (SetThreadAffinityMask(thread_handle, affinity_mask) == 0) {
					return E_FAIL;
				}

				m_thread_info[thread_index].cpu_core = cpu_core;
			}

			return S_OK;
		}

		EXMETHOD bool GetThreadInfo(uint32_t thread_index, WorkerThreadInfo& info) const override
		{
			if (thread_index >= m_thread_count) {
				return false;
			}

			info = m_thread_info[thread_index];
			return true;
		}

		EXMETHOD std::vector<WorkerThreadInfo> GetAllThreadInfo() const override
		{
			return m_thread_info;
		}

		EXMETHOD void SetLoadBalancingStrategy(uint32_t strategy) override
		{
			m_load_balancing_strategy = strategy;
		}

		EXMETHOD HRESULT ForceLoadBalance() override
		{
			// 简化的负载均衡实现
			std::lock_guard<std::mutex> lock(m_queue_mutex);

			// 计算总任务数
			size_t total_tasks = 0;
			for (const auto& queue : m_work_queues) {
				total_tasks += queue.size();
			}

			if (total_tasks == 0) {
				return S_OK; // 没有任务需要均衡
			}

			// 重新分配任务
			std::vector<WorkItem> all_tasks;
			for (auto& queue : m_work_queues) {
				while (!queue.empty()) {
					all_tasks.push_back(queue.front());
					queue.pop();
				}
			}

			// 平均分配任务
			size_t tasks_per_queue = total_tasks / m_thread_count;
			size_t remaining_tasks = total_tasks % m_thread_count;

			size_t task_index = 0;
			for (uint32_t i = 0; i < m_thread_count; ++i) {
				size_t queue_size = tasks_per_queue + (i < remaining_tasks ? 1 : 0);
				for (size_t j = 0; j < queue_size && task_index < all_tasks.size(); ++j) {
					m_work_queues[i].push(all_tasks[task_index++]);
				}
			}

			m_stats.load_balancing_events++;
			return S_OK;
		}

	private:
		/// 工作项结构
		struct WorkItem
		{
			std::function<void()> work_function;
			TaskPriority priority;
			std::chrono::steady_clock::time_point submit_time;

			WorkItem() : priority(TaskPriority::NORMAL) {}
		};

		/// 工作线程处理函数
		void WorkerThreadProc(uint32_t thread_index)
		{
			while (!m_shutdown_requested) {
				if (m_state == ThreadPoolState::PAUSED) {
					// 暂停状态，等待恢复
					std::unique_lock<std::mutex> lock(m_wait_mutex);
					m_work_condition.wait(lock, [this]() {
						return m_state != ThreadPoolState::PAUSED || m_shutdown_requested;
					});
					continue;
				}

				WorkItem work_item;
				bool has_work = false;

				// 尝试从自己的队列获取工作
				{
					std::lock_guard<std::mutex> lock(m_queue_mutex);
					if (!m_work_queues[thread_index].empty()) {
						work_item = m_work_queues[thread_index].front();
						m_work_queues[thread_index].pop();
						has_work = true;
						m_stats.pending_tasks--;
					}
				}

				// 如果没有工作且启用了工作窃取，尝试从其他队列窃取工作
				if (!has_work && m_enable_work_stealing) {
					has_work = StealWork(thread_index, work_item);
					if (has_work) {
						m_stats.work_stealing_events++;
					}
				}

				if (has_work) {
					// 更新线程状态
					m_thread_info[thread_index].is_active = true;
					m_thread_info[thread_index].last_activity = std::chrono::steady_clock::now();

					// 执行工作
					auto start_time = std::chrono::steady_clock::now();

					try {
						work_item.work_function();
					}
					catch (...) {
						// 工作项执行失败，记录但不中断线程
					}

					auto end_time = std::chrono::steady_clock::now();
					auto work_time = std::chrono::duration_cast<std::chrono::milliseconds>(
						end_time - start_time);

					// 更新统计信息
					{
						std::lock_guard<std::mutex> lock(m_stats_mutex);
						m_thread_info[thread_index].tasks_processed++;
						m_thread_info[thread_index].total_work_time += work_time;
						m_stats.total_tasks_processed++;

						// 更新平均任务时间
						if (m_stats.total_tasks_processed > 0) {
							auto total_time = m_stats.average_task_time.count() * (m_stats.total_tasks_processed - 1) +
											 work_time.count();
							m_stats.average_task_time = std::chrono::milliseconds(total_time / m_stats.total_tasks_processed);
						}
					}

					m_thread_info[thread_index].is_active = false;
				} else {
					// 没有工作，等待新工作
					std::unique_lock<std::mutex> lock(m_wait_mutex);
					m_work_condition.wait_for(lock, std::chrono::milliseconds(100));
				}
			}
		}

		/// 工作窃取
		bool StealWork(uint32_t thread_index, WorkItem& work_item)
		{
			std::lock_guard<std::mutex> lock(m_queue_mutex);

			// 尝试从其他线程的队列窃取工作
			for (uint32_t i = 0; i < m_thread_count; ++i) {
				if (i != thread_index && !m_work_queues[i].empty()) {
					work_item = m_work_queues[i].front();
					m_work_queues[i].pop();
					m_stats.pending_tasks--;
					return true;
				}
			}

			return false;
		}

		/// 选择最佳队列
		uint32_t SelectBestQueue()
		{
			std::lock_guard<std::mutex> lock(m_queue_mutex);

			switch (m_load_balancing_strategy) {
			case 0: // Round Robin
				{
					static std::atomic<uint32_t> s_round_robin_index{0};
					return s_round_robin_index.fetch_add(1) % m_thread_count;
				}
			case 1: // Least Loaded
				{
					uint32_t best_queue = 0;
					size_t min_size = m_work_queues[0].size();

					for (uint32_t i = 1; i < m_thread_count; ++i) {
						if (m_work_queues[i].size() < min_size) {
							min_size = m_work_queues[i].size();
							best_queue = i;
						}
					}

					return best_queue;
				}
			case 2: // Random
				{
					static std::random_device rd;
					static std::mt19937 gen(rd());
					std::uniform_int_distribution<uint32_t> dis(0, m_thread_count - 1);
					return dis(gen);
				}
			default:
				return 0;
			}
		}

	private:
		uint32_t m_thread_count;
		bool m_enable_work_stealing;
		bool m_enable_cpu_affinity;
		std::atomic<ThreadPoolState> m_state;
		uint32_t m_load_balancing_strategy;
		bool m_is_initialized;
		std::atomic<bool> m_shutdown_requested;

		// 工作线程
		std::vector<std::thread> m_worker_threads;
		std::vector<WorkerThreadInfo> m_thread_info;

		// 工作队列（每个线程一个队列）
		std::vector<std::queue<WorkItem>> m_work_queues;
		mutable std::mutex m_queue_mutex;

		// 统计信息
		ThreadPoolStats m_stats;
		mutable std::mutex m_stats_mutex;

		// 同步
		std::condition_variable m_work_condition;
		std::mutex m_wait_mutex;
	};

	// 全局实例
	static IWorkerThreadPool* g_thread_pool = nullptr;

	/// 创建工作线程池
	HRESULT CreateWorkerThreadPool(IWorkerThreadPool** thread_pool)
	{
		if (!thread_pool) return E_INVALIDARG;

		try
		{
			auto worker_pool = new UIWorkerThreadPool();
			*thread_pool = worker_pool;
			return S_OK;
		}
		catch_default({});
	}

	/// 获取全局线程池
	IWorkerThreadPool* GetGlobalThreadPool()
	{
		return g_thread_pool;
	}

	/// 设置全局线程池
	void SetGlobalThreadPool(IWorkerThreadPool* thread_pool)
	{
		if (g_thread_pool) {
			g_thread_pool->Release();
		}
		g_thread_pool = thread_pool;
		if (g_thread_pool) {
			g_thread_pool->AddRef();
		}
	}

	/// 线程池工厂实现
	HRESULT UIThreadPoolFactory::CreateOptimalThreadPool(IWorkerThreadPool** thread_pool)
	{
		if (!thread_pool) {
			return E_INVALIDARG;
		}

		try
		{
			throw_if_failed(
				CreateWorkerThreadPool(thread_pool),
				L"创建线程池失败"
			);

			uint32_t thread_count = GetRecommendedThreadCount();
			bool work_stealing = DetectThreadingCapabilities();
			bool cpu_affinity = GetSystemCoreCount() > 4; // 多核系统启用CPU亲和性

			throw_if_failed(
				(*thread_pool)->Initialize(thread_count, work_stealing, cpu_affinity),
				L"初始化线程池失败"
			);

			return S_OK;
		}
		catch_default({});
	}

	HRESULT UIThreadPoolFactory::CreateHighPerformanceThreadPool(uint32_t thread_count,
																 IWorkerThreadPool** thread_pool)
	{
		if (!thread_pool) {
			return E_INVALIDARG;
		}

		try
		{
			throw_if_failed(
				CreateWorkerThreadPool(thread_pool),
				L"创建高性能线程池失败"
			);

			throw_if_failed(
				(*thread_pool)->Initialize(thread_count, true, true), // 启用所有优化
				L"初始化高性能线程池失败"
			);

			// 设置最优负载均衡策略
			(*thread_pool)->SetLoadBalancingStrategy(1); // Least Loaded

			return S_OK;
		}
		catch_default({});
	}

	uint32_t UIThreadPoolFactory::GetRecommendedThreadCount()
	{
		uint32_t hardware_threads = std::thread::hardware_concurrency();

		// 推荐使用物理核心数，但不超过硬件线程数
		uint32_t physical_cores = GetSystemCoreCount();
		return std::min(physical_cores, hardware_threads);
	}

	std::vector<uint32_t> UIThreadPoolFactory::GetRecommendedCPUCores()
	{
		std::vector<uint32_t> cores;
		uint32_t core_count = GetSystemCoreCount();

		// 推荐使用前N个物理核心
		for (uint32_t i = 0; i < core_count; ++i) {
			cores.push_back(i);
		}

		return cores;
	}

	bool UIThreadPoolFactory::DetectThreadingCapabilities()
	{
		// 检测系统是否支持高效的多线程
		return std::thread::hardware_concurrency() >= 4;
	}

	uint32_t UIThreadPoolFactory::GetSystemCoreCount()
	{
		// 在Windows上获取物理核心数
		SYSTEM_INFO sysinfo;
		GetSystemInfo(&sysinfo);
		return sysinfo.dwNumberOfProcessors;
	}

	uint32_t UIThreadPoolFactory::GetSystemLogicalProcessorCount()
	{
		return std::thread::hardware_concurrency();
	}
}
