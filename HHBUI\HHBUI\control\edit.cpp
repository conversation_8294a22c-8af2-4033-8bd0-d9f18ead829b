﻿#include "pch.h"
#include "edit.h"
#include "textserv.h"
#include <atlbase.h>
#include <common/winapi.h>
#include <common/mem_dc.h>
//编辑框标识_显示光标
#define EEF_BSHOWCARET 1
//编辑框标识_已显示光标
#define EEF_BCARETSHHOWED 2
//编辑框标识_光标描述表
#define EEF_BCARETCONTEXT 4
//编辑框标识_选中
#define EEF_BSELECTED 8

#define TO_DEFAULTCOLOREMOJI 0x1000
#define TO_DISPLAYFONTCOLOR 0x2000
#ifndef LY_PER_INCH
#define LY_PER_INCH 1440
#endif

#ifndef LY_PER_INCH
#define LY_PER_INCH 1440
#endif

#ifndef HIMETRIC_PER_INCH
#define HIMETRIC_PER_INCH 2540
#endif

// 富文本框EM_EXSETSEL消息lParam参数结构
struct H_CHARRANGE
{
    LONG cpMin;
    LONG cpMax;
};
typedef HRESULT(_stdcall* CTSFunc)(IUnknown* punkOuter, ITextHost* pITextHost, IUnknown** ppUnk);
namespace HHBUI
{
    //EM_消息 https://learn.microsoft.com/zh-cn/windows/win32/controls/em-canundo
    class TextHost : public ITextHost
    {
        UIEdit* m_pOwner;

    public:
        TextHost(UIEdit* pOwner)
        {
            m_pOwner = pOwner;
        };

        HRESULT STDMETHODCALLTYPE QueryInterface(
            /* [in] */ REFIID riid,
            /* [iid_is][out] */ _COM_Outptr_ void __RPC_FAR* __RPC_FAR* ppvObject)
        {
            return E_NOINTERFACE;
        };

        ULONG STDMETHODCALLTYPE AddRef(void)
        {
            return E_NOINTERFACE;
        };

        ULONG STDMETHODCALLTYPE Release(void)
        {
            return E_NOINTERFACE;
        };

        //@cmember Get the DC for the host
        HDC TxGetDC()
        {
            if (!UIWinApi::ToList.dwMajorVersion)
                return m_pOwner->p_data.mDc;
            HDC mhdc = nullptr;
            m_pOwner->m_data.canvas->GetDC(&mhdc);
            return mhdc;
        };

        //@cmember Release the DC gotten from the host
        INT TxReleaseDC(HDC hdc)
        {
            if (!UIWinApi::ToList.dwMajorVersion)
                return ReleaseDC(0, hdc);
            return m_pOwner->m_data.canvas->ReleaseDC();
        };

        //@cmember Show the scroll bar
        BOOL TxShowScrollBar(INT fnBar, BOOL fShow)
        {
            m_pOwner->SetScrollShow(fnBar == 0, fShow);
            return TRUE;
        };

        //@cmember Enable the scroll bar
        BOOL TxEnableScrollBar(INT fuSBFlags, INT fuArrowflags)
        {
            m_pOwner->SetScrollEnable(fuSBFlags, fuArrowflags);
            return TRUE;
        };

        //@cmember Set the scroll range
        BOOL TxSetScrollRange(
            INT fnBar,
            LONG nMinPos,
            INT nMaxPos,
            BOOL fRedraw)
        {
            auto prcText = m_pOwner->p_data.prctext;
            LONG nMaxFixed = 0;
            if (fnBar == SB_VERT)
            {
                nMaxFixed = nMaxPos - (prcText.bottom - prcText.top);
                if (nMaxFixed > prcText.bottom - prcText.top)
                    m_pOwner->SetPadding(CW_USEDEFAULT, CW_USEDEFAULT, 10, CW_USEDEFAULT);
                else
                    m_pOwner->SetPadding(CW_USEDEFAULT, CW_USEDEFAULT, 2, CW_USEDEFAULT);
            }
            else
            {
                nMaxFixed = nMaxPos - (prcText.right - prcText.left);
                if (nMaxFixed > prcText.right - prcText.left)
                    m_pOwner->SetPadding(CW_USEDEFAULT, CW_USEDEFAULT, CW_USEDEFAULT, 10);
                else
                    m_pOwner->SetPadding(CW_USEDEFAULT, CW_USEDEFAULT, CW_USEDEFAULT, 2);
            }
           
            //p_data.prctext
            return m_pOwner->SetScrollRange(fnBar == 0, nMinPos, nMaxFixed, fRedraw);
        };

        //@cmember Set the scroll position
        BOOL TxSetScrollPos(INT fnBar, INT nPos, BOOL fRedraw)
        {
            m_pOwner->SetScrollPos(fnBar == SB_HORZ, nPos, fRedraw);
            return TRUE;
        };

        //@cmember InvalidateRect
        void TxInvalidateRect(LPCRECT prc, BOOL fMode)
        {
            if (prc)
                m_pOwner->Redraw((RECT*)prc);
        };

        //@cmember Send a WM_PAINT to the window
        void TxViewChange(BOOL fUpdate) {};

        //@cmember Create the caret
        BOOL TxCreateCaret(HBITMAP hbmp, INT xWidth, INT yHeight)
        {
            if (!FLAGS_CHECK(m_pOwner->m_data.dwStyle, eos_edit_hiddencaret))
            {
                xWidth = 2;
                m_pOwner->p_data.rcCaret = { 0,0,xWidth - 1 ,yHeight };
                return CreateCaret(m_pOwner->GethWnd(), (HBITMAP)-1, xWidth, yHeight);
            }
            return FALSE;
        };

        //@cmember Show the caret
        BOOL TxShowCaret(BOOL fShow)
        {
            if (fShow)
            {
                FLAGS_ADD(m_pOwner->p_data.flags, EEF_BSHOWCARET);
                ShowCaret(m_pOwner->GethWnd());
            }
            else
            {
                FLAGS_DEL(m_pOwner->p_data.flags, EEF_BSHOWCARET);
                HideCaret(m_pOwner->GethWnd());
            }
            return TRUE;
        };

        //@cmember Set the caret position
        BOOL TxSetCaretPos(INT x, INT y)
        {
            x -= 3;
            OffsetRect(&m_pOwner->p_data.rcCaret, x, y);
            if (FLAGS_CHECK(m_pOwner->m_data.dwState, state_focus))
            {
                FLAGS_ADD(m_pOwner->p_data.flags, EEF_BCARETCONTEXT | EEF_BSHOWCARET);
                FLAGS_DEL(m_pOwner->p_data.flags, EEF_BCARETSHHOWED);
                m_pOwner->Redraw(m_pOwner->p_data.rcCaret);
            }
            x += m_pOwner->m_data.Frame_w.left;
            y += m_pOwner->m_data.Frame_w.top;
            return SetCaretPos(x, y);
        };

        //@cmember Create a timer with the specified timeout
        BOOL TxSetTimer(UINT idTimer, UINT uTimeout)
        {
            return SetTimer(m_pOwner->GethWnd(), idTimer, uTimeout, NULL);
        };

        //@cmember Destroy a timer
        void TxKillTimer(UINT idTimer)
        {
            KillTimer(m_pOwner->GethWnd(), idTimer);
        };

        //@cmember Scroll the content of the specified window's client area
        void TxScrollWindowEx(
            INT dx,
            INT dy,
            LPCRECT lprcScroll,
            LPCRECT lprcClip,
            HRGN hrgnUpdate,
            LPRECT lprcUpdate,
            UINT fuScroll) {};

        //@cmember Get mouse capture
        void TxSetCapture(BOOL fCapture) {};

        //@cmember Set the focus to the text window
        void TxSetFocus() {
            m_pOwner->SetFocus();
        };

        //@cmember Establish a new cursor shape
        void TxSetCursor(HCURSOR hcur, BOOL fText) 
        {

        };

        //@cmember Converts screen coordinates of a specified point to the client coordinates
        BOOL TxScreenToClient(LPPOINT lppt)
        {
            return FALSE;
        };

        //@cmember Converts the client coordinates of a specified point to screen coordinates
        BOOL TxClientToScreen(LPPOINT lppt)
        {
            return FALSE;
        };

        //@cmember Request host to activate text services
        HRESULT TxActivate(LONG* plOldState)
        {
            return E_FAIL;
        };

        //@cmember Request host to deactivate text services
        HRESULT TxDeactivate(LONG lNewState)
        {
            return E_FAIL;
        };

        //@cmember Retrieves the coordinates of a window's client area
        HRESULT TxGetClientRect(LPRECT prc)
        {
            RECT scr = m_pOwner->p_data.prctext.ToRect();
            CopyMemory(prc, &scr, sizeof(RECT));
            return S_OK;
        };

        //@cmember Get the view rectangle relative to the inset
        HRESULT TxGetViewInset(LPRECT prc)
        {
            // 请求文本宿主窗口中文本周围的空白区域的尺寸。
            RECT scr = m_pOwner->p_data.prcinset.ToRect();
            CopyMemory(prc, &scr, sizeof(RECT));
            return S_OK;
        };
        //@cmember Get the default character format for the text
        HRESULT TxGetCharFormat(const CHARFORMATW** ppCF)
        {
            *ppCF = &m_pOwner->p_data.cfDef;
            return S_OK;
        };

        //@cmember Get the default paragraph format for the text
        HRESULT TxGetParaFormat(const PARAFORMAT** ppPF)
        {
            *ppPF = &m_pOwner->p_data.ppf;
            return S_OK;
        };

        //@cmember Get the background color for the window
        COLORREF TxGetSysColor(INT nIndex)
        {
            if (nIndex == COLOR_WINDOWTEXT)
            {
                UIColor color_text;
                m_pOwner->GetColor(color_text_normal, color_text);
                return color_text.GetRGB();
            }
            else
            {
                return GetSysColor(nIndex);
            }
        };

        //@cmember Get the background (either opaque or transparent)
        HRESULT TxGetBackStyle(TXTBACKSTYLE* pstyle)
        {
            *pstyle = TXTBACK_TRANSPARENT;
            return S_OK;
        };

        //@cmember Get the maximum length for the text
        HRESULT TxGetMaxLength(DWORD* plength)
        {
            return S_OK;
        };

        //@cmember Get the bits representing requested scroll bars for the window
        HRESULT TxGetScrollBars(DWORD* pdwScrollBar)
        {
            *pdwScrollBar = ES_AUTOHSCROLL | ES_AUTOVSCROLL;
            if (FLAGS_CHECK(m_pOwner->m_data.dwStyle, eos_scroll_h))
            {
                *pdwScrollBar |= WS_HSCROLL;
            }
            if (FLAGS_CHECK(m_pOwner->m_data.dwStyle, eos_scroll_v))
            {
                *pdwScrollBar |= WS_VSCROLL;
            }
            if (FLAGS_CHECK(m_pOwner->m_data.dwStyle, eos_scroll_disableno))
            {
                *pdwScrollBar |= ES_DISABLENOSCROLL;
            }
            return S_OK;
        };

        //@cmember Get the character to display for password input
        HRESULT TxGetPasswordChar(_Out_ TCHAR* pch)
        {
            *pch = m_pOwner->p_data.charPsw;
            return S_OK;
        };

        //@cmember Get the accelerator character
        HRESULT TxGetAcceleratorPos(LONG* pcp)
        {
            return S_OK;
        };

        //@cmember Get the native size
        HRESULT TxGetExtent(LPSIZEL lpExtent)
        {
            //[垂直缩放系数] = [客户端矩形的像素高度] * 2540 / [HIMETRIC 垂直范围] * [像素每垂直英寸 (设备上下文) ]
            *lpExtent = m_pOwner->p_data.sizelExtent;
            return S_OK;
        };

        //@cmember Notify host that default character format has changed
        HRESULT OnTxCharFormatChange(const CHARFORMATW* pCF)
        {
            return E_FAIL;
        };

        //@cmember Notify host that default paragraph format has changed
        HRESULT OnTxParaFormatChange(const PARAFORMAT* pPF)
        {
            return E_FAIL;
        };

        //@cmember Bulk access to bit properties
        HRESULT TxGetPropertyBits(DWORD dwMask, DWORD* pdwBits)
        {
            DWORD dwProperties = (UIWinApi::ToList.dwMajorVersion ? TXTBIT_D2DDWRITE | TXTBIT_D2DPIXELSNAPPED | TXTBIT_D2DSUBPIXELLINES : 0);
            if ((m_pOwner->m_data.dwStyle & eos_edit_richtext) != 0)
            {
                dwProperties |= TXTBIT_RICHTEXT;
            }

            if ((m_pOwner->m_data.dwTextFormat & DT_SINGLELINE) != DT_SINGLELINE)
            {
                dwProperties |= TXTBIT_MULTILINE;
                if ((m_pOwner->m_data.dwStyle & eos_scroll_v) != 0 || (m_pOwner->m_data.dwStyle & eos_scroll_h) != 0)
                {
                    dwProperties |= TXTBIT_WORDWRAP;//判断有滚动条开启多行
                }
            }
            if ((m_pOwner->m_data.dwStyle & eos_edit_disabledrag) == 0) {
                dwProperties |= TXTBIT_DISABLEDRAG;
            }
            if ((m_pOwner->m_data.dwStyle & eos_edit_readonly) != 0)
            {
                dwProperties |= TXTBIT_READONLY;
            }

            if ((m_pOwner->m_data.dwStyle & eos_edit_usepassword) != 0)
            {
                dwProperties |= TXTBIT_USEPASSWORD;
            }

            if ((m_pOwner->m_data.dwStyle & eos_edit_hideselection) == 0)
            {
                dwProperties |= TXTBIT_HIDESELECTION;
            }

            if ((m_pOwner->m_data.dwStyle & eos_edit_autowordsel) != 0)
            {
                dwProperties |= TXTBIT_AUTOWORDSEL;
            }

            if ((m_pOwner->m_data.dwStyle & eos_edit_allowbeep) != 0)
            {
                dwProperties |= TXTBIT_ALLOWBEEP;
            }

            if ((m_pOwner->m_data.dwStyle & eos_edit_fSaveSelection) != 0)
            {
                dwProperties |= TXTBIT_SAVESELECTION;
            }

            *pdwBits = dwMask & dwProperties;
            return S_OK;
        };

        //@cmember Notify host of events
        HRESULT TxNotify(DWORD iNotify, LPVOID pv)
        {
            if (iNotify != EN_UPDATE)
            {
                m_pOwner->DispatchNotify(iNotify, 0, (size_t)pv);
                if (iNotify == EN_SELCHANGE)
                {
                    if (!FLAGS_CHECK(m_pOwner->m_data.dwStyle, eos_edit_readonly))
                    {
                        m_pOwner->SetState(state_focus, TRUE);
                        m_pOwner->Redraw();
                    }
                    SELCHANGE* pSelChange = (SELCHANGE*)pv;
                    if (pSelChange->chrg.cpMin == pSelChange->chrg.cpMax)
                    {
                        FLAGS_DEL(m_pOwner->p_data.flags, EEF_BSELECTED);
                    }
                    else
                    {
                        FLAGS_ADD(m_pOwner->p_data.flags, EEF_BSELECTED);
                    }
                }
            }
            return S_OK;
        };

        // East Asia Methods for getting the Input Context
        HIMC TxImmGetContext()
        {
            return NULL;
        };

        void TxImmReleaseContext(HIMC himc) {};

        //@cmember Returns HIMETRIC size of the control bar.
        HRESULT TxGetSelectionBarWidth(LONG* lSelBarWidth)
        {
            *lSelBarWidth = 0;
            return S_OK;
        };
    };
}
HHBUI::UIEdit::UIEdit(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-edit", lpszName, dwStyle, (dwStyleEx == 0 ? eos_ex_focusable | eos_ex_tabstop : dwStyleEx),
        nID, dwTextFormat);
    SetCursor(IDC_IBEAM);
}

LPCWSTR HHBUI::UIEdit::GetClipboardText()
{
    LPCWSTR text = 0;
    if (OpenClipboard(nullptr))
    {
        HANDLE hClipboardData = GetClipboardData(CF_UNICODETEXT);
        if (hClipboardData != nullptr)
        {
            // 锁定剪贴板数据并将其复制到内存中
            wchar_t* pszData = static_cast<wchar_t*>(GlobalLock(hClipboardData));
            if (pszData != nullptr)
            {
                text = pszData;
                GlobalUnlock(hClipboardData);
            }
        }
        CloseClipboard();
    }

    return text;
}
void HHBUI::UIEdit::SetCueBanner(LPCWSTR lpsztext, UIColor crText)
{
    if(!crText.empty())
        p_data.crBanner = crText;
    if (p_data.pBanner)
        LocalFree((HLOCAL)p_data.pBanner);
    p_data.pBanner = StrDupW(lpsztext);
    Redraw();
}
void HHBUI::UIEdit::SetColorCaret(UIColor dwColor)
{
    if (p_data.crCaret)
        delete p_data.crCaret;
    p_data.crCaret = new UIBrush(dwColor);
}
void HHBUI::UIEdit::LoadRtf(LPCWSTR pszFileName)
{
    if (FLAGS_CHECK(m_data.dwStyle, eos_edit_richtext))
    {
        FILE* peFile = NULL;
        _wfopen_s(&peFile, pszFileName, L"rb");
        if (!peFile)
            return;
        EDITSTREAM es{};
        es.dwCookie = (DWORD_PTR)peFile;
        es.pfnCallback = edit_StreamInCallback_FILE;
        BOOL ok = FALSE;
        edit_sendmessage(EM_STREAMIN, SF_RTF, (size_t)&es, ok);
        fclose(peFile);
    }
}
BOOL HHBUI::UIEdit::SaveRtf(LPCWSTR pszFileName)
{
    if (FLAGS_CHECK(m_data.dwStyle, eos_edit_richtext))
    {
        FILE* peFile = NULL;
        _wfopen_s(&peFile, pszFileName, L"wb");
        if (!peFile)
            return 0;
        EDITSTREAM es{};
        es.dwCookie = (DWORD_PTR)peFile;
        es.pfnCallback = edit_StreamInCallback_FILE;
        BOOL ok = FALSE;
        edit_sendmessage(EM_STREAMOUT, SF_RTF, (LPARAM)&es, ok);
        fclose(peFile);
        return ok;
    }
    return false;
}
void HHBUI::UIEdit::SetInputType(INT dwFlags)
{
    Style(dwFlags);
    Redraw();
}
INT HHBUI::UIEdit::GetInputType()
{
    return m_data.dwStyle;
}
void HHBUI::UIEdit::SetText(LPCWSTR lpString)
{
    edit_setText(lpString);
}
LPCWSTR HHBUI::UIEdit::GetText()
{
    size_t lLen = GetTextLength();
    LPTSTR lpText = NULL; BOOL Sok = 0;
    GETTEXTEX gt{};
    gt.flags = GT_DEFAULT;

    gt.cb = (DWORD)(sizeof(TCHAR) * (lLen + 1));
    gt.codepage = 1200;
    lpText = new TCHAR[lLen + 1];
    ::ZeroMemory(lpText, (lLen + 1) * sizeof(TCHAR));

    gt.lpDefaultChar = NULL;
    gt.lpUsedDefChar = NULL;
    edit_sendmessage(EM_GETTEXTEX, (WPARAM)&gt, (LPARAM)lpText, Sok);
    return lpText;
}
LPCWSTR HHBUI::UIEdit::GetCurSelText()
{
    CHARRANGE cr{}; BOOL Sok = 0;
    cr.cpMin = cr.cpMax = 0;
    edit_sendmessage(EM_EXGETSEL, 0, (LPARAM)&cr, Sok);
    LPWSTR lpText = NULL;
    lpText = new WCHAR[cr.cpMax - cr.cpMin + 1];
    ::ZeroMemory(lpText, (cr.cpMax - cr.cpMin + 1) * sizeof(WCHAR));
    edit_sendmessage(EM_GETSELTEXT, 0, (LPARAM)lpText, Sok);
    return lpText;
}
size_t HHBUI::UIEdit::GetTextLength()
{
    BOOL Sok = 0;
    GETTEXTLENGTHEX textLenEx{};
    textLenEx.flags = GTL_DEFAULT;
    textLenEx.codepage = 1200;
    LRESULT lResult = 0;
    lResult = edit_sendmessage(EM_GETTEXTLENGTHEX, (WPARAM)&textLenEx, 0, Sok);
    return lResult;
}
BOOL HHBUI::UIEdit::IsTextModify()
{
    BOOL Sok = 0;
    LRESULT lResult = edit_sendmessage(EM_GETMODIFY, 0, 0, Sok);
    return lResult != S_OK;
}
size_t HHBUI::UIEdit::GetTextCount()
{
    LRESULT lResult; BOOL ret = false;
    lResult = edit_sendmessage(EM_GETLINECOUNT, 0, 0, ret);
    return lResult;
}
void HHBUI::UIEdit::AppendText(LPCWSTR text, UIColor dwColor, BOOL bCanUndo)
{
    if (dwColor.empty())
    {
        SetSel(-1, -1);
        ReplaceSel(text, bCanUndo);
    }
    else
    {
        CHARFORMAT2W cf{};
        ZeroMemory(&cf, sizeof(cf));

        BOOL Sok = 0;
        edit_sendmessage(EM_GETCHARFORMAT, 0, (LPARAM)&cf, Sok);

        cf.cbSize = sizeof(CHARFORMAT2W);
        cf.dwMask = CFM_COLOR;
        cf.crTextColor = dwColor.GetRGB();

        this->ReplaceSel(text, FALSE);
        auto len = (INT)GetTextLength();
        this->SetSel(len - lstrlenW(text), len);

        edit_sendmessage(EM_SETCHARFORMAT, SCF_SELECTION, (LPARAM)&cf, Sok);
        this->SetSel(-1, -1);

        edit_sendmessage(EM_GETCHARFORMAT, 0, (LPARAM)&cf, Sok);
        edit_sendmessage(EM_SETCHARFORMAT, SCF_SELECTION, (LPARAM)&cf, Sok);
    }
}
void HHBUI::UIEdit::SetSel(INT cpMin, INT cpMax, bool bNoScroll)
{
    CHARRANGE cr{};
    cr.cpMin = cpMin;
    cr.cpMax = cpMax;
    LRESULT lResult; BOOL Sok = 0;
    lResult = edit_sendmessage(EM_EXSETSEL, 0, (LPARAM)&cr, Sok);
    if (bNoScroll)
        edit_sendmessage(EM_SCROLLCARET, 0, 0L, Sok);
}
POINT HHBUI::UIEdit::GetSel()
{
    LRESULT lResult; BOOL ret = false;
    lResult = edit_sendmessage(EM_GETSEL, 0, 0, ret);
    POINT rel{ GET_X_LPARAM(lResult), GET_Y_LPARAM(lResult) };
    return rel;
}
void HHBUI::UIEdit::SelAll()
{
    SetSel(0, -1);
}
void HHBUI::UIEdit::ReplaceSel(LPCWSTR text, bool bCanUndo)
{
    BOOL Sok = 0;
    edit_sendmessage(EM_REPLACESEL, (WPARAM)bCanUndo, (LPARAM)text, Sok);
}
BOOL HHBUI::UIEdit::SetLimitText(size_t nLength)
{
    BOOL ret = false;
    edit_sendmessage(EM_LIMITTEXT, nLength, 0, ret);
    return ret;
}
size_t HHBUI::UIEdit::SetFindText(LPCWSTR text, BOOL fGetSel, BOOL bRorder, BOOL fAlef, BOOL bMatchcase, BOOL fWholeword)
{
    FINDTEXTEXW textRange{}; BOOL ret = false;
    textRange.chrg.cpMin = fGetSel ? GetSel().y : 0;
    textRange.chrg.cpMax = -1;
    textRange.lpstrText = text;
    DWORD dwMask = bRorder ? 0 : FR_DOWN;
    if (fAlef)
        dwMask |= FR_MATCHALEFHAMZA;
    if (bMatchcase)
        dwMask |= FR_MATCHCASE;
    if (fWholeword)
        dwMask |= FR_WHOLEWORD;
    textRange.chrg.cpMin = edit_sendmessage(EM_FINDTEXTEXW, dwMask, (LPARAM)&textRange, ret);
    if (textRange.chrg.cpMin != -1)
    {
        textRange.chrg.cpMax = textRange.chrg.cpMin + lstrlenW(text);
        edit_sendmessage(EM_SETSEL, textRange.chrg.cpMin, textRange.chrg.cpMax, ret);
    }
    return textRange.chrg.cpMin;
}
BOOL HHBUI::UIEdit::Redo()
{
    LRESULT lResult; BOOL ret = false;
    lResult = edit_sendmessage(EM_REDO, 0, 0, ret);
    return ret;
}
BOOL HHBUI::UIEdit::Undo()
{
    LRESULT lResult; BOOL ret = false;
    lResult = edit_sendmessage(EM_UNDO, 0, 0, ret);
    return ret;
}
void HHBUI::UIEdit::Clear()
{
    BOOL ret = false;
    edit_sendmessage(WM_CLEAR, 0, 0, ret);
}
void HHBUI::UIEdit::Copy()
{
    BOOL ret = false;
    edit_sendmessage(WM_COPY, 0, 0, ret);
}
void HHBUI::UIEdit::Cut()
{
    BOOL ret = false;
    edit_sendmessage(WM_CUT, 0, 0, ret);
}
void HHBUI::UIEdit::Paste()
{
    BOOL ret = false;
    edit_sendmessage(WM_PASTE, 0, 0, ret);
}
LRESULT HHBUI::UIEdit::SetSelCharFormat(INT dwMask, UIColor crText, LPCWSTR wzFontFace, DWORD fontSize, INT yOffset, BOOL bBold, BOOL bItalic, BOOL bUnderLine, BOOL bStrikeOut, BOOL bLink)
{
    CHARFORMAT2W Format;  BOOL Sok = 0;
    memset(&Format, 0, sizeof(CHARFORMAT2W));
    Format.cbSize = sizeof(CHARFORMAT2W);
    Format.dwMask = dwMask;
    DWORD dwEffects = 0;
    if ((dwMask & CFM_COLOR) == CFM_COLOR)
    {
        Format.crTextColor = crText.GetRGB();
    }
    if ((dwMask & CFM_OFFSET) == CFM_OFFSET)
    {
        Format.yOffset = yOffset;
    }
    if ((dwMask & (CFM_BOLD | CFM_ITALIC | CFM_UNDERLINE | CFM_STRIKEOUT | CFM_LINK)) != 0)
    {
        if (bBold)
        {
            dwEffects = dwEffects | CFE_BOLD;
        }
        if (bItalic)
        {
            dwEffects = dwEffects | CFE_ITALIC;
        }
        if (bUnderLine)
        {
            dwEffects = dwEffects | CFE_UNDERLINE;
        }
        if (bStrikeOut)
        {
            dwEffects = dwEffects | CFE_STRIKEOUT;
        }
        if (bLink)
        {
            dwEffects = dwEffects | CFE_LINK;
        }
        Format.dwEffects = dwEffects;
    }
    if ((dwMask & CFM_FACE) == CFM_FACE)
    {
        RtlMoveMemory(Format.szFaceName, wzFontFace, LF_FACESIZE);
    }
    if ((dwMask & CFM_SIZE) == CFM_SIZE)
    {
        Format.yHeight = fontSize * 1440 / 96;
    }
    return edit_sendmessage(EM_SETCHARFORMAT, SCF_SELECTION, (LPARAM)&Format, Sok);
}
LRESULT HHBUI::UIEdit::SetSelParFormat(DWORD dwMask, WORD wNumbering, INT dxStartIndent, INT dxRightIndent, INT dxOffset, WORD wAlignment)
{
    PARAFORMAT2 Format; BOOL Sok = 0;
    memset(&Format, 0, sizeof(PARAFORMAT2));
    Format.cbSize = sizeof(PARAFORMAT2);
    Format.dwMask = dwMask;
    if ((dwMask & PFM_NUMBERING) == PFM_NUMBERING)
    {
        Format.wNumbering = wNumbering;
    }
    if ((dwMask & PFM_STARTINDENT) == PFM_STARTINDENT)
    {
        Format.dxStartIndent = dxStartIndent * 20;
    }
    if ((dwMask & PFM_RIGHTINDENT) == PFM_RIGHTINDENT)
    {
        Format.dxRightIndent = dxRightIndent * 20;
    }
    if ((dwMask & PFM_OFFSET) == PFM_OFFSET)
    {
        Format.dxOffset = dxOffset;
    }
    if ((dwMask & PFM_ALIGNMENT) == PFM_ALIGNMENT)
    {
        Format.wAlignment = wAlignment;
    }
    return edit_sendmessage(EM_SETPARAFORMAT, 0, (LPARAM)&Format, Sok);
}
// 检查字符串是否为数值
bool IsNumeric(const std::wstring& str)
{
    if (str.empty())
        return false;
    for (wchar_t ch : str)
    {
        if (!iswdigit(ch))
            return false;
    }
    return true;
}
LRESULT HHBUI::UIEdit::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_CREATE)
	{
        p_data.crBanner = UIColor(0, 0, 0, 128);
        p_data.charPsw = 9679;
        p_data.crCaret = new UIBrush((UIColor(0, 0, 0, 255)));
      
        FLAGS_ADD(m_data.dwFlags, EOF_BDISABLESPACEANDENTER);
        TextHost* pITH = new TextHost(this);
        CTSFunc CreateTextServicesL = NULL;
        CreateTextServicesL = (CTSFunc)GetProcAddress(UIWinApi::ToList.Msftedit, "CreateTextServices");
        if (pITH != 0)
        {
            IUnknown* pIUnk = nullptr;
            if (CreateTextServicesL(NULL, pITH, &pIUnk) == 0)
            {
                ITextServices* pITS = nullptr;
                IID* pIID_ITextServices = (IID*)(VOID*)GetProcAddress(UIWinApi::ToList.Msftedit, "IID_ITextServices");
                pIUnk->QueryInterface(*pIID_ITextServices, (LPVOID*)&pITS);

               
                pITS->OnTxInPlaceActivate(0);
                pITS->OnTxUIActivate();
                LRESULT ret;
                if (FLAGS_CHECK(m_data.dwStyle, eos_edit_richtext))
                    pITS->TxSendMessage(EM_LIMITTEXT, 0x7FFFFFFE, 0, &ret);
                pITS->TxSendMessage(WM_USER + 389, 0, 0, &ret);
                pITS->TxSendMessage(EM_SETTYPOGRAPHYOPTIONS, TO_DEFAULTCOLOREMOJI | TO_DISPLAYFONTCOLOR, TO_DEFAULTCOLOREMOJI | TO_DISPLAYFONTCOLOR, &ret);
                pITS->TxSendMessage(EM_SETEVENTMASK, 0, ENM_CHANGE | ENM_SELCHANGE | ENM_LINK | ENM_DRAGDROPDONE, &ret);
                pITS->TxSendMessage(EM_AUTOURLDETECT, FLAGS_CHECK(m_data.dwStyle, eos_edit_parseurl), 0, &ret);
            
                p_data.ith = pITH;
                p_data.its = pITS;
                setppf(&p_data.ppf);
                setpcf(&p_data.cfDef);

                SetPadding(3, CW_USEDEFAULT, 5, CW_USEDEFAULT);
                edit_setText(m_data.pstrTitle);
                edit_size();
            }
        }
	}
    else if (uMsg == WM_DESTROY)
    {
        ::KillTimer(hWnd, (size_t)this + TIMER_EDIT_CARET);
        BOOL ret = FALSE;
        edit_sendmessage(uMsg, wParam, lParam, ret);
        delete p_data.crCaret;
        ((ITextServices*)p_data.its)->Release();
        ((ITextHost*)p_data.ith)->Release();
        delete p_data.ith;
        if (!UIWinApi::ToList.dwMajorVersion)
            ExMemDCDestroy(p_data.mDc, p_data.hBmp, p_data.pBits);

    }
    else if (uMsg == WM_SIZE)
    {
        //setppf(&p_data.ppf);
        //setpcf(&p_data.cfDef);
        edit_size();
    }
    else if (uMsg == WM_VSCROLL || uMsg == WM_HSCROLL)
    {
        LRESULT ret = 0;
        if (LOWORD(wParam) == SB_THUMBTRACK || LOWORD(wParam) == SB_THUMBPOSITION)
        {
            POINT scrollPos{};
            INT pos = 0, lpnMin = 0, lpnMax = 0, lpnPos = 0;
            if (uMsg == WM_VSCROLL)
            {
                GetScrollInfo(SB_HORZ, lpnMin, lpnMax, lpnPos, pos);
                scrollPos.y = pos;
            }
            else
            {
                GetScrollInfo(SB_VERT, lpnMin, lpnMax, lpnPos, pos);
                scrollPos.x = pos;
            }
            ((ITextServices*)p_data.its)->TxSendMessage(EM_SETSCROLLPOS, 0, reinterpret_cast<LPARAM>(&scrollPos), &ret);
        }
        else
        {
            BOOL _ret = FALSE;
            return edit_sendmessage(uMsg, wParam, lParam, _ret);
        }
        return ret;
    }
    else if (uMsg == WM_MOUSEWHEEL)
    {
        uMsg = 0;
        if ((m_data.dwStyle & eos_scroll_v) == eos_scroll_v)
        {
            uMsg = WM_VSCROLL;
        }
        else if ((m_data.dwStyle & eos_scroll_h) == eos_scroll_h)
        {
            uMsg = WM_HSCROLL;
        }
        auto zDelta = (short)GET_Y_LPARAM(wParam);
        if (uMsg != 0)
        {
            BOOL ret = FALSE;
            edit_sendmessage(uMsg, zDelta > 0 ? SB_LINEUP : SB_LINEDOWN, 0, ret);
            return 1;
        }
    }
    else if (uMsg == WM_NOTIFY)
    {

    }
    else if (uMsg == WM_ERASEBKGND)
    {

    }
    else if (uMsg == WM_NCCREATE || uMsg == WM_NCCALCSIZE)
    {

    }
    else if (uMsg == WM_MOUSEHOVER)
    {
        SetState(state_hover, FALSE);
        Redraw();
    }
    else if (uMsg == WM_MOUSELEAVE)
    {
        SetState(state_hover, TRUE);
        Redraw();
    }
    else if (uMsg == WM_SETFOCUS)
    {
        if (FLAGS_CHECK(m_data.dwStyle, eos_edit_readonly) && !FLAGS_CHECK(m_data.dwStyle, eos_edit_hideselection))
        {
            return 1;
        }
        m_data.pWnd->SetIme(!((m_data.dwStyle & eos_edit_usepassword) == eos_edit_usepassword));
        ((ITextServices*)p_data.its)->OnTxUIActivate();
        BOOL ret = FALSE;
        edit_sendmessage(uMsg, 0, 0, ret);
        SetState(state_focus, FALSE);
        Redraw();
      

        if (!((m_data.dwStyle & eos_edit_hiddencaret) == eos_edit_hiddencaret))
        {
            ::SetTimer(hWnd, (size_t)this + TIMER_EDIT_CARET, 500, edit_timer_caret);
         
        }
        if (!FLAGS_CHECK(m_data.dwStyle, eos_edit_richtext))
            SetSel(0, -1, TRUE);
    }
    else if (uMsg == WM_KILLFOCUS)
    {
        if (!((m_data.dwStyle & eos_edit_hiddencaret) == eos_edit_hiddencaret))
        {
            ::KillTimer(hWnd, (size_t)this + TIMER_EDIT_CARET);
            if ((p_data.flags & (EEF_BSHOWCARET | EEF_BCARETCONTEXT)) == (EEF_BSHOWCARET | EEF_BCARETCONTEXT))
            {
                p_data.flags -= (p_data.flags & (EEF_BSHOWCARET | EEF_BCARETCONTEXT));
                Redraw(&p_data.rcCaret);
            }
        }
        BOOL ret = FALSE;
        edit_sendmessage(uMsg, 0, 0, ret);
        ((ITextServices*)p_data.its)->OnTxUIDeactivate();
        SetState(state_focus, TRUE);
        DestroyCaret();
        m_data.pWnd->SetIme(FALSE);
        Redraw();
    }
    else if (uMsg == WM_KEYDOWN)
    {
        if ((m_data.dwStyle & eos_edit_disablectrl) != eos_edit_disablectrl)
        {
            switch (wParam)
            {
            case  0x41:  //selete  all 
            case  0x46:  //ctrl+f
            case  0x56:  //paste
                if ((m_data.dwStyle & eos_edit_numericinput) == eos_edit_numericinput)
                {
                    std::wstring clipboardText = GetClipboardText();
                    if (!IsNumeric(clipboardText))
                        return FALSE;
                }
                break;
            case  0x58:  //cut
            case  0x43:  //copy
            case  0x5A:  //undo
            case  0x59:  //redo
                break;
            }
        }
        BOOL ret = FALSE;
        edit_sendmessage(uMsg, wParam, lParam, ret);

    }
    else if (uMsg == WM_CHAR)
    {
        BOOL ret = FALSE;
        if ((m_data.dwStyle & eos_edit_numericinput) == eos_edit_numericinput)
        {
            if (!(wParam > 44 && wParam < 58 && wParam != 47))
            {
                return 1;
            }
        }
        if (wParam == VK_TAB)
        {
            if (!((m_data.dwStyle & eos_edit_allowtab) == eos_edit_allowtab))
            {
                return 1;
            }
        }
        edit_sendmessage(uMsg, wParam, lParam, ret);
    }
    else if (uMsg == WM_CONTEXTMENU)
    {
        edit_contextmenu(hWnd, wParam, GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
    }
    else if (uMsg == WM_COMMAND)
    {
        edit_command(uMsg, wParam, lParam);
    }
    else if (uMsg == WM_SETTEXT)
    {
        edit_setText(m_data.pstrTitle);
    }
    else if (uMsg == WM_SYSCOLORCHANGE)
    {
        if (wParam == color_text_normal)
        {
            UIColor dwColor;
            GetColor(color_text_normal, dwColor);
            COLORREF crOld = p_data.cfDef.crTextColor;
            p_data.cfDef.crTextColor = dwColor.GetRGB() & 0x00ffffff;
            ((ITextServices*)p_data.its)->OnTxPropertyBitsChange(TXTBIT_CHARFORMATCHANGE, TXTBIT_CHARFORMATCHANGE);
            return crOld;
        }
    }
    else if (uMsg == WM_SETFONT)
    {
        setppf(&p_data.ppf);
        setpcf(&p_data.cfDef);
        edit_size();
        //edit_setText(m_data.pstrTitle);
    }
    else if (uMsg == WM_SETCURSOR)
    {
        POINT pt{};
        pt.x = GET_X_LPARAM(wParam);
        pt.y = GET_Y_LPARAM(wParam);
        if (FLAGS_CHECK(m_data.dwStyle, eos_edit_readonly))
        {
            SetCursor(IDC_ARROW);
        }
        if (p_data.prctext.PtInRect(pt.x, pt.y) ? FALSE : TRUE)
        {
            return 0;
        }

    }
    else
    {
        BOOL bFree = FALSE;
        LRESULT ret = edit_sendmessage(uMsg, wParam, lParam, bFree);
        if (bFree)
        {
            return ret;
        }
    }
	return S_OK;
}

void HHBUI::UIEdit::OnPaintProc(ps_context ps)
{
    LPCWSTR lpBanner = p_data.pBanner;
    BOOL bDrawBanner = FALSE;
    if (lpBanner)
    {
        if (p_data.its == 0)
        {
            bDrawBanner = TRUE;
        }
        else
        {
            SIZE sz{};
            sz.cx = GTL_DEFAULT;
            sz.cy = CP_WINUNICODE;
            BOOL ret;
            if (edit_sendmessage(EM_GETTEXTLENGTHEX, (size_t)&sz, 0, ret) == 0)
            {
                bDrawBanner = TRUE;
            }
        }
        if (bDrawBanner)
        {
            bDrawBanner = FALSE;
            if (!((ps.dwState & state_focus) != 0 && (ps.dwStyle & eos_edit_showtipsalways) == 0) || (ps.dwStyle & eos_edit_readonly) != 0)
            {
                ps.hCanvas->DrawTextByColor(ps.hFont, lpBanner, ps.dwTextFormat, ps.rcPaint.left, ps.rcPaint.top, ps.rcPaint.right, ps.rcPaint.bottom, p_data.crBanner);
                bDrawBanner = TRUE;
            }
        }
    }
    if (p_data.its != 0)
    {
        auto rcTmp= ps.rcText;
       // IntersectRect(&rcTmp, &ps.rcText, &ps.rcPaint);
        BOOL ismove = (m_data.pWnd->Flags() & EWF_BSIZEMOVING) == EWF_BSIZEMOVING;

        if (!UIWinApi::ToList.dwMajorVersion)
        {
            HDC mDc = p_data.mDc;
            HDC hDc = 0;
            ps.hCanvas->GetDC(&hDc);
            RECT scr = rcTmp.ToRect();
            ((ITextServices*)p_data.its)->TxDraw(DVASPECT_CONTENT, 0, NULL, NULL, hDc, NULL, NULL, NULL, &scr, NULL, NULL, ismove ? TXTVIEW_INACTIVE : TXTVIEW_ACTIVE);
            BitBlt(hDc, rcTmp.left, rcTmp.top, rcTmp.right - rcTmp.left, rcTmp.bottom - rcTmp.top, mDc, 0, 0, SRCPAINT);
            ps.hCanvas->ReleaseDC();
        }
        else
        {
            RECT rcTmp1{ (LONG)rcTmp.left, (LONG)rcTmp.top, (LONG)(rcTmp.right - rcTmp.left), (LONG)(rcTmp.bottom - rcTmp.top) };
            RECT rc = rcTmp.ToRect();
            ((ITextServices2*)p_data.its)->TxDrawD2D(UIDrawContext::ToList.d2d_dc, (LPCRECTL)&rc, &rcTmp1, ismove ? TXTVIEW_INACTIVE : TXTVIEW_ACTIVE);

        }
        if (!(m_data.dwStyle & eos_edit_hiddencaret) &&
            !(p_data.flags & EEF_BSELECTED) &&
            (p_data.flags & EEF_BCARETCONTEXT) &&
            !(p_data.flags & EEF_BCARETSHHOWED))
        {
            rcTmp = p_data.rcCaret;
            if (rcTmp.right > 0.f && rcTmp.bottom > 0.f)
            {
                ps.hCanvas->FillRect(p_data.crCaret, rcTmp.left, rcTmp.top, rcTmp.right, rcTmp.bottom);

            }
        }

    }
}

void HHBUI::UIEdit::edit_size()
{
    GetRect(p_data.prctext, grt_text, TRUE);
    p_data.prctext.left += 2.f;
    p_data.prctext.right -= 2.f;
    INT width = p_data.prctext.right - p_data.prctext.left;
    INT height = p_data.prctext.bottom - p_data.prctext.top;
    if (width <= 0)
        width = 1;
    if (height <= 0)
        height = 1;
    if (!UIWinApi::ToList.dwMajorVersion)
    {
        if (p_data.mDc)
            ExMemDCDestroy(p_data.mDc, p_data.hBmp, p_data.pBits);
        if (ExMemDCCreate(p_data.mDc, p_data.hBmp, p_data.pBits, width, height))
        {
            /*
            for (INT i = 0; i < width; i++)
            {
                for (INT j = 0; j < height; j++)
                {
                    ((INT*)p_data.pBits)[i * height + j] = -16777216;
                }
            }*/
        }
     
    }
    else
    {
        p_data.mDc = GetDC(0);
    }
   
    p_data.sizelExtent.cx = MulDiv(width, HIMETRIC_PER_INCH, USER_DEFAULT_SCREEN_DPI);
    p_data.sizelExtent.cy = MulDiv(height, HIMETRIC_PER_INCH, USER_DEFAULT_SCREEN_DPI);

    DWORD tmp = TXTBIT_CLIENTRECTCHANGE | TXTBIT_EXTENTCHANGE;
    if ((m_data.dwTextFormat & DT_SINGLELINE) == DT_SINGLELINE || (m_data.dwTextFormat & DT_VCENTER) == DT_VCENTER && (m_data.dwStyle & eos_scroll_v) != eos_scroll_v)
    {
        TEXTMETRICW tmrc = {};
        LOGFONTW logfont = {};
        auto hFont = GetFont();
        if (hFont->GetLogFont(&logfont))
        {
            HFONT hfont = CreateFontIndirectW(&logfont);
            HGDIOBJ hgdiobj = SelectObject(p_data.mDc, hfont);
            GetTextMetricsW(p_data.mDc, &tmrc);
            SelectObject(p_data.mDc, hgdiobj);
            DeleteObject(hfont);

            if (UIWinApi::ToList.dwMajorVersion)
                DeleteDC(p_data.mDc);
            tmp |= TXTBIT_VIEWINSETCHANGE;
        }
        p_data.prcinset.top = MulDiv((height - tmrc.tmHeight) / 2, HIMETRIC_PER_INCH, USER_DEFAULT_SCREEN_DPI);
    }
    else
    {
        SetScrollInfo(SB_VERT, SIF_PAGE, 0, 0, height, 0, FALSE);
        SetScrollInfo(SB_HORZ, SIF_PAGE, 0, 0, width, 0, FALSE);
    }
    if (p_data.its)
    {
        ((ITextServices*)p_data.its)->OnTxPropertyBitsChange(tmp, tmp);
    }
}

LRESULT HHBUI::UIEdit::edit_sendmessage(INT uMsg, WPARAM wParam, LPARAM lParam, BOOL& sOK)
{
    LRESULT ret = 0;
    ITextServices* pits = (ITextServices*)p_data.its;
    if (pits != nullptr)
    {
        sOK = pits->TxSendMessage(uMsg, wParam, lParam, &ret) == S_OK;
    }
    return ret;
}

void HHBUI::UIEdit::edit_contextmenu(HWND hWnd, WPARAM wParam, INT x, INT y)
{
    if (FLAGS_CHECK(m_data.dwStyle, eos_edit_disablemenu))
    {
        return;
    }
    if (FLAGS_CHECK(m_data.dwStyle, eos_edit_usepassword))
    {
        return;
    }
    if (FLAGS_CHECK(m_data.dwStyle, eos_edit_readonly))
    {
        return;
    }
    HMENU hMenu = GetSubMenu(UIWinApi::ToList.hMenuEdit, 0);
    BOOL sOK;
    LRESULT tmp = edit_sendmessage(EM_CANUNDO, 0, 0, sOK); //撤销
    EnableMenuItem(hMenu, 0, tmp ? 1024 : 1026);
    SIZE sz;
    edit_sendmessage(EM_EXGETSEL, 0, (size_t)&sz, sOK);
    LONG index = sz.cy - sz.cx;
    index = index > 0 ? index : 0;
    EnableMenuItem(hMenu, 2, index != 0 ? 1024 : 1026);              //剪切
    EnableMenuItem(hMenu, 3, index != 0 ? 1024 : 1026);              //复制
    EnableMenuItem(hMenu, 5, index != 0 ? 1024 : 1026);              //删除
    tmp = edit_sendmessage(EM_CANPASTE, 0, 0, sOK);          //粘贴
    EnableMenuItem(hMenu, 4, tmp != 0 ? 1024 : 1026);                //剪切
    sz.cx = GTL_DEFAULT;
    sz.cy = CP_WINUNICODE;
    tmp = edit_sendmessage(EM_GETTEXTLENGTHEX, (size_t)&sz, 0, sOK);
    EnableMenuItem(hMenu, 7, tmp != 0 ? 1024 : 1026); //全选
  
    EnableMenuItem(hMenu, 9, 1024); //从右到左
    EnableMenuItem(hMenu, 10, 1024);
    EnableMenuItem(hMenu, 11, 1024);
    UIMenu::Popup(m_data.pWnd, hMenu, x, y);
}

void HHBUI::UIEdit::edit_command(INT uMsg, WPARAM wParam, LPARAM lParam)
{
    BOOL sOK;
    if (wParam == WM_UNDO || wParam == WM_CUT || wParam == WM_COPY || wParam == WM_PASTE || wParam == WM_CLEAR)
    {
        if (wParam == WM_PASTE)
        {
            if ((m_data.dwStyle & eos_edit_numericinput) == eos_edit_numericinput)
            {
                std::wstring clipboardText = GetClipboardText();
                if (!IsNumeric(clipboardText))
                    return;
            }
        }
        uMsg = wParam;
        wParam = 0;
        edit_sendmessage(uMsg, wParam, lParam, sOK);
    }
    else if (wParam == WM_APP)
    {
        if (FLAGS_CHECK(m_data.dwTextFormat, Right))
            FLAGS_DEL(m_data.dwTextFormat, Right);
        else
        {
            if (FLAGS_CHECK(m_data.dwTextFormat, Center))
                FLAGS_DEL(m_data.dwTextFormat, Center);
            FLAGS_ADD(m_data.dwTextFormat, Right);
        }
        setppf(&p_data.ppf);
    }
    else if (wParam == WM_APP + 1)
    {
        output(22);
    }
    else
    {
        if (wParam == EM_SETSEL)
        {
            uMsg = EM_EXSETSEL;
            wParam = 0;

            CHARRANGE charRange = { 0, -1 };
            edit_sendmessage(uMsg, wParam, reinterpret_cast<size_t>(&charRange), sOK);
        }

    }
}

void HHBUI::UIEdit::edit_setText(LPCWSTR lpsztext)
{
    if (lpsztext)
    {
        if ((m_data.dwStyle & eos_edit_numericinput) == eos_edit_numericinput)
        {
            if (!IsNumeric(lpsztext))
                return;
        }
        if ((m_data.dwStyle & eos_edit_newline) != eos_edit_newline)//禁止回车时,删除\r\n
        {
            std::wstring strValue = lpsztext;
            std::wstring::size_type iFind = 0;
            while (true)
            {
                iFind = strValue.find(L"\r\n", iFind);
                if (std::wstring::npos == iFind)
                    break;
                strValue.replace(iFind, 2, L"");
            }
            ((ITextServices*)p_data.its)->TxSetText(strValue.c_str());
            return;
        }
        ((ITextServices*)p_data.its)->TxSetText(lpsztext);
    }
    else
        ((ITextServices*)p_data.its)->TxSetText(NULL);

}

void HHBUI::UIEdit::edit_timer_caret(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime)
{
    UIEdit* pObj = (UIEdit*)(idEvent - TIMER_EDIT_CARET);
    if ((pObj->p_data.flags & EEF_BCARETCONTEXT) == EEF_BCARETCONTEXT)
    {
        pObj->p_data.flags -= (pObj->p_data.flags & EEF_BCARETCONTEXT);
        pObj->p_data.flags |= EEF_BCARETSHHOWED;
    }
    else
    {
        pObj->p_data.flags |= (EEF_BSHOWCARET | EEF_BCARETCONTEXT);
        pObj->p_data.flags -= (pObj->p_data.flags & EEF_BCARETSHHOWED);
    }
    pObj->Redraw();
}

DWORD HHBUI::UIEdit::edit_StreamInCallback_FILE(DWORD_PTR dwCookie, LPBYTE pbBuff, LONG cb, LONG* pcb)
{
    FILE* f = (FILE*)dwCookie;
    LONG nReaded = (LONG)fread(pbBuff, 1, cb, f);
    if (pcb)
        *pcb = nReaded;
    return 0;
}

void HHBUI::UIEdit::setppf(PARAFORMAT2* ppf)
{
    memset(ppf, 0, sizeof(PARAFORMAT2));
    ppf->cbSize = sizeof(PARAFORMAT2);
    DWORD dwMask = PFM_ALL;
    WORD tmp;
    if ((m_data.dwTextFormat & DT_CENTER) == DT_CENTER)
    {
        tmp = PFA_CENTER;
    }
    else
    {
        if ((m_data.dwTextFormat & DT_RIGHT) == DT_RIGHT)
        {
            tmp = PFA_RIGHT;
        }
        else
        {
            tmp = PFA_LEFT;
        }
    }
    ppf->wAlignment = tmp;
    ppf->cTabCount = 1;
    ppf->rgxTabs[0] = lDefaultTab;
    ppf->dwMask = dwMask;
    ((ITextServices*)p_data.its)->OnTxPropertyBitsChange(TXTBIT_PARAFORMATCHANGE, TXTBIT_PARAFORMATCHANGE);
}

void HHBUI::UIEdit::setpcf(CHARFORMAT2W *pcf)
{
    LOGFONTW logfont = {};
    auto hFont = GetFont();
    hFont->GetLogFont(&logfont);
    memset(pcf, 0, sizeof(CHARFORMAT2W));
    pcf->cbSize = sizeof(CHARFORMAT2W);
    DWORD dwEffects = 0;

    if (logfont.lfWeight >= FW_BOLD)
    {
        dwEffects |= CFE_BOLD;
    }

    if (logfont.lfItalic != 0)
    {
        dwEffects |= CFE_ITALIC;
    }

    if (logfont.lfUnderline != 0)
    {
        dwEffects |= CFE_UNDERLINE;
    }

    if (logfont.lfStrikeOut != 0)
    {
        dwEffects |= CFE_STRIKEOUT;
    }
    pcf->dwEffects = dwEffects;
    pcf->yHeight = abs(MulDiv(logfont.lfHeight, LY_PER_INCH, USER_DEFAULT_SCREEN_DPI));
    pcf->bCharSet = logfont.lfCharSet;
    pcf->bPitchAndFamily = logfont.lfPitchAndFamily;
    UIColor tmpcr;
    GetColor(color_text_normal, tmpcr);
    pcf->crTextColor = tmpcr.GetRGB() & 0x00ffffff;
    pcf->dwMask = CFM_SIZE | CFM_OFFSET | CFM_FACE | CFM_CHARSET | CFM_COLOR | CFM_BOLD | CFM_ITALIC | CFM_UNDERLINE;
    wcscpy_s(pcf->szFaceName, logfont.lfFaceName);

    ((ITextServices*)p_data.its)->OnTxPropertyBitsChange(TXTBIT_CHARFORMATCHANGE, TXTBIT_CHARFORMATCHANGE);
}

