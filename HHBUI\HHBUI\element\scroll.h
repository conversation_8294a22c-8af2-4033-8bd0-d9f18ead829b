﻿#pragma once
/*滚动条 ps.不需要手动创建*/
namespace HHBUI
{
	enum ObjsbFlags
	{
		ess_horizontalscroll = 0,               //水平滚动条
		ess_verticalscroll = 1,                 //垂直滚动条
		ess_lefttopalign = 1 << 1,              //左顶对齐
		ess_rightbottomalign = 1 << 2,          //右底对齐
		ess_controlbutton = 1 << 3,             //控制按钮
	};
	class TOAPI UIScroll : public UIControl
	{
	public:
        UIScroll(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = eos_ex_focusable, INT nID = 0);
	protected:
        EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
        EXMETHOD void OnPaintProc(ps_context ps) override;
        static void CALLBACK sb_timer(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime);
        INT sb_OnScrollbar(INT uMsg, WPARAM wParam, LPARAM lParam, INT nPage, INT nLine = 0);
        void sb_nccalcsize();
        void sb_calcthumb(BOOL bVScroll);
        INT sb_pos2point(BOOL bVert, INT& cxy);
        INT sb_point2pos(INT x, INT y, BOOL bVert, BOOL bCheckPos);
        INT sb_realsetinfo(HWND hWnd, INT Mask, INT nMin, INT nMax, INT nPage, INT nPos, BOOL bRedraw);
        void sb_nchittest(INT x, INT y);
        void sb_mousemove(WPARAM wParam, INT x, INT y);
        size_t sb_parentnotify(WPARAM wParam, LPARAM lParam, INT uMsg);
        void sb_leftbuttondown(LPARAM lParam);
        void sb_oncommand(WPARAM wParam, LPARAM lParam);
        void sb_oncontextmenu(LPARAM lParam);
        void sb_show(BOOL fshow);
        void sb_set_wArrows(INT wArrows, BOOL fRedraw);

        struct si_s
        {
            UIBrush* hBrush = nullptr;
            INT httype = 0;
            INT nMin = 0;
            INT nMax = 0;
            INT nPage = 0;
            INT nPos = 0;
            INT nTrackPos = 0;
            INT nTrackPosOffset = 0;
            ExRectF xyz{},rcRegion{},rcArrow1{},rcArrow2{},rcThumb{};
            INT wArrows = 0;
            BOOL isRadius = FALSE;
            UIColor color_normal{};
            UIColor color_hover{};
            UIColor color_down{};
            UIColor color_btn_up{};
            UIColor color_btn_down{};
        }p_data;
        friend class UIControl;
        friend class UIWnd;
	};
}
