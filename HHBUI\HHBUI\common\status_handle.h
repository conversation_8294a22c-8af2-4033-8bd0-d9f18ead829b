﻿#pragma once
#define EX_WARNING_MARK		L"[警告]"
#define EX_WARNING(TXT)	EX_WARNING_MARK TXT
//调试相关
#define __M_WIDEN(x) L##x
#define M_WIDEN(x) __M_WIDEN(x)
#define M_INFO_DBG M_WIDEN(__FILE__), M_WIDEN(__FUNCTION__)

namespace HHBUI
{

	inline std::wstring _M_DBG_INFO_(std::wstring __FILE, std::wstring __FUN)
	{
		auto pos = __FILE.rfind(L"\\");
		if (pos == -1)
			pos = __FILE.rfind(L"/");
		if (pos == -1)
			pos = 0;
		else
			pos++;
		return L"[" + __FILE.substr(pos, __FILE.length() - pos) + L"] -> " + __FUN;
	}

	LPCWSTR ExStatusGetText(HRESULT status, bool* free);
	void ExOutError(std::wstring cls, std::wstring error);
	HRESULT ExStatusHandle(HRESULT status, LPCWSTR file, int line, LPCWSTR text);

}

