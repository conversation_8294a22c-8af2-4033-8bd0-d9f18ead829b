/**
** =====================================================================================
**
**       文件名称: render_profiler.h
**       创建时间: 2025-07-28
**       文件描述: 【HHBUI】渲染性能监控系统 - 高精度渲染性能分析与调试框架 （声明文件）
**
**       主要功能:
**       - 高精度GPU渲染性能监控
**       - 实时渲染统计与分析
**       - 渲染调试与诊断工具
**       - 性能瓶颈检测与优化建议
**       - 渲染事件追踪与时序分析
**       - 多维度性能指标监控
**       - 性能报告生成与导出
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11 API
**       - 高精度GPU时间戳查询
**       - 异常安全保证与错误恢复机制
**       - 低开销性能监控算法
**       - 多线程安全的数据收集
**       - 实时性能数据可视化
**       - 智能性能分析与建议
**
**       更新记录:
**       2025-07-28 v1.0.0.0 : 1. 创建渲染性能监控系统
**                             2. 实现高精度GPU性能监控
**                             3. 添加实时统计与分析
**                             4. 支持渲染调试与诊断
**                             5. 集成性能瓶颈检测
**                             6. 添加多维度指标监控
**                             7. 确保低开销高精度
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include <d3d11.h>
#include <wrl.h>
#include <chrono>
#include <vector>
#include <string>
#include <unordered_map>

namespace HHBUI
{
	/// 性能计数器类型
	enum class ProfilerCounterType
	{
		FRAME_TIME,          // 帧时间
		GPU_TIME,            // GPU时间
		DRAW_CALLS,          // 绘制调用次数
		TRIANGLES,           // 三角形数量
		VERTICES,            // 顶点数量
		TEXTURE_SWITCHES,    // 纹理切换次数
		SHADER_SWITCHES,     // 着色器切换次数
		RENDER_TARGET_SWITCHES, // 渲染目标切换次数
		GPU_MEMORY_USAGE,    // GPU内存使用量
		CPU_MEMORY_USAGE,    // CPU内存使用量
		CUSTOM               // 自定义计数器
	};

	/// 性能计数器数据
	struct ProfilerCounter
	{
		ProfilerCounterType type;
		std::string name;
		double current_value;
		double min_value;
		double max_value;
		double average_value;
		uint32_t sample_count;
		std::vector<double> history;  // 历史数据（用于图表显示）
		
		ProfilerCounter() : type(ProfilerCounterType::CUSTOM), current_value(0.0), 
			min_value(DBL_MAX), max_value(0.0), average_value(0.0), sample_count(0) {}
	};

	/// GPU查询对象
	struct GPUQuery
	{
		Microsoft::WRL::ComPtr<ID3D11Query> timestamp_start;
		Microsoft::WRL::ComPtr<ID3D11Query> timestamp_end;
		Microsoft::WRL::ComPtr<ID3D11Query> timestamp_disjoint;
		bool is_active;
		std::chrono::high_resolution_clock::time_point cpu_start_time;
		
		GPUQuery() : is_active(false) {}
	};

	/// 渲染性能监控器
	class UIRenderProfiler
	{
	public:
		UIRenderProfiler();
		~UIRenderProfiler();

		/// 初始化性能监控器
		HRESULT Initialize(ID3D11Device* device, ID3D11DeviceContext* context);

		/// 关闭性能监控器
		void Shutdown();

		/// 开始帧监控
		void BeginFrame();

		/// 结束帧监控
		void EndFrame();

		/// 开始GPU时间测量
		void BeginGPUEvent(const std::string& event_name);

		/// 结束GPU时间测量
		void EndGPUEvent(const std::string& event_name);

		/// 添加自定义计数器
		void AddCounter(const std::string& name, ProfilerCounterType type);

		/// 更新计数器值
		void UpdateCounter(const std::string& name, double value);

		/// 增加计数器值
		void IncrementCounter(const std::string& name, double delta = 1.0);

		/// 获取计数器数据
		const ProfilerCounter* GetCounter(const std::string& name) const;

		/// 获取所有计数器
		const std::unordered_map<std::string, ProfilerCounter>& GetAllCounters() const { return m_counters; }

		/// 获取当前帧率
		float GetCurrentFPS() const { return m_current_fps; }

		/// 获取平均帧率
		float GetAverageFPS() const { return m_average_fps; }

		/// 获取GPU使用率
		float GetGPUUsage() const;

		/// 获取GPU内存使用情况
		uint64_t GetGPUMemoryUsage() const;

		/// 重置所有统计数据
		void ResetStats();

		/// 启用/禁用性能监控
		void SetEnabled(bool enabled) { m_enabled = enabled; }

		/// 是否启用性能监控
		bool IsEnabled() const { return m_enabled; }

		/// 设置历史数据保存数量
		void SetHistorySize(uint32_t size) { m_history_size = size; }

		/// 导出性能数据到文件
		HRESULT ExportToFile(const std::wstring& file_path) const;

		/// 生成性能报告
		std::string GenerateReport() const;

	private:
		void UpdateFPS();
		void UpdateGPUQueries();
		void UpdateCounter(ProfilerCounter& counter, double value);
		HRESULT CreateGPUQueries();

	private:
		Microsoft::WRL::ComPtr<ID3D11Device> m_device;
		Microsoft::WRL::ComPtr<ID3D11DeviceContext> m_context;

		// 性能计数器
		std::unordered_map<std::string, ProfilerCounter> m_counters;
		
		// GPU查询
		std::unordered_map<std::string, GPUQuery> m_gpu_queries;
		Microsoft::WRL::ComPtr<ID3D11Query> m_frame_query_start;
		Microsoft::WRL::ComPtr<ID3D11Query> m_frame_query_end;
		Microsoft::WRL::ComPtr<ID3D11Query> m_frame_query_disjoint;

		// 帧率统计
		std::chrono::high_resolution_clock::time_point m_frame_start_time;
		std::chrono::high_resolution_clock::time_point m_last_fps_update;
		uint32_t m_frame_count;
		float m_current_fps;
		float m_average_fps;
		std::vector<float> m_fps_history;

		// 配置
		bool m_enabled;
		uint32_t m_history_size;
		bool m_gpu_queries_supported;
	};

	/// 渲染调试器
	class UIRenderDebugger
	{
	public:
		UIRenderDebugger();
		~UIRenderDebugger();

		/// 初始化调试器
		HRESULT Initialize(ID3D11Device* device, ID3D11DeviceContext* context);

		/// 关闭调试器
		void Shutdown();

		/// 启用/禁用线框模式
		void SetWireframeMode(bool enable);

		/// 启用/禁用深度缓冲区可视化
		void SetDepthVisualization(bool enable);

		/// 启用/禁用过度绘制检测
		void SetOverdrawDetection(bool enable);

		/// 设置调试颜色模式
		void SetDebugColorMode(int mode); // 0=正常, 1=纹理坐标, 2=法线, 3=深度

		/// 捕获当前帧到文件
		HRESULT CaptureFrame(const std::wstring& file_path);

		/// 验证渲染状态
		bool ValidateRenderState();

		/// 检查资源泄漏
		std::vector<std::string> CheckResourceLeaks();

		/// 获取设备信息
		std::string GetDeviceInfo() const;

		/// 获取驱动信息
		std::string GetDriverInfo() const;

	private:
		Microsoft::WRL::ComPtr<ID3D11Device> m_device;
		Microsoft::WRL::ComPtr<ID3D11DeviceContext> m_context;
		Microsoft::WRL::ComPtr<ID3D11Debug> m_debug_interface;

		bool m_wireframe_mode;
		bool m_depth_visualization;
		bool m_overdraw_detection;
		int m_debug_color_mode;
	};

	/// 全局性能监控器实例
	extern UIRenderProfiler* g_render_profiler;
	extern UIRenderDebugger* g_render_debugger;

	/// 便捷宏定义
	#define RENDER_PROFILE_BEGIN_FRAME() if(g_render_profiler) g_render_profiler->BeginFrame()
	#define RENDER_PROFILE_END_FRAME() if(g_render_profiler) g_render_profiler->EndFrame()
	#define RENDER_PROFILE_BEGIN_EVENT(name) if(g_render_profiler) g_render_profiler->BeginGPUEvent(name)
	#define RENDER_PROFILE_END_EVENT(name) if(g_render_profiler) g_render_profiler->EndGPUEvent(name)
	#define RENDER_PROFILE_INCREMENT(name) if(g_render_profiler) g_render_profiler->IncrementCounter(name)
	#define RENDER_PROFILE_UPDATE(name, value) if(g_render_profiler) g_render_profiler->UpdateCounter(name, value)

	/// RAII性能事件类
	class ScopedRenderEvent
	{
	public:
		ScopedRenderEvent(const std::string& name) : m_name(name)
		{
			RENDER_PROFILE_BEGIN_EVENT(m_name);
		}
		
		~ScopedRenderEvent()
		{
			RENDER_PROFILE_END_EVENT(m_name);
		}

	private:
		std::string m_name;
	};

	#define RENDER_PROFILE_SCOPED_EVENT(name) ScopedRenderEvent _scoped_event(name)
}
