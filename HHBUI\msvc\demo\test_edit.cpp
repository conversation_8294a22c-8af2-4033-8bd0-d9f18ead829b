﻿#include "hhbui.h"

using namespace HHBUI;
LRESULT CALLBACK OnButton_edit_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (nCode == WMM_CLICK)
	{
		auto objedit = (UIEdit*)window->FindUIView(L"1005");
		if (nID == 2001)
		{
			output(objedit->GetTextLength(), objedit->GetText());
		}
		else if (nID == 2002)
		{
			objedit->SetSelCharFormat(CFM_SIZE, UIColor(), L"", 60);
			objedit->AppendText(L"我是添加的文本", UIColor(20, 126, 255, 155));
		}
		else if (nID == 2003)
		{
			auto objFindedit = (UIEdit*)window->FindUIView(L"1006");
			objedit->SetFocus();
			objedit->SetFindText(objFindedit->GetText());
			output(objFindedit->GetText());


			//objFindedit = (UIEdit*)window->FindUIView(L"1004");
			//objFindedit->SetText(L"567");
		}
	}
	return S_OK;
}
void testedit(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 700, 400, L"hello edit", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);
	window->Layout_Init(elt_absolute);


	auto edit1 = new UIEdit(window, 20, 70, 120, 40, L"普通的编辑框", 0, 0, 1001, SingleLine| Center| Middle);
	edit1->SetColor(color_background, UIColor(47, 64, 86, 255));
	edit1->SetColor(color_border, UIColor(0, 108, 190, 255));
	edit1->SetColor(color_text_normal, UIColor(255, 255, 255, 255));
	edit1->SetColorCaret(UIColor(255, 255, 255, 255));
	//edit1->SetFontFromFamily(L"微软雅黑", 16);

	auto edit2 = new UIEdit(window, 20, 120, 120, 40, NULL, eos_edit_usepassword, 0, 1002);
	edit2->SetColor(color_text_normal, UIColor(0, 108, 190));
	edit2->SetColor(color_border, UIColor(0, 108, 190, 255));
	edit2->SetCueBanner(L"请输入密码", UIColor(0, 108, 190, 255));

	auto edit3 = new UIEdit(window, 20, 170, 120, 40, L"我只读", eos_edit_readonly | eos_edit_hideselection, 0, 1003);
	edit3->SetColor(color_border, UIColor(194, 195, 201, 255));
	edit3->SetCueBanner(L"禁止输入");

	auto edit4 = new UIEdit(window, 20, 220, 120, 40, NULL, eos_edit_numericinput, 0, 1004);
	edit4->SetColor(color_border, UIColor(95, 184, 120, 255));
	edit4->SetCueBanner(L"输入数字");
	
	//测试富文本

	auto edit5 = new UIEdit(window, 170, 70, 450, 190, L"富文本编辑框ABCDEGF", eos_scroll_v | eos_scroll_controlbutton | eos_edit_richtext | eos_edit_newline | eos_edit_parseurl, 0, 1005, Left | WordBreak);
	edit5->SetColor(color_border, UIColor(255, 87, 34, 255));
	edit5->SetScrollRadius(TRUE);
	edit5->SetScrollColor(UIColor(1, 170, 237, 255), UIColor(30, 159, 255, 255), UIColor(30, 159, 255, 255));
	window->Layout_Absolute_Setedge(edit5, elcp_absolute_left, elcp_absolute_type_px, 170);
	window->Layout_Absolute_Setedge(edit5, elcp_absolute_top, elcp_absolute_type_px, 70);
	window->Layout_Absolute_Setedge(edit5, elcp_absolute_right, elcp_absolute_type_px, 20);
	window->Layout_Absolute_Setedge(edit5, elcp_absolute_bottom, elcp_absolute_type_px, 80);
	//LPVOID fontdata;
	//size_t fontSize = 0;
	//UIreadFile(LR"(D:\Code\Font\汉仪游园体W.ttf)", fontdata, fontSize);
	//output(UIFont::LoadFromMem(fontdata, fontSize, L"汉仪游园体"));
	edit5->SetFontFromFamily(L"Ink Free", 16, HHBUI::FONT_STYLE_BOLD);

	auto btn1 = new UIButton(window, 170, 270, 100, 36, L"取内容", 0, 0, 2001);
	btn1->SetStyle(fill, primary);
	btn1->SetRadius(0);
	btn1->SetEvent(WMM_CLICK, OnButton_edit_Event);
	window->Layout_Absolute_Setedge(btn1, elcp_absolute_left, elcp_absolute_type_px, 170);
	window->Layout_Absolute_Setedge(btn1, elcp_absolute_bottom, elcp_absolute_type_px, 30);

	auto btn2 = new UIButton(window, 280, 270, 100, 36, L"添加内容", 0, 0, 2002);
	btn2->SetStyle(fill, primary);
	btn2->SetRadius(0);
	btn2->SetEvent(WMM_CLICK, OnButton_edit_Event);
	window->Layout_Absolute_Setedge(btn2, elcp_absolute_left, elcp_absolute_type_px, 280);
	window->Layout_Absolute_Setedge(btn2, elcp_absolute_bottom, elcp_absolute_type_px, 30);

	auto edit6 = new UIEdit(window, 390, 270, 120, 36, NULL, 0, 0, 1006);
	edit6->SetColor(color_border, UIColor(0, 108, 190, 255));
	edit6->SetCueBanner(L"寻找内容");
	window->Layout_Absolute_Setedge(edit6, elcp_absolute_left, elcp_absolute_type_px, 390);
	window->Layout_Absolute_Setedge(edit6, elcp_absolute_bottom, elcp_absolute_type_px, 30);

	auto btn3 = new UIButton(window, 520, 270, 100, 36, L"寻找下一个", 0, 0, 2003);
	btn3->SetStyle(fill, primary);
	btn3->SetRadius(0);
	btn3->SetEvent(WMM_CLICK, OnButton_edit_Event);
	window->Layout_Absolute_Setedge(btn3, elcp_absolute_left, elcp_absolute_type_px, 520);
	window->Layout_Absolute_Setedge(btn3, elcp_absolute_bottom, elcp_absolute_type_px, 30);

	window->Show();
	//window->MessageLoop();
}