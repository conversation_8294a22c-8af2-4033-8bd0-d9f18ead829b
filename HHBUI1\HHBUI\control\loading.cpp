﻿#include "pch.h"
#include "loading.h"
#include "loading_internal.h"
#include <element/font_pool.h>

#define DECLPROP(name, type, def) \
    struct name { \
      type value = def; \
      operator type() { return value; } \
      name(const type& v) : value(v) {} \
    };
using float_ptr = float*;
DECLPROP(FloatPtr, float_ptr, nullptr)

HHBUI::UILoading::UILoading(UIBase *hParent, INT x, INT y, INT width, INT height, INT dwStyle, INT dwStyleEx, INT nID)
{
	InitSubControl(hParent, x, y, width, height, L"form-loading", L"", dwStyle, dwStyleEx, nID, -1);
	p_data.hBrush = new UIBrush();
    p_data.pathGeometry = new UIPath();
    p_data.crbg = UIColor(0, 0, 0);
    SetCircleTessellationMaxError(0.30f);
}

void HHBUI::UILoading::SetStyle(info_loading_style style)
{
    p_data.style = style;
    // Default speed
    p_data.speed = 4.f;
    static const std::unordered_map<info_loading_style, float> speedMap = {
        {e_st_rainbow, 8.f},
        {e_st_angle, 6.f},
        {e_st_dots, 1.f},
        {e_st_ang, 6.f},
        {e_st_vdots, 2.7f},
        {e_st_bounce_ball, 2.7f},
        {e_st_eclipse, 6.f},
        {e_st_bouncedots, 6.f},
        {e_st_fadedots, 8.f},
        {e_st_scaledots, 7.f},
        {e_st_movingdots, 30.f},
        {e_st_barchartsine, 4.8f},
        {e_st_incfulldots, 5.6f},
        {e_st_barsrotatefade, 7.6f},
        {e_st_fadebars, 4.8f},
        {e_st_pulsar, 1.f},
        {e_st_barchartrainbow, 6.8f},
        {e_st_angtwin, 6.f},
        {e_st_twinpulsar, 0.5f},
        {e_st_blocks, 5.f},
        {e_st_twinball, 6.f},
        {e_st_gooeyballs, 2.f},
        {e_st_moonline, 5.f},
        {e_st_fluid, 3.8f},
        {e_st_fadepulsar, 1.5f},
        {e_st_rotatedatom, 2.1f},
        {e_st_rainbowballs, 1.5f},
        {e_st_hbodots, 1.1f},
        {e_st_wavedots, 6.f},
        {e_st_sinsquares, 1.f},
        {e_st_zipdots, 6.f},
        {e_st_trianglesshift, 1.8f},
        {e_st_circularlines, 1.5f},
        {e_st_patternrings, 2.1f},
        {e_st_pointsshift, 2.f},
        {e_st_circularpoints, 10.f},
        {e_st_curvedcircle, 1.f},
        {e_st_rainbowshot, 1.5f},
        {e_st_rotatingheart, 8.f},
        {e_st_dotstopoints, 1.8f},
        {e_st_herbertballs, 2.f},
        {e_st_herbertballs3d, 1.4f},
        {e_st_squareloading, 3.f},
        {e_st_rainbowmix, 8.f},
        {e_st_angmix, 8.f},
        {e_st_twinhbodots, 1.1f},
        {e_st_moondots, 1.1f},
        {e_st_rotatesegmentspulsar, 1.1f}
    };
    auto it = speedMap.find(style);
    if (it != speedMap.end()) {
        p_data.speed = it->second;
    }

    if (style == e_st_textfading)
    {
        delete p_data.hBrush;
        FLOAT psWidth = m_data.Frame.right - m_data.Frame.left;
        FLOAT psHeight = m_data.Frame.bottom - m_data.Frame.top;
        UIColor dwColor = p_data.crHue;
        p_data.hBrush = new UIBrush(false, psHeight / 2.f, psHeight / 2.f, psHeight, psHeight, p_data.crHue, dwColor.SetA(0.2f));
        //p_data.hBrush->SetRadialCenterOffset(psHeight, psHeight / 2 + 5);
        p_data.mode = 1;
        p_data.minth = 20;
    }
    else
    {
        delete p_data.hBrush;
        p_data.hBrush = new UIBrush();
    }
}


INT HHBUI::UILoading::GetStyle()
{
    return p_data.style;
}

void HHBUI::UILoading::SetRainbow(float radius, float thickness, float velocity, float ang_min, float ang_max, int arcs, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.ang_min = ang_min;
    if (ang_max == 0.f)
        ang_max = PI_2;
    p_data.ang_max = ang_max;
    p_data.arcs = arcs;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetDots(float radius, float thickness, float velocity, size_t dots, float minth, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
    p_data.minth = minth;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetAng(float radius, float thickness, float velocity, float angle, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.angle = angle;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetVDots(float radius, float thickness, float velocity, size_t dots, size_t mdots, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
    p_data.mdots = mdots;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetBounceBall(float radius, float thickness, float velocity, int dots, bool shadow)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
    p_data.shadow = shadow;
}

void HHBUI::UILoading::SetAngEclipse(float radius, float thickness, float velocity, float angle)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.angle = angle;
}

void HHBUI::UILoading::SetIngYang(float radius, float thickness, bool reverse, float yang_detlta_r, float velocity, float angle)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.angle = angle;
    p_data.dots = yang_detlta_r;
    p_data.shadow = reverse;
}

void HHBUI::UILoading::SetBounceDots(float radius, float thickness, float velocity, size_t dots, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.mode = mode;
    p_data.dots = dots;
}

void HHBUI::UILoading::SetFadeDots(float radius, float thickness, float velocity, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetScaleDots(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}
void HHBUI::UILoading::SetIncScaleDots(float radius, float thickness, float velocity, size_t dots, float angle, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
    p_data.mode = mode;
    p_data.angle = angle;
}
void HHBUI::UILoading::SetMovingDots(float radius, float thickness, float velocity, size_t dots)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
}
void HHBUI::UILoading::SetRotateDots(float radius, float thickness, float velocity, int dots, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = (dots == -1 ? ImMax<int>(int(ImSin(UIEngine::GetUptime() * 0.5f) * 8), 3) : dots);
    p_data.mode = mode;
}
void HHBUI::UILoading::SetClock(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}
void HHBUI::UILoading::SetBarChartSine(float radius, float thickness, float velocity, int bars, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = bars;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetTwinAng180(float radius1, float radius2, float thickness, float velocity, float angle, int mode)
{
    p_data.radius = radius1;
    p_data.minth = radius2;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.angle = angle == 0.f ? PI_DIV_4 : angle;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetIncDots(float radius, float thickness, float velocity, size_t dots)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
}
void HHBUI::UILoading::SetIncFullDots(float radius, float thickness, float velocity, size_t dots)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
}
void HHBUI::UILoading::SetBarsRotateFade(float rmin, float rmax, float thickness, float velocity, size_t bars)
{
    p_data.ang_min = rmin;
    p_data.radius = rmax;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = bars;
}
void HHBUI::UILoading::SetFadeBars(float w, float velocity, size_t bars, bool scale)
{
    p_data.radius = w;
    p_data.velocity = velocity;
    p_data.dots = bars;
    p_data.shadow = scale;
}
void HHBUI::UILoading::SetPulsar(float radius, float thickness, float velocity, bool sequence, float angle, int mode, bool isdots)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.shadow = sequence;
    p_data.angle = angle;
    p_data.mode = mode;
    p_data.isdots = isdots;
}
void HHBUI::UILoading::SetBarChartRainbow(float radius, float thickness, float velocity, int bars, int mode, bool fColor)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = bars;
    p_data.mode = mode;
    p_data.isdots = fColor;
}

void HHBUI::UILoading::SetAngTwin(float radius1, float radius2, float thickness, float velocity, float angle, size_t arcs, int mode)
{
    p_data.radius = radius1;
    p_data.minth = radius2;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.angle = angle;
    p_data.dots = arcs;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetTwinPulsar(float radius, float thickness, float velocity, int rings, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = rings;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetBlocks(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}
void HHBUI::UILoading::SetTwinBall(float radius1, float radius2, float thickness, float b_thickness, float velocity, size_t balls)
{
    p_data.radius = radius1;
    p_data.minth = radius2;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.angle = b_thickness;
    p_data.dots = balls;
}
void HHBUI::UILoading::SetGooeyBalls(float radius, float velocity, int mode)
{
    p_data.radius = radius;
    p_data.mode = mode;
    p_data.velocity = velocity;
}
void HHBUI::UILoading::SetMoonLine(float radius, float thickness, float velocity, float angle)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.angle = angle == 0.f ? IM_PI : angle;
    p_data.velocity = velocity;
}
void HHBUI::UILoading::SetFluid(float radius, float velocity, int bars)
{
    p_data.radius = radius;
    p_data.velocity = velocity;
    p_data.dots = bars;
}
void HHBUI::UILoading::SetArcFade(float radius, float thickness, float velocity, size_t arcs, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.dots = arcs;
    p_data.velocity = velocity;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetFadePulsar(float radius, float velocity, int rings, int mode)
{
    p_data.radius = radius;
    p_data.dots = rings;
    p_data.velocity = velocity;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetFilledArcFade(float radius, float velocity, size_t arcs, int mode)
{
    p_data.radius = radius;
    p_data.dots = arcs;
    p_data.velocity = velocity;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetRotatedAtom(float radius, float thickness, float velocity, int elipses, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.dots = elipses;
    p_data.velocity = velocity;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetRainbowBalls(float radius, float thickness, float velocity, int balls, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.dots = balls;
    p_data.velocity = velocity;
    p_data.mode = mode;
}
void HHBUI::UILoading::ScaleBlocks(float radius, float thickness, float velocity, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetHboDots(float radius, float thickness, float minfade, float ryk, float velocity, size_t dots)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.dots = dots;
    p_data.velocity = velocity;
    p_data.ang_min = minfade;
    p_data.ang_max = ryk;
}
void HHBUI::UILoading::SetSwingDots(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}
void HHBUI::UILoading::SetWaveDots(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}
void HHBUI::UILoading::SetSinSquares(float radius, float thickness, float velocity, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.mode = mode;
}
void HHBUI::UILoading::SetZipDots(float radius, float thickness, float velocity, size_t dots, float offset_k, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.ang_min = offset_k;
    p_data.dots = dots;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetTrianglesShift(float radius, float thickness, float velocity, size_t bars)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = bars;
}

void HHBUI::UILoading::SetCircularLines(float radius, float velocity, int lines, int mode)
{
    p_data.radius = radius;
    p_data.velocity = velocity;
    p_data.dots = lines;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetPatternRings(float radius, float thickness, float velocity, int elipses, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = elipses;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetPointsShift(float radius, float thickness, float velocity, size_t bars)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = bars;
}

void HHBUI::UILoading::SetCircularPoints(float radius, float thickness, float velocity, int lines)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = lines;
}

void HHBUI::UILoading::SetCurvedCircle(float radius, float thickness, float velocity, size_t circles)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = circles;
}

void HHBUI::UILoading::SetPatternEclipse(float radius, float thickness, float velocity, int elipses, float delta_a, float delta_y)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = elipses;
    p_data.ang_min = delta_a;
    p_data.ang_max = delta_y;
}

void HHBUI::UILoading::SetRainbowShot(float radius, float thickness, float velocity, int balls)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = balls;
}

void HHBUI::UILoading::SetSpiral(float radius, float thickness, float velocity, size_t arcs, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = arcs;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetDnaDots(float radius, float thickness, float velocity, float delta, bool mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.ang_min = delta;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetSolarBalls(float radius, float thickness, float velocity, size_t balls, bool Scale)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = balls;
    p_data.isdots = Scale;
}

void HHBUI::UILoading::SetRotatingHeart(float radius, float thickness, float velocity, float ang_min)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.ang_min = ang_min;
}

void HHBUI::UILoading::SetFluidPoints(float radius, float thickness, float velocity, size_t dots, float delta)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
    p_data.ang_min = delta;
}

void HHBUI::UILoading::SetDotsToPoints(float radius, float thickness, float offset_k, float velocity, size_t dots)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
    p_data.ang_min = offset_k;
}

void HHBUI::UILoading::SetThreeDots(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}

void HHBUI::UILoading::SetCaleidospcope(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}

void HHBUI::UILoading::SetFiveDots(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}

void HHBUI::UILoading::SetHerbertBalls(float radius, float thickness, float velocity, int balls)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = balls;
}

void HHBUI::UILoading::SetHerbertBalls3D(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}

void HHBUI::UILoading::SetSquareLoading(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}

void HHBUI::UILoading::SetTwinAng360(float radius1, float radius2, float thickness, float velocity, float speed2, int mode)
{
    p_data.radius = radius1;
    p_data.minth = radius2;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.angle = speed2;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetPulsarBall(float radius, float thickness, float velocity, bool shadow, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.shadow = shadow;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetRainbowMix(float radius, float thickness, float velocity, float ang_min, float ang_max, int arcs, int mode)
{
    p_data.radius = radius;
    p_data.ang_min = ang_min;
    p_data.ang_max = ang_max;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = arcs;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetAngMix(float radius, float thickness, float velocity, float angle, int arcs, int mode)
{
    p_data.radius = radius;
    p_data.dots = arcs;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.angle = angle;
    p_data.mode = mode;
}

void HHBUI::UILoading::SetTwinHboDots(float radius, float thickness, float minfade, float ryk, float velocity, size_t dots, float delta)
{
    p_data.radius = radius;
    p_data.ang_min = minfade;
    p_data.ang_max = ryk;
    p_data.minth = delta;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
}

void HHBUI::UILoading::SetMoonDots(float radius, float thickness, float velocity)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
}

void HHBUI::UILoading::SetRotateSegmentsPulsar(float radius, float thickness, float velocity, size_t arcs, size_t layers)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = arcs;
    p_data.mdots = layers;
}

void HHBUI::UILoading::SetPointsArcBounce(float radius, float thickness, float velocity, size_t points, int circles, float rspeed)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = points;
    p_data.mdots = circles;
    p_data.minth = rspeed;
}

void HHBUI::UILoading::SetSomeScaleDots(float radius, float thickness, float velocity, size_t dots, int mode)
{
    p_data.radius = radius;
    p_data.thickness = UIEngine::ScaleValue(thickness);
    p_data.velocity = velocity;
    p_data.dots = dots;
    p_data.mode = mode;
}


void HHBUI::UILoading::SetCrHue(UIColor normal)
{
    p_data.crHue = normal;
}
void HHBUI::UILoading::GetCrHue(UIColor& normal)
{
    normal = p_data.crHue;
}
void HHBUI::UILoading::SetCrBg(UIColor bg)
{
    p_data.crbg = bg;
}

void HHBUI::UILoading::SetRadius(float radius)
{
    p_data.radius = radius;
}
void HHBUI::UILoading::GetCrBg(UIColor& bg)
{
    bg = p_data.crbg;
}
float HHBUI::UILoading::GetRadius()
{
    return  p_data.radius;
}

void HHBUI::UILoading::SetThickness(float thickness)
{
    p_data.thickness = UIEngine::ScaleValue(thickness);
}

void HHBUI::UILoading::SetSpeed(float speed)
{
    p_data.speed = speed;
}

void HHBUI::UILoading::SetVelocity(float velocity)
{
    p_data.velocity = velocity;
}

void HHBUI::UILoading::SetAng_min(float ang_min)
{
    p_data.ang_min = ang_min;
}

void HHBUI::UILoading::SetAng_max(float ang_max)
{
    p_data.ang_max = ang_max;
}

void HHBUI::UILoading::SetArcs(int arcs)
{
    p_data.arcs = arcs;
}

void HHBUI::UILoading::SetMode(int mode)
{
    p_data.mode = mode;
}

int HHBUI::UILoading::GetMode()
{
    return  p_data.mode;
}

void HHBUI::UILoading::SetNextdot(float* nextdot)
{
    p_data.nextdot = nextdot;
}

void HHBUI::UILoading::SetDots(size_t dots)
{
    p_data.dots = dots;
}

size_t HHBUI::UILoading::GetDots()
{
    return p_data.dots;
}

void HHBUI::UILoading::SetMdots(size_t mdots)
{
    p_data.mdots = mdots;
}

void HHBUI::UILoading::SetMinth(float minth)
{
    p_data.minth = minth;
}

float HHBUI::UILoading::GetMinth()
{
    return p_data.minth;
}

void HHBUI::UILoading::SetAngle(float angle)
{
    p_data.angle = angle;
}

LRESULT HHBUI::UILoading::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_DESTROY)
    {
        delete p_data.hBrush;
        delete p_data.pathGeometry;
    }
	return S_OK;
}

void HHBUI::UILoading::OnPaintProc(ps_context ps)
{
    switch (p_data.style)
    {
    case e_st_rainbow:
        Rainbow(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.ang_min, p_data.ang_max, p_data.arcs, p_data.mode);
        break;
    case e_st_angle:
        break;
    case e_st_dots:
    {
        static float nextdot = 0, nextdot2;
        nextdot -= 0.07f;
        if (p_data.nextdot == nullptr)
            p_data.nextdot = FloatPtr{ &nextdot };
        Dots(ps, p_data.nextdot, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.minth, p_data.mode);
        break;
    }
    case e_st_ang:
        Ang(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.angle, p_data.mode);
        break;
    case e_st_vdots:
        VDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mdots, p_data.mode);
        break;
    case e_st_bounce_ball:
        BounceBall(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.shadow);
        break;
    case e_st_eclipse:
        AngEclipse(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.angle == 0.f ? IM_PI : p_data.angle);
        break;
    case e_st_ingyang:
        IngYang(ps, p_data.radius, p_data.thickness, p_data.shadow, p_data.dots, p_data.speed * p_data.velocity, p_data.angle == 0.f ? IM_PI * 0.7f : p_data.angle);
        break;
    case e_st_bouncedots:
        BounceDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_fadedots:
        FadeDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.mode);
        break;
    case e_st_scaledots:
        ScaleDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_movingdots:
        MovingDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_rotatedots:
        RotateDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_clock:
        Clock(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_barchartsine:
        BarChartSine(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_twinang180:
        TwinAng180(ps, p_data.radius, p_data.minth, p_data.thickness, p_data.speed * p_data.velocity, p_data.angle, p_data.mode);
        break;
    case e_st_incdots:
        IncDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_incscaledots:
        IncScaleDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.angle, p_data.mode);
        break;
    case e_st_incfulldots:
        IncFullDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_barsrotatefade:
        BarsRotateFade(ps, p_data.ang_min, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_fadebars:
        FadeBars(ps, p_data.radius, p_data.speed * p_data.velocity, p_data.dots, p_data.shadow);
        break;
    case e_st_pulsar:
        Pulsar(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.shadow, p_data.angle, p_data.mode, p_data.isdots);
        break;
    case e_st_barchartrainbow:
        BarChartRainbow(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode, p_data.isdots);
        break;
    case e_st_angtwin:
        AngTwin(ps, p_data.radius, p_data.minth, p_data.thickness, p_data.speed * p_data.velocity, p_data.angle, p_data.dots, p_data.mode);
        break;
    case e_st_twinpulsar:
        TwinPulsar(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_blocks:
        Blocks(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_twinball:
        TwinBall(ps, p_data.radius, p_data.minth, p_data.thickness, p_data.angle, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_gooeyballs:
        GooeyBalls(ps, p_data.radius, p_data.speed * p_data.velocity, p_data.mode);
        break;
    case e_st_moonline:
        MoonLine(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.angle);
        break;
    case e_st_fluid:
        Fluid(ps, p_data.radius, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_arcfade:
        ArcFade(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_fadepulsar:
        FadePulsar(ps, p_data.radius, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_filledarcfade:
        FilledArcFade(ps, p_data.radius, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_rotatedatom:
        RotatedAtom(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_rainbowballs:
        RainbowBalls(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_scaleblocks:
        ScaleBlocks(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.mode);
        break;
    case e_st_hbodots:
        HboDots(ps, p_data.radius, p_data.thickness, p_data.ang_min, p_data.ang_max, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_swingdots:
        SwingDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_wavedots:
        WaveDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_sinsquares:
        SinSquares(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.mode);
        break;
    case e_st_zipdots:
        ZipDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.ang_min, p_data.mode);
        break;
    case e_st_trianglesshift:
        TrianglesShift(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_circularlines:
        CircularLines(ps, p_data.radius, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_patternrings:
        PatternRings(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_pointsshift:
        PointsShift(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_circularpoints:
        CircularPoints(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_curvedcircle:
        CurvedCircle(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_patterneclipse:
        PatternEclipse(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.ang_min, p_data.ang_max);
        break;
    case e_st_rainbowshot:
        RainbowShot(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_spiral:
        Spiral(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    case e_st_dnadots:
        DnaDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.ang_min, p_data.mode);
        break;
    case e_st_solarballs:
        SolarBalls(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.isdots);
        break;
    case e_st_rotatingheart:
        RotatingHeart(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.ang_min);
        break;
    case e_st_fluidpoints:
        FluidPoints(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.ang_min);
        break;
    case e_st_dotstopoints:
        DotsToPoints(ps, p_data.radius, p_data.thickness, p_data.ang_min, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_threedots:
        ThreeDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_caleidospcope:
        Caleidospcope(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_fivedots:
        FiveDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_herbertballs:
        HerbertBalls(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots);
        break;
    case e_st_herbertballs3d:
        HerbertBalls3D(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_squareloading:
        SquareLoading(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_textfading:
        TextFading(ps);
        break;
    case e_st_twinang360:
        TwinAng360(ps, p_data.radius, p_data.minth, p_data.thickness, p_data.speed * p_data.velocity, p_data.angle, p_data.mode);
        break;
    case e_st_pulsarball:
        PulsarBall(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.shadow, p_data.mode);
        break;
    case e_st_rainbowmix:
        RainbowMix(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.ang_min, p_data.ang_max, p_data.dots, p_data.mode);
        break;
    case e_st_angmix:
        AngMix(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.angle, p_data.dots, p_data.mode);
        break;
    case e_st_twinhbodots:
        TwinHboDots(ps, p_data.radius, p_data.thickness, p_data.ang_min, p_data.ang_max, p_data.speed * p_data.velocity, p_data.dots, p_data.minth);
        break;
    case e_st_moondots:
        MoonDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity);
        break;
    case e_st_rotatesegmentspulsar:
        RotateSegmentsPulsar(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mdots);
        break;
    case e_st_pointsarcbounce:
        PointsArcBounce(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mdots, p_data.minth);
        break;
    case e_st_somescaledots:
        SomeScaleDots(ps, p_data.radius, p_data.thickness, p_data.speed * p_data.velocity, p_data.dots, p_data.mode);
        break;
    }

}

void HHBUI::UILoading::Rainbow(ps_context ps, float radius, float thickness, float speed, float ang_min, float ang_max, int arcs, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = std::abs(std::sin(UIEngine::GetUptime()) * (num_segments - 5));
    for (int i = 0; i < arcs; ++i)
    {
        const float rb = (radius / arcs) * (i + 1);
        const float a_min = ImMax(ang_min, PI_2 * ((float)start) / (float)num_segments + (IM_PI / arcs) * i);
        const float a_max = ImMin(ang_max, PI_2 * ((float)num_segments + 3 * (i + 1)) / (float)num_segments);
        // Circle drawing function
        auto circle = [&](const std::function<D2D1_POINT_2F(int)>& point_func, UIColor dbc, float dth) {
            p_data.pathGeometry->Reset();
            p_data.pathGeometry->BeginPath();
            if (p_data.crHue.A() == 0)
            {
                static int hue = 0;
                p_data.hBrush->SetColor(UIColor::HSV(++hue * 0.005f, 0.8f, 0.8f));
                if (hue > 1000)
                    hue = 0;
            }
            else
                p_data.hBrush->SetColor(p_data.crHue);

            for (int i = 0; i < num_segments; i++) {
                D2D1_POINT_2F p = point_func(i);
                if (i == 0) {
                    p_data.pathGeometry->StartFigure(centre.x + p.x, centre.y + p.y);
                }
                else {
                    p_data.pathGeometry->LineTo(centre.x + p.x, centre.y + p.y);
                }
            }
            p_data.pathGeometry->EndPath();
            ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, dth);

        };

        circle([&](int i) {
            const float a = a_min + (static_cast<float>(i) / static_cast<float>(num_segments)) * (a_max - a_min);
            const float rspeed = a + UIEngine::GetUptime() * speed;
            float pulse_factor = 1.f;
            if (mode == 1) {
                pulse_factor = 0.8f + 0.2f * std::sin(UIEngine::GetUptime() * 1.5f); // Pulsate between 0.8 and 1.0
            }
            const float pulsating_radius = rb * pulse_factor;
            return D2D1::Point2F(std::cos(rspeed) * pulsating_radius, std::sin(rspeed) * pulsating_radius);
            }, p_data.crHue, thickness);
    }
}
void HHBUI::UILoading::Dots(ps_context ps, float* nextdot, float radius, float thickness, float speed, size_t dots, float minth, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float start = (float)UIEngine::GetUptime() * speed;
    const float bg_angle_offset = PI_2 / dots;
    dots = ImMin(dots, (size_t)32);
    const size_t mdots = dots / 2;

    float def_nextdot = 0;
    float& ref_nextdot = nextdot ? *nextdot : def_nextdot;
    if (ref_nextdot < 0.f)
        ref_nextdot = (float)dots;

    auto radiusmode = [radius, mode, dots](float a, size_t i) {
        switch (mode) {
        case 2: return damped_trifolium(a) * radius;
        case 3: return (radius / dots) * i;
        } return radius;
        };
    auto thcorrect = [&thickness, &ref_nextdot, &mdots, &minth](size_t i) {
        const float nth = minth < 0.f ? thickness / 2.f : minth;
        return ImMax(nth, ImSin(((i - ref_nextdot) / mdots) * IM_PI) * thickness);
        };

    switch (mode) {
    case 1: start = damped_infinity(start * 1.1f, 1.f).second; break;
    case 4: start = ease_outquad(ImSin(ImFmod(start, IM_PI))); break;
    case 5: start = ease_inoutexpo(ImSin(ImFmod(start, IM_PI))); break;
    }
    if (p_data.crHue.A() == 0)
    {
        static int hue = 0;
        p_data.hBrush->SetColor(UIColor::HSV(++hue * 0.005f, 0.8f, 0.8f));
        if (hue == 128)
            hue = 0;
    }
    else
        p_data.hBrush->SetColor(p_data.crHue);
    for (size_t i = 0; i <= dots; i++)
    {
        float a = start + (i * bg_angle_offset);
        a = ImFmod(a, PI_2);
        float th = minth < 0 ? thickness / 2.f : minth;

        if (ref_nextdot + mdots < dots) {
            if (i > ref_nextdot && i < ref_nextdot + mdots)
                th = thcorrect(i);
        }
        else {
            if ((i > ref_nextdot && i < dots) || (i < ((int)(ref_nextdot + mdots)) % dots))
                th = thcorrect(i);
        }
        ps.hCanvas->DrawPoint(p_data.hBrush, centre.x + ImCos(-a) * radiusmode(a, i), centre.y + ImSin(-a) * radiusmode(a, i), th, TRUE);
    }



}
void HHBUI::UILoading::Ang(ps_context ps, float radius, float thickness, float speed, float angle, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    UIColor Color = p_data.crHue;
    if (Color.A() == 0)
    {
        static int hue = 0;
        Color = UIColor::HSV(++hue * 0.0011f, 0.8f, 0.8f);
        if (hue > 1000)
            hue = 0;
    }

    auto circle = [&](const std::function<D2D1_POINT_2F(int)>& point_func, UIColor dbc, float dth) {
        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        p_data.hBrush->SetColor(dbc);
        for (int i = 0; i < num_segments; i++) {
            D2D1_POINT_2F p = point_func(i);
            if (i == 0) {
                p_data.pathGeometry->StartFigure(centre.x + p.x, centre.y + p.y);
            }
            else {
                p_data.pathGeometry->LineTo(centre.x + p.x, centre.y + p.y);
            }
        }
        p_data.pathGeometry->EndPath();
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, dth);

    };
    const float start = UIEngine::GetUptime() * speed;
    float b = 0.f;
    switch (mode) 
    {
      case 1: b = damped_gravity(ImSin(start * 1.1f)) * angle; break;
      case 2: radius = (0.8f + ImCos(start) * 0.2f) * radius; break;
      case 3: b = damped_infinity(start * 1.1f, 1.f).second; break;
    }

        auto radiusmode = [radius, mode] (float a) { switch (mode) { case 4: return damped_trifolium(a) * radius; } return radius; };
        circle([&] (int i) {                                                         // Draw the background of the spinner using the `circle` function, with the specified background color and thickness.
            const float a = start + (i * (PI_2 / (num_segments - 1)));               // Calculate the angle for each segment based on the start angle and the number of segments.
            return D2D1::Point2F(ImCos(a) * radiusmode(a), ImSin(a) * radiusmode(a));
        }, p_data.crbg, thickness);

        circle([&] (int i) {                                                        // Draw the spinner itself using the `circle` function, with the specified color and thickness.
            const float a = start - b + (i * angle / num_segments);
            return D2D1::Point2F(ImCos(a) * radiusmode(a), ImSin(a) * radiusmode(a));
        }, Color, thickness);


}
void HHBUI::UILoading::VDots(ps_context ps, float radius, float thickness, float speed, size_t dots, size_t mdots, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float start = UIEngine::GetUptime() * speed;
    switch (mode) {
    case 1: start += ease_inoutquad(ImSin(ImFmod(start, IM_PI))); break;
    }

    const float bg_angle_offset = PI_2_DIV(dots);
    dots = ImMin(dots, (size_t)32);

    p_data.hBrush->SetColor(p_data.crbg);
    for (size_t i = 0; i <= dots; i++)
    {
        float a = ImFmod(start + (i * bg_angle_offset), PI_2);
        ImVec2 p = ImVec2(centre.x + ImCos(-a) * radius, centre.y + ImSin(-a) * radius);
        ps.hCanvas->DrawPoint(p_data.hBrush, p.x, p.y, thickness / 2, TRUE);
    }

    if (p_data.crHue.empty())
    {
        static int hue = 0;
        p_data.hBrush->SetColor(UIColor::HSV(++hue * 0.0011f, 0.8f, 0.8f));
        if (hue == 128)
            hue = 0;
    }
    else
        p_data.hBrush->SetColor(p_data.crHue);
    const float d_ang = (mdots / (float)dots) * PI_2;
    const float angle_offset = (d_ang) / dots;
    for (size_t i = 0; i < dots; i++)
    {
        const float a = start + (i * angle_offset);
        ImVec2 p = ImVec2(centre.x + ImCos(a) * radius, centre.y + ImSin(a) * radius);
        ps.hCanvas->DrawPoint(p_data.hBrush, p.x, p.y, thickness, TRUE);
    }
}
void HHBUI::UILoading::BounceBall(ps_context ps, float radius, float thickness, float speed, size_t dots, bool shadow)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float vtime = 0.f;
    float hmax = 1.f;

    vtime += 0.05f;
    hmax += 0.01f;


    constexpr float rkoeff[9] = { 0.1f, 0.15f, 0.17f, 0.25f, 0.31f, 0.19f, 0.08f, 0.24f, 0.9f };
    const int iterations = shadow ? 4 : 1;
    for (int j = 0; j < iterations; j++) {
        UIColor c = p_data.crHue;
        c.SetA(1.f - 0.15f * j);
        p_data.hBrush->SetColor(c);
        for (size_t i = 0; i < dots; i++) {
            float start = ImFmod(UIEngine::GetUptime() * speed * (1 + rkoeff[i % 9]) - (IM_PI / 12.f) * j, IM_PI);
            float sign = ((i % 2 == 0) ? 1.f : -1.f);
            float offset = (i == 0) ? 0.f : (floorf((i + 1) / 2.f + 0.1f) * sign * 2.f * thickness);
            float maxht = damped_gravity(ImSin(ImFmod(hmax, IM_PI))) * radius;

            ImVec2 p = ImVec2(centre.x + offset, centre.y + radius - ImSin(start) * 2.f * maxht);
            ps.hCanvas->DrawEllipse(p_data.hBrush, p.x, p.y, p.x + thickness, p.y + thickness, thickness);
        }
    }
}
void HHBUI::UILoading::AngEclipse(ps_context ps, float radius, float thickness, float speed, float angle)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = UIEngine::GetUptime() * speed;
    const float angle_offset = angle / num_segments;
    const float th = thickness / num_segments;
    p_data.hBrush->SetColor(p_data.crHue);

    for (INT i = 0; i < num_segments; i++)
    {
        const float a = start + (i * angle_offset);
        const float a1 = start + ((i + 1) * angle_offset);

        ImVec2 x = ImVec2(centre.x + ImCos(a) * radius, centre.y + ImSin(a) * radius);
        ImVec2 y = ImVec2(centre.x + ImCos(a1) * radius, centre.y + ImSin(a1) * radius);

        ps.hCanvas->DrawLine(p_data.hBrush, x.x, x.y, y.x, y.y, th * i);
    }

}
void HHBUI::UILoading::IngYang(ps_context ps, float radius, float thickness, bool reverse, float yang_detlta_r, float speed, float angle)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);

    const float startI = UIEngine::GetUptime() * speed;
    const float startY = UIEngine::GetUptime() * (speed + (yang_detlta_r > 0.f ? ImClamp(yang_detlta_r * 0.5f, 0.5f, 2.f) : 0.f));
    const float angle_offset = angle / num_segments;
    const float th = thickness / num_segments;
    p_data.hBrush->SetColor(p_data.crHue);

    for (int i = 0; i < num_segments; i++)
    {
        const float a = startI + (i * angle_offset);
        const float a1 = startI + ((i + 1) * angle_offset);

        ImVec2 x = ImVec2(centre.x + ImCos(a) * radius, centre.y + ImSin(a) * radius);
        ImVec2 y = ImVec2(centre.x + ImCos(a1) * radius, centre.y + ImSin(a1) * radius);

        ps.hCanvas->DrawLine(p_data.hBrush, x.x, x.y, y.x, y.y, th * i);
    }
    const float ai_end = startI + (num_segments * angle_offset);
    ImVec2 circle_i_center{ centre.x + ImCos(ai_end) * radius, centre.y + ImSin(ai_end) * radius };
    ps.hCanvas->DrawPoint(p_data.hBrush, circle_i_center.x, circle_i_center.y, thickness * 1.2f, TRUE);

    p_data.hBrush->SetColor(p_data.crbg);
    const float rv = reverse ? -1.f : 1.f;
    const float yang_radius = (radius - yang_detlta_r);
    for (int i = 0; i < num_segments; i++)
    {
        const float a = startY + IM_PI + (i * angle_offset);
        const float a1 = startY + IM_PI + ((i + 1) * angle_offset);

        ImVec2 x = ImVec2(centre.x + ImCos(a * rv) * yang_radius, centre.y + ImSin(a * rv) * yang_radius);
        ImVec2 y = ImVec2(centre.x + ImCos(a1 * rv) * yang_radius, centre.y + ImSin(a1 * rv) * yang_radius);
        ps.hCanvas->DrawLine(p_data.hBrush, x.x, x.y, y.x, y.y, th * i);
    }
    const float ay_end = startY + IM_PI + (num_segments * angle_offset);
    ImVec2 circle_y_center{ centre.x + ImCos(ay_end * rv) * yang_radius, centre.y + ImSin(ay_end * rv) * yang_radius };
    ps.hCanvas->DrawPoint(p_data.hBrush, circle_y_center.x, circle_y_center.y, thickness * 1.2f, TRUE);
}
void HHBUI::UILoading::BounceDots(ps_context ps, float radius, float thickness, float speed, size_t dots, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };

    const float nextItemKoeff = 2.5f;
    const float heightKoeff = 2.f;
    const float heightSpeed = 0.8f;
    const float hsize = dots * (thickness * nextItemKoeff) / 2.f - (thickness * nextItemKoeff) * 0.5f;

    float start = UIEngine::GetUptime() * speed;
    const float offset = PI_DIV(dots);
    p_data.hBrush->SetColor(p_data.crHue);

    for (size_t i = 0; i < dots; i++) {
        float a = start + (IM_PI - i * offset);
        switch (mode) {
        case 1: a = damped_spring(1, 10.f, 1.0f, ImSin(ImFmod(start + i * PI_DIV(dots * 2), PI_2))); break;
        case 2: a = damped_infinity((float)(start + i * PI_DIV(dots * 2)), radius).second; break;
        }
        float y = centre.y + ImSin(a * heightSpeed) * thickness * heightKoeff;

        ImVec2 p = ImVec2(centre.x - hsize + i * (thickness * nextItemKoeff), ImMin(y, centre.y));
        ps.hCanvas->DrawEllipse(p_data.hBrush, p.x, p.y, p.x + thickness, p.y + thickness, thickness);
    }
}
void HHBUI::UILoading::FadeDots(ps_context ps, float radius, float thickness, float speed, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    const float start = UIEngine::GetUptime() * speed;
    const float nextItemKoeff = 2.5f;
    const float dots = (size.x / (thickness * nextItemKoeff));
    const float heightSpeed = 0.8f;
   
    for (INT i = 0; i < (INT)dots; i++)
    {
        float a = mode ? damped_spring(1, 10.f, 1.0f, ImSin(ImFmod(start + (IM_PI - i * (IM_PI / dots)), PI_2)))
            : ImSin(start + (IM_PI - i * (IM_PI / dots)) * heightSpeed);

        ImVec2 p = ImVec2(centre.x - (size.x / 2.f) + i * thickness * nextItemKoeff, centre.y);

        UIColor c = p_data.crHue;
        c.SetA(ImMax(0.1f, a));
        p_data.hBrush->SetColor(c);
        ps.hCanvas->DrawEllipse(p_data.hBrush, p.x, p.y, p.x + thickness, p.y + thickness, thickness);
    }
}
void HHBUI::UILoading::ScaleDots(ps_context ps, float radius, float thickness, float speed)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    const float nextItemKoeff = 2.5f;
    const float heightSpeed = 0.8f;
    const float dots = (size.x / (thickness * nextItemKoeff));
    const float start = UIEngine::GetUptime() * speed;

    for (INT i = 0; i < (INT)dots; i++)
    {
        const float a = start + (IM_PI - i * PI_DIV(dots));
        const float th = thickness * ImSin(a * heightSpeed);

        ImVec2 p = ImVec2(centre.x - (size.x / 2.f) + i * thickness * nextItemKoeff, centre.y);

        UIColor c = p_data.crHue;
        c.SetA(0.1f);
        p_data.hBrush->SetColor(c);
        ps.hCanvas->DrawEllipse(p_data.hBrush, p.x, p.y, p.x + thickness, p.y + thickness, thickness);
        p_data.hBrush->SetColor(p_data.crHue);
        ps.hCanvas->DrawEllipse(p_data.hBrush, p.x, p.y, p.x + thickness, p.y + thickness, th);

    }
}
void HHBUI::UILoading::IncScaleDots(ps_context ps, float radius, float thickness, float speed, size_t dots, float angle, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float start = UIEngine::GetUptime() * speed;
    float astart = ImFmod(start, IM_PI / dots);
    start -= astart;
    const float bg_angle_offset = IM_PI / dots;
    dots = ImMin(dots, (size_t)32);
    p_data.hBrush->SetColor(p_data.crHue);
    for (size_t i = 0; i <= dots; i++)
    {
        float a = start + (i * bg_angle_offset);
        a += ease((ease_mode)mode, a, angle);
        float th = thickness * ImMax(0.1f, i / (float)dots);

        ImVec2 p = ImVec2(centre.x + ImCos(a) * radius, centre.y + ImSin(a) * radius);
        ps.hCanvas->DrawEllipse(p_data.hBrush, p.x, p.y, p.x + thickness, p.y + thickness, th);
    }
}
void HHBUI::UILoading::MovingDots(ps_context ps, float radius, float thickness, float speed, size_t dots)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    ImVec2 pos = ImVec2((ps.uWidth / 2.f) - radius, (ps.uHeight / 2.f) - radius);
    const float nextItemKoeff = 2.5f;
    const float heightKoeff = 2.f;
    const float heightSpeed = 0.8f;
    const float start = ImFmod(UIEngine::GetUptime() * speed, size.x);
    p_data.hBrush->SetColor(p_data.crHue);
    float offset = 0;
    for (size_t i = 0; i < dots; i++)
    {
        float th = thickness;
        offset = ImFmod(start + i * (size.x / dots), size.x);

        if (offset < thickness) { th = offset; }
        if (offset > size.x - thickness) { th = size.x - offset; }

        ImVec2 p = ImVec2(pos.x + offset - thickness, centre.y);
        ps.hCanvas->DrawPoint(p_data.hBrush, p.x, p.y, th, TRUE);
    }
}
void HHBUI::UILoading::RotateDots(ps_context ps, float radius, float thickness, float speed, size_t dots, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float velocity = p_data.velocity;
    float vtime = p_data.vtime;

    float dtime = ImFmod((float)vtime, IM_PI);
    float start = (vtime += velocity);
    if (dtime > 0.f && dtime < PI_DIV_2) { velocity += 0.001f * speed; }
    else if (dtime > IM_PI * 0.9f && dtime < IM_PI) { velocity -= 0.01f * speed; }
    if (velocity > 0.1f) velocity = 0.1f;
    if (velocity < 0.01f) velocity = 0.01f;

    p_data.velocity = velocity;
    p_data.vtime = vtime;

    p_data.hBrush->SetColor(p_data.crHue);
    ps.hCanvas->DrawPoint(p_data.hBrush, centre.x, centre.y, thickness, TRUE);

    for (size_t i = 0; i < dots; i++)
    {
        float a = 0.f;
        switch (mode) {
        case 1: a = start + i * PI_2_DIV(dots) + damped_spring(1, 10.f, 1.0f, ImSin(start + i * PI_2_DIV(dots)), PI_2_DIV(dots), 0); break;
        case 2: a = start + i * PI_2_DIV(dots) + damped_infinity(start + i * PI_DIV(dots * 2), 1.f).second; break;
        default:
            a = start + (i * PI_2_DIV(dots));
        }
        ImVec2 p = ImVec2(centre.x + ImCos(a) * radius, centre.y + ImSin(a) * radius);
        ps.hCanvas->DrawPoint(p_data.hBrush, p.x, p.y, thickness, TRUE);
    }

    if (mode == 3) {
        float sm_thickness = thickness * 0.5f;
        const float bstart = UIEngine::GetUptime() * speed;
        for (size_t i = 0; i < dots; i++)
        {
            const float b = bstart + (IM_PI - i * PI_DIV(dots) * 2.f);
            ImVec2 p = ImVec2(centre.x + ImCos(b) * radius, centre.y + ImSin(b) * radius);
            ps.hCanvas->DrawPoint(p_data.hBrush, p.x, p.y, sm_thickness, TRUE);
        }
    }
}
void HHBUI::UILoading::Clock(ps_context ps, float radius, float thickness, float speed)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = UIEngine::GetUptime() * speed;
    const float bg_angle_offset = PI_2 / (num_segments - 1);

    auto circle = [&](const std::function<ImVec2(int)>& point_func, UIColor dbc, float dth) {
        p_data.hBrush->SetColor(dbc);
        for (int i = 0; i < num_segments; i++) {
            ImVec2 p = point_func(i);
            ps.hCanvas->DrawPoint(p_data.hBrush, centre.x + p.x, centre.y + p.y, dth, TRUE);
        }
    };


    circle([&](int i) { return ImVec2(ImCos(i * bg_angle_offset) * radius, ImSin(i * bg_angle_offset) * radius); }, p_data.crbg, thickness);
    p_data.hBrush->SetColor(p_data.crHue);

    ps.hCanvas->DrawLine(p_data.hBrush, centre.x, centre.y, centre.x + cosf(start) * radius, centre.y + sinf(start) * radius, thickness * 2);
    ps.hCanvas->DrawLine(p_data.hBrush, centre.x, centre.y, centre.x + cosf(start * 0.5f) * radius / 2.f, centre.y + sinf(start * 0.5f) * radius / 2.f, thickness * 2);

}
void HHBUI::UILoading::BarChartSine(ps_context ps, float radius, float thickness, float speed, size_t bars, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 pos = ImVec2((ps.uWidth / 2.f) - radius - bars, (ps.uHeight / 2.f) - radius - bars);
    UIColor Color = p_data.crHue;
    if (Color.A() == 0)
    {
        static int hue = 0;
        p_data.hBrush->SetColor(UIColor::HSV(++hue * 0.005f, 0.8f, 0.8f));
        if (hue > 1000)
            hue = 0;
    }
    else
        p_data.hBrush->SetColor(Color);
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    const float nextItemKoeff = 1.5f;
    const float yOffsetKoeftt = 0.8f;
    const float heightSpeed = 0.8f;

    const float start = UIEngine::GetUptime() * speed;
    const float offset = IM_PI / bars;
    for (size_t i = 0; i < bars; i++)
    {
        const float angle = ImMax(PI_DIV_2, (1.f - i / (float)bars) * IM_PI);
        float a = start + (IM_PI - i * offset);
        float clearing = ImMax(0.1f, ImSin(a * heightSpeed));
        Color.SetA(clearing);
        p_data.hBrush->SetColor(Color);

        float h = mode ? ImSin(a) * size.y / 2.f
            : (0.6f + 0.4f * clearing) * size.y;
        float halfs = mode ? 0 : size.y / 2.f;

        D2D1_RECT_F barRect = D2D1::RectF(
            pos.x + i * (thickness * nextItemKoeff) - thickness / 2,
            centre.y + halfs,
            pos.x + i * (thickness * nextItemKoeff) + thickness / 2,
            centre.y + halfs - h * yOffsetKoeftt
        );
        ps.hCanvas->FillRect(p_data.hBrush, barRect.left, barRect.top, barRect.right, barRect.bottom);
      
    }
}
void HHBUI::UILoading::TwinAng180(ps_context ps, float radius1, float radius2, float thickness, float speed, float angle, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float radius = ImMax(radius1, radius2);
    int num_segments = CalcCircleAutoSegmentCount(radius);
    num_segments *= 8;
    const float start = ImFmod(UIEngine::GetUptime() * speed, PI_2);
      const float aoffset = ImFmod(UIEngine::GetUptime(), PI_2);
      const float bofsset = (aoffset > IM_PI) ? IM_PI : aoffset;
      const float angle_offset = PI_2_DIV(num_segments);
      float ared_min = 0, ared = 0;
      if (aoffset > IM_PI)
        ared_min = aoffset - IM_PI;
      p_data.hBrush->SetColor(p_data.crHue);
  
      auto radiusmode = [mode] (float a, float r, float f) { switch (mode) { case 2: return damped_trifolium(a, 0.f, f) * r; } return r; };
      for (INT i = 0; i <= num_segments / 2 + 1; i++)
      {
        ared = start + (i * angle_offset);
        switch (mode) {
        case 1: ared += damped_infinity(start, angle).second; break;
        }

        if (i * angle_offset < ared_min)
          continue;

        if (i * angle_offset > bofsset)
          break;

        ImVec2 p = ImVec2(centre.x + ImCos(ared) * radiusmode(ared, radius2, -1.1f), centre.y + ImSin(ared) * radiusmode(ared, radius2, -1.1f));
        ps.hCanvas->DrawPoint(p_data.hBrush, p.x, p.y, thickness, TRUE);
      }

      p_data.hBrush->SetColor(p_data.crbg);
      for (INT i = 0; i <= 2 * num_segments + 1; i++)
      {
        const float a = ared + ared_min + (i * angle_offset);
        if (i * angle_offset < ared_min)
          continue;

        if (i * angle_offset > bofsset)
          break;

        ImVec2 p = ImVec2(centre.x + ImCos(a) * radiusmode(a, radius1, 1.f), centre.y + ImSin(a) * radiusmode(a, radius1, 1.f));
        ps.hCanvas->DrawPoint(p_data.hBrush, p.x, p.y, thickness, TRUE);
     
      }
}
void HHBUI::UILoading::IncDots(ps_context ps, float radius, float thickness, float speed, size_t dots)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float start = UIEngine::GetUptime() * speed;
    float astart = ImFmod(start, PI_DIV(dots));
    start -= astart;
    dots = ImMin<size_t>(dots, 32);
    UIColor Color = p_data.crHue;

    for (size_t i = 0; i <= dots; i++)
    {
        float a = start + (i * PI_DIV(dots - 1));
        float clearing = ImMax(0.1f, i / (float)dots);
        Color.SetA(clearing);
        p_data.hBrush->SetColor(Color);

        ImVec2 p = ImVec2(centre.x + ImCos(a) * radius, centre.y + ImSin(a) * radius);
        ps.hCanvas->DrawEllipse(p_data.hBrush, p.x, p.y, p.x + thickness, p.y + thickness, thickness);
    }
}
void HHBUI::UILoading::IncFullDots(ps_context ps, float radius, float thickness, float speed, size_t dots)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    dots = ImMin<size_t>(dots, 32);
    float start = UIEngine::GetUptime() * speed;
    float astart = ImFmod(start, IM_PI / dots);
    start -= astart;
    const float bg_angle_offset = IM_PI / dots;
    UIColor Color = p_data.crHue;
    Color.SetA(0.1f);
    p_data.hBrush->SetColor(Color);
    for (size_t i = 0; i < dots * 2; i++)
    {
        float a = start + (i * bg_angle_offset);
        ImVec2 p = ImVec2(centre.x + ImCos(a) * radius, centre.y + ImSin(a) * radius);
        ps.hCanvas->DrawEllipse(p_data.hBrush, p.x, p.y, p.x + thickness, p.y + thickness, thickness);
    }

    for (size_t i = 0; i < dots; i++)
    {
        float a = start + (i * bg_angle_offset);
        ImVec2 p = ImVec2(centre.x + ImCos(a) * radius, centre.y + ImSin(a) * radius);
        float clearing = ImMax(0.1f, i / (float)dots);
        Color.SetA(clearing);

        p_data.hBrush->SetColor(Color);
        ps.hCanvas->DrawEllipse(p_data.hBrush, p.x, p.y, p.x + thickness, p.y + thickness, thickness);
    }
}
void HHBUI::UILoading::BarsRotateFade(ps_context ps, float rmin, float rmax, float thickness, float speed, size_t bars)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float radius = rmax;

    float start = UIEngine::GetUptime() * speed;
    float astart = ImFmod(start, IM_PI / bars);
    start -= astart;
    const float bg_angle_offset = IM_PI / bars;
    bars = ImMin<size_t>(bars, 32);
    UIColor Color = p_data.crHue;

    for (size_t i = 0; i <= bars; i++)
    {
        float a = start + (i * bg_angle_offset);
        float clearing = ImMax(0.1f, i / (float)bars);
        Color.SetA(clearing);
        p_data.hBrush->SetColor(Color);
        // Calculate the start and end points for the line
        D2D1_POINT_2F startPoint = D2D1::Point2F(centre.x + std::cos(a) * rmin, centre.y + std::sin(a) * rmin);
        D2D1_POINT_2F endPoint = D2D1::Point2F(centre.x + std::cos(a) * rmax, centre.y + std::sin(a) * rmax);
        ps.hCanvas->DrawLine(p_data.hBrush, startPoint.x, startPoint.y, endPoint.x, endPoint.y, thickness);
    }


}
void HHBUI::UILoading::FadeBars(ps_context ps, float w, float speed, size_t bars, bool scale)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float radius = (w * 0.5f) * bars;
    ImVec2 pos = ImVec2((ps.uWidth / 2.f) - radius, ps.uHeight / 2.f);
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    const float nextItemKoeff = 1.5f;
    const float yOffsetKoeftt = 0.8f;
    const float heightSpeed = 0.8f;
    const float start = UIEngine::GetUptime() * speed;

    const float offset = IM_PI / bars;
    UIColor Color = p_data.crHue;
    for (size_t i = 0; i < bars; i++)
    {
        float a = start + (IM_PI - i * offset);
        float clearing = ImMax(0.1f, ImSin(a * heightSpeed));
        Color.SetA(clearing);
        p_data.hBrush->SetColor(Color);
        float h = (scale ? (0.6f + 0.4f * clearing) : 1.f) * size.y / 2;

        // Calculate position of the bar
        D2D1_POINT_2F topLeft = D2D1::Point2F(
            pos.x + i * (w * 1.5f) - w / 2,
            pos.y - h * 0.8f
        );

        D2D1_POINT_2F bottomRight = D2D1::Point2F(
            pos.x + i * (w * 1.5f) + w / 2,
            pos.y + h * 0.8f
        );
        ps.hCanvas->FillRect(p_data.hBrush, topLeft.x, topLeft.y, bottomRight.x, bottomRight.y);
    }
}
void HHBUI::UILoading::Pulsar(ps_context ps, float radius, float thickness, float speed, bool sequence, float angle, int mode, bool isdots)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);

    float radius_b = p_data.ang_min == 0.f ? 0.8f : p_data.ang_min;

    const float start = UIEngine::GetUptime() * speed;
    const float bg_angle_offset = PI_2 / (num_segments - 1);

  
    auto circle = [&](const std::function<ImVec2(int)>& point_func, UIColor dbc, float dth) {
        if (!isdots)
        {
            p_data.pathGeometry->Reset();
            p_data.pathGeometry->BeginPath();
        }
        p_data.hBrush->SetColor(dbc);
        for (int i = 0; i < num_segments; i++) {
            ImVec2 p = point_func(i);
            if (isdots)
                ps.hCanvas->DrawPoint(p_data.hBrush, centre.x + p.x, centre.y + p.y, dth, TRUE);
            else
            {
                if (i == 0) {
                    p_data.pathGeometry->StartFigure(centre.x + p.x, centre.y + p.y);
                }
                else {
                    p_data.pathGeometry->LineTo(centre.x + p.x, centre.y + p.y);
                }
            }
        }
        if (!isdots)
        {
            p_data.pathGeometry->EndPath();
            ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, dth);
        }
    };


    float start_r = ImFmod(start, PI_DIV_2);
    switch (mode) {
    case 1: start_r = damped_infinity(start_r, angle).second; break;
    }
    float radius_k = ImSin(start_r);
    float radius1 = radius_k * radius;

    circle([&](int i) {
        return ImVec2(ImCos(i * bg_angle_offset) * radius1, ImSin(i * bg_angle_offset) * radius1);
        }, p_data.crHue, thickness);

    if (sequence) { radius_b -= (0.005f * speed); radius_b = ImMax(radius_k, ImMax(0.8f, radius_b)); }
    else { radius_b = (1.f - radius_k); }

    p_data.ang_min = radius_b;
    float radius_tb = sequence ? ImMax(radius_k, radius_b) * radius : (radius_b * radius);
    circle([&](int i) {
        return ImVec2(ImCos(i * bg_angle_offset) * radius_tb, ImSin(i * bg_angle_offset) * radius_tb);
        }, p_data.crbg, thickness);
}
void HHBUI::UILoading::BarChartRainbow(ps_context ps, float radius, float thickness, float speed, size_t bars, int mode, bool fColor)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    const float nextItemKoeff = 1.5f;
    const float yOffsetKoeftt = 0.8f;
    ImVec2 pos = ImVec2(centre.x - (thickness * bars) / nextItemKoeff - thickness, centre.y - radius);

    const float start = UIEngine::GetUptime() * speed;
    const float hspeed = 0.1f + ImSin(UIEngine::GetUptime() * 0.1f) * 0.05f;
    constexpr float rkoeff[6] = { 4.f, 13.f, 3.4f, 8.7f, 25.f, 11.f };
    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    UIColor Color = p_data.crHue;
    if (Color.empty())
    {
        static int hue = 0;
        Color = UIColor::HSV(++hue * 0.0011f, 0.8f, 0.8f);
        if (hue > 1000)
            hue = 0;
    }
    for (size_t i = 0; i < bars; i++)
    {
        if (fColor)
        {
            Color.ToHSV(out_h, out_s, out_v);
            Color = UIColor::HSV(out_h + i * 0.1f, out_s, out_v);
        }
        p_data.hBrush->SetColor(Color);

        float h = (0.6f + 0.4f * ImSin(start + (1.f + rkoeff[i % 6] * i * hspeed))) * size.y;
        h += ease((ease_mode)mode, start, radius / 2);

        D2D1_RECT_F rect = D2D1::RectF(
            pos.x + i * (thickness * nextItemKoeff) - thickness / 2,
            centre.y + size.y / 2.f - h * yOffsetKoeftt,
            pos.x + i * (thickness * nextItemKoeff) + thickness / 2,
            centre.y + size.y / 2.f
        );

        ps.hCanvas->FillRect(p_data.hBrush, rect.left, rect.top, rect.right, rect.bottom);
    }
}
void HHBUI::UILoading::AngTwin(ps_context ps, float radius1, float radius2, float thickness, float speed, float angle, size_t arcs, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float radius = ImMax(radius1, radius2);
    int num_segments = CalcCircleAutoSegmentCount(radius);

    float start = UIEngine::GetUptime() * speed;
    const float bg_angle_offset = PI_2 / num_segments;
    p_data.hBrush->SetColor(p_data.crbg);
    p_data.pathGeometry->Reset();
    p_data.pathGeometry->BeginPath();
    for (int i = 0; i <= num_segments; i++) {
        const float a = start + (i * bg_angle_offset);

        ImVec2 p = ImVec2(centre.x + ImCos(a) * radius1, centre.y + ImSin(a) * radius1);
        if (i == 0) {
            p_data.pathGeometry->StartFigure(p.x, p.y);
        }
        else {
            p_data.pathGeometry->LineTo(p.x, p.y);
        }
    }
    p_data.pathGeometry->EndPath();
    ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);

    p_data.hBrush->SetColor(p_data.crHue);

    const float angle_offset = angle / num_segments;
    for (size_t arc_num = 0; arc_num < arcs; ++arc_num) {
        float arc_start = 2 * IM_PI / arcs;
        float b = ease((ease_mode)mode, start + arc_num * PI_DIV(2) / arcs, IM_PI, 1.0f, 0.0f);
        //switch (mode) {
        //case 1: b = start + damped_spring(1, 10.f, 1.0f, , )), 1, 0); break;
        //case 2: b = start + damped_infinity(PI_2 - angle, start).second; break;
        //default: b = start;
        //}

        for (int i = 0; i < num_segments; i++) {
            const float a = start + b + arc_start * arc_num + (i * angle_offset);

            ImVec2 p = ImVec2(centre.x + ImCos(a) * radius2, centre.y + ImSin(a) * radius2);
            ps.hCanvas->DrawPoint(p_data.hBrush, p.x, p.y, thickness, TRUE);
        }
    }


}
void HHBUI::UILoading::TwinPulsar(ps_context ps, float radius, float thickness, float speed, size_t rings, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float bg_angle_offset = PI_2 / (num_segments - 1);
    const float koeff = PI_DIV(2 * rings);
    float start = UIEngine::GetUptime() * speed;
    auto circle = [&](const std::function<ImVec2(int)>& point_func, UIColor dbc, float dth) {
        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        p_data.hBrush->SetColor(dbc);
        for (int i = 0; i < num_segments; i++) {
            ImVec2 p = point_func(i);
            if (i == 0) {
                p_data.pathGeometry->StartFigure(centre.x + p.x, centre.y + p.y);
            }
            else {
                p_data.pathGeometry->LineTo(centre.x + p.x, centre.y + p.y);
            }
        }
        p_data.pathGeometry->EndPath();
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, dth);
    };

    for (size_t num_ring = 0; num_ring < rings; ++num_ring) {
        float radius_k = ImSin(ImFmod(start + (num_ring * koeff), PI_DIV_2));
        float radius1 = radius_k * radius;
        radius1 += ease((ease_mode)mode, start, radius);
        UIColor cr = p_data.crHue;
        cr.SetA(radius_k > 0.5f ? 2.f - (radius_k * 2.f) : p_data.crHue.GetA());

        circle([&](int i) {
            const float a = start + (i * bg_angle_offset);
            return ImVec2(ImCos(a) * radius1, ImSin(a) * radius1);
        }, cr, thickness);
    }
}
void HHBUI::UILoading::Blocks(ps_context ps, float radius, float thickness, float speed)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float offset_block = radius * 2.f / 3.f;
    ImVec2 lt{ centre.x - thickness - offset_block + thickness, centre.y - offset_block };
    int start = (int)ImFmod(UIEngine::GetUptime() * speed, 8.f);

    const ImVec2ih poses[] = { {0, 0}, {1, 0}, {2, 0}, {2, 1}, {2, 2}, {1, 2}, {0, 2}, {0, 1} };

    int ti = 0;
    for (const auto& rpos : poses)
    {
        const UIColor& c = (ti == start) ? p_data.crHue : p_data.crbg;
        p_data.hBrush->SetColor(c);
        D2D1_RECT_F rect = D2D1::RectF(
            lt.x + rpos.x * offset_block,                        // 左上角 X
            lt.y + rpos.y * offset_block,                        // 左上角 Y
            lt.x + rpos.x * offset_block + thickness,            // 右下角 X
            lt.y + rpos.y * offset_block + thickness             // 右下角 Y
        );
        ps.hCanvas->FillRect(p_data.hBrush, rect.left, rect.top, rect.right, rect.bottom);
        ti++;
    }
}
void HHBUI::UILoading::TwinBall(ps_context ps, float radius1, float radius2, float thickness, float b_thickness, float speed, size_t balls)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float radius = ImMax(radius1, radius2);
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = UIEngine::GetUptime() * speed;
    const float bg_angle_offset = PI_2 / num_segments;

    p_data.pathGeometry->Reset();
    p_data.pathGeometry->BeginPath();
    p_data.hBrush->SetColor(p_data.crbg);
    for (int i = 0; i <= num_segments; i++)
    {
        const float a = start + (i * bg_angle_offset);
        ImVec2 p = ImVec2(centre.x + ImCos(a) * radius1, centre.y + ImSin(a) * radius1);
        if (i == 0) {
            p_data.pathGeometry->StartFigure(p.x, p.y);
        }
        else {
            p_data.pathGeometry->LineTo(p.x, p.y);
        }
    }
    p_data.pathGeometry->EndPath();
    ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);
    p_data.hBrush->SetColor(p_data.crHue);
    for (size_t b_num = 0; b_num < balls; ++b_num)
    {
        float b_start = PI_2 / balls;
        const float a = b_start * b_num + start;
        ImVec2 p = ImVec2(centre.x + ImCos(a) * radius2, centre.y + ImSin(a) * radius2);
        ps.hCanvas->FillEllipse(p_data.hBrush, p.x - thickness, p.y - thickness, p.x - thickness + b_thickness, p.y - thickness + b_thickness);
    }
}
void HHBUI::UILoading::GooeyBalls(ps_context ps, float radius, float speed, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);

    float start = ImFmod(UIEngine::GetUptime() * speed, IM_PI);
    start = mode ? damped_spring(1, 10.f, 1.0f, ImSin(start), 1, 0) : start;
    const float radius1 = (0.4f + 0.3f * ImSin(start)) * radius;
    const float radius2 = radius - radius1;
    p_data.hBrush->SetColor(p_data.crHue);

    ImVec2 p = ImVec2(centre.x - radius + radius1, centre.y);
    ps.hCanvas->FillEllipse(p_data.hBrush, p.x, p.y, p.x + radius1, p.y + radius1);
    p = ImVec2(centre.x - radius + radius1 * 1.2f + radius2, centre.y);
    ps.hCanvas->FillEllipse(p_data.hBrush, p.x, p.y, p.x + radius2, p.y + radius2);
}
void HHBUI::UILoading::MoonLine(ps_context ps, float radius, float thickness, float speed, float angle)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = UIEngine::GetUptime() * speed;
    const float angle_offset = (angle * 0.5f) / num_segments;
    const float th = thickness / num_segments;
    p_data.hBrush->SetColor(p_data.crbg);
    D2D1_ELLIPSE backgroundCircle = D2D1::Ellipse(D2D1::Point2F(centre.x, centre.y), radius, radius);
    ps.hCanvas->FillEllipse(p_data.hBrush, backgroundCircle);

    auto draw_gradient = [&](const std::function<float(int)>& b, const std::function<float(int)>& e, const std::function<float(int)>& th) {

        p_data.hBrush->SetColor(p_data.crHue);
        for (int i = 0; i < num_segments; i++)
        {
            float startX = centre.x + ImCos(start + b(i)) * radius;
            float startY = centre.y + ImSin(start + b(i)) * radius;
            float endX = centre.x + ImCos(start + e(i)) * radius;
            float endY = centre.y + ImSin(start + e(i)) * radius;
            ps.hCanvas->DrawLine(p_data.hBrush, startX, startY, endX, endY, th(i));
        }

    };

    draw_gradient([&](int i) { return (num_segments + i) * angle_offset; },
        [&](int i) { return (num_segments + i + 1) * angle_offset; },
        [&](int i) { return thickness - th * i; });

    draw_gradient([&](int i) { return (i)*angle_offset; },
        [&](int i) { return (i + 1) * angle_offset; },
        [&](int i) { return th * i; });

    draw_gradient([&](int i) { return (num_segments + i) * angle_offset; },
        [&](int i) { return (num_segments + i + 1) * angle_offset; },
        [&](int i) { return thickness - th * i; });

    const float b_angle_offset = (PI_2 - angle) / num_segments;
    draw_gradient([&](int i) { return num_segments * angle_offset * 2.f + (i * b_angle_offset); },
        [&](int i) { return num_segments * angle_offset * 2.f + ((i + 1) * b_angle_offset); },
        [](int) { return 1.f; });
}
void HHBUI::UILoading::Fluid(ps_context ps, float radius, float speed, size_t bars)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 size = ImVec2(radius, radius * 2);
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float hspeed = 0.1f + ImSin(UIEngine::GetUptime() * 0.1f) * 0.05f;
    constexpr float rkoeff[6][3] = { {0.15f, 0.1f, 0.1f}, {0.033f, 0.15f, 0.8f}, {0.017f, 0.25f, 0.6f}, {0.037f, 0.1f, 0.4f}, {0.25f, 0.1f, 0.3f}, {0.11f, 0.1f, 0.2f} };
    const float j_k = radius * 2.f / num_segments;
    ImVec2 pos = ImVec2(centre.x - radius, 0.f);
    UIColor Color = p_data.crHue;
    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    Color.ToHSV(out_h, out_s, out_v);
    for (size_t i = 0; i < bars; i++)
    {
        Color = UIColor::HSV(out_h - i * 0.1f, out_s, out_v, rkoeff[i % 6][1]);
        p_data.hBrush->SetColor(Color);
        for (int j = 0; j < num_segments; ++j) {
            float h = (0.6f + 0.3f * ImSin(UIEngine::GetUptime() * (speed * rkoeff[i % 6][2] * 2.f) + (2.f * rkoeff[i % 6][0] * j * j_k))) * (radius * 2.f * rkoeff[i % 6][2]);
        
            D2D1_RECT_F rect = D2D1::RectF(
                pos.x + j * j_k,
                centre.y + size.y / 2.f - h,
                pos.x + (j + 1) * j_k,
                centre.y + size.y / 2.f);

            ps.hCanvas->FillRect(p_data.hBrush, rect.left, rect.top, rect.right, rect.bottom);
        }
    }
}
void HHBUI::UILoading::ArcFade(ps_context ps, float radius, float thickness, float speed, size_t arcs, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = ImFmod(UIEngine::GetUptime() * speed, IM_PI * 4.f);
    const float arc_angle = PI_2 / (float)arcs;
    const float angle_offset = arc_angle / num_segments;
    UIColor Color = p_data.crHue;

    for (size_t arc_num = 0; arc_num < arcs; ++arc_num)
    {
        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        for (int i = 0; i <= num_segments + 1; i++)
        {
            const float a = arc_angle * arc_num + (i * angle_offset) - PI_DIV_2 - PI_DIV_4;
            ImVec2 p = ImVec2(centre.x + ImCos(a) * radius, centre.y + ImSin(a) * radius);
            if (i == 0) {
                p_data.pathGeometry->StartFigure(p.x, p.y);
            }
            else {
                p_data.pathGeometry->LineTo(p.x, p.y);
            }
        }
        const float a = arc_angle * arc_num;
        if (start < PI_2) {
            Color.SetA(0.f);
            if (start > a && start < (a + arc_angle)) { Color.SetA(1.f - (start - a) / (float)arc_angle); }
            else if (start < a) { Color.SetA(1.f); }
            float woff = ease((ease_mode)mode, start - a, 4.f);
            Color.SetA(ImMax(0.05f, 1.f - Color.GetA() - woff));
        }
        else {
            const float startk = start - PI_2;
            Color.SetA(0.f);
            if (startk > a && startk < (a + arc_angle)) { Color.SetA(1.f - (startk - a) / (float)arc_angle); }
            else if (startk < a) { Color.SetA(1.f); }
            float woff = ease((ease_mode)mode, start - a, 4.f);
            Color.SetA(ImMax(0.05f, Color.GetA() + woff));
        }

        p_data.hBrush->SetColor(Color);
        p_data.pathGeometry->EndPath();
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);
    }
 
}
void HHBUI::UILoading::FadePulsar(ps_context ps, float radius, float speed, size_t rings, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float bg_angle_offset = PI_2_DIV(num_segments);
    float start = UIEngine::GetUptime() * speed;
    UIColor Color = p_data.crHue;
    if (mode == 2)//DoubleFadePulsar
    {
        const float bg_angle_offset = PI_2_DIV(num_segments);
        float radius_b = p_data.vtime == 0.f ? 0.8f : p_data.vtime;
        float start_r = ImFmod(start, PI_DIV_2);
        float radius_k = ImSin(start_r);
        D2D1_ELLIPSE ellipse = D2D1::Ellipse(D2D1::Point2F(centre.x, centre.y), radius_k * radius, radius_k * radius);
        ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);

        radius_b = (1.f - radius_k);
        p_data.vtime = radius_b;
        ellipse = D2D1::Ellipse(D2D1::Point2F(centre.x, centre.y), radius_b * radius, radius_b * radius);
        Color.SetA(ImMin(0.3f, radius_b));
        p_data.hBrush->SetColor(Color);
        ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
    }
    else
    {
        const float koeff = PI_DIV(2 * rings);
        for (size_t num_ring = 0; num_ring < rings; ++num_ring) {
            float radius_k = ImSin(ImFmod(start + (num_ring * koeff), PI_DIV_2));

            float a = (radius_k > 0.5f) ? (2.f - (radius_k * 2.f)) : Color.GetA();
            a -= ease((ease_mode)mode, start, a);

            Color.SetA(a);
            p_data.hBrush->SetColor(Color);
            D2D1_ELLIPSE ellipse = D2D1::Ellipse(D2D1::Point2F(centre.x, centre.y), radius_k * radius, radius_k * radius);
            ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
        }
    }
}
void HHBUI::UILoading::FilledArcFade(ps_context ps, float radius, float speed, size_t arcs, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = ImFmod(UIEngine::GetUptime() * speed, IM_PI * 4.f);
    const float arc_angle = PI_2 / (float)arcs;
    const float angle_offset = arc_angle / num_segments;
    UIColor Color = p_data.crHue;
    float blend = 0.f;
    for (size_t arc_num = 0; arc_num < arcs; ++arc_num)
    {
        const float b = arc_angle * arc_num - PI_DIV_2 - PI_DIV_4;
        const float e = arc_angle * arc_num + arc_angle - PI_DIV_2 - PI_DIV_4;
        const float a = arc_angle * arc_num;
    
        float vradius = radius;
        if (start < PI_2) {
            blend = 0.f;
            if (start > a && start < (a + arc_angle)) { blend = 1.f - (start - a) / (float)arc_angle; }
            else if (start < a) { blend = 1.f; }
            blend = ImMax(0.f, 1.f - blend);
            if (mode == 1)
                vradius = radius * blend;
        }
        else
        {
            const float startk = start - PI_2;
            blend = 0.f;
            if (startk > a && startk < (a + arc_angle)) { blend = 1.f - (startk - a) / (float)arc_angle; }
            else if (startk < a) { blend = 1.f; }
            if (mode == 1)
                vradius = radius * blend;
        }
        Color.SetA(blend);
        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        p_data.pathGeometry->StartFigure(centre.x, centre.y);
        p_data.pathGeometry->LineTo(centre.x, centre.y);

        for (int i = 0; i <= num_segments + 1; i++)
        {
            const float ar = arc_angle * arc_num + (i * angle_offset) - PI_DIV_2 - PI_DIV_4;
            p_data.pathGeometry->LineTo(centre.x + ImCos(ar) * vradius, centre.y + ImSin(ar) * vradius);
        }
        p_data.hBrush->SetColor(Color);
        p_data.pathGeometry->EndPath();
        ps.hCanvas->FillPath(p_data.hBrush, p_data.pathGeometry);
    }
}
void HHBUI::UILoading::RotatedAtom(ps_context ps, float radius, float thickness, float speed, size_t elipses, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = UIEngine::GetUptime() * speed;
    p_data.hBrush->SetColor(p_data.crHue);
    if (mode == 2)
    {
        elipses = std::min<size_t>(elipses, 3);
        auto draw_rotated_ellipse = [&](float alpha, float start) {
            alpha = ImFmod(alpha, IM_PI);
            float a = radius;
            float b = radius / 2.f;

            p_data.pathGeometry->Reset();
            p_data.pathGeometry->BeginPath();
            for (int i = 0; i < num_segments; ++i) {
                float anga = (i * (PI_2 / (num_segments - 1)));

                float xx = a * ImCos(anga) * ImCos(alpha) + b * ImSin(anga) * ImSin(alpha) + centre.x;
                float yy = b * ImSin(anga) * ImCos(alpha) - a * ImCos(anga) * ImSin(alpha) + centre.y;
                if (i == 0) {
                    p_data.pathGeometry->StartFigure(xx, yy);
                }
                else {
                    p_data.pathGeometry->LineTo(xx, yy);
                }
            }
            p_data.pathGeometry->EndPath();
            ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);

            float anga = ImFmod(start, PI_2);
            float x = a * ImCos(anga) * ImCos(alpha) + b * ImSin(anga) * ImSin(alpha) + centre.x;
            float y = b * ImSin(anga) * ImCos(alpha) - a * ImCos(anga) * ImSin(alpha) + centre.y;
            return ImVec2{ x, y };
            };

        ImVec2 ppos[3];
        for (size_t i = 0; i < elipses; ++i) {
            ppos[i % 3] = draw_rotated_ellipse((IM_PI * (float)i / elipses), start * (1.f + 0.1f * i));
        }

        UIColor pcolors[3] = { UIColor(255, 0, 0), UIColor(0, 255, 0), UIColor(0, 0, 255) };
        for (size_t i = 0; i < elipses; ++i) {

            p_data.hBrush->SetColor(pcolors[i]);
            D2D1_ELLIPSE ellipse = D2D1::Ellipse(D2D1::Point2F(ppos[i].x, ppos[i].y), thickness * 2, thickness * 2);
            ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
        }
    }
    else
    {
        auto draw_rotated_ellipse = [&](float alpha) {
            std::array<ImVec2, 36> pts;
            if (num_segments <= (int)pts.size())
            {
                alpha = ImFmod(alpha, IM_PI);
                float a = radius;
                float b = radius / 2.f;

                const float bg_angle_offset = PI_2 / num_segments;
                for (int i = 0; i < num_segments; ++i) {
                    float anga = (i * bg_angle_offset);
                    float h = ease((ease_mode)mode, start, radius);

                    pts[i].x = (a + h) * ImCos(anga) * ImCos(alpha) + b * ImSin(anga) * ImSin(alpha) + centre.x;
                    pts[i].y = (b + h) * ImSin(anga) * ImCos(alpha) - a * ImCos(anga) * ImSin(alpha) + centre.y;
                }
                for (int i = 1; i < num_segments; ++i) {
                    ps.hCanvas->DrawLine(p_data.hBrush, pts[i - 1].x, pts[i - 1].y, pts[i].x, pts[i].y, thickness);
                }
                ps.hCanvas->DrawLine(p_data.hBrush, pts[num_segments - 1].x, pts[num_segments - 1].y, pts[0].x, pts[0].y, thickness);
            }
        };

        for (size_t i = 0; i < elipses; ++i) {
            draw_rotated_ellipse(start + (IM_PI * (float)i / elipses));
        }
    }
}
void HHBUI::UILoading::RainbowBalls(ps_context ps, float radius, float thickness, float speed, size_t balls, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = ImFmod(UIEngine::GetUptime() * speed * 3.f, IM_PI);
    const float colorback = 0.3f + 0.2f * ImSin(UIEngine::GetUptime() * speed);
    const float rstart = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    const float radius1 = (0.7f + 0.2f * ImSin(start)) * radius;
    const float angle_offset = PI_2 / balls;
    const bool rainbow = p_data.crHue.A() == 0;
    float out_h = 0.0, out_s = 0.0, out_v = 0.0;

    UIColor Color = p_data.crHue;
    if (rainbow)
        Color.ToHSV(out_h, out_s, out_v);

    for (size_t i = 0; i < balls; i++)
    {
        const float a = rstart + (i * angle_offset);
        UIColor c = rainbow ? UIColor::HSV(out_h + i * (1.f / balls) + colorback, out_s, out_v) : Color;
        float rb = radius1 + ease((ease_mode)mode, start, radius1);

        p_data.hBrush->SetColor(c);
        D2D1_ELLIPSE ellipse = D2D1::Ellipse(D2D1::Point2F(centre.x + ImCos(a) * rb, centre.y + ImSin(a) * rb), thickness, thickness);
        ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
    }
}
void HHBUI::UILoading::ScaleBlocks(ps_context ps, float radius, float thickness, float speed, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float offset_block = radius * 2.f / 3.f;
    ImVec2 lt{ centre.x - offset_block - (thickness / 2), centre.y - offset_block - (thickness / 2) };

    const ImVec2ih poses[] = { {0, 0}, {1, 0}, {2, 0}, {0, 1}, {1, 1}, {2, 1}, {0, 2}, {1, 2}, {2, 2} };
    constexpr float rkoeff[9] = { 0.1f, 0.15f, 0.17f, 0.25f, 0.6f, 0.15f, 0.1f, 0.12f, 0.22f };
    UIColor Color = p_data.crHue;
    int ti = 0;
    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    Color.ToHSV(out_h, out_s, out_v);
    if (mode == 2)
    {
        const float hside = (thickness / 2.f);
        const float offsets[] = { 0.f,    0.8f,   0.8f,   1.6f,   1.6f,   1.6f,   2.4f,   2.4f,   3.2f };
        for (const auto& rpos : poses)
        {
            const UIColor c = UIColor::HSV(out_h + offsets[ti], out_s, out_v);
            p_data.hBrush->SetColor(c);

            const float strict = (0.5f + 0.5f * ImSin(-UIEngine::GetUptime() * speed + offsets[ti % 9]));
            const float side = ImClamp<float>(strict + 0.1f, 0.1f, 1.f) * hside;
            D2D1_RECT_F rect = D2D1::RectF(
                lt.x + hside + (rpos.x * offset_block) - side,
                lt.y + hside + (rpos.y * offset_block) - side,
                lt.x + hside + (rpos.x * offset_block) + side,
                lt.y + hside + (rpos.y * offset_block) + side);
            ps.hCanvas->FillRect(p_data.hBrush, rect.left, rect.top, rect.right, rect.bottom);
            ti++;
        }
    }
    else
    {
        for (const auto& rpos : poses)
        {
            UIColor c = UIColor::HSV(out_h + ti * 0.1f, out_s, out_v);
            p_data.hBrush->SetColor(c);
            if (mode) {
                float h = (0.1f + 0.4f * ImSin(UIEngine::GetUptime() * (speed * rkoeff[ti % 9])));
                D2D1_ELLIPSE ellipse = D2D1::Ellipse(
                    D2D1::Point2F(lt.x + rpos.x * offset_block, lt.y + rpos.y * offset_block),
                    std::max<float>(1.f, h * thickness), std::max<float>(1.f, h * thickness)
                );
                ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
            }
            else {
                float h = (0.8f + 0.4f * ImSin(UIEngine::GetUptime() * (speed * rkoeff[ti % 9])));
                D2D1_RECT_F rect = D2D1::RectF(
                    lt.x + rpos.x * offset_block,
                    lt.y + rpos.y * offset_block,
                    lt.x + rpos.x * offset_block + h * thickness,
                    lt.y + rpos.y * offset_block + h * thickness
                );
                ps.hCanvas->FillRect(p_data.hBrush, rect.left, rect.top, rect.right, rect.bottom);

            }
            ti++;
        }
    }
}
void HHBUI::UILoading::HboDots(ps_context ps, float radius, float thickness, float minfade, float ryk, float speed, size_t dots)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = UIEngine::GetUptime() * speed;
    UIColor Color = p_data.crHue;
    for (size_t i = 0; i < dots; i++)
    {
        const float astart = start + PI_2_DIV(dots) * i;
        Color.SetA(ImMax(minfade, ImSin(astart + PI_DIV_2)));
        p_data.hBrush->SetColor(Color);

        float x = centre.x + (std::sin(astart) * radius) - (radius / 2);
        float y = centre.y + (ryk * std::cos(astart) * radius) - (radius / 2);
        ps.hCanvas->FillEllipse(p_data.hBrush, x, y, x + radius, y + radius);
    }
}
void HHBUI::UILoading::SwingDots(ps_context ps, float radius, float thickness, float speed)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = UIEngine::GetUptime() * speed;
    constexpr int elipses = 2;
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    auto get_rotated_ellipse_pos = [&](float alpha, float start) {
        std::array<ImVec2, 36> pts;

        alpha = ImFmod(alpha, IM_PI);
        float a = radius;
        float b = radius / 10.f;

        float anga = ImFmod(start, PI_2);
        float x = a * ImCos(anga) * ImCos(alpha) + b * ImSin(anga) * ImSin(alpha) + centre.x;
        float y = b * ImSin(anga) * ImCos(alpha) - a * ImCos(anga) * ImSin(alpha) + centre.y;
        return ImVec2{ x, y };
    };

    UIColor Color = p_data.crHue;
    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    Color.ToHSV(out_h, out_s, out_v);
    for (int i = 0; i < elipses; ++i) {
        ImVec2 ppos = get_rotated_ellipse_pos((IM_PI * (float)i / elipses) + PI_DIV_4, start + PI_DIV_2 * i);
        const float y_delta = ImAbs(centre.y - ppos.y);
        float th_koeff = ImMax((y_delta / size.y) * 4.f, 0.5f);
        const UIColor c = UIColor::HSV(out_h + i * 0.5f, out_s, out_v);
        p_data.hBrush->SetColor(c);
        D2D1_ELLIPSE ellipse = D2D1::Ellipse(D2D1::Point2F(ppos.x, ppos.y), th_koeff * thickness, th_koeff * thickness);
        ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
    }
}
void HHBUI::UILoading::WaveDots(ps_context ps, float radius, float thickness, float speed)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    const float nextItemKoeff = 2.5f;
    const float dots = (size.x / (thickness * nextItemKoeff));
    const float offset = PI_DIV(dots);
    const float start = UIEngine::GetUptime() * speed;
    UIColor Color = p_data.crHue;

    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    Color.ToHSV(out_h, out_s, out_v);
    for (size_t i = 0; i < dots; i++)
    {
        float a = start + (IM_PI - i * offset);
        float y = centre.y + ImSin(a) * (size.y / 2.f);
        UIColor c = UIColor::HSV(out_h + i * (1.f / dots * 2.f), out_s, out_v);
        p_data.hBrush->SetColor(c);

        D2D1_ELLIPSE ellipse = D2D1::Ellipse(D2D1::Point2F(centre.x - (size.x / 2.f) + i * thickness * nextItemKoeff, y), thickness, thickness);
        ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
    }
}
void HHBUI::UILoading::SinSquares(ps_context ps, float radius, float thickness, float speed, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = ImFmod(UIEngine::GetUptime(), IM_PI);
    const float rstart = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    const float radius1 = radius / 2.5f + thickness;
    const float angle_offset = PI_DIV_2;
    p_data.hBrush->SetColor(p_data.crHue);

    std::vector<D2D1_POINT_2F> points(4);
    for (int i = 0; i <= 4; i++)
    {
        const float a = rstart + (i * angle_offset);
        const float begin_a = a - PI_DIV_2;
        float roff = ImMax(ImSin(start) - 0.5f, 0.f) * (radius * 0.4f);
        switch (mode) {
        case 1: roff = ease_inoutquad(roff); break;
        case 2: roff = ease_inoutexpo(roff) * (radius * 0.3f); break;
        }
        ImVec2 tri_centre(centre.x + ImCos(a) * (radius1 + roff), centre.y + ImSin(a) * (radius1 + roff));
        for (int pi = 0; pi < 4; ++pi) {
            points[pi] = { tri_centre.x + ImCos(begin_a + pi * PI_DIV_2) * radius1, tri_centre.y + ImSin(begin_a + pi * PI_DIV_2) * radius1 };
        }
        ps.hCanvas->FillPoly(p_data.hBrush, &points[0], points.size());

    }
}
void HHBUI::UILoading::ZipDots(ps_context ps, float radius, float thickness, float speed, size_t dots, float offset_k, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float nextItemKoeff = 3.5f;
    const float heightKoeff = 2.f;
    const float heightSpeed = 0.8f;
    const float hsize = dots * (thickness * nextItemKoeff) / 2.f - (thickness * nextItemKoeff) * 0.5f;
    const float start = UIEngine::GetUptime() * speed;
    const float offset = PI_DIV(dots);
    p_data.hBrush->SetColor(p_data.crHue);

    for (size_t i = 0; i < dots; i++)
    {
        const float sina = ImSin((start + (IM_PI - i * offset)) * heightSpeed);
        const float y = ImMin(centre.y + sina * thickness * heightKoeff, centre.y);
        const float deltay = ImAbs(y - centre.y);

        if (mode == 1)
        {
            const float hradius = radius;
            const float sinb = ImSin((start + (IM_PI + IM_PI * offset_k - i * offset)) * heightSpeed);
            const float y2 = ImMin(sinb, 0.f) * (hradius * offset_k);
            const float y3 = (y + y2);
            D2D1_POINT_2F p1 = { centre.x - hsize + i * (thickness * nextItemKoeff), y3 };
            D2D1_POINT_2F p2 = { centre.x - hsize + i * (thickness * nextItemKoeff), y3 + 2 * deltay };

            ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
            ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p2, thickness, thickness));
            ps.hCanvas->DrawLine(p_data.hBrush, p1.x, p1.y, p2.x, p2.y, thickness * 2.f, 0, 1);
        }
        else
        {
            D2D1_ELLIPSE ellipse = D2D1::Ellipse(D2D1::Point2F(centre.x - hsize + i * (thickness * nextItemKoeff), y), thickness, thickness);
            ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
            ellipse = D2D1::Ellipse(D2D1::Point2F(centre.x - hsize + i * (thickness * nextItemKoeff), y + 2 * deltay), thickness, thickness);
            ps.hCanvas->FillEllipse(p_data.hBrush, ellipse);
        }
    }
}
void HHBUI::UILoading::TrianglesShift(ps_context ps, float radius, float thickness, float speed, size_t bars)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float lerp_koeff = (ImSin(UIEngine::GetUptime() * speed) + 1.f) * 0.5f;

    const float angle_offset = PI_2 / bars;
    float start = UIEngine::GetUptime() * speed;
    const float astart = ImFmod(start, angle_offset);
    const float save_start = start;
    start -= astart;
    const float angle_offset_t = angle_offset * 0.3f;
    bars = ImMin<size_t>(bars, 32);

    const float rmin = radius - thickness;
    auto get_points = [&](float left, float right, float r1, float r2) -> std::array<D2D1_POINT_2F, 4> {
        return {
            D2D1::Point2F(centre.x + ImCos(left) * r1, centre.y + ImSin(left) * r1),
            D2D1::Point2F(centre.x + ImCos(left) * r2, centre.y + ImSin(left) * r2),
            D2D1::Point2F(centre.x + ImCos(right) * r2, centre.y + ImSin(right) * r2),
            D2D1::Point2F(centre.x + ImCos(right) * r1, centre.y + ImSin(right) * r1)
        };
        };

    UIColor rc = p_data.crHue;
    for (size_t i = 0; i < bars; i++) {
        float left = start + (i * angle_offset) - angle_offset_t;
        float right = start + (i * angle_offset) + angle_offset_t;
        float centera = start - PI_DIV_2 + (i * angle_offset);
        float rmul = 1.f - ImClamp(ImAbs(centera - save_start), 0.f, PI_DIV_2) / PI_DIV_2;
        rc.SetA(ImMax(rmul, 0.1f));
        p_data.hBrush->SetColor(rc);
        rmul *= 1.5f;
        rmul = ImMax(0.5f, rmul);
        const float r1 = ImMax(rmin * rmul, rmin);
        const float r2 = ImMax(radius * rmul, radius);
        auto points = get_points(left, right, r1, r2);
        ps.hCanvas->FillPoly(p_data.hBrush, &points[0], points.size());
    }
}
void HHBUI::UILoading::CircularLines(ps_context ps, float radius, float speed, size_t lines, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);

    auto ghalf_pi = [](float f) -> float { return ImMin(f, PI_DIV_2); };
    const float start = ImFmod(UIEngine::GetUptime() * speed, IM_PI);
    const float bg_angle_offset = PI_2_DIV(lines);
    UIColor c = p_data.crHue;
    for (size_t j = 0; j < 3; ++j)
    {
        const float start_offset = j * PI_DIV(7.f);
        const float rmax = ImMax(ImSin(ghalf_pi(start - start_offset)), 0.3f) * radius;
        const float rmin = ImMax(ImSin(ghalf_pi(start - PI_DIV_4 - start_offset)), 0.3f) * radius;
        c.SetA(1.f - j * 0.3f);
        p_data.hBrush->SetColor(c);

        for (size_t i = 0; i <= lines; i++)
        {
            float a = (i * bg_angle_offset);
            a += ease((ease_mode)mode, start_offset, radius);
            D2D1_POINT_2F p1 = { centre.x + cos(a) * rmin, centre.y + sin(a) * rmin };
            D2D1_POINT_2F p2 = { centre.x + cos(a) * rmax, centre.y + sin(a) * rmax };
            ps.hCanvas->DrawLine(p_data.hBrush, p1.x, p1.y, p2.x, p2.y, 1.f);
        }
    }
}
void HHBUI::UILoading::PatternRings(ps_context ps, float radius, float thickness, float speed, size_t elipses, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    float start = 0.f;
    if (mode == 0)
    {
        start = UIEngine::GetUptime() * speed;
        elipses = std::max<size_t>(elipses, 1);
    }
    else
    {
        start = ImFmod(UIEngine::GetUptime() * speed, PI_2);
        num_segments *= 4;
    }

    const float aoffset = ImFmod(UIEngine::GetUptime(), PI_2);
    const float bofsset = (aoffset > IM_PI) ? IM_PI : aoffset;
    const float angle_offset = PI_2 / num_segments;
    float ared_min = 0, ared = 0;
    if (aoffset > IM_PI)
        ared_min = aoffset - IM_PI;
    ImVec2 pos = ImVec2(0.f, centre.y - radius);
    ImVec2 size = ImVec2(radius * 2, radius * 2);


    auto draw_rotated_ellipse = [&](float alpha, float tr, float y) {
        alpha = ImFmod(alpha, IM_PI);
        float a = radius;
        float b = radius / 2.f;
        UIColor c = p_data.crHue;
        const float bg_angle_offset = PI_2 / (num_segments - 1);
        c.SetA(tr);
        p_data.hBrush->SetColor(c);

        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        for (int i = 0; i < num_segments; ++i) {
            float anga = (i * bg_angle_offset);
            float xx = a * ImCos(anga) * ImCos(alpha) + b * ImSin(anga) * ImSin(alpha) + centre.x;
            float yy = b * ImSin(anga) * ImCos(alpha) - a * ImCos(anga) * ImSin(alpha) + centre.y + y;

            if (i == 0) {
                p_data.pathGeometry->StartFigure(xx, yy);
            }
            else {
                p_data.pathGeometry->LineTo(xx, yy);
            }
        }
        p_data.pathGeometry->EndPath();
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);
    };
    auto draw_ellipse = [&](float alpha, float y, float r) {

        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        for (int i = 0; i <= num_segments + 1; i++)
        {
            ared = start + (i * angle_offset);
            float a = r;
            float b = r * 0.25f;

            const float xx = a * ImCos(ared) * ImCos(alpha) + b * ImSin(ared) * ImSin(alpha) + centre.x;
            const float yy = b * ImSin(ared) * ImCos(alpha) - a * ImCos(ared) * ImSin(alpha) + pos.y + y;
            if (i == 0) {
                p_data.pathGeometry->StartFigure(xx, yy);
            }
            else {
                p_data.pathGeometry->LineTo(xx, yy);
            }

            if (i * angle_offset < ared_min * 2)
                continue;

            if (i * angle_offset > bofsset * 2.f)
                break;
        }
        p_data.pathGeometry->EndPath();
        p_data.hBrush->SetColor(p_data.crHue);
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);
    };

    if (mode == 0)
    {
        for (size_t i = 0; i < elipses; ++i)
        {
            const float h = (0.5f * ImSin(start + (IM_PI / elipses) * i));
            draw_rotated_ellipse(0.f, 0.1f + (0.9f / elipses) * i, radius * h);
        }
    }
    else
    {
        for (size_t i = 0; i < elipses; ++i)
        {
            float y = i * ((float)(size.y * 0.7f) / (float)elipses) + (size.y * 0.15f);
            draw_ellipse(0, y, radius * ImSin((i + 1) * (IM_PI / (elipses + 1))));
        }
    }
}
void HHBUI::UILoading::PointsShift(ps_context ps, float radius, float thickness, float speed, size_t bars)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float lerp_koeff = (ImSin(UIEngine::GetUptime() * speed) + 1.f) * 0.5f;

    const float angle_offset = PI_2 / bars;
    float start = UIEngine::GetUptime() * speed;
    const float astart = ImFmod(start, angle_offset);
    const float save_start = start;
    start -= astart;
    const float angle_offset_t = angle_offset * 0.3f;
    bars = ImMin<size_t>(bars, 32);

    const float rmin = radius - thickness;

    UIColor rc = p_data.crHue;
    for (size_t i = 0; i < bars; i++) {
        float left = start + (i * angle_offset) - angle_offset_t;
        float centera = start - PI_DIV_2 + (i * angle_offset);
        float rmul = 1.f - ImClamp(ImAbs(centera - save_start), 0.f, PI_DIV_2) / PI_DIV_2;
        rc.SetA(ImMax(rmul, 0.1f));
        p_data.hBrush->SetColor(rc);
        rmul *= 1.f + ImSin(rmul * IM_PI);
        const float r = ImMax(radius * rmul, radius);
        D2D1_POINT_2F p1 = { centre.x + ImCos(left) * r, centre.y + ImSin(left) * r };

        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
    }
}
void HHBUI::UILoading::CircularPoints(ps_context ps, float radius, float thickness, float speed, size_t lines)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = ImFmod(UIEngine::GetUptime() * speed, radius);
    const float bg_angle_offset = (PI_2) / lines;
    UIColor c = p_data.crHue;
    for (size_t j = 0; j < 3; ++j)
    {
        const float start_offset = j * radius / 3.f;
        const float rmax = ImFmod((start + start_offset), radius);
        c.SetA(ImSin((radius - rmax) / radius * IM_PI));
        p_data.hBrush->SetColor(c);

        for (size_t i = 0; i < lines; i++)
        {
            float a = (i * bg_angle_offset);
            D2D1_POINT_2F p1 = { centre.x + ImCos(a) * rmax, centre.y + ImSin(a) * rmax };
            ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
        }
    }
}
void HHBUI::UILoading::CurvedCircle(ps_context ps, float radius, float thickness, float speed, size_t circles)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    const float bg_angle_offset = PI_2 / num_segments;
    UIColor Color = p_data.crHue;
    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    Color.ToHSV(out_h, out_s, out_v);

    for (size_t j = 0; j < circles; j++)
    {
        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        const float rr = radius - ((radius * 0.5f) / circles) * j;
        const float start_a = start * (1.1f * (j + 1));
        for (int i = 0; i <= num_segments; i++)
        {
            const float a = start_a + (i * bg_angle_offset);
            const float r = rr - (0.2f * (i % 2)) * rr;
            ImVec2 p = ImVec2(centre.x + ImCos(a) * r, centre.y + ImSin(a) * r);
            if (i == 0) {
                p_data.pathGeometry->StartFigure(p.x, p.y);
            }
            else {
                p_data.pathGeometry->LineTo(p.x, p.y);
            }
        }
        p_data.hBrush->SetColor(UIColor::HSV(out_h + (j * 1.f / circles), out_s, out_v));
        p_data.pathGeometry->EndPath();
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);
    }
}
void HHBUI::UILoading::PatternEclipse(ps_context ps, float radius, float thickness, float speed, size_t elipses, float delta_a, float delta_y)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = UIEngine::GetUptime() * speed;
    elipses = std::max<size_t>(elipses, 1);
    int num_segments = CalcCircleAutoSegmentCount(radius);
    auto draw_rotated_ellipse = [&](const ImVec2& pp, float alpha, float tr, float r, float x, float y) {
        alpha = ImFmod(alpha, IM_PI);
        float a = r;
        float b = r / delta_a;
        UIColor Color = p_data.crHue;
        Color.SetA(tr);
        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        for (int i = 0; i < num_segments; ++i) {
            float anga = (i * PI_2 / (num_segments - 1));
            float xx = a * ImCos(anga) * ImCos(alpha) + b * ImSin(anga) * ImSin(alpha) + pp.x + x;
            float yy = b * ImSin(anga) * ImCos(alpha) - a * ImCos(anga) * ImSin(alpha) + pp.y + y;
            if (i == 0) {
                p_data.pathGeometry->StartFigure(xx, yy);
            }
            else {
                p_data.pathGeometry->LineTo(xx, yy);
            }
        }
        p_data.hBrush->SetColor(Color);
        p_data.pathGeometry->EndPath();
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);
    };

    for (size_t i = 0; i < elipses; ++i)
    {
        const float rkoeff = (0.5f + 0.5f * ImSin(start + (IM_PI / elipses) * i));
        const float h = ((1.f / elipses) * (i + 1));
        const float anga = start + (i * PI_DIV_2 / elipses);
        const float yoff = ((1.f / elipses) * i) * delta_y;
        float a = (radius * (1.f - h));
        float b = (radius * (1.f - h)) / delta_a;
        float xx = a * ImCos(anga) * ImCos(0) + b * ImSin(anga) * ImSin(0) + centre.x;
        float yy = b * ImSin(anga) * ImCos(0) - a * ImCos(anga) * ImSin(0) + centre.y;
        draw_rotated_ellipse(ImVec2(xx, yy), 0.f, 0.3f + (0.7f / elipses) * i, radius * h, 0.f, yoff * radius);
    }
}
void HHBUI::UILoading::RainbowShot(ps_context ps, float radius, float thickness, float speed, size_t balls)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    UIColor color = p_data.crHue;
    const float start = ImFmod(UIEngine::GetUptime() * speed * 3.f, PI_2);
    const float colorback = 0.3f + 0.2f * ImSin(UIEngine::GetUptime() * speed);
    const float rstart = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    const float angle_offset = PI_2 / balls;
    const bool rainbow = color.A() == 0;
    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    color.ToHSV(out_h, out_s, out_v);

    for (size_t i = 0; i <= balls; i++)
    {
        float centera = start - PI_DIV_2 + (i * angle_offset);
        float rmul = ImMax(0.2f, 1.f - ImSin(centera));
        const float radius1 = ImMin(radius * rmul, radius);
        const float a = (i * angle_offset);
        UIColor c = rainbow ? UIColor::HSV(out_h + i * (1.f / balls) + colorback, out_s, out_v) : color;
        D2D1_POINT_2F p = { centre.x + cos(a) * radius1, centre.y + sin(a) * radius1 };
        p_data.hBrush->SetColor(c);
        ps.hCanvas->DrawLine(p_data.hBrush, centre.x, centre.y, p.x, p.y, thickness, 0, TRUE);
    }
}
void HHBUI::UILoading::Spiral(ps_context ps, float radius, float thickness, float speed, size_t arcs, int mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    float a = radius / num_segments;
    if (mode == 0)
        a = radius / num_segments;
    else
    {
        a = (radius * 3.f) / num_segments;
        num_segments *= 4;
    }

    float b = a;
    p_data.hBrush->SetColor(p_data.crHue);
    if (mode == 0)
    {
        ImVec2 last = centre;
        for (size_t arc_num = 0; arc_num < (num_segments * arcs); ++arc_num)
        {
            float angle = (PI_2 / num_segments) * arc_num;
            float x = centre.x + (a + b * angle) * ImCos(start + angle);
            float y = centre.y + (a + b * angle) * ImSin(start + angle);

            ps.hCanvas->DrawLine(p_data.hBrush, last.x, last.y, x, y, thickness);
            last = ImVec2(x, y);
        }
    }
    else
    {
        auto half_eye = [&](float side) {
            for (int arc_num = 0; arc_num < num_segments; ++arc_num) {
                float angle = (PI_2 / num_segments) * arc_num;
                float x = centre.x + (a + b * angle) * ImCos((start + angle) * side);
                float y = centre.y + (a + b * angle) * ImSin((start + angle) * side);
                D2D1_POINT_2F p1 = { x, y };
                ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
            }
        };

        half_eye(1.f);
        half_eye(-1.f);
    }
}
void HHBUI::UILoading::DnaDots(ps_context ps, float radius, float thickness, float speed, float delta, bool mode)
{
    ImVec2 centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    const float nextItemKoeff = 2.5f;
    const float dots = (size.x / (thickness * nextItemKoeff));
    const float start = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    UIColor color = p_data.crHue;

    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    color.ToHSV(out_h, out_s, out_v);
    auto draw_point = [&](float angle, int i) {
        float a = angle + start + (IM_PI - i * PI_DIV(dots));
        float th_koeff = 1.f + ImSin(a + PI_DIV_2) * 0.5f;

        float pp = mode ? centre.x + ImSin(a) * size.x * delta
            : centre.y + ImSin(a) * size.y * delta;
        UIColor c = UIColor::HSV(out_h + i * (1.f / dots * 2.f), out_s, out_v);
        D2D1_POINT_2F p = mode ? D2D1::Point2F(pp, centre.y - (size.y * 0.5f) + i * thickness * nextItemKoeff)
            : D2D1::Point2F(centre.x - (size.x * 0.5f) + i * thickness * nextItemKoeff, pp);
        p_data.hBrush->SetColor(c);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness * th_koeff, thickness * th_koeff));
        return p;
    };

    p_data.hBrush->SetColor(color);
    for (int i = 0; i < dots; i++) {
        D2D1_POINT_2F p1 = draw_point(0, i);
        D2D1_POINT_2F p2 = draw_point(IM_PI, i);
        ps.hCanvas->DrawLine(p_data.hBrush, p1.x, p1.y, p2.x, p2.y, thickness * 0.5f);
    }
}
void HHBUI::UILoading::SolarBalls(ps_context ps, float radius, float thickness, float speed, size_t balls, bool Scale)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = UIEngine::GetUptime() * speed;
    const float bg_angle_offset = PI_2 / num_segments;
    if (Scale)
    {
        p_data.hBrush->SetColor(p_data.crHue);
        for (size_t i = 0; i < balls; ++i) {
            const float rb = (radius / balls) * 1.3f * (i + 1);
            const float a = start * (1.0f + 0.1f * i);
            D2D1_POINT_2F p = D2D1::Point2F(centre.x + ImCos(a) * rb, centre.y + ImSin(a) * rb);

            ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, ((thickness * 2.f) / balls) * i, ((thickness * 2.f) / balls) * i));
        }
    }
    else
    {
        p_data.hBrush->SetColor(p_data.crbg);
        for (size_t i = 0; i < balls; ++i) {
            const float rb = (radius / balls) * 1.3f * (i + 1);
            ps.hCanvas->DrawEllipse(p_data.hBrush, D2D1::Ellipse(centre, rb, rb), thickness * 0.3f);
        }
        p_data.hBrush->SetColor(p_data.crHue);
        for (size_t i = 0; i < balls; ++i) {
            const float rb = (radius / balls) * 1.3f * (i + 1);
            const float a = start * (1.0f + 0.1f * i);
            D2D1_POINT_2F p = D2D1::Point2F(centre.x + ImCos(a) * rb, centre.y + ImSin(a) * rb);

            ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
        }
    }
}
void HHBUI::UILoading::RotatingHeart(ps_context ps, float radius, float thickness, float speed, float ang_min)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    // Calculate the start angle of the spinner based on the current time and speed.
    const float start = UIEngine::GetUptime() * speed;

    // Modify the number of segments to ensure the heart shape is complete.
    num_segments = (num_segments * 3) / 2;
    auto circle = [&](const std::function<D2D1_POINT_2F(int)>& point_func, UIColor dbc, float dth) {
        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        p_data.hBrush->SetColor(dbc);
        for (int i = 0; i < num_segments; i++) {
            D2D1_POINT_2F p = point_func(i);
            if (i == 0) {
                p_data.pathGeometry->StartFigure(centre.x + p.x, centre.y + p.y);
            }
            else {
                p_data.pathGeometry->LineTo(centre.x + p.x, centre.y + p.y);
            }
        }
        p_data.pathGeometry->EndPath();
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, dth);

    };
    // Create a lambda function to rotate points.
    auto rotate = [](const D2D1_POINT_2F& point, float angle) {
        const float s = ImSin(angle), c = ImCos(angle);
        return D2D1::Point2F(point.x * c - point.y * s, point.x * s + point.y * c);
    };

    // Calculate the radius of the bottom of the heart.
    const float rb = radius * ImMax(0.8f, ImSin(start * 2));
    auto scale = [rb](float v) { return v / 16.f * rb; };

    // Draw the heart spinner by calling the circle function, passing in a lambda function that defines the shape of the heart.
    circle([&](int i) {
        const float a = PI_2 * i / num_segments;
        const float x = (scale(16) * ImPow(ImSin(a), 3));
        const float y = -1.f * (scale(13) * ImCos(a) - scale(5) * ImCos(2 * a) - scale(2) * ImCos(3 * a) - ImCos(4 * a));
        return rotate(D2D1::Point2F(x, y), ang_min);
    }, p_data.crHue, thickness);
}
void HHBUI::UILoading::FluidPoints(ps_context ps, float radius, float thickness, float speed, size_t dots, float delta)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float rkoeff[3] = { 0.033f, 0.3f, 0.8f };
    const float hspeed = 0.1f + ImSin(UIEngine::GetUptime() * 0.1f) * 0.05f;
    const float j_k = radius * 2.f / num_segments;
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    ImVec2 pos = ImVec2(centre.x - radius, 0.f);

    UIColor color = p_data.crHue;

    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    color.ToHSV(out_h, out_s, out_v);
    for (int j = 0; j < num_segments; ++j) {
        float h = (0.6f + delta * ImSin(UIEngine::GetUptime() * (speed * rkoeff[2] * 2.f) + (2.f * rkoeff[0] * j * j_k))) * (radius * 2.f * rkoeff[2]);
        for (size_t i = 0; i < dots; i++) {
            UIColor c = UIColor::HSV(out_h - i * 0.1f, out_s, out_v);
            p_data.hBrush->SetColor(c);
            D2D1_POINT_2F p = D2D1::Point2F(pos.x + j * j_k, centre.y + size.y / 2.f - (h / dots) * i);
            ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));

        }
    }
}
void HHBUI::UILoading::DotsToPoints(ps_context ps, float radius, float thickness, float offset_k, float speed, size_t dots)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float nextItemKoeff = 3.5f;
    const float hsize = dots * (thickness * nextItemKoeff) / 2.f - (thickness * nextItemKoeff) * 0.5f;
    const float start = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    const float offset = PI_DIV(dots);
    UIColor color = p_data.crHue;

    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    color.ToHSV(out_h, out_s, out_v);
    if (start < PI_DIV_2) {
        const float sina = ImSin(start);
        for (size_t i = 0; i < dots; i++) {
            const float xx = ImMax(sina * (i * (thickness * nextItemKoeff)), 0.f);
            UIColor c = UIColor::HSV(out_h + i * ((1.f / dots) * 2.f), out_s, out_v);
            p_data.hBrush->SetColor(c);
            D2D1_POINT_2F p = D2D1::Point2F(centre.x - hsize + xx, centre.y);
            ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
        }
    }
    else {
        for (size_t i = 0; i < dots; i++) {
            const float sina = ImSin(ImMax(start - (IM_PI / dots) * i, PI_DIV_2));
            const float xx = ImMax(1.f * (i * (thickness * nextItemKoeff)), 0.f);
            const float th = sina * thickness;
            UIColor c = UIColor::HSV(out_h + i * ((1.f / dots) * 2.f), out_s, out_v);
            p_data.hBrush->SetColor(c);
            D2D1_POINT_2F p = D2D1::Point2F(centre.x - hsize + xx, centre.y);
            ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, th, th));
        }
    }
}
void HHBUI::UILoading::ThreeDots(ps_context ps, float radius, float thickness, float speed)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    const float start = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    const float nextItemKoeff = 2.5f;
    const float offset = size.x / 4.f;
    p_data.hBrush->SetColor(p_data.crHue);
    float ab = start;
    int msize = 2;
    if (start < IM_PI) { ab = 0; msize = 1; }
    for (int i = 0; i < msize; i++)
    {
        float a = ab + i * IM_PI - PI_DIV_2;
        D2D1_POINT_2F p = D2D1::Point2F(centre.x - offset + ImSin(a) * offset, centre.y + ImCos(a) * offset);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }

    float ba = start; msize = 2;
    if (start > IM_PI && start < PI_2) { ba = 0; msize = 1; }
    for (int i = 0; i < msize; i++)
    {
        float a = -ba + i * IM_PI + PI_DIV_2;
        D2D1_POINT_2F p = D2D1::Point2F(centre.x + offset + ImSin(a) * offset, centre.y + ImCos(a) * offset);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }
}
void HHBUI::UILoading::Caleidospcope(ps_context ps, float radius, float thickness, float speed)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    const float start = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    const float nextItemKoeff = 2.5f;
    const float offset = size.x / 4.f;

    float ab = start;
    int msize = 2;
    UIColor color = p_data.crHue;

    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    color.ToHSV(out_h, out_s, out_v);
    for (int i = 0; i < msize; i++)
    {
        float a = ab - i * IM_PI;
        UIColor c = UIColor::HSV(out_h + (0.1f * i), out_s, out_v, 0.7f);
        p_data.hBrush->SetColor(c);
        D2D1_POINT_2F p = D2D1::Point2F(centre.x - offset + ImSin(a) * offset, centre.y + ImCos(a) * offset);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }

    for (int i = 0; i < msize; i++)
    {
        float a = ab + i * IM_PI + PI_DIV_2;
        UIColor c = UIColor::HSV(out_h + 0.2f + (0.1f * i), out_s, out_v, 0.7f);
        p_data.hBrush->SetColor(c);
        D2D1_POINT_2F p = D2D1::Point2F(centre.x + ImSin(a) * offset, centre.y - offset + ImCos(a) * offset);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }

    float ba = start; msize = 2;
    for (int i = 0; i < msize; i++)
    {
        float a = -ba + i * IM_PI + PI_DIV_2;
        UIColor c = UIColor::HSV(out_h + 0.4f + (0.1f * i), out_s, out_v, 0.7f);
        p_data.hBrush->SetColor(c);
        D2D1_POINT_2F p = D2D1::Point2F(centre.x + offset + ImSin(a) * offset, centre.y + ImCos(a) * offset);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }

    for (int i = 0; i < msize; i++)
    {
        float a = ab - i * IM_PI + PI_DIV_4;
        UIColor c = UIColor::HSV(out_h + 0.6f + (0.1f * i), out_s, out_v, 0.7f);
        p_data.hBrush->SetColor(c);
        D2D1_POINT_2F p = D2D1::Point2F(centre.x + ImSin(a) * offset, centre.y + offset + ImCos(a) * offset);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }
}
void HHBUI::UILoading::FiveDots(ps_context ps, float radius, float thickness, float speed)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    ImVec2 size = ImVec2(radius * 2, radius * 2);
    const float start = ImFmod(UIEngine::GetUptime() * speed, PI_2 * 2);
    const float nextItemKoeff = 2.5f;
    const float offset = size.x / 4.f;
    p_data.hBrush->SetColor(p_data.crHue);
    float ab = 0;
    int msize = 1;
    if (start < IM_PI) { ab = start; msize = 2; }
    for (int i = 0; i < msize; i++)
    {
        float a = -ab + i * IM_PI - PI_DIV_2;
        D2D1_POINT_2F p = D2D1::Point2F(centre.x - offset + ImSin(a) * offset, centre.y + ImCos(a) * offset);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }

    float ba = 0; msize = 1;
    if (start > IM_PI && start < PI_2) { ba = start; msize = 2; }
    for (int i = 0; i < msize; i++)
    {
        float a = -ba + i * IM_PI;
        D2D1_POINT_2F p = D2D1::Point2F(centre.x + ImSin(a) * offset, centre.y + offset + ImCos(a) * offset);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }

    float bc = 0; msize = 1;
    if (start > PI_2 && start < IM_PI * 3) { bc = start; msize = 2; }
    for (int i = 0; i < msize; i++)
    {
        float a = -bc + i * IM_PI - IM_PI;
        D2D1_POINT_2F p = D2D1::Point2F(centre.x + ImSin(a) * offset, centre.y - offset + ImCos(a) * offset);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }

    float bd = 0; msize = 1;
    if (start > IM_PI * 3 && start < IM_PI * 4) { bd = start; msize = 2; }
    for (int i = 0; i < msize; i++)
    {
        float a = -bd + i * IM_PI + PI_DIV_2;
        D2D1_POINT_2F p = D2D1::Point2F(centre.x + offset + ImSin(a) * offset, centre.y + ImCos(a) * offset);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }
}
void HHBUI::UILoading::HerbertBalls(ps_context ps, float radius, float thickness, float speed, size_t balls)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = ImFmod(UIEngine::GetUptime(), IM_PI);
    const float rstart = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    const float radius1 = 0.3f * radius;
    const float radius2 = 0.8f * radius;
    const float angle_offset = PI_2 / balls;
    p_data.hBrush->SetColor(p_data.crHue);
    for (size_t i = 0; i < balls; i++)
    {
        const float a = rstart + (i * angle_offset);
        D2D1_POINT_2F p = D2D1::Point2F(centre.x + ImCos(a) * radius1, centre.y + ImSin(a) * radius1);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }

    for (size_t i = 0; i < balls * 2; i++)
    {
        const float a = -rstart + (i * angle_offset / 2.f);
        D2D1_POINT_2F p = D2D1::Point2F(centre.x + ImCos(a) * radius2, centre.y + ImSin(a) * radius2);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, thickness, thickness));
    }
}
void HHBUI::UILoading::HerbertBalls3D(ps_context ps, float radius, float thickness, float speed)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = ImFmod(UIEngine::GetUptime(), IM_PI);
    const float rstart = ImFmod(UIEngine::GetUptime() * speed, PI_2);
    const float radius1 = 0.3f * radius;
    const float radius2 = 0.8f * radius;
    const int balls = 2;
    const float angle_offset = PI_2 / balls;
    p_data.hBrush->SetColor(p_data.crHue);
    ImVec2 frontpos, backpos;
    for (int i = 0; i < balls; i++)
    {
        const float a = rstart + (i * angle_offset);
        const float t = (i == 1 ? 0.7f : 1.f) * thickness;
        const ImVec2 pos = ImVec2(centre.x + ImCos(a) * radius1, centre.y + ImSin(a) * radius1);
        D2D1_POINT_2F p = D2D1::Point2F(pos.x, pos.y);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, t, t));

        if (i == 0) frontpos = pos; else backpos = pos;
    }

    ImVec2 lastpos;
    for (int i = 0; i <= balls * 2; i++)
    {
        const float a = -rstart + (i * angle_offset / 2.f);
        const ImVec2 pos = ImVec2(centre.x + ImCos(a) * radius2, centre.y + ImSin(a) * radius2);
        float t = sqrt(pow(pos.x - frontpos.x, 2) + pow(pos.y - frontpos.y, 2)) / (radius * 1.f) * thickness;

        D2D1_POINT_2F p = D2D1::Point2F(pos.x, pos.y);
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p, t, t));
        ps.hCanvas->DrawLine(p_data.hBrush, pos.x, pos.y, backpos.x, backpos.y, ImMax(thickness / 2.f, 1.f));
        if (i > 0) {
            ps.hCanvas->DrawLine(p_data.hBrush, pos.x, pos.y, lastpos.x, lastpos.y, ImMax(thickness / 2.f, 1.f));
        }
        ps.hCanvas->DrawLine(p_data.hBrush, pos.x, pos.y, frontpos.x, frontpos.y, ImMax(thickness / 2.f, 1.f));
        lastpos = pos;
    }
}
void HHBUI::UILoading::SquareLoading(ps_context ps, float radius, float thickness, float speed)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = ImFmod(UIEngine::GetUptime() * speed, PI_2 + PI_DIV_2);
    const float arc_angle = PI_DIV_2;
    const float ht = thickness / 2.f;

    const float best_radius = radius * 1.4f;
    float a = arc_angle * 3 - PI_DIV_4 + (start > PI_2 ? start * 2.f : 0);
    ImVec2 last_pos(centre.x + ImCos(a) * best_radius, centre.y + ImSin(a) * best_radius);
    ImVec2 ppMin, ppMax;
    p_data.hBrush->SetColor(p_data.crHue);

    for (size_t arc_num = 0; arc_num < 4; ++arc_num) {
        a = arc_angle * arc_num - PI_DIV_4 + (start > PI_2 ? start * 2.f : 0);
        ImVec2 pp(centre.x + ImCos(a) * best_radius, centre.y + ImSin(a) * best_radius);
        ps.hCanvas->DrawLine(p_data.hBrush, last_pos.x, last_pos.y, pp.x, pp.y, thickness);
        last_pos = pp;

        if (start < PI_2) {
            if (arc_num == 2) ppMin = ImVec2(centre.x + ImCos(a) * best_radius * 0.8f, centre.y + ImSin(a) * best_radius * 0.8f);
            else if (arc_num == 0) ppMax = ImVec2(centre.x + ImCos(a) * best_radius * 0.8f, centre.y + ImSin(a) * best_radius * 0.8f);
        }
    }

    if (start < PI_2) {
        ppMax.y = ppMin.y + (start / PI_2) * (ppMax.y - ppMin.y);
        ps.hCanvas->FillRect(p_data.hBrush, ppMin.x, ppMin.y, ppMax.x, ppMax.y);

    }
}
void HHBUI::UILoading::TextFading(ps_context ps)
{
    auto pstrTitle = GetText();
    if (pstrTitle)
    {
        if (p_data.ang_min > ps.uWidth * 2) {
            p_data.mode = -1; // 到达右边界，改变方向为向左
        }
        else if (p_data.ang_min < -(int)ps.uWidth) {
            p_data.mode = 1; // 到达左边界，改变方向为向右
        }
        p_data.ang_min += p_data.minth * p_data.mode;

        p_data.hBrush->SetRadialCenter(p_data.ang_min, ps.uHeight / 2.f);
        ps.hCanvas->DrawTextExW(p_data.hBrush, ps.hFont, pstrTitle, ps.dwTextFormat, 0, 0, ps.uWidth, ps.uHeight);

    }
}
void HHBUI::UILoading::TwinAng360(ps_context ps, float radius1, float radius2, float thickness, float speed1, float speed2, int mode)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float radius = ImMax(radius1, radius2);
    int num_segments = CalcCircleAutoSegmentCount(radius) * 4;

    float start1 = ImFmod(UIEngine::GetUptime() * speed1, PI_2);
    float start2 = ImFmod(UIEngine::GetUptime() * speed2, PI_2);
    const float aoffset = ImFmod(UIEngine::GetUptime(), 2.f * IM_PI);
    const float bofsset = (aoffset > IM_PI) ? IM_PI : aoffset;
    const float angle_offset = PI_2 / num_segments;
    float ared_min = 0, ared = 0;
    if (aoffset > IM_PI)
        ared_min = aoffset - IM_PI;

    p_data.pathGeometry->Reset();
    p_data.pathGeometry->BeginPath();
    p_data.hBrush->SetColor(p_data.crHue);
    for (int i = 0; i <= num_segments + 1; i++) {
        ared = (mode ? damped_spring(1, 10.f, 1.0f, ImSin(ImFmod(start1 + 0 * PI_DIV(2), PI_2))) : start1) + (i * angle_offset);
        ImVec2 p = ImVec2(centre.x + ImCos(ared) * radius2, centre.y + ImSin(ared) * radius2);
        if (i == 0) {
            p_data.pathGeometry->StartFigure(p.x, p.y);
        }
        else {
            p_data.pathGeometry->LineTo(p.x, p.y);
        }
        if (i * angle_offset < ared_min * 2) continue;
        if (i * angle_offset > bofsset * 2.f) break;
    }
    p_data.pathGeometry->EndPath();
    ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);

    p_data.pathGeometry->Reset();
    p_data.pathGeometry->BeginPath();
    for (int i = 0; i <= num_segments + 1; i++) {
        ared = (mode ? damped_spring(1, 10.f, 1.0f, ImSin(ImFmod(start2 + 1 * PI_DIV(2), PI_2))) : start2) + (i * angle_offset);
        ImVec2 p = ImVec2(centre.x + ImCos(-ared) * radius1, centre.y + ImSin(-ared) * radius1);
        if (i == 0) {
            p_data.pathGeometry->StartFigure(p.x, p.y);
        }
        else {
            p_data.pathGeometry->LineTo(p.x, p.y);
        }
        if (i * angle_offset < ared_min * 2) continue;
        if (i * angle_offset > bofsset * 2.f) break;
    }
    p_data.pathGeometry->EndPath();
    ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);
}
void HHBUI::UILoading::PulsarBall(ps_context ps, float radius, float thickness, float speed, bool shadow, int mode)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const int iterations = shadow ? 4 : 1;
    UIColor color = p_data.crHue;
    int num_segments = CalcCircleAutoSegmentCount(radius);
    for (int j = 0; j < iterations; j++) {
        UIColor c = color;
        c.SetA(1.f - 0.15f * j);
        p_data.hBrush->SetColor(c);

        float start = ImFmod(UIEngine::GetUptime() * speed - (IM_PI / 12.f) * j, IM_PI);
        float maxht = damped_gravity(ImSin(ImFmod(start, IM_PI))) * (radius * 0.6f);
        D2D1_POINT_2F p1 = { centre.x, centre.y };
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, maxht, maxht));
    }
    p_data.hBrush->SetColor(color);
    const float angle_offset = PI_DIV_2 / num_segments;
    const int arcs = 2;
    for (size_t arc_num = 0; arc_num < arcs; ++arc_num) {
        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        float arc_start = 2 * IM_PI / arcs;
        float start = ImFmod(UIEngine::GetUptime() * speed - (IM_PI * arc_num), IM_PI);
        float b = mode ? start + damped_spring(1, 10.f, 1.0f, ImSin(ImFmod(start + arc_num * PI_DIV(2) / arcs, IM_PI)), 1, 0) : start;
        float maxht = (damped_gravity(ImSin(ImFmod(start, IM_PI))) * 0.3f + 0.7f) * radius;
        for (int i = 0; i < num_segments; i++) {
            const float a = b + arc_start * arc_num + (i * angle_offset);
            ImVec2 p = ImVec2(centre.x + ImCos(a) * maxht, centre.y + ImSin(a) * maxht);
            if (i == 0) {
                p_data.pathGeometry->StartFigure(p.x, p.y);
            }
            else {
                p_data.pathGeometry->LineTo(p.x, p.y);
            }
        }
        p_data.pathGeometry->EndPath();
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);
    }
}
void HHBUI::UILoading::RainbowMix(ps_context ps, float radius, float thickness, float speed, float ang_min, float ang_max, size_t arcs, int mode)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    UIColor color = p_data.crHue;

    auto circle = [&](const std::function<ImVec2(int)>& point_func, UIColor dbc, float dth) {
        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        p_data.hBrush->SetColor(dbc);
        for (int i = 0; i < num_segments; i++) {
            ImVec2 p = point_func(i);
            if (i == 0) {
                p_data.pathGeometry->StartFigure(centre.x + p.x, centre.y + p.y);
            }
            else {
                p_data.pathGeometry->LineTo(centre.x + p.x, centre.y + p.y);
            }
        }
        p_data.pathGeometry->EndPath();
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, dth);

    };

    float out_h = 0.0, out_s = 0.0, out_v = 0.0;
    color.ToHSV(out_h, out_s, out_v);
    for (size_t i = 0; i < arcs; ++i)
    {
        const float rb = (radius / arcs) * (i + 1);

        const float start = ImAbs(ImSin(UIEngine::GetUptime()) * (num_segments - 5));
        const float a_min = ImMax(ang_min, PI_2 * ((float)start) / (float)num_segments + (IM_PI / arcs) * i);
        const float a_max = ImMin(ang_max, PI_2 * ((float)num_segments + 3 * (i + 1)) / (float)num_segments);
        const float koeff = mode ? (1.1f - 1.f / (i + 1)) : 1.f;
        UIColor c = UIColor::HSV(out_h + i * (1.f / arcs), out_s, out_v);

        circle([&](int i) {
            const float a = a_min + ((float)i / (float)num_segments) * (a_max - a_min);
            const float rspeed = a + UIEngine::GetUptime() * speed * koeff;
            return ImVec2(ImCos(rspeed) * rb, ImSin(rspeed) * rb);
            }, c, thickness);
    }
}
void HHBUI::UILoading::AngMix(ps_context ps, float radius, float thickness, float speed, float angle, size_t arcs, int mode)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);

    auto circle = [&](const std::function<ImVec2(int)>& point_func, UIColor dbc, float dth) {
        p_data.pathGeometry->Reset();
        p_data.pathGeometry->BeginPath();
        p_data.hBrush->SetColor(dbc);
        for (int i = 0; i < num_segments; i++) {
            ImVec2 p = point_func(i);
            if (i == 0) {
                p_data.pathGeometry->StartFigure(centre.x + p.x, centre.y + p.y);
            }
            else {
                p_data.pathGeometry->LineTo(centre.x + p.x, centre.y + p.y);
            }
        }
        p_data.pathGeometry->EndPath();
        ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, dth);

    };

    for (size_t i = 0; i < arcs; ++i)
    {
        const float koeff = (1.1f - 1.f / (i + 1));
        float start = UIEngine::GetUptime() * speed * koeff;                        // The start angle of the spinner is calculated based on the current time and the specified speed.
        radius = (mode == 2) ? (0.8f + ImCos(start) * 0.2f) * radius : radius;
        const float rb = (radius / arcs) * (i + 1);
        const float b = (mode == 1) ? damped_gravity(ImSin(start * 1.1f)) * angle : 0.f;
        circle([&](int i) {                                                        // Draw the spinner itself using the `circle` function, with the specified color and thickness.
            const float a = start - b + (i * angle / num_segments);
            return ImVec2(ImCos(a) * rb, ImSin(a) * rb);
            }, p_data.crHue, thickness);
    }
}
void HHBUI::UILoading::TwinHboDots(ps_context ps, float radius, float thickness, float minfade, float ryk, float speed, size_t dots, float delta)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = UIEngine::GetUptime() * speed;
    UIColor color = p_data.crHue;
    for (size_t i = 0; i < dots; i++)
    {
        const float astart = start + PI_2_DIV(dots) * i;
        color.SetA(ImMax(minfade, ImSin(astart + PI_DIV_2)));
        p_data.hBrush->SetColor(color);
        D2D1_POINT_2F p1 = { centre.x + ImSin(astart) * radius, centre.y + ryk * ImCos(astart) * radius + radius * delta };
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
    }

    for (size_t i = 0; i < dots; i++)
    {
        const float astart = start + PI_2_DIV(dots) * i;
        color.SetA(ImMax(minfade, ImSin(astart + PI_DIV_2)));
        p_data.hBrush->SetColor(color);

        D2D1_POINT_2F p1 = { centre.x + ImSin(astart) * radius, centre.y - ryk * ImCos(astart) * radius - radius * delta };
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
    }
}
void HHBUI::UILoading::MoonDots(ps_context ps, float radius, float thickness, float speed)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    const float start = UIEngine::GetUptime() * speed;

    const float astart = ImFmod(start, IM_PI * 2.f);
    const float bstart = astart + IM_PI;

    const float sina = ImSin(astart);
    const float sinb = ImSin(bstart);
    const UIColor first = p_data.crHue, second = p_data.crbg;


    if (astart < PI_DIV_2 || astart > IM_PI + PI_DIV_2) {

        p_data.hBrush->SetColor(first);
        D2D1_POINT_2F p1 = { centre.x + sina * thickness, centre.y };
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
        p_data.hBrush->SetColor(second);
        p1 = { centre.x + sinb * thickness, centre.y };
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
        p_data.hBrush->SetColor(first);
        ps.hCanvas->DrawEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness), 2.f);
    }
    else {

        p_data.hBrush->SetColor(second);
        D2D1_POINT_2F p1 = { centre.x + sinb * thickness, centre.y };
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
        p_data.hBrush->SetColor(first);
        p1 = { centre.x + sinb * thickness, centre.y };
        ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
        p_data.hBrush->SetColor(first);
        p1 = { centre.x + sina * thickness, centre.y };
        ps.hCanvas->DrawEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness), 2.f);
    }
}
void HHBUI::UILoading::RotateSegmentsPulsar(ps_context ps, float radius, float thickness, float speed, size_t arcs, size_t layers)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float arc_angle = PI_2 / (float)arcs;
    const float angle_offset = arc_angle / num_segments;
    float r = radius;
    float reverse = 1.f;

    const float bg_angle_offset = PI_2_DIV(num_segments);
    const float koeff = PI_DIV(2 * layers);
    float start = UIEngine::GetUptime() * speed;
    UIColor color = p_data.crHue;

    for (size_t num_ring = 0; num_ring < layers; ++num_ring) {
        float radius_k = ImSin(ImFmod(start + (num_ring * koeff), PI_DIV_2));
        UIColor c = color;
        c.SetA((radius_k > 0.5f) ? (2.f - (radius_k * 2.f)) : color.GetA());
        p_data.hBrush->SetColor(c);

        for (size_t arc_num = 0; arc_num < arcs; ++arc_num)
        {
            p_data.pathGeometry->Reset();
            p_data.pathGeometry->BeginPath();
            for (int i = 2; i <= num_segments - 2; i++)
            {
                const float a = start * (1.f + 0.1f * num_ring) + arc_angle * arc_num + (i * angle_offset);

                ImVec2 p = ImVec2(centre.x + ImCos(a * reverse) * (r * radius_k), centre.y + ImSin(a * reverse) * (r * radius_k));
                if (i == 2) {
                    p_data.pathGeometry->StartFigure(p.x, p.y);
                }
                else {
                    p_data.pathGeometry->LineTo(p.x, p.y);
                }

            }
            p_data.pathGeometry->EndPath();
            ps.hCanvas->DrawPath(p_data.hBrush, p_data.pathGeometry, thickness);
        }
    }
}

void HHBUI::UILoading::PointsArcBounce(ps_context ps, float radius, float thickness, float speed, size_t points, size_t circles, float rspeed)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    int num_segments = CalcCircleAutoSegmentCount(radius);
    const float start = ImFmod(UIEngine::GetUptime() * speed, IM_PI * 4.f);
    const float arc_angle = PI_2 / (float)points;
    const float angle_offset = arc_angle / num_segments;
    float dspeed = rspeed;
    for (size_t c_num = 0; c_num < circles; c_num++)
    {
        float mr = radius * (1.f - (1.f / (circles + 2.f) * c_num));
        float adv_angle = IM_PI * c_num;// *(1.f + (0.1f * circles) * c_num);
        for (size_t arc_num = 0; arc_num < points; ++arc_num)
        {
            const float b = arc_angle * arc_num - PI_DIV_2 - PI_DIV_4;
            const float e = arc_angle * arc_num + arc_angle - PI_DIV_2 - PI_DIV_4;
            const float a = arc_angle * arc_num;
            UIColor c = p_data.crHue;
            float vradius = mr;
            if (start < PI_2) {
                c.SetA(0.f);
                if (start > a && start < (a + arc_angle)) { c.SetA(1.f - (start - a) / (float)arc_angle); }
                else if (start < a) { c.SetA(1.f); }
                c.SetA(ImMax(0.f, 1.f - c.GetA()));
                vradius = mr * c.GetA();
            }
            else
            {
                const float startk = start - PI_2;
                c.SetA(0.f);
                if (startk > a && startk < (a + arc_angle)) { c.SetA(1.f - (startk - a) / (float)arc_angle); }
                else if (startk < a) { c.SetA(1.f); }
                vradius = mr * c.GetA();
            }
            p_data.hBrush->SetColor(c);
            const float ar = start * dspeed + adv_angle + arc_angle * arc_num - PI_DIV_2 - PI_DIV_4;
            D2D1_POINT_2F p1 = { centre.x + ImCos(ar) * vradius, centre.y + ImSin(ar) * vradius };
            ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thickness, thickness));
        }
        dspeed += rspeed;
    }
}

void HHBUI::UILoading::SomeScaleDots(ps_context ps, float radius, float thickness, float speed, size_t dots, int mode)
{
    D2D1_POINT_2F  centre{ ps.uWidth / 2.f,ps.uHeight / 2.f };
    float start = UIEngine::GetUptime() * speed;
    float astart = ImFmod(start, IM_PI / dots);
    start -= astart;
    const float bg_angle_offset = IM_PI / dots;
    dots = ImMin(dots, (size_t)32);
    p_data.hBrush->SetColor(p_data.crHue);

    for (size_t j = 0; j < 4; j++)
    {
        float r = radius * (1.f - (0.15f * j));
        for (size_t i = 0; i <= dots; i++)
        {
            float a = start * (mode ? (1.f + j * 0.05f) : 1.f) + (i * bg_angle_offset);
            float th = thickness * ImMax(0.1f, i / (float)dots);
            float thh = th * (1.f - (0.2f * j));

            D2D1_POINT_2F p1 = { centre.x + ImCos(a) * r, centre.y + ImSin(a) * r };
            ps.hCanvas->FillEllipse(p_data.hBrush, D2D1::Ellipse(p1, thh, thh));
        }
    }
}


int HHBUI::UILoading::CalcCircleAutoSegmentCount(float radius)
{
    // Automatic segment count
    const int radius_idx = (int)(radius + 0.999999f); // ceil to never reduce accuracy
    if (radius_idx >= 0 && radius_idx < IM_ARRAYSIZE(p_data.CircleSegmentCounts))
        return p_data.CircleSegmentCounts[radius_idx]; // Use cached value
    else
        return p_data.CircleSegmentCounts[0];
}
void HHBUI::UILoading::SetCircleTessellationMaxError(float max_error)
{
    if (p_data.CircleSegmentMaxError == max_error)
        return;

    _ASSERT(max_error > 0.0f);
    p_data.CircleSegmentMaxError = max_error;
    for (int i = 0; i < IM_ARRAYSIZE(p_data.CircleSegmentCounts); i++)
    {
        const float radius = (float)i;
        p_data.CircleSegmentCounts[i] = (BYTE)((i > 0) ? IM_DRAWLIST_CIRCLE_AUTO_SEGMENT_CALC(radius, p_data.CircleSegmentMaxError) : IM_DRAWLIST_ARCFAST_SAMPLE_MAX);
    }
}
