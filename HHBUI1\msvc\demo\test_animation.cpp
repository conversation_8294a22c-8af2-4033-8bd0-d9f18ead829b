﻿#include "hhbui.h"

using namespace HHBUI;
UIStatic* staticico = nullptr;

LRESULT CALLBACK OnAnimationEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto obj = (UIButton*)UIView;
	auto window = (UIWnd*)pWnd;
	if (nCode == WMM_CLICK)
	{
		if (nID == 1001)
		{
			POINT ptOrg;
			ptOrg.x = GET_X_LPARAM(lParam) / UIEngine::GetDefaultScale();
			ptOrg.y = GET_Y_LPARAM(lParam) / UIEngine::GetDefaultScale();
		
			ExRectF tmpCT{}, movCT{};
			obj->GetRect(tmpCT);
			staticico->GetRect(movCT);
			UIAnimation::Start(obj, movCT.left, tmpCT.left + ptOrg.x - 10, movCT.top, tmpCT.top + ptOrg.y - 32, AniEffect::Default, 20, FALSE, 1, TRUE, FALSE);
		
		}
	}
	else if (nCode == WMM_EASING)
	{
		if (nID == 1001)
		{
			auto easing = (info_Animation*)lParam;
			staticico->Move(easing->nCurrentX, easing->nCurrentY, CW_USEDEFAULT, CW_USEDEFAULT);
		}
	}
	return S_OK;
}
void testanimation(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 800, 500, L"hello Ani", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto bstatic = new UIStatic(window, 20, 70, 550, 400, NULL, 0, 0, 1001);
	bstatic->SetColor(color_background, UIColor(230, 231, 232, 255));
	bstatic->SetColor(color_border, UIColor(194, 195, 201, 255));
	bstatic->SetEvent(WMM_CLICK, OnAnimationEvent);
	bstatic->SetEvent(WMM_EASING, OnAnimationEvent);


	staticico = new UIStatic(window, 20, 70, 32, 32);
	staticico->SetBackgImage(L"icons\\lollipop.png", 0, 0, 0, 0, bif_disablescale);

	window->Show();
}
