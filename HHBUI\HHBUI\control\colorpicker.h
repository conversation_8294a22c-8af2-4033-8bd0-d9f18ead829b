﻿#pragma once
namespace HHBUI
{
	enum info_picker_config
	{
		eos_picker_palette = 4,                      //调色板
		eos_picker_colorh = 8,                       //调色板-HSV H
		eos_picker_colors = 16,                      //调色板-HSV S
		eos_picker_colora = 128,                     //调色板-HSV A
		WMM_PICKER_GETCOLOR = MCM_GETCOLOR,          //获取颜色事件
	};
	class TOAPI UIColorPicker : public UIControl
	{
	public:
		UIColorPicker(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
		//弹出或关闭
		void Popup();
		//关闭
		void Close();
		//是否已弹出
		BOOL IsPopup();
		//设置默认颜色
		void SetColour(UIColor dwColor);
		//获取颜色
		void GetColour(UIColor& dwColor);
		//设置下拉箭头颜色
		void SetColourArrow(UIColor dwColor);

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		static LRESULT CALLBACK OnWndMsgCrProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);
		static LRESULT CALLBACK On_free_Proc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);
		void Picker_paint(ps_context ps);
		void Picker_ColorS_paint(ps_context ps);
		void Picker_ColorA_paint(ps_context ps);
		void Picker_ColorH_paint(ps_context ps);
		void Picker_Info_paint(ps_context ps);

		void Init_colorh(INT uWidth, INT uHeight);
		void Init_colors(INT uWidth, INT uHeight);

		void Picker_ColorS_down(LPARAM lParam);
		void Picker_ColorA_down(LPARAM lParam);
		void Picker_ColorH_down(LPARAM lParam);



		struct ColorPicker_s
		{
			BOOL down = FALSE;
			FLOAT fAngle = 0.0f;
			ExRectF rect[9]{};
			WORD v = 0, s = 0, hue = 0;
			INT nProcessTime = 0, Index = -1;
			UIColorPicker* hinfo = nullptr;
			UIColorPicker* hColorA = nullptr;
			UIColorPicker* hColorH = nullptr;
			UIColorPicker* hColorS = nullptr;
			UIEdit* hEdit = nullptr;
			UIColor color = {}, colorarrow = {};
			UIImage *dstImgwhee = nullptr;
			UIBrush* hBrush = nullptr;
			UIWnd* pWnd = nullptr;
			HBITMAP bmp_handle = nullptr;
			DWORD* bmp_data = nullptr;
			std::wstring html = L"";
		}p_data;
	};
}
