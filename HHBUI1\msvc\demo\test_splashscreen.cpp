﻿#include "hhbui.h"
#include <thread>
#include <chrono>
#include <mutex>
using namespace HHBUI;

// 全局变量，用于存储闪屏对象指针
UISplashScreen* g_pSplash = nullptr;
std::mutex g_splashMutex; // 互斥锁，保护对全局闪屏对象的访问

// 添加一个辅助函数，检查UIImage是否有效
bool IsImageValid(UIImage* pImage)
{
    if (!pImage)
        return false;

    // 检查图像是否有有效尺寸
    UINT width = 0, height = 0;
    if (SUCCEEDED(pImage->GetSize(width, height)) && width > 0 && height > 0)
        return true;

    return false;
}

// 模拟耗时操作的线程函数
void SimulateLoading(UISplashScreen* pSplash)
{
    // 模拟初始化过程
    for (int i = 0; i <= 100; i += 5)
    {
        // 更新进度条
        pSplash->SetProgress(i);

        // 根据进度更新显示文本
        if (i < 20)
            pSplash->UpdateText(L"正在初始化系统资源...");
        else if (i < 40)
            pSplash->UpdateText(L"正在加载配置文件...");
        else if (i < 60)
            pSplash->UpdateText(L"正在准备用户界面...");
        else if (i < 80)
            pSplash->UpdateText(L"正在连接服务器...");
        else
            pSplash->UpdateText(L"即将完成，请稍候...");

        // 模拟耗时操作
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

// 关闭闪屏窗口的线程函数
void AutoCloseSplashScreen(UISplashScreen* pSplash, int milliseconds)
{
    if (!pSplash) return;

    // 等待指定时间
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));

    // 隐藏闪屏窗口
    pSplash->Hide();

    // 通知主线程可以删除对象了
    std::lock_guard<std::mutex> lock(g_splashMutex);
    if (g_pSplash == pSplash) {
        g_pSplash = nullptr; // 只有当全局指针仍然指向这个对象时才设为nullptr
    }
}

// 安全地删除闪屏对象
void SafeDeleteSplashScreen()
{
    UISplashScreen* pTemp = nullptr;

    {
        std::lock_guard<std::mutex> lock(g_splashMutex);
        if (g_pSplash)
        {
            pTemp = g_pSplash;
            g_pSplash = nullptr;
        }
    }

    // 在锁外删除对象，避免死锁
    if (pTemp)
    {
        pTemp->Hide();
        delete pTemp;
    }
}

// SplashScreen演示窗口的消息处理回调
LRESULT CALLBACK OnSplashTestWndProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
    auto window = (UIWnd*)pWnd;

    if (uMsg == WM_CLOSE)
    {
        // 窗口关闭时安全删除闪屏对象
        SafeDeleteSplashScreen();
        return 0;
    }

    return S_OK;
}

// 按钮事件处理回调
LRESULT CALLBACK OnSplashBtnProc(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
    auto window = (UIWnd*)pWnd;
    auto control = (UIControl*)UIView;

    if (nCode == WMM_CLICK)
    {
        // 安全删除之前的闪屏对象
        SafeDeleteSplashScreen();

        // 创建新的闪屏对象
        UISplashScreen* pNewSplash = new UISplashScreen();

        // 将新创建的对象保存到全局变量中
        {
            std::lock_guard<std::mutex> lock(g_splashMutex);
            g_pSplash = pNewSplash;
        }

        switch (nID)
        {
        case 1001: // 基本闪屏
        {
            // 显示不带自动关闭的闪屏
            pNewSplash->Show(L"这是一个基本的闪屏窗口",
                UIColor(30.0f / 255.0f, 30.0f / 255.0f, 30.0f / 255.0f, 1.0f),
                UIColor(1.0f, 1.0f, 1.0f, 1.0f),
                28,
                0);  // 设置为0表示不自动关闭

            // 创建线程在5秒后关闭闪屏
            std::thread closeThread(AutoCloseSplashScreen, pNewSplash, 5000);
            closeThread.detach();
        }
        break;

        case 1002: // 带进度条的闪屏
        {
            // 显示闪屏，不自动关闭
            pNewSplash->Show(L"正在加载，请稍候...",
                UIColor(30.0f / 255.0f, 30.0f / 255.0f, 30.0f / 255.0f, 1.0f),
                UIColor(1.0f, 1.0f, 1.0f, 1.0f),
                24,
                0);

            // 显示进度条
            pNewSplash->ShowProgress(TRUE);

            // 创建线程模拟加载过程
            std::thread loadingThread([pSplash = pNewSplash]() {
                // 确保在这个线程中不会再使用全局指针
                SimulateLoading(pSplash);

                // 延迟一下，让用户看到100%的进度
                std::this_thread::sleep_for(std::chrono::milliseconds(1500));

                // 隐藏闪屏窗口
                pSplash->Hide();

                // 通知主线程可以删除对象了
                std::lock_guard<std::mutex> lock(g_splashMutex);
                if (g_pSplash == pSplash) {
                    g_pSplash = nullptr;
                }
                });

            // 分离线程，让其独立运行
            loadingThread.detach();
        }
        break;

        case 1003: // 带背景图的闪屏
        {
            // 创建渐变背景的高级闪屏示例
            pNewSplash->Show(L"", // 先不显示文字，后面会通过动画更新
                UIColor(20.0f / 255.0f, 20.0f / 255.0f, 20.0f / 255.0f, 1.0f),  // 完全不透明的背景色
                UIColor(1.0f, 1.0f, 1.0f, 1.0f),
                32,  // 更大的字号
                0);  // 不自动关闭

            // 设置合适的尺寸和圆角效果
            pNewSplash->SetSize(960, 540);
            pNewSplash->SetRadius(15);

            // 使用图片作为背景
            LPVOID imgData = nullptr;
            size_t imgSize = 0;

            // 尝试多种路径加载图片
            const wchar_t* imagePaths[] = {
                L"icons\\IMG_0783.JPG",
                L"icons\\IMG_1236.JPG",
                L"icons\\1.jpg"
            };
            const int imageCount = sizeof(imagePaths) / sizeof(imagePaths[0]);

            // 获取模块路径，构建绝对路径
            WCHAR modulePath[MAX_PATH] = { 0 };
            GetModuleFileNameW(NULL, modulePath, MAX_PATH);
            WCHAR* pLastSlash = wcsrchr(modulePath, L'\\');
            if (pLastSlash) {
                *(pLastSlash + 1) = L'\0'; // 截断，只保留路径部分
            }

            // 尝试逐一加载图片
            bool imageLoaded = false;
            const wchar_t* loadedPath = nullptr;

            // 1. 先尝试直接路径
            for (int i = 0; i < imageCount; i++)
            {
                const wchar_t* imagePath = imagePaths[i];
                if (UIreadFile(imagePath, imgData, imgSize) && imgData && imgSize > 0)
                {
                    imageLoaded = true;
                    loadedPath = imagePath;
                    break;
                }
            }

            // 2. 如果直接路径失败，尝试模块路径
            if (!imageLoaded && pLastSlash)
            {
                WCHAR fullPath[MAX_PATH] = { 0 };

                for (int i = 0; i < imageCount; i++)
                {
                    const wchar_t* imagePath = imagePaths[i];
                    wcscpy_s(fullPath, modulePath);
                    wcscat_s(fullPath, imagePath);

                    if (UIreadFile(fullPath, imgData, imgSize) && imgData && imgSize > 0)
                    {
                        imageLoaded = true;
                        loadedPath = fullPath;
                        break;
                    }
                }
            }

            // 3. 如果模块路径失败，尝试上级目录
            if (!imageLoaded)
            {
                const wchar_t* upDirPaths[] = {
                    L"..\\icons\\IMG_0783.JPG",
                    L"..\\..\\icons\\IMG_0783.JPG"
                };
                const int upDirCount = sizeof(upDirPaths) / sizeof(upDirPaths[0]);

                for (int i = 0; i < upDirCount; i++)
                {
                    const wchar_t* path = upDirPaths[i];
                    if (UIreadFile(path, imgData, imgSize) && imgData && imgSize > 0)
                    {
                        imageLoaded = true;
                        loadedPath = path;
                        break;
                    }
                }
            }

            if (imageLoaded)
            {
                // 设置背景图 - 完全不透明模式
                pNewSplash->SetBackgImage(imgData, imgSize);

                // 显示进度条
                pNewSplash->ShowProgress(TRUE);

                // 创建线程模拟加载进度和更新文本
                std::thread loadingThread([pSplash = pNewSplash, loadedPathCopy = loadedPath]() {
                    // 首先显示应用名称和加载的图片路径（仅用于调试）
                    std::wstring message = L"PhotoMaster Pro\n已加载图片：";
                    if (loadedPathCopy)
                        message += loadedPathCopy;
                    pSplash->UpdateText(message.c_str());
                    std::this_thread::sleep_for(std::chrono::milliseconds(800));

                    // 模拟应用启动流程
                    const wchar_t* loadingTexts[] = {
                        L"PhotoMaster Pro\n准备资源中...",
                        L"PhotoMaster Pro\n加载图像处理引擎...",
                        L"PhotoMaster Pro\n初始化滤镜库...",
                        L"PhotoMaster Pro\n加载用户偏好设置...",
                        L"PhotoMaster Pro\n准备就绪，即将进入..."
                    };

                    for (int i = 0; i <= 100; i += 4)
                    {
                        // 更新进度
                        pSplash->SetProgress(i);

                        // 根据进度阶段更新文本
                        int textIndex = i / 25; // 0-4 共5个阶段
                        if (textIndex > 4) textIndex = 4;
                        pSplash->UpdateText(loadingTexts[textIndex]);

                        // 模拟加载延迟
                        std::this_thread::sleep_for(std::chrono::milliseconds(100));
                    }

                    // 完成后延迟一秒，让用户看到100%
                    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
                    });
                loadingThread.detach();
            }
            else
            {
                // 构建尝试过的路径列表
                std::wstring pathsInfo = L"图片资源加载失败\n尝试过以下路径:\n";

                // 添加直接路径
                for (int i = 0; i < imageCount && i < 2; i++) {
                    pathsInfo += imagePaths[i];
                    pathsInfo += L"\n";
                }

                // 添加模块路径示例
                if (pLastSlash) {
                    pathsInfo += modulePath;
                    pathsInfo += imagePaths[0];
                    pathsInfo += L"\n";
                }

                // 添加上级目录示例
                pathsInfo += L"..\\icons\\IMG_0783.JPG\n";

                // 显示详细错误信息
                pNewSplash->UpdateText(pathsInfo.c_str());
                pNewSplash->SetSize(700, 300);
                pNewSplash->SetRadius(10);
                pNewSplash->ShowProgress(TRUE);

                // 即使图片加载失败，也模拟进度
                std::thread simpleThread([pSplash = pNewSplash]() {
                    for (int i = 0; i <= 100; i += 5)
                    {
                        pSplash->SetProgress(i);
                        std::this_thread::sleep_for(std::chrono::milliseconds(150));
                    }
                    });
                simpleThread.detach();
            }

            // 创建线程在6秒后关闭闪屏
            std::thread closeThread(AutoCloseSplashScreen, pNewSplash, 6000);
            closeThread.detach();
        }
        break;

        case 1004: // 自定义样式闪屏
        {
            // 显示不带自动关闭的闪屏
            pNewSplash->Show(L"自定义样式的闪屏窗口",
                UIColor(0.0f, 102.0f / 255.0f, 204.0f / 255.0f, 1.0f),
                UIColor(1.0f, 1.0f, 1.0f, 1.0f),
                26,
                0);  // 设置为0表示不自动关闭

            // 设置窗口大小
            pNewSplash->SetSize(700, 200);

            // 设置窗口圆角
            pNewSplash->SetRadius(20);

            // 显示进度条
            pNewSplash->ShowProgress(TRUE);
            pNewSplash->SetProgress(60);

            // 创建线程在5秒后关闭闪屏
            std::thread closeThread(AutoCloseSplashScreen, pNewSplash, 5000);
            closeThread.detach();
        }
        break;
        }
    }

    return S_OK;
}

// 新增：显示不同背景图的闪屏
LRESULT CALLBACK OnImageSplashBtnProc(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
    auto window = (UIWnd*)pWnd;
    auto control = (UIControl*)UIView;

    if (nCode == WMM_CLICK)
    {
        // 安全删除之前的闪屏对象
        SafeDeleteSplashScreen();

        // 创建新的闪屏对象
        UISplashScreen* pNewSplash = new UISplashScreen();

        // 将新创建的对象保存到全局变量中
        {
            std::lock_guard<std::mutex> lock(g_splashMutex);
            g_pSplash = pNewSplash;
        }

        // 定义不同的图片和样式
        struct SplashImageStyle {
            const wchar_t* imagePath;
            const wchar_t* text;
            UIColor bgColor;
            UIColor textColor;
            INT fontSize;
            INT width;
            INT height;
            INT radius;
            BOOL showProgress;
        };

        // 根据按钮ID选择不同的样式
        SplashImageStyle style;
        switch (nID)
        {
        case 2001: // 清新自然风格
            style = {
                L"icons\\IMG_0783.JPG",
                L"自然风光 - 清新启程",
                UIColor(20.0f / 255.0f, 80.0f / 255.0f, 20.0f / 255.0f, 1.0f),  // 使用完全不透明的背景色
                UIColor(1.0f, 1.0f, 1.0f, 1.0f),
                32,
                900, 600, 10,
                FALSE
            };
            break;
        case 2002: // 科技风格
            style = {
                L"icons\\200.jpg",
                L"科技风格 - 创新未来",
                UIColor(10.0f / 255.0f, 10.0f / 255.0f, 60.0f / 255.0f, 1.0f),  // 使用完全不透明的背景色
                UIColor(0.0f, 200.0f / 255.0f, 1.0f, 1.0f),
                28,
                800, 450, 5,
                TRUE
            };
            break;
        case 2003: // 动态GIF风格
        {
            // 安全删除之前的闪屏对象
            SafeDeleteSplashScreen();

            // 创建新的闪屏对象
            UISplashScreen* pNewSplash = new UISplashScreen();

            // 将新创建的对象保存到全局变量中
            {
                std::lock_guard<std::mutex> lock(g_splashMutex);
                g_pSplash = pNewSplash;
            }

            // 先显示闪屏窗口，设置完全不透明的背景色
            pNewSplash->Show(L"动态风格 - 精彩呈现",
                UIColor(30.0f / 255.0f, 30.0f / 255.0f, 30.0f / 255.0f, 1.0f),
                UIColor(1.0f, 220.0f / 255.0f, 120.0f / 255.0f, 1.0f),
                30,
                0);

            // 设置窗口大小和圆角
            pNewSplash->SetSize(700, 500);
            pNewSplash->SetRadius(15);

            // 尝试多种图片格式和路径
            const wchar_t* imagesToTry[] = {
                L"icons\\121.gif",
                L"icons\\119.gif",
                L"icons\\120.gif",
                L"icons\\pthy.gif",
                L"icons\\3.png",    // 尝试PNG格式
                L"icons\\4.png",
                L"icons\\5.png",
                L"icons\\1.jpg",    // 尝试JPG格式
                L"icons\\100.jpg"
            };
            const int imageCount = sizeof(imagesToTry) / sizeof(imagesToTry[0]);

            // 使用内存方式加载图片
            LPVOID imgData = nullptr;
            size_t imgSize = 0;
            bool imageLoaded = false;
            const wchar_t* loadedPath = nullptr;

            // 获取模块路径
            WCHAR modulePath[MAX_PATH] = { 0 };
            GetModuleFileNameW(NULL, modulePath, MAX_PATH);
            WCHAR* pLastSlash = wcsrchr(modulePath, L'\\');
            if (pLastSlash) {
                *(pLastSlash + 1) = L'\0'; // 截断，只保留路径部分
            }

            // 尝试不同的图片和路径
            for (int i = 0; i < imageCount; i++)
            {
                const wchar_t* imagePath = imagesToTry[i];

                // 尝试直接路径
                if (UIreadFile(imagePath, imgData, imgSize) && imgData && imgSize > 0)
                {
                    imageLoaded = true;
                    loadedPath = imagePath;
                    break;
                }

                // 尝试模块路径
                if (pLastSlash)
                {
                    WCHAR fullPath[MAX_PATH] = { 0 };
                    wcscpy_s(fullPath, modulePath);
                    wcscat_s(fullPath, imagePath);

                    if (UIreadFile(fullPath, imgData, imgSize) && imgData && imgSize > 0)
                    {
                        imageLoaded = true;
                        loadedPath = imagePath; // 仅显示相对路径部分
                        break;
                    }
                }
            }

            // 如果上面的方法都失败，尝试直接使用UIImage加载
            if (!imageLoaded)
            {
                for (int i = 0; i < imageCount; i++)
                {
                    const wchar_t* imagePath = imagesToTry[i];
                    UIImage* pImage = new UIImage(imagePath);
                    if (pImage && IsImageValid(pImage))
                    {
                        pNewSplash->SetBackgImage(pImage);
                        imageLoaded = true;
                        loadedPath = imagePath;
                        break;
                    }
                    delete pImage;
                }
            }
            else
            {
                // 使用内存数据设置背景图
                pNewSplash->SetBackgImage(imgData, imgSize);
            }

            if (imageLoaded)
            {
                // 设置背景图成功
                std::wstring message = L"动态风格 - 精彩呈现\n已加载图片：";
                if (loadedPath)
                    message += loadedPath;
                pNewSplash->UpdateText(message.c_str());

                // 显示进度条
                pNewSplash->ShowProgress(TRUE);

                // 创建线程模拟加载进度
                std::thread loadingThread([pSplash = pNewSplash]() {
                    // 模拟加载进度
                    for (int i = 0; i <= 100; i += 2)
                    {
                        pSplash->SetProgress(i);
                        std::this_thread::sleep_for(std::chrono::milliseconds(50));
                    }
                    });
                loadingThread.detach();
            }
            else
            {
                // 构建尝试过的路径列表
                std::wstring pathsInfo = L"图片加载失败\n尝试过以下路径:\n";
                for (int i = 0; i < imageCount && i < 3; ++i) {
                    pathsInfo += imagesToTry[i];
                    pathsInfo += L"\n";
                }
                if (imageCount > 3) {
                    pathsInfo += L"...等";
                    pathsInfo += std::to_wstring(imageCount);
                    pathsInfo += L"个路径";
                }

                // 显示详细错误信息
                pNewSplash->UpdateText(pathsInfo.c_str());
            }

            // 创建线程在6秒后关闭闪屏
            std::thread closeThread(AutoCloseSplashScreen, pNewSplash, 6000);
            closeThread.detach();

            return S_OK;
        }
        break;
        case 2004: // 简约商务风格
            style = {
                L"icons\\AA1wuPe0.webp",
                L"简约商务 - 专业可靠",
                UIColor(0.0f, 0.0f, 0.0f, 1.0f),  // 使用完全不透明的背景色
                UIColor(1.0f, 1.0f, 1.0f, 1.0f),
                26,
                800, 450, 0,  // 无圆角的矩形
                TRUE
            };
            break;
        }

        // 使用内存方式加载图片
        LPVOID imgData = nullptr;
        size_t imgSize = 0;

        // 尝试多种路径加载图片
        std::vector<const wchar_t*> paths;

        // 1. 先尝试相对路径
        paths.push_back(style.imagePath);

        // 2. 获取模块路径，构建绝对路径
        WCHAR modulePath[MAX_PATH] = { 0 };
        GetModuleFileNameW(NULL, modulePath, MAX_PATH);
        WCHAR* pLastSlash = wcsrchr(modulePath, L'\\');
        WCHAR fullPath[MAX_PATH] = { 0 };

        if (pLastSlash)
        {
            *(pLastSlash + 1) = L'\0'; // 截断，只保留路径部分
            wcscpy_s(fullPath, modulePath);
            wcscat_s(fullPath, style.imagePath);
            paths.push_back(fullPath);
        }

        // 3. 尝试上级目录
        paths.push_back(L"..\\icons\\IMG_0783.JPG");
        paths.push_back(L"..\\..\\icons\\IMG_0783.JPG");

        // 尝试逐一加载图片
        bool imageLoaded = false;
        const wchar_t* loadedPath = nullptr;

        for (const auto& path : paths)
        {
            if (UIreadFile(path, imgData, imgSize) && imgData && imgSize > 0)
            {
                imageLoaded = true;
                loadedPath = path;
                break;
            }
        }

        // 1. 先显示闪屏窗口，设置完全不透明的背景色
        pNewSplash->Show(style.text,
            style.bgColor,  // 已经使用完全不透明的背景色
            style.textColor,
            style.fontSize,
            0);

        // 2. 设置窗口大小和圆角
        pNewSplash->SetSize(style.width, style.height);
        pNewSplash->SetRadius(style.radius);

        // 3. 如果图片加载成功，设置背景图
        if (imageLoaded)
        {
            // 设置背景图
            pNewSplash->SetBackgImage(imgData, imgSize);

            // 如果需要显示进度条
            if (style.showProgress)
            {
                pNewSplash->ShowProgress(TRUE);

                // 创建线程模拟加载进度
                std::thread loadingThread([pSplash = pNewSplash]() {
                    // 模拟加载进度
                    for (int i = 0; i <= 100; i += 2)
                    {
                        pSplash->SetProgress(i);
                        std::this_thread::sleep_for(std::chrono::milliseconds(50));
                    }
                    });
                loadingThread.detach();
            }
        }
        else
        {
            // 构建尝试过的路径列表
            std::wstring pathsInfo = L"背景图片加载失败\n尝试过以下路径:\n";
            for (size_t i = 0; i < paths.size() && i < 3; ++i) {
                pathsInfo += paths[i];
                pathsInfo += L"\n";
            }
            if (paths.size() > 3) {
                pathsInfo += L"...等";
                pathsInfo += std::to_wstring(paths.size());
                pathsInfo += L"个路径";
            }

            // 显示详细错误信息
            pNewSplash->UpdateText(pathsInfo.c_str());
        }

        // 创建线程在6秒后关闭闪屏
        std::thread closeThread(AutoCloseSplashScreen, pNewSplash, 6000);
        closeThread.detach();
    }

    return S_OK;
}

void testsplashscreen(HWND hWnd)
{
    auto window = new UIWnd(0, 0, 800, 600, L"闪屏窗口 (SplashScreen) 演示", 0, 0,
        UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT |
        UISTYLE_TITLE | UISTYLE_MOVEABLE, hWnd, nullptr, 0, OnSplashTestWndProc);

    window->SetBackgColor(UIColor(253, 253, 255, 255));
    window->SetBorderColor(UIColor(20, 126, 255, 255));
    window->SetShadowColor(UIColor(20, 126, 255, 155));
    window->SetRadius(10);

    // 说明文本
    auto introStatic = new UIStatic(window, 20, 70, 760, 60, L"SplashScreen组件用于显示应用程序启动过程中的闪屏窗口，"
        L"可以设置自动关闭时间、显示进度条、自定义背景图片和样式等。\n"
        L"点击下方按钮查看不同样式的闪屏效果。");
    introStatic->SetColor(color_background, UIColor(240, 240, 240, 255));
    introStatic->SetColor(color_border, UIColor(220, 220, 220, 255));
    introStatic->SetRadius(5, 5, 5, 5);
    introStatic->SetPadding(10, 10);

    // 基本闪屏按钮
    auto basicBtn = new UIButton(window, 20, 150, 370, 50, L"基本闪屏窗口", 0, 0, 1001);
    basicBtn->SetColor(color_text_normal, UIColor(255, 255, 255, 255));
    basicBtn->SetCrBkg(UIColor(20, 126, 255, 255), UIColor(40, 146, 255, 255), UIColor(10, 116, 245, 255));
    basicBtn->SetRadius(5.0f);
    basicBtn->SetEvent(WMM_CLICK, OnSplashBtnProc);

    // 带进度条的闪屏按钮
    auto progressBtn = new UIButton(window, 410, 150, 370, 50, L"带进度条的闪屏", 0, 0, 1002);
    progressBtn->SetColor(color_text_normal, UIColor(255, 255, 255, 255));
    progressBtn->SetCrBkg(UIColor(20, 126, 255, 255), UIColor(40, 146, 255, 255), UIColor(10, 116, 245, 255));
    progressBtn->SetRadius(5.0f);
    progressBtn->SetEvent(WMM_CLICK, OnSplashBtnProc);

    // 带背景图的闪屏按钮
    auto imageBtn = new UIButton(window, 20, 220, 370, 50, L"带背景图的闪屏", 0, 0, 1003);
    imageBtn->SetColor(color_text_normal, UIColor(255, 255, 255, 255));
    imageBtn->SetCrBkg(UIColor(76, 175, 80, 255), UIColor(96, 195, 100, 255), UIColor(56, 155, 60, 255));
    imageBtn->SetRadius(5.0f);
    imageBtn->SetEvent(WMM_CLICK, OnSplashBtnProc);

    // 自定义样式的闪屏按钮
    auto customBtn = new UIButton(window, 410, 220, 370, 50, L"自定义样式闪屏", 0, 0, 1004);
    customBtn->SetColor(color_text_normal, UIColor(255, 255, 255, 255));
    customBtn->SetCrBkg(UIColor(76, 175, 80, 255), UIColor(96, 195, 100, 255), UIColor(56, 155, 60, 255));
    customBtn->SetRadius(5.0f);
    customBtn->SetEvent(WMM_CLICK, OnSplashBtnProc);

    // 新增：主题背景闪屏分隔标题
    auto themeTitle = new UIStatic(window, 20, 290, 760, 30, L"主题背景闪屏示例", 0, 0, 0);
    themeTitle->SetColor(color_text_normal, UIColor(50, 50, 50, 255));
    themeTitle->SetFontFromFamily(nullptr, 20, FontStyle::Bold);

    // 新增：自然风格背景闪屏按钮
    auto natureBtn = new UIButton(window, 20, 330, 370, 50, L"自然风光主题", 0, 0, 2001);
    natureBtn->SetColor(color_text_normal, UIColor(255, 255, 255, 255));
    natureBtn->SetCrBkg(UIColor(46, 125, 50, 255), UIColor(66, 145, 70, 255), UIColor(26, 105, 30, 255));
    natureBtn->SetRadius(5.0f);
    natureBtn->SetEvent(WMM_CLICK, OnImageSplashBtnProc);

    // 新增：科技风格背景闪屏按钮
    auto techBtn = new UIButton(window, 410, 330, 370, 50, L"科技风格主题", 0, 0, 2002);
    techBtn->SetColor(color_text_normal, UIColor(255, 255, 255, 255));
    techBtn->SetCrBkg(UIColor(13, 71, 161, 255), UIColor(33, 91, 181, 255), UIColor(0, 51, 141, 255));
    techBtn->SetRadius(5.0f);
    techBtn->SetEvent(WMM_CLICK, OnImageSplashBtnProc);

    // 新增：动态GIF背景闪屏按钮
    auto gifBtn = new UIButton(window, 20, 400, 370, 50, L"动态GIF主题", 0, 0, 2003);
    gifBtn->SetColor(color_text_normal, UIColor(255, 255, 255, 255));
    gifBtn->SetCrBkg(UIColor(216, 27, 96, 255), UIColor(236, 47, 116, 255), UIColor(196, 7, 76, 255));
    gifBtn->SetRadius(5.0f);
    gifBtn->SetEvent(WMM_CLICK, OnImageSplashBtnProc);

    // 新增：简约商务背景闪屏按钮
    auto businessBtn = new UIButton(window, 410, 400, 370, 50, L"简约商务主题", 0, 0, 2004);
    businessBtn->SetColor(color_text_normal, UIColor(255, 255, 255, 255));
    businessBtn->SetCrBkg(UIColor(55, 71, 79, 255), UIColor(75, 91, 99, 255), UIColor(35, 51, 59, 255));
    businessBtn->SetRadius(5.0f);
    businessBtn->SetEvent(WMM_CLICK, OnImageSplashBtnProc);

    // 说明标签
    auto noteStatic = new UIStatic(window, 20, 470, 760, 110, L"提示：\n"
        L"1. 带进度条的闪屏示例使用异步线程模拟加载过程\n"
        L"2. 所有闪屏窗口都会显示5-6秒钟，便于观察效果\n"
        L"3. 在实际应用中，闪屏通常在主窗口加载前显示\n"
        L"4. 主题背景闪屏展示了不同风格设计和效果，适合不同类型的应用", 0, 0, 0);
    noteStatic->SetColor(color_background, UIColor(255, 243, 224, 255));
    noteStatic->SetColor(color_border, UIColor(255, 224, 178, 255));
    noteStatic->SetColor(color_text_normal, UIColor(230, 81, 0, 255));
    noteStatic->SetRadius(5, 5, 5, 5);
    noteStatic->SetPadding(10, 10);

    window->Show();
}