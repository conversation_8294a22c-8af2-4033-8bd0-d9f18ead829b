﻿#include "hhbui.h"
#include "demo.h"
#include "resource_loader.h"

using namespace HHBUI;
LRESULT CALLBACK OnViewMsg_image_Proc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (uMsg == WM_TIMER)
	{
		if (nID == 1003)
		{

		}
		else if (nID == 1004)
		{

		}
	}

	return S_OK;
}


void testimagebox(HWND hWnd)
{
	using namespace HHBUI;

	// 从DLL资源加载Logo图片
	std::vector<BYTE> logoImageData;
	UIImage* logoImage = nullptr;

	if (ResDLL::LoadPngFromResourceDLL(ResDLL::IDP_LOGO_96, logoImageData))
	{
		// 从内存创建图像
		logoImage = new UIImage(logoImageData.data(), logoImageData.size());
	}

	// 从DLL资源加载锁图标
	std::vector<BYTE> lockImageData;
	UIImage* lockImage = nullptr;

	if (ResDLL::LoadPngFromResourceDLL(ResDLL::IDP_LOCK_96, lockImageData))
	{
		// 从内存创建图像
		lockImage = new UIImage(lockImageData.data(), lockImageData.size());
	}

	// 从DLL资源加载主页图标
	std::vector<BYTE> homeImageData;
	UIImage* homeImage = nullptr;

	if (ResDLL::LoadPngFromResourceDLL(ResDLL::IDP_HOME_96, homeImageData))
	{
		// 从内存创建图像
		homeImage = new UIImage(homeImageData.data(), homeImageData.size());
	}

	// 从DLL资源加载动画GIF
	std::vector<BYTE> loginGifData;
	UIImage* loginGif = nullptr;

	if (ResDLL::LoadGifFromResourceDLL(ResDLL::IDG_LOGIN, loginGifData))
	{
		// 从内存创建GIF图像
		loginGif = new UIImage(loginGifData.data(), loginGifData.size());
	}

	// 创建窗口
	auto window = new UIWnd(0, 0, 800, 600, L"ImageBox测试 - 从资源DLL加载", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_CENTERWINDOW);
	window->SetBackgColor(UIColor(255, 255, 255, 255));

	// 显示从资源DLL加载的Logo
	auto imgbox1 = new UIImageBox(window, 50, 50, 96, 96, NULL, 0, 0, 1000);
	if (logoImage)
	{
		imgbox1->SetImg(logoImage);
		imgbox1->SetTipsText(L"Logo图标 (从资源DLL加载)");
	}
	else
	{
		// 如果DLL资源加载失败，使用本地图片
		imgbox1->SetImg(L"icons\\1.png");
		imgbox1->SetTipsText(L"Logo图标 (本地加载)");
	}

	// 显示从资源DLL加载的锁图标
	auto imgbox2 = new UIImageBox(window, 200, 50, 96, 96, NULL, 0, 0, 1001);
	if (lockImage)
	{
		imgbox2->SetImg(lockImage);
		imgbox2->SetTipsText(L"锁图标 (从资源DLL加载)");
	}
	else
	{
		imgbox2->SetImg(L"icons\\2.png");
		imgbox2->SetTipsText(L"锁图标 (本地加载)");
	}

	// 显示从资源DLL加载的主页图标
	auto imgbox3 = new UIImageBox(window, 350, 50, 96, 96, NULL, 0, 0, 1002);
	if (homeImage)
	{
		imgbox3->SetImg(homeImage);
		imgbox3->SetTipsText(L"主页图标 (从资源DLL加载)");
	}
	else
	{
		imgbox3->SetImg(L"icons\\3.png");
		imgbox3->SetTipsText(L"主页图标 (本地加载)");
	}

	// 显示从资源DLL加载的GIF动画
	auto imgbox4 = new UIImageBox(window, 500, 50, 200, 200, NULL, 0, 0, 1003);
	if (loginGif)
	{
		imgbox4->SetImg(loginGif);
		imgbox4->SetTipsText(L"登录动画 (从资源DLL加载)");
	}
	else
	{
		imgbox4->SetImg(L"icons\\119.gif");
		imgbox4->SetTipsText(L"登录动画 (本地加载)");
	}

	// 添加一些本地图片示例
	auto imgbox5 = new UIImageBox(window, 50, 200, 320, 240, NULL, 0, 0, 1004);
	imgbox5->SetImg(L"icons\\IMG_0783.JPG");
	imgbox5->SetTipsText(L"本地图片");
	imgbox5->SetRadius(10, 10, 10, 10);
	imgbox5->SetColor(color_border, UIColor(200, 200, 200, 255));

	// 显示窗口
	window->Show();
}