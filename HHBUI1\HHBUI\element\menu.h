﻿#pragma once
namespace HHBUI
{
	class TOAPI UIMenu
	{
	public:
		UIMenu();
		~UIMenu();
		/*
		 * @brief 添加菜单项目
		 * @param  dwFlags  标志参数参考:https://learn.microsoft.com/zh-cn/windows/win32/api/winuser/nf-winuser-appendmenuw
		 * @param  uIDNewItem  菜单项目ID 建议1000开始,如果此项需要子菜单 直接引用子菜单句柄 而dwFlags需要设置MF_POPUP。
		 * @param  lpwzName  菜单项目标题
		 * @param  hbmpItem  菜单项目图标(建议16*16)
		 * @return [BOOL]
		 */
		BOOL Append(DWORD dwFlags, UINT_PTR uIDNewItem, LPCWSTR lpwzName = nullptr, UIImage *hbmpItem = nullptr);
		//禁止菜单项目
		BOOL EnableItem(UINT uIDNewItem, BOOL wEnable);
		/*
		 * @brief 弹出菜单
		 * @param  uFlags  标志参考：https://learn.microsoft.com/zh-cn/windows/win32/api/winuser/nf-winuser-trackpopupmenu
		 * @param  x,y  弹出坐标 默认鼠标位置
		 * @param  crText 文本颜色
		 * @param  crTextHot 文本点燃颜色
		 * @param  crBackground  背景颜色 (默认跟随父窗口风格)
		 * @param  crShadow 阴影颜色 (默认跟随父窗口风格)
		 * @param  crBorder 边框颜色 (默认跟随父窗口风格)
		 * @param  crItemHot 项目点燃背景颜色
		 * @param  pfnCallback  消息事件
		 * @param  IsRadius 是否开启圆角 (默认跟随父窗口风格)
		 * @return [BOOL]
		 */
		BOOL Popup(UIWnd* pWnd, DWORD uFlags = 0, INT x = 0, INT y = 0, UIColor crText = {}, UIColor crTextHot = {}, UIColor crBackground = {}, UIColor crShadow = {}, UIColor crBorder = {}, UIColor crItemHot = {}, MsgPROC pfnCallback = NULL, BOOL IsRadius = FALSE);
		static BOOL Popup(UIWnd* pWnd, HMENU hMenu, INT x = 0, INT y = 0);
		//关闭菜单
		BOOL End(BOOL Destroy = false);
		//获取菜单项目标题
		INT GetString(UINT uIDNewItem, LPCWSTR& lpNewString);
		//修改菜单项目
		BOOL SetItem(DWORD dwFlags, UINT uIDNewItem, LPCWSTR lpwzName = nullptr, UIImage *hbmpItem = nullptr);
		//删除菜单项目
		BOOL Remove(UINT uIDNewItem);
		//取菜单句柄
		HMENU GetMenu();


	protected:
		HMENU m_hMenu = 0;

	};
}
