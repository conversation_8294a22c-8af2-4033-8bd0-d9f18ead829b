﻿//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by resource.rc
//

// 资源类型前缀
// IDI_ - 图标
// IDB_ - 位图
// IDP_ - PNG
// IDJ_ - JPEG/JPG
// IDG_ - GIF

// Administrator Icons 管理员图标
#define IDP_ADMIN_48                    1001   // 48x48 男管理员
#define IDP_ADMIN_96                    1002   // 96x96 男管理员

// Animation Icons 启动动画图标
#define IDP_ANIMATION_96                1101   // 96x96 启动动画

// Backgrounds 背景图片
#define IDP_BG_EVA                      1201   // 二次元背景-动漫角色
#define IDP_BG_ANIME                    1202   // 二次元背景-动漫角色
#define IDP_BG_FUJI                     1203   // 富士山-枫叶-樱花

// Home Icons 主页图标
#define IDP_HOME_48                     1301   // 48x48 主页
#define IDP_HOME_96                     1302   // 96x96 主页
#define IDP_HOME_144                    1303   // 144x144 主页
#define IDP_HOME_240                    1304   // 240x240 主页

// Lock Icons 锁上的锁图标
#define IDP_LOCK_48                     1401   // 48x48 锁上的锁
#define IDP_LOCK_96                     1402   // 96x96 锁上的锁
#define IDP_LOCK_100                    1403   // 100x100 锁上的锁
#define IDP_LOCK_144                    1404   // 144x144 锁上的锁
#define IDP_LOCK_240                    1405   // 240x240 锁上的锁

// Unlock Icons 打开的锁图标
#define IDP_UNLOCK_48                   1501   // 48x48 打开的锁
#define IDP_UNLOCK_96                   1502   // 96x96 打开的锁
#define IDP_UNLOCK_100                  1503   // 100x100 打开的锁
#define IDP_UNLOCK_144                  1504   // 144x144 打开的锁
#define IDP_UNLOCK_240                  1505   // 240x240 打开的锁

// Login Icons 登录图标
#define IDG_LOGIN                       1601   // 登录按钮图标GIF
#define IDP_LOGIN_100                   1602   // 登录按钮图标PNG

// Logo Icons Logo图标
#define IDP_LOGO_60                     1701   // 60x60 登录窗口Logo “T” 图标
#define IDP_LOGO_96                     1702   // 96x96 登录窗口Logo “T” 图标

// 新对象的下一组默认值
//
#ifdef APSTUDIO_INVOKED                        // 如果APSTUDIO_INVOKED被定义，则执行以下代码
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        1800   // 下一个资源值
#define _APS_NEXT_COMMAND_VALUE         40001  // 下一个命令值
#define _APS_NEXT_CONTROL_VALUE         1001   // 下一个控件值
#define _APS_NEXT_SYMED_VALUE           101    // 下一个符号值
#endif                                         // APSTUDIO_READONLY_SYMBOLS
#endif                                         // APSTUDIO_INVOKED
