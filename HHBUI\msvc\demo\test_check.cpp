﻿#include "hhbui.h"

using namespace HHBUI;
LRESULT CALLBACK OnWndMsg_CheckProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (obj)
	{
		if (uMsg == WM_LBUTTONUP)
		{
			if (obj->GetID() == UISTYLE_BTN_HELP)
			{
				static BOOL fChecked = FALSE; INT rel = 0;
				if (fChecked)
					rel = window->PopupMsg(L"我是一个信息框？", L"提示：", MB_YESNO | MB_ICONQUESTION);
				else
					rel = window->PopupMsg(L"我是一个信息框？", L"提示：", MB_YESNO | MB_ICONQUESTION, L"不再提示", &fChecked);
				output(rel, fChecked);
			}
			else if (obj->GetID() == 2001)
			{
				auto fParent = (UIControl*)window->FindUIView(L"1000");
				auto View = (UIControl*)window->FindUIView(L"1001");
				View->SetParent(fParent);
			}
		}
	}
	return S_OK;
}
LRESULT CALLBACK OnButton_Check_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (nCode == WMM_CHECK)
	{
		output(wParam);
	}
	return S_OK;
}

void testcheck(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 700, 500, L"hello Check", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_BTN_HELP | UISTYLE_MOVEABLE, hWnd, NULL, 0, OnWndMsg_CheckProc);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto bCheck = new UICheck(window, 20, 70, 120, 20, L"选择框-在左边");
	bCheck->SetEvent(WMM_CHECK, OnButton_Check_Event);
	bCheck->SetState(indeterminate);
	//bCheck->SetColor(color_text_normal, UIColor(0, 108, 190, 255));

	auto bCheck1 = new UICheck(window, 20, 120, 120, 20, L"选择框-在右边");
	bCheck1->SetColor(color_text_normal, UIColor(184, 2, 11, 255));
	bCheck1->SetBoxPosition(FALSE);
	bCheck1->SetState(indeterminate);
	bCheck1->SetEvent(WMM_CHECK, OnButton_Check_Event);

	auto bCheck2 = new UICheck(window, 20, 170, 120, 20, L"单选框-在左边");
	bCheck2->SetRadio(TRUE);
	bCheck2->SetColor(color_text_normal, UIColor(184, 2, 11, 255));
	bCheck2->SetEvent(WMM_CHECK, OnButton_Check_Event);

	auto bCheck3 = new UICheck(window, 20, 220, 120, 20, L"单选框-在右边", 1001);
	bCheck3->SetRadio(TRUE);
	bCheck3->SetColor(color_text_normal, UIColor(184, 2, 11, 255));
	bCheck3->SetBoxPosition(FALSE);
	bCheck3->SetEvent(WMM_CHECK, OnButton_Check_Event);

	auto bstatic = new UIStatic(window, 200, 70, 300, 300, L"单选组", 0, 0, 1000, Top | Center);
	bstatic->SetColor(color_background, UIColor(230, 231, 232, 255));
	bstatic->SetColor(color_border, UIColor(194, 195, 201, 255));
	bstatic->SetColor(color_text_normal, UIColor(184, 2, 11, 255));

	auto bCheck4 = new UICheck(bstatic, 20, 60, 220, 20, L"单选框-组中只能选中一个");
	bCheck4->SetRadio(TRUE);
	bCheck4->SetColor(color_text_normal, UIColor(184, 2, 11, 255));
	bCheck4->SetEvent(WMM_CHECK, OnButton_Check_Event);

	auto bCheck5 = new UICheck(bstatic, 20, 100, 220, 20, L"单选框-组中只能选中一个");
	bCheck5->SetRadio(TRUE);
	bCheck5->SetColor(color_text_normal, UIColor(184, 2, 11, 255));
	bCheck5->SetEvent(WMM_CHECK, OnButton_Check_Event);

	auto bCheck6 = new UICheck(bstatic, 20, 140, 220, 20, L"单选框-组中只能选中一个");
	bCheck6->SetRadio(TRUE);
	bCheck6->SetColor(color_text_normal, UIColor(184, 2, 11, 255));
	bCheck6->SetEvent(WMM_CHECK, OnButton_Check_Event);

	auto btn1 = new UIButton(window, 20, 270, 100, 36, L"置父切换", 0, 0, 2001);
	btn1->SetStyle(fill, primary);
	btn1->SetRadius(0);
	window->Show();
}