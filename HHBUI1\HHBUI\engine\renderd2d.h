﻿#pragma once
#define GDIPVER 0x110   /*定义高版本的GDI+(1.1)*/
#pragma comment(lib, "Gdiplus.lib") /*链接GDIPlus库*/
#include <GdiPlus.h>
#include "render_api.h"
#include <string>

namespace HHBUI
{
	struct UIDevice
	{
		//设备名称
		LPCWSTR deviceName = nullptr;
		//设备索引
		int deviceIndex = 0;
	};
	class TOAPI UIDrawContext
	{
		typedef HRESULT(WINAPI* PFN_DCompositionCreateDevice2)(IUnknown* renderingDevice, REFIID iid, void** dcompositionDevice);
	public:
		//初始化并指定渲染设备索引 通过EnumRenderDevice获取。 -1 = 自动选择
		static HRESULT Init(int device);
		static void UnInit();
		static ID2D1Bitmap1* CreateBitmap(INT width, INT height);
		static BOOL ToWicBitmap(ID2D1Bitmap* pBitmap, INT nWidth, INT nHeight, IWICBitmap** pWICBitmap);
		static BOOL ToWicImage(ID2D1Image* pImg, D2D1_SIZE_U size, ID2D1Bitmap** pBitmap);
		static void DrawImageRotate(ID2D1DeviceContext* pContext, ID2D1Bitmap* pBitmapSource, FLOAT Left, FLOAT Top, FLOAT fAngle);
		static void DrawBrushRotate(ID2D1DeviceContext* pContext, UIbrush* pBrushSource, FLOAT Left, FLOAT Top, FLOAT Width, FLOAT Height, FLOAT fAngle);
		static D2D1_POINT_2F RotatePoint(D2D1_POINT_2F point, D2D1_POINT_2F center, FLOAT angle);
		static void DrawRotatedLines(ID2D1DeviceContext* pDeviceContext, ID2D1Brush* brush, FLOAT uLeft, FLOAT uTop, FLOAT uWidth, FLOAT uHeight, FLOAT angle);
		/*枚举渲染设备列表*/
		static BOOL EnumRenderDevice(UIDevice *deviceName, int& numDevices);
		inline static struct drawcontext_s
		{
			PFN_DCompositionCreateDevice2 DCompositionCreateDevice2 = nullptr;
			HMODULE module_dcomp = nullptr;
			ULONG_PTR gdip_hToken = 0;
			ID2D1Factory1* d2d_factory = nullptr;
			IDWriteFactory* dwrite_factory = nullptr;
			IDWriteFactory4* dwrite_factory4 = nullptr;
			IWICImagingFactory* wic_factory = nullptr;
			ID2D1Device* d2d_device = nullptr;
			ID2D1DeviceContext* d2d_dc = nullptr;
			ID2D1GdiInteropRenderTarget* d2d_gdiInterop = nullptr;
			ID3D11Device* pd3dDevice = nullptr;
			ID3D11DeviceContext* pd3dDeviceContext = nullptr;
			UIFPSCounter fpsCounter;  // Assume default constructor is available for UIFPSCounter
			std::atomic_uint fpsCache = 0;

			// 新增高级渲染管理器
			class IRenderManager* advanced_render_manager = nullptr;
			class UIShaderManager* shader_manager = nullptr;
			class UIBufferManager* buffer_manager = nullptr;
			class UIInputLayoutManager* input_layout_manager = nullptr;

			// 渲染统计
			RenderStats render_stats = {};
			bool debug_mode = false;
			bool advanced_features_enabled = false;

		} ToList;

		// 高级渲染功能
		static HRESULT InitAdvancedRendering();
		static void ShutdownAdvancedRendering();
		static IRenderManager* GetAdvancedRenderManager();
		static HRESULT EnableAdvancedFeatures(bool enable);
		static bool IsAdvancedFeaturesEnabled();

		// 着色器管理
		static HRESULT CreateCustomShader(ShaderType type, LPCWSTR source_code,
			LPCSTR entry_point, IShader** shader);
		static HRESULT LoadShaderFromFile(ShaderType type, LPCWSTR file_path,
			LPCSTR entry_point, IShader** shader);

		// 缓冲区管理
		static HRESULT CreateVertexBuffer(const void* vertices, uint32_t vertex_count,
			uint32_t vertex_size, bool dynamic, IBuffer** buffer);
		static HRESULT CreateIndexBuffer(const uint32_t* indices, uint32_t index_count,
			bool dynamic, IBuffer** buffer);
		static HRESULT CreateConstantBuffer(uint32_t size, bool dynamic, IBuffer** buffer);

		// 纹理管理
		static HRESULT CreateTexture2D(uint32_t width, uint32_t height, DXGI_FORMAT format,
			const void* initial_data, bool render_target, ITexture** texture);
		static HRESULT LoadTextureFromFile(LPCWSTR file_path, ITexture** texture);

		// 渲染状态管理
		static HRESULT CreateRenderState(IRenderState** render_state);
		static HRESULT SetBlendMode(bool enable, D3D11_BLEND src_blend, D3D11_BLEND dest_blend);
		static HRESULT SetDepthTest(bool enable, bool write_enable, D3D11_COMPARISON_FUNC func);

		// 高级绘制功能
		static HRESULT DrawPrimitive(D3D11_PRIMITIVE_TOPOLOGY topology, uint32_t vertex_count, uint32_t start_vertex = 0);
		static HRESULT DrawIndexedPrimitive(D3D11_PRIMITIVE_TOPOLOGY topology, uint32_t index_count, uint32_t start_index = 0, uint32_t base_vertex = 0);

		// 性能监控
		static const RenderStats& GetRenderStats();
		static void ResetRenderStats();
		static uint64_t GetGPUMemoryUsage();
		static float GetFrameTime();
		static float GetGPUTime();

		// 高级渲染控制
		static HRESULT BeginAdvancedFrame();
		static HRESULT EndAdvancedFrame();
		static HRESULT PresentAdvanced(bool vsync = true);
		static HRESULT SetAdvancedViewport(float x, float y, float width, float height);
		static HRESULT ClearAdvancedRenderTarget(float r = 0.0f, float g = 0.0f, float b = 0.0f, float a = 1.0f);

		// GDI+集成接口
		static HRESULT ConvertGdiPlusBitmapToD2D(Gdiplus::Bitmap* gdi_bitmap, ID2D1Bitmap** d2d_bitmap);
		static HRESULT BeginHybridRendering();
		static HRESULT EndHybridRendering();
		static HRESULT SwitchToGdiPlusMode();
		static HRESULT SwitchToD2DMode();

		// 调试和性能分析
		static std::string GetPerformanceReport();
		static HRESULT ExportPerformanceData(LPCWSTR file_path);
		static void SetDebugMode(bool enable);
		static bool IsDebugMode();


	};
}
