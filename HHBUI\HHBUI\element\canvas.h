﻿#pragma once

namespace HHBUI
{
	/// 文本格式 支持系统DT_开头常量
	enum TextFormat
	{
		    Left = 0x00000000,							// 文本格式：左对齐
			Top = 0x00000000,							// 文本格式：顶对齐
			Center = 0x00000001,						// 文本格式：水平居中
			Right = 0x00000002,							// 文本格式：右对齐
			Middle = 0x00000004,						// 文本格式：垂直居中
			Bottom = 0x00000008,						// 文本格式：底对齐
			WordBreak = 0x00000010,						// 文本格式：按字断开
			SingleLine = 0x00000020,					// 文本格式：单行
			ExpandTabs = 0x00000040,					// 文本格式：展开制表符 
			TabStop = 0x00000080,						// 文本格式：自定制表符宽度 (高字节表示制表符宽度)
			NoClip = 0x00000100,						// 文本格式：忽略裁剪
			ExternalLeading = 0x00000200,				// 文本格式：
			//占用一个 0x00000400 的位
			Prefix = 0x00000800,						// 文本格式：转义前缀符 (&转义为下划线)
			PathEllipsis = 0x00004000,					// 文本格式：路径省略 (路径中间省略)
			EndEllipsis = 0x00008000,					// 文本格式：末尾省略 (末尾变为省略号)
			RtlReading = 0x00020000,					// 文本格式：从右往左
			WordEllipsis = 0x00040000,					// 文本格式：单词忽略 (按单词省略)
			NoFullWidthCharBreak = 0x00080000,			// 文本格式：
			HidePrefix = 0x00100000,					// 文本格式：隐藏前缀符 (被转义的&不显示下划线)

			Vertical = 0x00200000						// 文本格式：垂直显示
	};
	enum ImageMode
	{
		    Default = 0,
			ScaleFill = 1,
			ScaleCenter = 2,
			Tile = 3,
			Mirror = 4,

			LeftTop = 11,
			CenterTop = 12,
			RightTop = 13,
			LeftMiddle = 14,
			CenterMiddle = 15,
			RightMiddle = 16,
			LeftBottom = 17,
			CenterBottom = 18,
			RightBottom = 19,
			Grids = 0x1000,
	};

	EXENUM(GridsImageMode)
	{
		    Default = 0x00000000,
			LeftNone = 0x00000001,
			LeftTile = 0x00000002,
			LeftMirror = 0x00000004,

			TopNone = 0x00000010,
			TopTile = 0x00000020,
			TopMirror = 0x00000040,

			RightNone = 0x00000100,
			RightTile = 0x00000200,
			RightMirror = 0x00000400,

			BottomNone = 0x00001000,
			BottomTile = 0x00002000,
			BottomMirror = 0x00004000,

			CenterNone = 0x00010000,
			CenterTile = 0x00020000,
			CenterMirror = 0x00040000,
	};

	struct info_GridsImage
	{
		float left;
		float top;
		float right;
		float bottom;
		DWORD flags;

		info_GridsImage()
		{
			left = top = right = bottom = 0.0F;
			flags = GridsImageMode::Default;
		}

		info_GridsImage(float left, float top, float right, float bottom, DWORD flags = GridsImageMode::Default)
		{
			this->left = left;
			this->top = top;
			this->right = right;
			this->bottom = bottom;
			this->flags = flags;
		}
		info_GridsImage(float size, DWORD flags = GridsImageMode::Default)
			: info_GridsImage(size, size, size, size, flags)
		{ }
		info_GridsImage(float horz, float vert, DWORD flags = GridsImageMode::Default)
			: info_GridsImage(horz, vert, horz, vert, flags)
		{ }

		inline bool empty() const { return left == 0.0F && top == 0.0F && right == 0.0F && bottom == 0.0F; }
	};

	EXENUM(IconPos)
	{
		    Left,
			Top,
			Right,
			Bottom,
	};

	enum CanvasDrawMode
	{
		    Blend = 0x00000000,
			Over = 0x00000001,
			OverBlend = 0x00000002,
	};
	class TOAPI UICanvas
	{
	public:
		UICanvas(UIWnd* pWnd, size_t width, size_t height);
		~UICanvas();
		SIZE GetSize();
		HRESULT Resize(size_t width, size_t height);

		HRESULT GetClipRect(ExRectF* r_clip) const;
		HRESULT GetClipRegion(UIRegion *r_clip_region) const;
		HRESULT SetClipRect(float left, float top, float right, float bottom);
		HRESULT SetClipRegion(UIRegion *clip_regioin);
		HRESULT ResetClip();

		HRESULT GetTransform(ExMatrix* r_matrix);
		HRESULT SetTransform(const ExMatrix* matrix);

		HRESULT GetDC(HDC* r_dc);
		HRESULT ReleaseDC();
		HRESULT Flush();
		HRESULT ToImage(UIImage** dstImg, BOOL cdraw = false, FLOAT fBlur = 0.f);

		HRESULT BeginDraw();
		HRESULT EndDraw();

		HRESULT Clear(UIColor color = {});
		HRESULT Clear(UIBrush *hBrush);
		//画点
		HRESULT DrawPoint(UIBrush *hBrush, FLOAT x, FLOAT y, FLOAT strokeWidth, BOOL isRadius = false);
		//画线
		HRESULT DrawLine(UIBrush *brush, float x1, float y1, float x2, float y2, float strokeWidth, DWORD strokeStyle = D2D1_DASH_STYLE_SOLID, BOOL isRadius = false);
		HRESULT DrawLine(UIBrush* brush, D2D1_POINT_2F Start, D2D1_POINT_2F End, float strokeWidth, DWORD strokeStyle = D2D1_DASH_STYLE_SOLID, BOOL isRadius = false);
		//画矩形
		HRESULT DrawRect(UIBrush *brush, float left, float top, float right, float bottom, float strokeWidth, DWORD strokeStyle = D2D1_DASH_STYLE_SOLID);
		//画椭圆
		HRESULT DrawEllipse(UIBrush *brush, float left, float top, float right, float bottom, float strokeWidth, DWORD strokeStyle = D2D1_DASH_STYLE_SOLID);
		HRESULT DrawEllipse(UIBrush* brush, D2D1_ELLIPSE backgroundCircle, float strokeWidth);
		//画圆角矩形
		HRESULT DrawRoundRect(UIBrush *brush, float left, float top, float right, float bottom,
			float radius, float strokeWidth, DWORD strokeStyle = D2D1_DASH_STYLE_SOLID);
		HRESULT DrawCustomRoundRect(UIBrush *brush, float left, float top, float right, float bottom,
			float radius_left_top, float radius_right_top, float radius_right_bottom, float radius_left_bottom, float strokeWidth, DWORD strokeStyle = D2D1_DASH_STYLE_SOLID);
		HRESULT DrawPath(UIBrush* brush, UIPath* path, float strokeWidth, DWORD strokeStyle = D2D1_DASH_STYLE_SOLID, DWORD lineCap = D2D1_CAP_STYLE_SQUARE);
		/*
         * @brief 画多边形
         * @param left 多边形外接椭圆的坐标
         * @param top 多边形外接椭圆的坐标
         * @param right 多边形外接椭圆的坐标
         * @param bottom 多边形外接椭圆的坐标
         * @param NumberOfEdges 边数
         * @param Angle 角度
         * @param strokeWidth 绘制的线宽
         * @param strokeStyle 绘制的线类型
		 * @param lineCap 绘制的线帽
         * @return [BOOL]
         */
		HRESULT DrawPoly(UIBrush* hBrush, FLOAT left, FLOAT top, FLOAT right, FLOAT bottom, size_t NumberOfEdges, FLOAT Angle = 0, FLOAT strokeWidth = 1.0f, UINT strokeStyle = D2D1_DASH_STYLE_SOLID, DWORD lineCap = D2D1_CAP_STYLE_SQUARE);
		//填充多边形
		HRESULT FillPoly(UIBrush *hBrush, FLOAT left, FLOAT top, FLOAT right, FLOAT bottom, size_t NumberOfEdges, FLOAT Angle = 0);
		HRESULT FillPoly(UIBrush* hBrush, D2D1_POINT_2F* points, size_t NumberOfEdges);

		//填充矩形
		HRESULT FillRect(UIBrush *brush, float left, float top, float right, float bottom);
		//填充椭圆
		HRESULT FillEllipse(UIBrush *brush, float left, float top, float right, float bottom);
		HRESULT FillEllipse(UIBrush* brush, D2D1_ELLIPSE backgroundCircle);
		//填充圆角矩形
		HRESULT FillRoundRect(UIBrush *brush, float left, float top, float right, float bottom,
			float radius);
		HRESULT FillCustomRoundRect(UIBrush *brush, float left, float top, float right, float bottom,
			float radius_left_top, float radius_right_top, float radius_right_bottom, float radius_left_bottom);
		//填充路径
		HRESULT FillPath(UIBrush *brush, UIPath *path);
		HRESULT FillPath(UIBrush* brush, ID2D1Geometry* path);
		//画区域
		HRESULT DrawRegion(UIBrush* brush, UIRegion* region, float strokeWidth, DWORD strokeStyle = D2D1_DASH_STYLE_SOLID);
		//填充区域
		HRESULT FillRegion(UIBrush *brush, UIRegion *region);
		//测量文本
		static HRESULT CalcTextSize(UIFont *font, LPCWSTR text, DWORD text_format,
			float max_width, float max_height, float* r_width, float* r_height);
		//测量图标文本
		static HRESULT CalcTextWithIcon(UIFont *font, LPCWSTR text, DWORD text_format,
			float left, float top, float right, float bottom, UIImage *icon_image, float icon_width, float icon_height, DWORD icon_pos, float split_size,
			ExRectF* r_text_rect, ExRectF* r_icon_rect, ExRectF* r_content_rect);

		HRESULT DeviceText(ID2D1DeviceContext* pContext, UIBrush* brush, UIFont* font, LPCWSTR text, 
			DWORD text_format, float left, float top, float right, float bottom);
		HRESULT StrokeText(UIBrush *brush, UIFont *font, LPCWSTR text, 
			DWORD text_format, float left, float top, float right, float bottom);
		HRESULT FillText(UIBrush *brush, UIFont *font, LPCWSTR text, 
			DWORD text_format, float left, float top, float right, float bottom);
		HRESULT DrawTextByColor(UIFont* font, LPCWSTR text,  DWORD text_format,
			float left, float top, float right, float bottom, UIColor text_color, FLOAT* layoutWidth = 0, FLOAT* layoutHeight = 0);
		HRESULT DrawText(UIBrush *brush, UIFont *font, LPCWSTR text, 
			DWORD text_format, float left, float top, float right, float bottom);
		HRESULT DrawTextEx(UIBrush *brush, UIFont *font, LPCWSTR text, DWORD text_format, float left, float top, float right, float bottom);
		/*
		 * @brief 画文本颜色组
		 * @param brush 默认颜色画刷
		 * @param font 字体
		 * @param text 内容
		 * @param text_format 内容文本格式
		 * @param TextRange 特定文本范围组
		 * @param textColor 特定文本颜色组
		 * @param count 数组总数
		 * @param r_matrix 矩阵效果
		 */
		HRESULT DrawTextAndColor(UIBrush* brush, UIFont* font, LPCWSTR text, DWORD text_format, const POINT* TextRange, const UIColor *textColor, int count,
			float left, float top, float right, float bottom, ExMatrix* r_matrix = nullptr);
		HRESULT DrawTextEffect(UIBrush *brush, UIFont *font,
			LPCWSTR text, DWORD text_format, float left, float top, float right, float bottom, ExMatrix* r_matrix);
		HRESULT DrawTextAndFontName(UIBrush* brush, LPCWSTR text, DWORD text_format, float left, float top, float right, float bottom,
			LPCWSTR lpwzFontFace = {}, INT dwFontSize = 0, DWORD dwFontStyle = 0);
		HRESULT DrawImage(UIImage *image, float left, float top, INT alpha = 255);
		HRESULT DrawImageRect(UIImage *image, float left, float top, float right, float bottom,
			ImageMode mode = ImageMode::Default, INT alpha = 255, INT radius = 0);
		HRESULT DrawImagePart(UIImage *image, float left, float top,
			float src_left, float src_top, float src_right, float src_bottom, INT alpha = 255);
		HRESULT DrawImagePartRect(UIImage *image, float left, float top, float right, float bottom,
			float src_left, float src_top, float src_right, float src_bottom,
			ImageMode mode = ImageMode::Default, INT alpha = 255, INT radius = 0);
		//绘制九宫格图像
		HRESULT DrawGridsImage(UIImage *image, float left, float top, float right, float bottom,
			info_GridsImage* grids, INT alpha = 255);
		HRESULT DrawGridsImagePart(UIImage *image, float left, float top, float right, float bottom,
			float src_left, float src_top, float src_right, float src_bottom,
			info_GridsImage* grids, INT alpha = 255);

		HRESULT DrawShadow(UIBrush *bkg_brush, float left, float top, float right, float bottom,
			float size, float radius_left_top = 0.0F, float radius_right_top = 0.0F,
			float radius_right_bottom = 0.0F, float radius_left_bottom = 0.0F,
			float offset_x = 0.0F, float offset_y = 0.0F);
		HRESULT DrawShadowFromRegion(UIBrush *bkg_brush, UIRegion *region, float size,
			float offset_x = 0.0F, float offset_y = 0.0F);

		HRESULT DrawCanvas(UICanvas *canvas_src, float left, float top, float right, float bottom,
			float src_left, float src_top, CanvasDrawMode mode, INT alpha = 255, BOOL cdraw = false);
		/*
         * @brief 画泛光特效
         * @param hImg 需要处理的图像 当hImg为0时会从目标画布canvas_src提取内容
         * @param shadowColor 阴影颜色
         * @param isBloom 是否开启Bloom效果
		 * @param pMaximumlight 亮度阈值
         * @param pBlur 模糊系数
         */
		HRESULT DrawBloom(UICanvas* canvas_src, UIImage* hImg = nullptr, UIColor shadowColor = {}, BOOL isBloom = false, FLOAT pMaximumlight = 0.6f, FLOAT pBlur = 10.0f);
		HRESULT DrawBloom(UICanvas* canvas_src, UIColor scRGB, POINT padding, FLOAT fblur);
		//模糊 当pBitmap=nullptr模糊当前画布内容
		HRESULT blur(LPVOID pBitmap, FLOAT fDeviation, RECT* lprc = nullptr, FLOAT radius = 0, BOOL fmodesoft = false);
		//画布旋转 0-360角度 必须在开始绘制后、在结束绘制之前必须使用重置矩阵
		void Rotate(FLOAT fAngle, FLOAT scaleX = 0, FLOAT scaleY = 0);
		/*
         * @brief  画曲线组
         * @param  Points 曲线组:至少需要两个点才能创建一条曲线
         * @param  count 曲线组数量
         * @param  tension 张力 范围通常是 [0, 1]，其中 0 表示最平滑的曲线，而 1 则表示最大张力
         * @param  strokeWidth 线宽度
         * @param  Fillmode 填充模式 true填充,false中空
         * @param  Openmode 是否闭合
         * @return [BOOL]返回是否成功
         */
		HRESULT DrawCurves(UIBrush* hBrush, const ExPointF* Points, int count, float tension, float strokeWidth, bool Fillmode = true, bool Openmode = false);
		HRESULT DrawCurvesEx(UIBrush* hBrush, const ExPointF* Points, int count, float strokeWidth, bool Openmode = false);
		/*
         * @brief 填充曲线组
         * @param  Points 曲线组:至少需要两个点才能创建一条曲线
         * @param  count 曲线组数量
         * @param  tension 张力 范围通常是 [0, 1]，其中 0 表示最平滑的曲线，而 1 则表示最大张力
         * @param  Openmode 是否闭合
         * @return [BOOL]返回是否成功
         */
		HRESULT FillCurves(UIBrush* hBrush, const ExPointF* Points, int count, float tension, bool Openmode = true);
		//画贝塞尔曲线组 (顶点数组|填充模式,默认无填充|设置为封闭图形,默认为是)顶点数组需要4个顶点: 起点,两个控制点,终点
		HRESULT DrawBezies(UIBrush* hBrush, const ExPointF* Points, float strokeWidth, bool Fillmode = true, bool Openmode = false);

		//画贝塞尔曲线 坐标需要4个顶点： 起点,两个控制点,终点
		HRESULT DrawBezier(UIBrush* hBrush, FLOAT x1, FLOAT y1, FLOAT x2, FLOAT y2, FLOAT x3, FLOAT y3, FLOAT x4, FLOAT y4,
			float strokeWidth, bool Fillmode = true, bool Openmode = false);
		//画二次方贝塞尔曲线 坐标需要3个顶点：起点,控制点,终点
		HRESULT DrawQuadraticBezier(UIBrush* hBrush, FLOAT x1, FLOAT y1, FLOAT x2, FLOAT y2, FLOAT x3, FLOAT y3, float strokeWidth, bool Fillmode = true, bool Openmode = false);
		//取描述表
		LPVOID GetContext(INT index);
		//是否正在绘制
		BOOL IsDrawing();

		private:
			static IDWriteTextLayout* ToLayout(UIFont *font, LPCWSTR text,
				ExRectF rect, DWORD text_format) MAYTHROW;
			ID2D1Bitmap1* m_bitmap = nullptr;
			SIZE m_size{};
			bool m_drawing = false, m_clip = false;
			UIRegion *m_clip_region = nullptr;
			UIWnd* m_pWnd = nullptr;
			D2D1_INTERPOLATION_MODE m_interpolation_mode = D2D1_INTERPOLATION_MODE_LINEAR;
			friend class UITextRender;
			friend class UIPath;
	};

}

