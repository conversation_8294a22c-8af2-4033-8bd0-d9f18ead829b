﻿#include "pch.h"
#include <ShlObj.h>
#include <fstream>
#include <common/Exception.h>
#include <common/memory.h>
#include <corecrt_io.h>
#include <filesystem>

// 全局资源目录路径
static std::wstring g_resourceDirectory;

HRESULT TOAPI HHBUI::UIreadFile(LPCWSTR filePath, LPVOID& retData, size_t& retSize)
{
    CHECK_PARAM(filePath);

    //打开文件
    std::fstream fs(filePath, std::ios::in | std::ios::binary);
    handle_if_false(fs.is_open(), EE_IO, L"打开文件失败");

    //获取文件大小
    fs.seekg(0, std::ios::end);
    size_t size = fs.tellg();

    //申请内存
    auto data = (uint8_t*)ExAlloc(size);
    handle_if_false(data, E_OUTOFMEMORY, L"申请数据块内存失败");

    try
    {
        //读取文件
        fs.seekg(0, std::ios::beg);
        throw_if_false(fs.read((char*)data, size), EE_IO, L"读取文件失败");

        //设置到数据块
        retData = data;
        retSize = size;
        return S_OK;
    }
    catch_default({});
}

HRESULT TOAPI HHBUI::UIWriteFile(LPCWSTR file, const LPVOID data, size_t size)
{
    CHECK_PARAM(file);

    //打开文件
    std::fstream fs(file, std::ios::out | std::ios::binary | std::ios::trunc);
    handle_if_false(fs.is_open(), EE_IO, L"打开文件失败");

    //如果数据为空，则直接关闭文件
    if (data == nullptr || size == 0) {
        fs.close();
        return S_OK;
    }

    //写入文件
    handle_if_false(fs.write((const char*)data, size), EE_IO, L"写入文件失败");
    return S_OK;
}

HRESULT TOAPI HHBUI::UIGetFileSize(LPCWSTR file, size_t& r_size)
{
    CHECK_PARAM(file);

    //打开文件
    std::fstream fs(file, std::ios::in | std::ios::binary);
    handle_if_false(fs.is_open(), EE_IO, L"打开文件失败");

    //获取文件大小
    fs.seekg(0, std::ios::end);
    r_size = fs.tellg();
    return S_OK;
}

BOOL TOAPI HHBUI::UIfileOpenDlg(HWND hWnd, BOOL bOpenFileDialog, LPCWSTR lpszFilter, LPCTSTR lpszDefExt, LPCWSTR lpszFileName, LPCWSTR* retstrFile)
{
    BOOL rel = FALSE;
    OPENFILENAME m_ofn;
    BOOL m_bOpenFileDialog = bOpenFileDialog;// TRUE for file open, FALSE for file save
    TCHAR m_szFileTitle[_MAX_FNAME]{}; // contains file title after return

    // 将大数组移到堆上 - 使用明确的常量避免溢出
    constexpr size_t kKilobyte = 1024;
    const size_t fileNameBufferSize = 128 * kKilobyte;  // 避免溢出的安全计算
    std::unique_ptr<TCHAR[]> m_szFileNamePtr = std::make_unique<TCHAR[]>(fileNameBufferSize);
    TCHAR* m_szFileName = m_szFileNamePtr.get();

    DWORD dwFlags = OFN_HIDEREADONLY | OFN_OVERWRITEPROMPT;
    if (m_bOpenFileDialog)
    {
        dwFlags = OFN_ALLOWMULTISELECT | OFN_EXPLORER;
    }

    std::wstring lpstrFilter = L"所有文件|*.*||";
    if (lpszFilter != NULL)
        lpstrFilter = lpszFilter;
    // 使用循环遍历字符串，并替换字符
    for (size_t i = 0; i < lpstrFilter.length(); i++) {
        if (lpstrFilter[i] == '|') {
            lpstrFilter[i] = '\0';
        }
    }

    memset(&m_ofn, 0, sizeof(m_ofn)); // initialize structure to 0/NULL
    m_szFileName[0] = _T('\0');
    m_szFileTitle[0] = _T('\0');

    m_ofn.lStructSize = sizeof(m_ofn);
    m_ofn.lpstrFile = m_szFileName;
    m_ofn.nMaxFile = fileNameBufferSize;
    m_ofn.lpstrDefExt = lpszDefExt;
    m_ofn.lpstrFileTitle = (LPTSTR)m_szFileTitle;
    m_ofn.nMaxFileTitle = _MAX_FNAME;
    m_ofn.Flags = dwFlags;
    m_ofn.lpstrFilter = lpstrFilter.c_str();
    m_ofn.hwndOwner = hWnd;

    // setup initial file name
    if (lpszFileName != NULL)
        _tcscpy_s(m_szFileName, fileNameBufferSize, lpszFileName);
    if (m_bOpenFileDialog)
    {
        if (GetOpenFileNameW(&m_ofn))
        {
            std::wstring filePath(m_szFileName);
            std::vector<std::wstring> fileNames;
            std::wstring str;
            wchar_t* p = m_szFileName + filePath.length() + 1;
            size_t folderPathLength = wcslen(p);
            if (folderPathLength == 0)
            { // 单个文件选择
                *retstrFile = StrDupW(filePath.c_str());
            }
            else
            {
                while (*p) {
                    std::wstring fileName(p);
                    fileNames.emplace_back(fileName);

                    p += fileName.length() + 1;
                }
                INT sn = 0;
                for (const auto& fileName : fileNames) {
                    std::wstring completePath = filePath + L"\\" + fileName;
                    if (sn > 0)
                    {
                        str += L"|" + completePath;
                    }
                    else
                    {
                        str += completePath;
                    }
                    sn++;
                }
                *retstrFile = StrDupW(str.c_str());
            }
            rel = TRUE;
        }
    }
    else
    {
        if (GetSaveFileNameW(&m_ofn))
        {
            *retstrFile = StrDupW(m_szFileName);
            rel = TRUE;
        }
    }
    return rel;
}

BOOL TOAPI HHBUI::UIfileOpenFolder(HWND hWnd, LPCWSTR lpszTitle, LPCWSTR* retstrFile)
{
    TCHAR szPath[MAX_PATH] = { 0 };		//存放选择的目录路径 初始化为0
    BROWSEINFO bi = { 0 };  // 初始化所有成员为0
    bi.hwndOwner = hWnd;
    bi.pidlRoot = NULL;
    bi.pszDisplayName = szPath;
    bi.lpszTitle = lpszTitle;
    bi.ulFlags = BIF_DONTGOBELOWDOMAIN | BIF_RETURNONLYFSDIRS | BIF_NEWDIALOGSTYLE;//有新建文件夹按钮
    bi.lpfn = NULL;
    bi.lParam = 0;
    bi.iImage = 0;
    //弹出选择目录对话框
    LPITEMIDLIST lp = SHBrowseForFolder(&bi);

    if (lp)
    {
        if (SHGetPathFromIDList(lp, szPath))
        {
            *retstrFile = StrDupW(szPath);
            return TRUE;
        }
    }
    return FALSE;
}

void TOAPI HHBUI::UIGetFiles(std::wstring file_name, std::vector<std::wstring>& files, std::function<bool(const std::wstring&)> fun_is_valid)
{
    files.clear();
    //文件句柄
    intptr_t hFile = 0;
    //文件信息（用Unicode保存使用_wfinddata_t，多字节字符集使用_finddata_t）
    _wfinddata_t fileinfo;
    if ((hFile = _wfindfirst(file_name.c_str(), &fileinfo)) != -1)
    {
        do
        {
            if (fun_is_valid(fileinfo.name))
                files.push_back(fileinfo.name);
        } while (_wfindnext(hFile, &fileinfo) == 0);
    }
    _findclose(hFile);
}

LPCWSTR TOAPI HHBUI::UIGetFileName(LPCWSTR filePath, BOOL fWiExt)
{
    std::filesystem::path spath(filePath);
    if (fWiExt)
        return StrDupW(spath.stem().wstring().c_str());  // 获取不带扩展名的文件名
    return StrDupW(spath.filename().wstring().c_str());
}

// ==================== 新增增强版文件操作函数 ====================

// 1. 增强版文件读取函数

HRESULT TOAPI HHBUI::UIreadFileEx(LPCWSTR filePath, LPVOID& retData, size_t& retSize,
    BOOL tryAlternatePaths, BOOL enableDebugOutput)
{
    CHECK_PARAM(filePath);

    // 用于存储错误信息
    std::wstring errorDetails;
    HRESULT finalResult = E_FAIL;

    // 要尝试的路径列表
    std::vector<std::wstring> pathsToTry;

    // 始终添加原始路径
    pathsToTry.push_back(filePath);

    // 如果需要尝试替代路径
    if (tryAlternatePaths)
    {
        WCHAR exePath[MAX_PATH] = { 0 };
        WCHAR currentDir[MAX_PATH] = { 0 };

        // 获取可执行文件路径
        if (GetModuleFileNameW(NULL, exePath, MAX_PATH) > 0)
        {
            // 获取应用程序目录
            WCHAR* lastSlash = wcsrchr(exePath, L'\\');
            if (lastSlash) *lastSlash = L'\0';

            std::wstring fileName = PathFindFileNameW(filePath);
            std::wstring dirPath = filePath;
            size_t lastSlashPos = dirPath.find_last_of(L"\\/");
            if (lastSlashPos != std::wstring::npos)
                dirPath = dirPath.substr(0, lastSlashPos);

            // 添加基于应用程序目录的路径
            pathsToTry.push_back(std::wstring(exePath) + L"\\" + fileName);
            pathsToTry.push_back(std::wstring(exePath) + L"\\..\\Resource\\" + fileName);
            pathsToTry.push_back(std::wstring(exePath) + L"\\" + dirPath + L"\\" + fileName);

            // 专门为富士山背景图片添加路径
            if (wcsstr(filePath, L"富士山") != nullptr || wcsstr(filePath, L"枫叶") != nullptr ||
                wcsstr(filePath, L"樱花") != nullptr) {
                pathsToTry.push_back(std::wstring(exePath) + L"\\..\\Resource\\icons\\Backgrounds\\富士山-枫叶-樱花.png");
            }
        }

        // 添加基于当前目录的路径
        if (GetCurrentDirectoryW(MAX_PATH, currentDir) > 0)
        {
            pathsToTry.push_back(std::wstring(currentDir) + L"\\" + PathFindFileNameW(filePath));
        }
    }

    // 输出调试信息
    if (enableDebugOutput)
    {
        OutputDebugStringW(L"UIreadFileEx: 尝试加载文件\n");
        OutputDebugStringW(L"原始路径: ");
        OutputDebugStringW(filePath);
        OutputDebugStringW(L"\n");
    }

    // 尝试每个路径
    for (const auto& path : pathsToTry)
    {
        if (enableDebugOutput)
        {
            OutputDebugStringW(L"尝试路径: ");
            OutputDebugStringW(path.c_str());
            OutputDebugStringW(L"\n");
        }

        // 首先尝试使用标准fstream方式
        std::fstream fs(path.c_str(), std::ios::in | std::ios::binary);
        if (fs.is_open())
        {
            // 获取文件大小
            fs.seekg(0, std::ios::end);
            size_t size = fs.tellg();

            // 申请内存
            auto data = (uint8_t*)ExAlloc(size);
            if (!data) {
                fs.close();
                finalResult = E_OUTOFMEMORY;
                if (enableDebugOutput)
                    OutputDebugStringW(L"内存分配失败\n");
                continue;
            }

            // 读取文件内容
            fs.seekg(0, std::ios::beg);
            if (!fs.read((char*)data, size)) {
                ExFree(data);
                fs.close();
                if (enableDebugOutput)
                    OutputDebugStringW(L"文件读取失败\n");
                continue;
            }

            // 关闭文件
            fs.close();

            // 设置返回值
            retData = data;
            retSize = size;

            if (enableDebugOutput)
                OutputDebugStringW(L"文件加载成功\n");

            return S_OK;
        }

        // fstream失败，尝试使用WinAPI
        HANDLE hFile = CreateFileW(
            path.c_str(),
            GENERIC_READ,
            FILE_SHARE_READ,
            NULL,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            NULL
        );

        if (hFile != INVALID_HANDLE_VALUE)
        {
            // 获取文件大小
            LARGE_INTEGER fileSize;
            if (!GetFileSizeEx(hFile, &fileSize))
            {
                CloseHandle(hFile);
                if (enableDebugOutput) {
                    OutputDebugStringW(L"获取文件大小失败\n");
                }
                continue;
            }

            // 分配内存
            size_t size = (size_t)fileSize.QuadPart;
            uint8_t* data = (uint8_t*)ExAlloc(size);

            if (!data)
            {
                CloseHandle(hFile);
                finalResult = E_OUTOFMEMORY;
                if (enableDebugOutput) {
                    OutputDebugStringW(L"内存分配失败\n");
                }
                continue;
            }

            // 读取数据
            DWORD bytesRead = 0;
            if (!ReadFile(hFile, data, (DWORD)size, &bytesRead, NULL) || bytesRead != size)
            {
                ExFree(data);
                CloseHandle(hFile);
                if (enableDebugOutput) {
                    OutputDebugStringW(L"文件读取失败\n");
                }
                continue;
            }

            // 关闭文件句柄
            CloseHandle(hFile);

            // 成功读取
            retData = data;
            retSize = size;

            if (enableDebugOutput)
                OutputDebugStringW(L"文件加载成功!\n");

            return S_OK;
        }
        else if (enableDebugOutput)
        {
            DWORD error = GetLastError();
            WCHAR errMsg[256];
            swprintf_s(errMsg, L"打开文件失败: 错误码 %d\n", error);
            OutputDebugStringW(errMsg);
        }
    }

    // 所有尝试均失败
    if (enableDebugOutput)
        OutputDebugStringW(L"所有路径均加载失败!\n");

    return finalResult != E_FAIL ? finalResult : EE_IO;
}

// 2. 路径处理工具函数

BOOL TOAPI HHBUI::UIFileExists(LPCWSTR filePath)
{
    DWORD attr = GetFileAttributesW(filePath);
    return (attr != INVALID_FILE_ATTRIBUTES &&
        !(attr & FILE_ATTRIBUTE_DIRECTORY));
}

HRESULT TOAPI HHBUI::UIGetAppDirectory(LPWSTR outPath, DWORD bufferSize)
{
    if (!outPath || bufferSize == 0)
        return E_INVALIDARG;

    WCHAR exePath[MAX_PATH];
    if (GetModuleFileNameW(NULL, exePath, MAX_PATH) == 0)
        return HRESULT_FROM_WIN32(GetLastError());

    WCHAR* lastSlash = wcsrchr(exePath, L'\\');
    if (!lastSlash)
        return E_UNEXPECTED;

    // 截断到目录部分
    *lastSlash = L'\0';

    if (wcslen(exePath) >= bufferSize)
        return HRESULT_FROM_WIN32(ERROR_INSUFFICIENT_BUFFER);

    wcscpy_s(outPath, bufferSize, exePath);
    return S_OK;
}

HRESULT TOAPI HHBUI::UIGetAbsolutePath(LPCWSTR relativePath, LPWSTR absolutePath, DWORD bufferSize)
{
    if (!relativePath || !absolutePath || bufferSize == 0)
        return E_INVALIDARG;

    WCHAR currentDir[MAX_PATH];
    if (!GetCurrentDirectoryW(MAX_PATH, currentDir))
        return HRESULT_FROM_WIN32(GetLastError());

    // 检查是否已经是绝对路径
    if (PathIsRelativeW(relativePath))
    {
        // 结合当前目录和相对路径
        if (!PathCombineW(absolutePath, currentDir, relativePath))
            return E_FAIL;
    }
    else
    {
        // 已经是绝对路径，直接复制
        if (wcslen(relativePath) >= bufferSize)
            return HRESULT_FROM_WIN32(ERROR_INSUFFICIENT_BUFFER);

        wcscpy_s(absolutePath, bufferSize, relativePath);
    }

    return S_OK;
}

HRESULT TOAPI HHBUI::UINormalizePath(LPCWSTR path, LPWSTR normalizedPath, DWORD bufferSize)
{
    if (!path || !normalizedPath || bufferSize == 0)
        return E_INVALIDARG;

    // 获取绝对路径
    WCHAR fullPath[MAX_PATH];
    if (FAILED(UIGetAbsolutePath(path, fullPath, MAX_PATH)))
        return E_FAIL;

    // 标准化路径（处理..和.）
    WCHAR canonicalPath[MAX_PATH];
    if (!PathCanonicalizeW(canonicalPath, fullPath))
        return E_FAIL;

    if (wcslen(canonicalPath) >= bufferSize)
        return HRESULT_FROM_WIN32(ERROR_INSUFFICIENT_BUFFER);

    wcscpy_s(normalizedPath, bufferSize, canonicalPath);
    return S_OK;
}

// 3. 图像文件专用函数

HRESULT TOAPI HHBUI::UIreadImageFile(LPCWSTR filePath, LPVOID& retData, size_t& retSize, BOOL tryAlternatePaths)
{
    // 首先尝试常规方式加载
    HRESULT hr = UIreadFileEx(filePath, retData, retSize, tryAlternatePaths, FALSE);
    if (SUCCEEDED(hr))
        return hr;

    // 如果常规方式失败，尝试使用WIC直接加载
    IWICImagingFactory* pFactory = nullptr;
    hr = CoCreateInstance(
        CLSID_WICImagingFactory,
        NULL,
        CLSCTX_INPROC_SERVER,
        IID_PPV_ARGS(&pFactory));

    if (FAILED(hr))
        return hr;

    // 要尝试的路径列表
    std::vector<std::wstring> pathsToTry;
    pathsToTry.push_back(filePath);

    // 如果需要尝试替代路径
    if (tryAlternatePaths)
    {
        WCHAR exePath[MAX_PATH] = { 0 };

        // 获取可执行文件路径
        if (GetModuleFileNameW(NULL, exePath, MAX_PATH) > 0)
        {
            // 获取应用程序目录
            WCHAR* lastSlash = wcsrchr(exePath, L'\\');
            if (lastSlash) *lastSlash = L'\0';

            std::wstring fileName = PathFindFileNameW(filePath);

            // 添加基于应用程序目录的路径
            pathsToTry.push_back(std::wstring(exePath) + L"\\" + fileName);
            pathsToTry.push_back(std::wstring(exePath) + L"\\..\\Resource\\icons\\Backgrounds\\" + fileName);

            // 特别处理富士山背景图片
            if (wcsstr(filePath, L"富士山") != nullptr || wcsstr(filePath, L"枫叶") != nullptr || wcsstr(filePath, L"樱花") != nullptr) {
                pathsToTry.push_back(std::wstring(exePath) + L"\\..\\Resource\\icons\\Backgrounds\\富士山-枫叶-樱花.png");
            }
        }
    }

    // 尝试每个路径
    for (const auto& path : pathsToTry)
    {
        IWICBitmapDecoder* pDecoder = nullptr;
        hr = pFactory->CreateDecoderFromFilename(
            path.c_str(),
            NULL,
            GENERIC_READ,
            WICDecodeMetadataCacheOnLoad,
            &pDecoder);

        if (SUCCEEDED(hr))
        {
            // 处理成功，可以从解码器获取图像数据
            // 这里简化处理，使用标准WinAPI方式读取
            IWICStream* pStream = nullptr;
            hr = pFactory->CreateStream(&pStream);

            if (SUCCEEDED(hr))
            {
                hr = pStream->InitializeFromFilename(path.c_str(), GENERIC_READ);
                if (SUCCEEDED(hr))
                {
                    // 获取文件大小
                    LARGE_INTEGER fileSize = { 0 };
                    ULARGE_INTEGER curPosition = { 0 };

                    pStream->Seek({ 0 }, STREAM_SEEK_END, &curPosition);
                    fileSize.QuadPart = curPosition.QuadPart;
                    pStream->Seek({ 0 }, STREAM_SEEK_SET, NULL);

                    // 分配内存
                    size_t size = (size_t)fileSize.QuadPart;
                    uint8_t* data = (uint8_t*)ExAlloc(size);

                    if (data)
                    {
                        ULONG bytesRead = 0;
                        hr = pStream->Read(data, (ULONG)size, &bytesRead);

                        if (SUCCEEDED(hr) && bytesRead == size)
                        {
                            retData = data;
                            retSize = size;

                            pStream->Release();
                            pDecoder->Release();
                            pFactory->Release();

                            return S_OK;
                        }

                        ExFree(data);
                    }
                }

                pStream->Release();
            }

            pDecoder->Release();
        }
    }

    pFactory->Release();
    return EE_IO;
}

HRESULT TOAPI HHBUI::UIConvertImageFormat(LPVOID srcData, size_t srcSize, LPVOID& dstData, size_t& dstSize, GUID targetFormat)
{
    if (!srcData || srcSize == 0)
        return E_INVALIDARG;

    dstData = nullptr;
    dstSize = 0;

    // 初始化COM和WIC
    IWICImagingFactory* pFactory = nullptr;
    HRESULT hr = CoCreateInstance(
        CLSID_WICImagingFactory,
        NULL,
        CLSCTX_INPROC_SERVER,
        IID_PPV_ARGS(&pFactory));

    if (FAILED(hr))
        return hr;

    // 创建内存流
    IWICStream* pStreamIn = nullptr;
    hr = pFactory->CreateStream(&pStreamIn);
    if (FAILED(hr)) {
        pFactory->Release();
        return hr;
    }

    // 初始化内存流
    hr = pStreamIn->InitializeFromMemory((BYTE*)srcData, (DWORD)srcSize);
    if (FAILED(hr)) {
        pStreamIn->Release();
        pFactory->Release();
        return hr;
    }

    // 创建解码器
    IWICBitmapDecoder* pDecoder = nullptr;
    hr = pFactory->CreateDecoderFromStream(
        pStreamIn,
        NULL,
        WICDecodeMetadataCacheOnLoad,
        &pDecoder);

    if (FAILED(hr)) {
        pStreamIn->Release();
        pFactory->Release();
        return hr;
    }

    // 获取第一帧
    IWICBitmapFrameDecode* pFrame = nullptr;
    hr = pDecoder->GetFrame(0, &pFrame);
    if (FAILED(hr)) {
        pDecoder->Release();
        pStreamIn->Release();
        pFactory->Release();
        return hr;
    }

    // 创建输出内存流
    IWICStream* pStreamOut = nullptr;
    hr = pFactory->CreateStream(&pStreamOut);
    if (FAILED(hr)) {
        pFrame->Release();
        pDecoder->Release();
        pStreamIn->Release();
        pFactory->Release();
        return hr;
    }

    // 创建编码器
    IWICBitmapEncoder* pEncoder = nullptr;
    hr = pFactory->CreateEncoder(targetFormat, NULL, &pEncoder);
    if (FAILED(hr)) {
        pStreamOut->Release();
        pFrame->Release();
        pDecoder->Release();
        pStreamIn->Release();
        pFactory->Release();
        return hr;
    }

    // 创建输出内存块
    IWICComponentInfo* pCompInfo = nullptr;
    hr = pFactory->CreateComponentInfo(targetFormat, &pCompInfo);
    if (FAILED(hr)) {
        pEncoder->Release();
        pStreamOut->Release();
        pFrame->Release();
        pDecoder->Release();
        pStreamIn->Release();
        pFactory->Release();
        return hr;
    }

    // 这里简化处理，使用固定大小的输出缓冲区
    // 实际应用中可能需要更复杂的处理来确定最佳大小
    constexpr ULONG OUTPUT_BUFFER_SIZE = 10 * 1024 * 1024;  // 10MB buffer
    BYTE* outputBuffer = (BYTE*)ExAlloc(OUTPUT_BUFFER_SIZE);
    if (!outputBuffer) {
        pCompInfo->Release();
        pEncoder->Release();
        pStreamOut->Release();
        pFrame->Release();
        pDecoder->Release();
        pStreamIn->Release();
        pFactory->Release();
        return E_OUTOFMEMORY;
    }

    // 初始化输出流
    hr = pStreamOut->InitializeFromMemory(outputBuffer, OUTPUT_BUFFER_SIZE);
    if (FAILED(hr)) {
        ExFree(outputBuffer);
        pCompInfo->Release();
        pEncoder->Release();
        pStreamOut->Release();
        pFrame->Release();
        pDecoder->Release();
        pStreamIn->Release();
        pFactory->Release();
        return hr;
    }

    // 初始化编码器
    hr = pEncoder->Initialize(pStreamOut, WICBitmapEncoderNoCache);
    if (FAILED(hr)) {
        ExFree(outputBuffer);
        pCompInfo->Release();
        pEncoder->Release();
        pStreamOut->Release();
        pFrame->Release();
        pDecoder->Release();
        pStreamIn->Release();
        pFactory->Release();
        return hr;
    }

    // 创建新帧
    IWICBitmapFrameEncode* pFrameEncode = nullptr;
    hr = pEncoder->CreateNewFrame(&pFrameEncode, NULL);
    if (FAILED(hr)) {
        ExFree(outputBuffer);
        pCompInfo->Release();
        pEncoder->Release();
        pStreamOut->Release();
        pFrame->Release();
        pDecoder->Release();
        pStreamIn->Release();
        pFactory->Release();
        return hr;
    }

    // 初始化新帧
    hr = pFrameEncode->Initialize(NULL);
    if (FAILED(hr)) {
        pFrameEncode->Release();
        ExFree(outputBuffer);
        pCompInfo->Release();
        pEncoder->Release();
        pStreamOut->Release();
        pFrame->Release();
        pDecoder->Release();
        pStreamIn->Release();
        pFactory->Release();
        return hr;
    }

    // 设置帧尺寸和像素格式
    UINT width, height;
    hr = pFrame->GetSize(&width, &height);
    if (SUCCEEDED(hr)) {
        hr = pFrameEncode->SetSize(width, height);
    }

    WICPixelFormatGUID pixelFormat;
    if (SUCCEEDED(hr)) {
        hr = pFrame->GetPixelFormat(&pixelFormat);
    }

    if (SUCCEEDED(hr)) {
        hr = pFrameEncode->SetPixelFormat(&pixelFormat);
    }

    // 复制图像数据
    if (SUCCEEDED(hr)) {
        hr = pFrameEncode->WriteSource(pFrame, NULL);
    }

    // 提交帧
    if (SUCCEEDED(hr)) {
        hr = pFrameEncode->Commit();
    }

    // 提交编码器
    if (SUCCEEDED(hr)) {
        hr = pEncoder->Commit();
    }

    // 获取输出大小
    if (SUCCEEDED(hr)) {
        ULARGE_INTEGER curPos;
        hr = pStreamOut->Seek({ 0 }, STREAM_SEEK_CUR, &curPos);
        if (SUCCEEDED(hr)) {
            dstSize = (size_t)curPos.QuadPart;
            dstData = outputBuffer;
            outputBuffer = nullptr; // 避免稍后释放
        }
    }

    // 清理
    if (outputBuffer) {
        ExFree(outputBuffer);
    }

    pFrameEncode->Release();
    pCompInfo->Release();
    pEncoder->Release();
    pStreamOut->Release();
    pFrame->Release();
    pDecoder->Release();
    pStreamIn->Release();
    pFactory->Release();

    return hr;
}

// 4. 文件操作辅助功能

HRESULT TOAPI HHBUI::UICreateDirectory(LPCWSTR dirPath)
{
    if (!dirPath)
        return E_INVALIDARG;

    // 标准化路径
    WCHAR normalizedPath[MAX_PATH];
    HRESULT hr = UINormalizePath(dirPath, normalizedPath, MAX_PATH);
    if (FAILED(hr))
        return hr;

    // 检查目录是否已存在
    DWORD attr = GetFileAttributesW(normalizedPath);
    if (attr != INVALID_FILE_ATTRIBUTES && (attr & FILE_ATTRIBUTE_DIRECTORY))
        return S_OK; // 目录已存在

    // 创建目录（包括父目录）
    if (SHCreateDirectoryExW(NULL, normalizedPath, NULL) == ERROR_SUCCESS)
        return S_OK;

    return HRESULT_FROM_WIN32(GetLastError());
}

HRESULT TOAPI HHBUI::UISetResourceDirectory(LPCWSTR resourceDir)
{
    if (!resourceDir)
        return E_INVALIDARG;

    // 标准化路径
    WCHAR normalizedPath[MAX_PATH];
    HRESULT hr = UINormalizePath(resourceDir, normalizedPath, MAX_PATH);
    if (FAILED(hr))
        return hr;

    g_resourceDirectory = normalizedPath;

    // 确保目录存在
    return UICreateDirectory(normalizedPath);
}

LPCWSTR TOAPI HHBUI::UIGetResourceDirectory()
{
    if (g_resourceDirectory.empty())
    {
        // 默认使用应用程序目录下的Resource文件夹
        WCHAR appDir[MAX_PATH];
        if (SUCCEEDED(UIGetAppDirectory(appDir, MAX_PATH)))
        {
            g_resourceDirectory = appDir;
            g_resourceDirectory += L"\\Resource";
        }
    }

    return g_resourceDirectory.c_str();
}

HRESULT TOAPI HHBUI::UIreadResourceFile(LPCWSTR fileName, LPVOID& retData, size_t& retSize)
{
    if (!fileName)
        return E_INVALIDARG;

    // 获取资源目录
    LPCWSTR resourceDir = UIGetResourceDirectory();

    // 构建完整路径
    std::wstring fullPath = resourceDir;
    fullPath += L"\\";
    fullPath += fileName;

    // 使用增强版函数读取
    return UIreadFileEx(fullPath.c_str(), retData, retSize, TRUE, FALSE);
}
