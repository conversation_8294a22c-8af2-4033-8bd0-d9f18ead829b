﻿#pragma once
#include <math.h>
#include <array>
#include <random>
#pragma region 快捷操作

#define EX_SWAP(A,B)	{auto T=A;A=B;B=T;}

#pragma endregion

#pragma region 计算判断

//延时
void SleepEx(INT ms);
 // 浮点数相等
inline bool float_eq(float a, float b) { return fabs(a - b) < 1e-6F; }

// 四舍五入
inline float roundf_(float r) { return (r > 0.0F) ? floor(r + 0.5F) : ceil(r - 0.5F); }

// 四舍五入
inline double roundd(double r) { return (r > 0.0) ? floor(r + 0.5) : ceil(r - 0.5); }

// 圆周率
inline float PI = 3.14159265358979323846F;

// 系统默认DPI
inline int _SYS_DEFAULT_DPI = 96;

// dp到px
inline long _px(float dp, int dpi) { return MulDiv(dp, dpi, _SYS_DEFAULT_DPI); }

// px到dp
inline float _dp(long px, int dpi) { return px * (float)_SYS_DEFAULT_DPI / dpi; }

// 整数到小数
inline float i2f(int n) { return *reinterpret_cast<float*>(&n); }

// 小数到整数
inline int f2i(float f) { return *reinterpret_cast<int*>(&f); }

// 有符号数到无符号数
inline unsigned int i2u(int n) { return *reinterpret_cast<unsigned int*>(&n); }

// 无符号数到有符号数
inline int u2i(unsigned int n) { return *reinterpret_cast<int*>(&n); }

// 生成两个整数之间的随机数
inline int random(int min, int max)
{
	return min + (rand() % (max - min + 1));
}
inline int randoms(int min, int max) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(min, max);
    return dis(gen);
}
// 生成两个小数间的随机数
inline float randomf(float min, float max)
{
	return min + (max - min) * (rand() / (RAND_MAX + 1.0F));
}

// 生成指定长度的随机字母字符串
inline std::wstring generate_random_letters(size_t length) {
    static const std::array<wchar_t, 52> letters = {
        L'a', L'b', L'c', L'd', L'e', L'f', L'g', L'h', L'i', L'j', L'k', L'l',
        L'm', L'n', L'o', L'p', L'q', L'r', L's', L't', L'u', L'v', L'w', L'x',
        L'y', L'z', L'A', L'B', L'C', L'D', L'E', L'F', L'G', L'H', L'I', L'J',
        L'K', L'L', L'M', L'N', L'O', L'P', L'Q', L'R', L'S', L'T', L'U', L'V',
        L'W', L'X', L'Y', L'Z'
    };

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<size_t> dis(0, letters.size() - 1);

    std::wstring result;
    result.reserve(length);  // 预先分配内存，避免重复分配

    // 使用 push_back 来避免每次连接时的内存重新分配
    for (size_t i = 0; i < length; ++i) {
        result.push_back(letters[dis(gen)]);
    }
    return result;
}
inline bool query_flags(unsigned int value, unsigned int flags)
{
	return (value & flags) == flags;
}

#pragma endregion

#pragma region 安全操作

// 安全删除
#define	SAFE_DELETE(p)			if(p != NULL){ delete (p); (p) = NULL; }

// 安全删除数组
#define	SAFE_DELETE_ARR(p)		if(p != NULL){ delete[] (p); (p) = NULL; }

// 安全释放对象
#define SAFE_RELEASE(pUnk)		if(pUnk != NULL){ (pUnk)->Release(); (pUnk) = NULL; }

// 安全释放
#define SAFE_FREE(h,FN,...)		if(h != NULL){ FN(h,##__VA_ARGS__); (h) = NULL; }

// 安全释放对象 (用于释放用句柄创建,但存储的是对象的情况)
#define SAFE_FREE_OBJ(pObj)		if(pObj != NULL){ EXHANDLE __H__ = pObj->GetHandle(); if(__H__ != NULL) ExHandleClose(__H__); else pObj->Release(); (pObj) = NULL; }

// 安全获取
#define SAFE_GET(pObj,XX)		(pObj != NULL ? pObj->XX : 0)

#pragma endregion

#pragma region 特定值生成

// 生成时钟ID
#define MAKE_TIMER_ID(P,ID)		((UINT_PTR)(((UINT_PTR)P) + (ID)))

// 合并复合整数
#define MAKEDWORD(A, B)			((DWORD_PTR)MAKELONG(A,B))

#pragma endregion

/*-------------指针操作相关函数-------------*/

//取指针size_t值
LONG_PTR __get(LPVOID lpAddr, LONG_PTR offset);
LONG_PTR __gets(LPVOID lpAddr, INT offset);
//取指针INT值
INT __get_int(LPVOID lpAddr, LONG_PTR offset);

//取指针FLOAT值
FLOAT __get_float(LPVOID lpAddr, LONG_PTR offset);
BOOL __get_bool(LPVOID lpAddr, LONG_PTR offset);
//取指针SHORT值
SHORT __get_short(LPVOID lpAddr, LONG_PTR offset);
//置指针SHORT值
SHORT __set_short(LPVOID lpAddr, LONG_PTR offset, SHORT value);
//置指针size_t值
LONG_PTR __set(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value);

//置指针FLOAT值
FLOAT __set_float(LPVOID lpAddr, LONG_PTR offset, FLOAT value);

//置指针INT值
INT __set_int(LPVOID lpAddr, LONG_PTR offset, INT value);

//指针位查询size_t值
BOOL __query(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value);

//指针位查询INT值
BOOL __query_int(LPVOID lpAddr, LONG_PTR offset, INT value);

//指针位删除size_t值
void __del(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value);

//指针位添加size_t值(位或)
void __add(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value);

//指针位添加size_t值(相加)
void __addn(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value);

//指针位减少size_t值(相减)
void __subn(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value);
















