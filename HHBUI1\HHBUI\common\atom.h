﻿#pragma once
#include <inttypes.h>
#include <Windows.h>
namespace HHBUI
{
	/// 原子号类型
	typedef uint32_t EXATOM;
	typedef uint8_t byte_t;
	/// 原子号：未知
	constexpr EXATOM EXATOM_UNKNOWN = 0x00000000;

	////////////////////

	/**
	 * @brief 原子号_自数据
	 * 通过数据计算原子号
	 * @param data 数据指针
	 * @param size 数据大小
	 * @return 返回对应原子号,失败返回{EXATOM_UNKNOWN}
	 */
	EXATOM ExAtomData(const void* data, size_t size);

	/**
	 * @brief 原子号_自字符串
	 * 通过字符串计算原子号
	 * @param str 字符串
	 * @return 返回对应原子号,失败返回{EXATOM_UNKNOWN}
	 */
	EXATOM ExAtomStrCase(LPCWSTR str);

	/**
	 * @brief 原子号_自字符串_忽略大小写
	 * 通过字符串计算原子号
	 * @param str 字符串
	 * @return 返回对应原子号,失败返回{EXATOM_UNKNOWN}
	 */
	EXATOM ExAtomStrNoCase(LPCWSTR str);

	/**
	 * @brief 原子号_自文件
	 * 通过文件计算原子号
	 * @param file 文件路径
	 * @return 返回对应原子号,失败返回{EXATOM_UNKNOWN}
	 */
	EXATOM ExAtomFile(LPCWSTR file);

	inline EXATOM ExAtom(LPCWSTR str)
	{
		return ExAtomStrNoCase(str);
	}
	inline EXATOM ExAtom(const void* ptr, size_t size)
	{
		return ExAtomData(ptr, size);
	}
	template<typename T>
	inline EXATOM ExAtom(const T& p)
	{
		return ExAtomData(&p, sizeof(T));
	}


}
