﻿#include "hhbui.h"
using namespace HHBUI;

void testgroupbox(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 870, 550, L"hello Groupbox", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);
	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);


	auto Group1 = new UIGroupbox(window, 30, 60, 300, 300,L"我是分组框111");
	Group1->SetRadius(10);
	Group1->SetStrokeWidth(2);
	Group1->SetCrBorder(UIColor(22, 183, 119, 255));
	Group1->SetCrBkg(UIColor(255, 184, 0, 155));

	auto Group2 = new UIGroupbox(window, 400, 60, 200, 100, L"我是分组框222");
	Group2->SetTextOffset(50);

	auto tib1 = new UIStatic(Group1, 30, 30, 150, 30, L"test Static1");
	tib1->SetColor(color_border, UIColor(155, 89, 91, 255));
	auto tib2 = new UIStatic(Group2, 30, 30, 150, 30, L"test Static2");
	tib2->SetColor(color_border, UIColor(155, 89, 91, 255));

	window->Show();

	//window->MessageLoop();
}