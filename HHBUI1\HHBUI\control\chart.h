﻿#pragma once
namespace HHBUI {
	enum chart_type {
		bar,		// 柱状图
		line,		// 折线图
		pie,		// 饼图
	};
	class TOAPI UIChart : public UIControl
	{
	public:
		UIChart() = default;
		//创建图表（如无需显示表名则标题填空即可）
		UIChart(UIBase* hParent, INT x, INT y, INT w, INT h, LPCWSTR title, chart_type type = bar, INT nID = 0);
		
		/**
		 * @brief 添加数据项
		 * @param name 子项名
		 * @param data 数据值
		 * @param color 图例颜色
		 * @param index 插入位置，从0开始，负数（默认）为添加到末尾
		 * @return 成功返回子项索引，失败返回-1
		 */
		INT AddItem(LPCWSTR name, float data, UIColor color = UIColor(20, 126, 255), INT index = -1);
		//修改数据项名
		BOOL SetItemName(INT index, LPCWSTR name);
		//修改数据项值
        BOOL SetItemData(INT index, float data);
		//修改数据项颜色
        BOOL SetItemColor(INT index, UIColor color);
		//删除数据项
		BOOL DelItem(INT index);
		//清空数据项
		void ClearItem();

		//设置图表类型
		void SetChartType(chart_type type);
		//设置数值显示方式（0：不显示，1：显示数值，2：显示百分比）
		void SetDataDisplayMode(int mode);
		//设置是否显示图例
        void SetLegend(bool isVL);
		//设置数据上限和下限是否自动计算（取消自动计算后默认下限和上限为0和100，请手动设置）
        void SetAutoMax(bool isAuto);
		//设置数据显示值上限和下限（仅非自动模式生效）
		void SetMaxData(float max, float min);

		//仅柱状图生效：是否为竖向
		void SetBarPoint(bool isVert);

		//仅折线图生效：是否为曲线
        void SetLineCurve(bool isCurve);
		//仅折线图生效：设置线宽
		void SetLineWidth(float width);

		//更新（每次修改数据项后需使用本命令进行更新）
        void Update();

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;

		void paint_bar(ps_context ps);
        void paint_line(ps_context ps);
        void paint_pie(ps_context ps);

		void ceil_data();

		struct chart_item {
			LPCWSTR name = nullptr;
			float data = 0;
			float per = 0.f;
			UIColor color = UIColor(20, 126, 255);

			POINTF ex{ 0.f };
		};

		void CalculatePercentages(std::vector<chart_item>& items);

		struct chart_data {
			int w = 0,h = 0;
			float gth = 16.f;			//参考数据字体高度
			float step = 25;			//数据参考线差值
			int lines = 1;				//参考线数量
			RECT frc = { 0 };
			float lw = 2.f;				//折线图：线宽

			bool maxAuto = true;		//数据上限是否自动计算
			float max = 100, min = 0;	//数据上限、下限
			float barW = 4.f, barS = 1.f;	//柱状图宽度、间隔

			int dataMode = 1;			//数值显示方式（0:不显示,1:数值,2:百分比）

			bool isVL = true;			//是否显示图例
			bool isVert = true;			//柱状图生效：是否为竖向
			bool isCurve = false;		//折线图生效：是否为曲线

            chart_type type = bar;
			std::vector<chart_item> list;

			UIBrush* brLine = nullptr;	//画刷：辅助线
			UIBrush* brItem = nullptr;	//画刷：数据项
			UIFont* ftGen = nullptr;	//字体：通用
		}p_data;
	};
}
