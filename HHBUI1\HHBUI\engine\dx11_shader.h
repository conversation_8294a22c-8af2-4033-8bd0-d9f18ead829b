/**
 * @file dx11_shader.h
 * @brief DX11着色器管理器
 */
/**
** =====================================================================================
**
**       文件名称: dx11_shader.h
**       创建时间: 2025-07-28
**       文件描述: 【HHBUI】DirectX11着色器管理系统 - 高性能着色器编译与管理框架 （声明文件）
**
**       主要功能:
**       - 高性能DirectX11着色器编译与管理
**       - 智能着色器缓存与资源优化
**       - 多类型着色器支持（顶点、像素、几何等）
**       - 实时着色器热重载与调试
**       - 着色器常量缓冲区管理
**       - 预编译着色器与运行时编译
**       - 着色器性能监控与统计
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11 API
**       - COM接口规范与智能指针管理
**       - 异常安全保证与错误恢复机制
**       - 高性能着色器编译与缓存系统
**       - 多线程安全的资源管理
**       - HLSL着色器语言完整支持
**       - 实时性能监控与调试诊断
**
**       更新记录:
**       2025-07-28 v1.0.0.0 : 1. 创建DirectX11着色器管理系统
**                             2. 实现高性能着色器编译框架
**                             3. 添加智能缓存与资源优化
**                             4. 支持多类型着色器管理
**                             5. 集成常量缓冲区管理
**                             6. 添加性能监控与调试
**                             7. 确保线程安全与异常安全
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include "common/unknown_impl.hpp"
#include <d3d11.h>
#include <d3dcompiler.h>
#include <wrl.h>
#include <string>
#include <unordered_map>

namespace HHBUI
{
	/// DX11着色器实现类
	class UIDx11Shader : public ExUnknownImpl<IShader>
	{
		EX_DECLEAR_INTERFACE_BEGIN();
		EX_DECLEAR_INTERFACE(IUnknown);
		EX_DECLEAR_INTERFACE_2(IID_IShader);
		EX_DECLEAR_INTERFACE_2(IID_IRenderObject);
		EX_DECLEAR_INTERFACE_END();

	public:
		UIDx11Shader(ID3D11Device* device, ID3D11DeviceContext* context, ShaderType type);
		virtual ~UIDx11Shader();

		// IRenderObject接口实现
		EXMETHOD RenderType GetRenderType() const override { return RenderType::D3D_ONLY; }
		EXMETHOD const RenderStats& GetRenderStats() const override { return m_stats; }
		EXMETHOD void ResetRenderStats() override { m_stats = RenderStats(); }

		// IShader接口实现
		EXMETHOD HRESULT Compile(LPCWSTR source_code, LPCSTR entry_point, LPCSTR target) override;
		EXMETHOD HRESULT LoadFromFile(LPCWSTR file_path, LPCSTR entry_point, LPCSTR target) override;
		EXMETHOD HRESULT Bind() override;
		EXMETHOD void Unbind() override;
		EXMETHOD ShaderType GetShaderType() const override { return m_shader_type; }
		EXMETHOD HRESULT SetConstantBuffer(uint32_t slot, IBuffer* buffer) override;

		// 获取原生着色器对象
		IUnknown* GetNativeShader() const { return m_shader.Get(); }

	private:
		HRESULT CompileShaderFromSource(LPCWSTR source_code, LPCSTR entry_point, LPCSTR target, ID3DBlob** blob);
		HRESULT CreateShaderFromBlob(ID3DBlob* blob);
		std::string GetShaderTarget() const;

	private:
		Microsoft::WRL::ComPtr<ID3D11Device> m_device;
		Microsoft::WRL::ComPtr<ID3D11DeviceContext> m_context;
		Microsoft::WRL::ComPtr<IUnknown> m_shader;  // 可以是任何类型的着色器
		Microsoft::WRL::ComPtr<ID3DBlob> m_bytecode;
		
		ShaderType m_shader_type;
		RenderStats m_stats;
		bool m_is_bound;
		
		// 常量缓冲区映射
		std::unordered_map<uint32_t, Microsoft::WRL::ComPtr<ID3D11Buffer>> m_constant_buffers;
	};

	/// 着色器管理器
	class UIShaderManager
	{
	public:
		UIShaderManager(ID3D11Device* device, ID3D11DeviceContext* context);
		~UIShaderManager();

		/// 创建着色器
		HRESULT CreateShader(ShaderType type, IShader** shader);

		/// 从文件加载着色器
		HRESULT LoadShaderFromFile(ShaderType type, LPCWSTR file_path, 
			LPCSTR entry_point, IShader** shader);

		/// 从源码编译着色器
		HRESULT CompileShaderFromSource(ShaderType type, LPCWSTR source_code, 
			LPCSTR entry_point, IShader** shader);

		/// 获取预编译的基础着色器
		IShader* GetBasicVertexShader();
		IShader* GetBasicPixelShader();

		/// 重新加载所有着色器
		HRESULT ReloadAllShaders();

		/// 清理资源
		void Cleanup();

	private:
		HRESULT CreateBasicShaders();
		std::string GetShaderTarget(ShaderType type) const;

	private:
		Microsoft::WRL::ComPtr<ID3D11Device> m_device;
		Microsoft::WRL::ComPtr<ID3D11DeviceContext> m_context;
		
		// 基础着色器
		Microsoft::WRL::ComPtr<IShader> m_basic_vertex_shader;
		Microsoft::WRL::ComPtr<IShader> m_basic_pixel_shader;
		
		// 着色器缓存
		std::unordered_map<std::wstring, Microsoft::WRL::ComPtr<IShader>> m_shader_cache;
	};

	/// 预定义的基础着色器源码
	namespace BasicShaders
	{
		// 基础顶点着色器
		extern const char* BASIC_VERTEX_SHADER;
		
		// 基础像素着色器
		extern const char* BASIC_PIXEL_SHADER;
		
		// 2D UI顶点着色器
		extern const char* UI_VERTEX_SHADER;
		
		// 2D UI像素着色器
		extern const char* UI_PIXEL_SHADER;
		
		// 文本渲染像素着色器
		extern const char* TEXT_PIXEL_SHADER;
		
		// 图像效果像素着色器
		extern const char* IMAGE_EFFECT_PIXEL_SHADER;
	}
}
