﻿#pragma once
#include <map>
#include <vector>
#include <functional>
namespace HHBUI
{
	struct info_objcaption
	{
		UIColor crTitle;                //标题颜色
		UIColor crBkg;                  //标题栏背景颜色
		UIColor crbutton_normal;        //按钮颜色-默认
		UIColor crbutton_hover;         //按钮颜色-点燃
		UIColor crbutton_down;          //按钮颜色-按下
		UIColor crBkgbutton_normal;     //按钮背景颜色-默认
		UIColor crBkgbutton_hover;      //按钮背景颜色-点燃
		UIColor crBkgbutton_down;       //按钮背景颜色-按下
		UIImage *imgbutton_close;     //按钮图像-关闭 1
		UIImage *imgbutton_max;       //按钮图像-最大化 2
		UIImage *imgbutton_restore;   //按钮图像-还原 3
		UIImage *imgbutton_min;       //按钮图像-最小化 4
		UIImage *imgbutton_menu;      //按钮图像-菜单 5
		UIImage *imgbutton_skin;      //按钮图像-皮肤 6
		UIImage *imgbutton_setting;   //按钮图像-设置 7
		UIImage *imgbutton_help;      //按钮图像-帮助 8
		HICON hicon;                 //标题栏图标
		INT titlebHeight;            //标题栏高度 默认40
		MsgPROC pfnSubmsg;        //事件回调
		//字体信息
		LOGFONTW* Fontinfo;
		LPCWSTR lpszFontfamily;
		INT dwFontsize;
		INT dwFontstyle;//参考 FontStyle
		INT dwTextFormat;   // 	文本格式
	};
	// 背景信息结构
	struct info_backgroundimage
	{
		DWORD dwFlags;   //标识
		UIImage *hImage; //图片句柄
		INT x;           //左上角横坐标
		INT y;           //左上角纵坐标
		DWORD dwRepeat;  //重复方式
		LPRECT lpGrid;   //九宫矩形
		DWORD curFrame;  //当前帧
		DWORD maxFrame;  //最大帧
		DWORD dwAlpha;   //透明度
		INT nDelay;
	};
	enum WndMsgwmEx
	{
		WM_EX_LCLICK = -3,                  //左键单击组件
		WM_EX_RCLICK = -4,                  //右键单击组件
		WM_EX_MCLICK = -5,                  //中键单击组件
		WM_EX_INITPOPUP = -6,                  //弹出式窗口初始化完毕
		WM_EX_EXITPOPUP = -7,                  //弹出式窗口即将销毁 wParam=0:即将销毁 wParam=1:是否可销毁, 返回1则取消销毁
		WM_EX_EASING = -8,                  //缓动	挂接事件用WMM_EASING
		WM_EX_DROP = -9,                  //控件接收到拖放 lParam为H_DROPINFO结构体,若处理后应当返回 DROPEFFECT_开头的常量
		WM_EX_LDCLICK = -12,                 //左键双击
		WM_EX_TRAYICON = -22,                 //托盘消息 lParam 为传递msg
		WM_EX_MENU = -23,                 //菜单消息 wParam 选中菜单ID lParam菜单句柄 该消息响应在弹出时 指定父句柄为控件消息 如果父句柄为窗口 用WM_COMMAND

	};
	enum EventFlags
	{
		WMM_CREATE = -99,                  //创建
		WMM_MOVE = -96,                  //控件移动
		WMM_SIZE = -95,                  //尺寸被改变
		WMM_ENABLE = -94,                  //禁止状态被改变
		WMM_SHOW = -93,                  //可视状态被改变
		WMM_LUP = -92,                  //左键被放开
		WMM_LEAVE = -91,                  //离开组件
		WMM_TIMER = -90,                  //时钟
		WMM_CHECK = -89,                  //选中
		WMM_TRAYICON = -88,                  //托盘图标
		WMM_INTDLG = -87,                  //对话框初始化完毕
		WMM_EASING = -86,                  //缓动
		WMM_RUP = -85,                  //右键被放开
		WMM_CLICK = -2,                   //左键被单击
		WMM_DBLCLK = -3,                   //左键被双击
		WMM_RCLICK = -5,                   //右键被单击
		WMM_RDBLCLK = -6,                   //右键被双击
		WMM_SETFOCUS = -7,                   //设置焦点
		WMM_KILLFOCUS = -8,                   //失去焦点
		WMM_WRAPPERRAW = -11,                  //空项目绘制提示 lParam为ps_context结构
		WMM_CUSTOMDRAW = -12,                  //自定义绘制 wParam为当前项目、lParam为ps_context结构
		WMM_HOVER = -13,                  //进入组件
		WMM_NCHITTEST = -14,                  //点击测试
		WMM_KEYDOWN = -15,                  //按下某键
		WMM_RELEASEDCAPTURE = -16,                  //取消鼠标捕获
		WMM_CHAR = -18,                  //字符输入
		WMM_TOOLTIPSCREATED = -19,                  //提示框即将弹出
		WMM_LDOWN = -20,                  //左键被按下
		WMM_RDOWN = -21,                  //右键被按下
		WMM_FONTCHANGED = -23,                  //字体被改变
		WMM_EX_CUSTOMDRAW = -24,                  //扩展自定义绘制 lParam为ps_customdraw结构
	};
	enum WndStyle : uint32_t
	{
		UISTYLE_BTN_CLOSE = 1,                   //关闭按钮
		UISTYLE_BTN_MAX = 1 << 1,                //最大化按钮
		UISTYLE_BTN_MIN = 1 << 2,                //最小化按钮
		UISTYLE_BTN_MENU = 1 << 3,               //菜单按钮
		UISTYLE_BTN_SKIN = 1 << 4,               //皮肤按钮
		UISTYLE_BTN_SETTING = 1 << 5,            //设置按钮
		UISTYLE_BTN_HELP = 1 << 6,               //帮助按钮
		UISTYLE_HASICON = 1 << 7,                //图标
		UISTYLE_TITLE = 1 << 8,                  //标题
		UISTYLE_FULLSCREEN = 1 << 9,             //全屏模式
		UISTYLE_SIZEABLE = 1 << 10,              //允许调整尺寸
		UISTYLE_MOVEABLE = 1 << 11,              //允许随意移动
		UISTYLE_NOSHADOW = 1 << 12,              //不显示窗口阴影
		UISTYLE_NOINHERITBKG = 1 << 13,          //不继承父窗口背景
		UISTYLE_NOTABBORDER = 1 << 14,           //不显示TAB焦点边框
		UISTYLE_ESCEXIT = 1 << 15,               //ESC关闭窗口
		UISTYLE_MMODAL = 1 << 16,                //模态窗口
		UISTYLE_CENTERWINDOW = 1 << 17,          //窗口居中
		UISTYLE_NOCAPTIONTOPMOST = 1 << 18,      //标题栏取消置顶
		UISTYLE_POPUPWINDOW = 1 << 19,           //弹出式窗口
		UISTYLE_NOTITLEBAR = 1 << 22,            //取消标题栏
	};
	enum BackgFlags
	{
		bif_default = 0,                    //默认
		bif_playimage = 1,                  //播放动画
		bif_disablescale = 1 << 1,          //禁用缩放

		//背景重复模式
		bir_default = 0,                   //默认(适应缩放)
		bir_epault = 1,                    //等比缩放
		bir_epault_center = 2,             //等比居中缩放
		bir_no_repeat = 3,                 //平铺(不重复)
		bir_repeat = 4,                    //水平垂直居中
		bir_repeat_center = 5,             //平铺居中
	};
	enum ToastsFlags
	{
		Toast_info = 0,                   //信息
		Toast_error = 1,                   //错误
		Toast_enquire = 1 << 2,              //询问
		Toast_success = 1 << 3,              //成功
		Toast_warning = 1 << 4,              //警告

		Toast_type_center = 0,                   //方向 居中上
		Toast_type_left_top = 1 << 5,              //方向 靠左上
		Toast_type_right_top = 1 << 6,              //方向 靠右上
		Toast_type_right_bottom = 1 << 7,              //方向 靠右下
	};

	// 窗口吸附位置枚举
	enum class SnapPosition : uint32_t
	{
		None = 0,           // 无吸附
		Left = 1,           // 左侧吸附
		Right = 2,          // 右侧吸附
		Top = 3,            // 顶部吸附
		Bottom = 4,         // 底部吸附
		TopLeft = 5,        // 左上角吸附
		TopRight = 6,       // 右上角吸附
		BottomLeft = 7,     // 左下角吸附
		BottomRight = 8,    // 右下角吸附
		Center = 9,         // 居中
		Maximize = 10       // 最大化
	};

	// 窗口布局模式枚举
	enum class LayoutMode : uint32_t
	{
		Manual = 0,         // 手动布局
		Cascade = 1,        // 层叠布局
		TileHorizontal = 2, // 水平平铺
		TileVertical = 3,   // 垂直平铺
		Grid = 4,           // 网格布局
		Smart = 5           // 智能布局
	};

	// 窗口吸附配置结构
	struct SnapConfig
	{
		bool enabled = true;                // 是否启用吸附
		int sensitivity = 10;               // 吸附敏感度(像素)
		int edgeMargin = 5;                // 边缘边距
		bool snapToWindows = true;         // 是否吸附到其他窗口
		bool snapToScreen = true;          // 是否吸附到屏幕边缘
		bool showPreview = true;           // 是否显示吸附预览
		UIColor previewColor = UIColor(100, 150, 255, 128); // 预览颜色
	};

	// 窗口布局信息结构
	struct WindowLayoutInfo
	{
		RECT originalRect;                 // 原始位置
		RECT currentRect;                  // 当前位置
		SnapPosition snapPosition;         // 吸附位置
		LayoutMode layoutMode;             // 布局模式
		int gridRow = -1;                  // 网格行(仅网格布局)
		int gridCol = -1;                  // 网格列(仅网格布局)
		bool isSnapped = false;           // 是否已吸附
		bool isInLayout = false;          // 是否在布局中
	};
	class TOAPI UIWnd : public UILayout
	{
	public:
		UIWnd() = default;
		~UIWnd();
		/*创建窗口
		* @param x,y,width,height 窗口尺寸
		* @param lpwzWindowName 窗口标题
		* @param dwStyle 窗口样式 WS_ 开头的Windows样式
		* @param dwStyleEx 扩展窗口样式 WS_EX_ 开头的Windows样式
		* @param dwUIStyle 界面样式 wws_开头
		* @param hWndParent 父窗口
		* @param lpwzClassName 窗口类名
		* @param lParam 附加参数
		* @param lpfnMsgProc 窗口创建后回调函数
		*/
		UIWnd(INT x, INT y, INT width, INT height, LPCWSTR lpwzWindowName, INT dwStyle = NULL, INT dwStyleEx = NULL, DWORD dwUIStyle = UISTYLE_CENTERWINDOW, HWND hWndParent = 0,
			LPCWSTR lpwzClassName = NULL, LPARAM lParam = 0, MsgPROC lpfnMsgProc = 0);
		UIWnd(HWND hWnd, DWORD dwUIStyle, LPARAM lParam = 0, MsgPROC lpfnMsgProc = 0);
		/*创建界面自XML文档
		* @param hWndParent - 父句柄 可为NULL
		* @param hRes - 资源包 可为NULL
		* @param lpLayoutXml - xml文档(如果指定了资源包 则为文件名从资源包读取)
		* @param lpfnMsgProc - 窗口创建后回调函数
		* @return 是否创建成功
		*/
		BOOL CreateUIFromXML(HWND hWndParent, UIZip* hRes, LPCWSTR lpLayoutXml, MsgPROC lpfnMsgProc = 0);
		/*保存窗口界面为XML文档到文件*/
		BOOL SaveUIFromXmlFile(LPCTSTR pszSavePath);
		//注册窗口类名
		static BOOL RegClass(LPCWSTR lpwzClassName, HICON hIcon = 0, HICON hIconsm = 0);
		//居中
		void CenterFrom();
		//最大化
		void Max();
		//最小化
		void Min();
		//取窗口句柄
		HWND GethWnd();
		//取窗口父句柄
		HWND GethWndParent();
		UIWnd* GetInstance();
		//取客户区矩形
		void GetClientRect(ExRectF& lpClientRect);
		//取窗口矩形
		void GetRect(ExRectF& lptRect);
		//从窗口句柄获取绑定界面
		static UIWnd* GetFromUIWnd(HWND hWnd);
		//取附加参数
		LPARAM GetlParam();
		//置附加参数
		void SetlParam(LPARAM dwlParam);
		//取窗口功能键
		INT GetKeys();
		//设置或获取标志
		INT Flags(INT dwFlags = 0);
		ti_s* GetToolrack(BOOL iRrack);
		//窗口可视
		BOOL Show(INT nCmdShow = SW_SHOWNORMAL);
		//查询可视
		BOOL IsVisible();
		//查询禁止
		BOOL IsDisabled();
		//设置窗口允许调整的最小尺寸
		void SetMinTrackSize(INT MinWidth, INT MinHeight);
		/*
         * @brief 设置标题
         * @param  lpString            [LPCWSTR]         新标题
         * @param  fTleRedraw          [BOOL]            是否只更新标题栏{不管有没有UISTYLE_NOTITLEBAR风格}
         * @return [BOOL]返回是否成功
         */
		BOOL SetText(LPCWSTR lpString, BOOL fTleRedraw = false);
		/*
         * @brief 获取标题
         * @param  fTleGet             [BOOL]            是否通过标题栏获取{如果存在}
         * @return [LPCWSTR]返回标题内容
         */
		LPCWSTR GetText(BOOL fTleGet = false);
		//查找控件、可以是类名(或ID)
		LPVOID FindUIView(LPCWSTR lpName);
		/*获取所有控件数量*/
		INT GetChildrenCout() const;
		/*
		 * @brief 设置和获取界面风格
		 * @param  wwsStyle           [DWORD]         参考wws_开头、参数为0获取
		 * @param  dwNewlong          [BOOL]          是否替换模式 如果为替换模式将会设置为新值而不是叠加，相反如果原先拥有该值会剔除 没有该值则会设置
		 * @return [DWORD]如果wwsStyle为0返回当前界面风格 否则返回0
		 */
		DWORD UIStyle(DWORD wwsStyle = 0, BOOL dwNewlong = false);
		/*
		 * @brief 设置和获取窗口风格
		 * @param  Style           [INT]         参考WS_开头、参数为0获取
		 * @return [INT]如果Style为0返回当前窗口风格 否则返回0
		 */
		INT Style(INT dwStyle = 0);
		/*
		 * @brief 设置和获取窗口扩展风格
		 * @param  Style           [INT]         参考WS_EX_开头、参数为0获取
		 * @return [INT]如果Style为0返回当前窗口风格 否则返回0
		 */
		INT StyleEx(INT dwStyleEx = 0);
		//移动窗口 不需要移动的参数设置为CW_USEDEFAULT
		BOOL Move(INT x, INT y, INT width = CW_USEDEFAULT, INT height = CW_USEDEFAULT, BOOL bRepaint = false);
		//更新窗口
		void Update(BOOL bUpdateLayout = false);
		//设置阴影颜色
		void SetShadowColor(UIColor dwColor, INT fSize = 0);
		//获取阴影颜色
		void GetShadowColor(UIColor& dwColor, INT& fSize);
		//设置边框颜色
		void SetBorderColor(UIColor dwColor, INT fSize = 0);
		//获取边框颜色
		void GetBorderColor(UIColor& dwColor, INT& fSize);
		//获取留白大小
		INT GetSideSize();
		//设置背景颜色
		void SetBackgColor(UIColor dwColor);
		//设置背景图像(会优先级于背景颜色)
		BOOL SetBackgImage(LPVOID lpImage, size_t dwImageLen, INT x = 0, INT y = 0, DWORD dwRepeat = bir_default, RECT* lpGrid = 0, INT dwFlags = bif_default, DWORD dwAlpha = 255);
		BOOL SetBackgImage(UIImage *hImage, BOOL fReset = false, INT x = 0, INT y = 0, DWORD dwRepeat = bir_default, RECT* lpGrid = 0, INT dwFlags = bif_default, DWORD dwAlpha = 255);
		BOOL SetBackgImage(LPCWSTR lpImagefile, INT x = 0, INT y = 0, DWORD dwRepeat = bir_default, RECT* lpGrid = 0, INT dwFlags = bif_default, DWORD dwAlpha = 255);
		//更新背景信息
		void SetBackgInfo(INT x = 0, INT y = 0, DWORD dwRepeat = bir_default, RECT* lpGrid = 0, INT dwFlags = bif_default, DWORD dwAlpha = 255);
		//设置背景播放.
		BOOL SetBackgPlay(BOOL fPlayFrames, BOOL fResetFrame = false);
		//是否启用输入法
		void SetIme(BOOL bEnable);
		//设置圆角度
		void SetRadius(INT radius);
		//获取圆角
		INT GetRadius();
		//设置透明度
		void SetAlpha(INT fAlpha);
		//设置标题栏信息
		void SetCaptionInfo(info_objcaption* Info);
		//获取标题栏信息
		LPVOID GetCaptionInfo(info_objcaption& Info, INT uType);
		//获取标题栏控制按钮
		LPVOID GetCaptionObj(INT uType = UISTYLE_BTN_CLOSE);
		/*
		 * @brief 设置模糊
		 * @param  fDeviation          [FLOAT]        模糊系数
		 * @param  fmodesoft           [BOOL]         是否硬边模糊
		 */
		void SetBlur(FLOAT fDeviation, BOOL fmodesoft = false, BOOL bRedraw = false);
		/*
         * @brief 弹出信息框
         * @param lpText 提示内容
         * @param lpTitle 标题
         * @param uType 类型 参考MB_开头 https://learn.microsoft.com/zh-cn/windows/win32/api/winuser/nf-winuser-messageboxw
         * @param lpCheckBox 选择框标题
         * @param lpCheckBoxChecked 选择框选中状态返回
         * @param dwUIStyle 界面风格标志 参考styleex_开头
         * @param crText 文本颜色
         * @param crBackground 背景颜色 需要界面风格标志styleex_noinheritbkg才有效
		 * @param dwButfig 按钮配置信息 传入info_button_config结构
         * @param dwMilliseconds 倒计时关闭时间,以毫秒为单位 如果超时，即用户未操作，消息框自动关闭，返回MB_TIMEDOUT
         * @param lpfnMsgProc 消息回调
         * @return [INT]如果消息框具有"取消"按钮 ，则如果按下 ESC 键或选中 "取消"按钮，该函数将返回 IDCANCEL 值。 如果消息框没有 取消 按钮，则按 ESC 将不起作用 - 除非存在MB_OK按钮。 如果显示MB_OK按钮，并且用户按下 ESC，则返回值将 IDOK
         */
		INT PopupMsg(LPCWSTR lpText, LPCWSTR lpTitle = NULL, INT uType = MB_ICONWARNING, LPCWSTR lpCheckBox = NULL, BOOL* lpCheckBoxChecked = 0,
			INT dwUIStyle = styleex_centewindow, UIColor crText = {}, UIColor crBackground = {}, LPVOID dwButfig = nullptr, INT dwMilliseconds = 0, MsgPROC lpfnMsgProc = nullptr);

		/*
		 * @brief 弹出托盘图标
		 * @param  lpwzInfo  提示信息
		 * @param  lpwzInfoTitle 提示标题
		 * @param  dwInfoFlags 标志
		 */
		BOOL PopupTrayIcon(LPCWSTR lpwzInfo, LPCWSTR lpwzInfoTitle = NULL, INT dwInfoFlags = NIIF_INFO);
		 /*
		 * @brief 弹出提示 一次性最多同时显示7条 ＞此数量剩余会处于挂起状态 等满足显示条件后会逐一显示剩余内容
		 * @param  uType  类型和方向 参考Toast_info|Toast_type_XX
		 * @param  dwMilliseconds 倒计时关闭时间,以秒为单位
		 * @param  ptOffset 偏移位置 默认30
		 * @param  hFontSize 字体大小
		 */
		BOOL PopupToast(LPCWSTR lpText, INT uType = Toast_info, INT dwMilliseconds = 0, INT ptOffset = 0, INT hFontSize = 15);
		//保存为图像 注意：如果控件不可见或未绘制状态需要打开cdraw
		HRESULT ToImage(UIImage** dstImg, BOOL cdraw = false, FLOAT fBlur = 0.f);
		/*获取最后一次绘制的帧率*/
		UINT GetLastFPS() const;
		/*设置渲染模式(默认被动渲染)
	     * @param active - 是否为主动渲染模式
	     */
		void SetRenderMode(bool active);
		//消息循环
		void MessageLoop();

		// ==================== 窗口吸附和布局管理功能 ====================

		/*
		 * @brief 启用或禁用窗口吸附功能
		 * @param enable 是否启用吸附
		 */
		void EnableWindowSnapping(bool enable = true);

		/*
		 * @brief 设置窗口吸附配置
		 * @param config 吸附配置
		 */
		void SetSnapConfig(const SnapConfig& config);

		/*
		 * @brief 获取窗口吸附配置
		 * @return 当前吸附配置
		 */
		SnapConfig GetSnapConfig() const;

		/*
		 * @brief 将窗口吸附到指定位置
		 * @param position 吸附位置
		 * @param animated 是否使用动画
		 */
		void SnapToPosition(SnapPosition position, bool animated = true);

		/*
		 * @brief 检查窗口是否已吸附到边缘
		 * @return 是否已吸附
		 */
		bool IsSnappedToEdge() const;

		/*
		 * @brief 获取当前窗口的吸附位置
		 * @return 当前吸附位置
		 */
		SnapPosition GetSnapPosition() const;

		/*
		 * @brief 设置窗口布局模式
		 * @param mode 布局模式
		 */
		void SetLayoutMode(LayoutMode mode);

		/*
		 * @brief 获取当前布局模式
		 * @return 当前布局模式
		 */
		LayoutMode GetLayoutMode() const;

		/*
		 * @brief 建议最优窗口位置
		 * @return 建议的窗口矩形
		 */
		RECT SuggestOptimalPosition();

		/*
		 * @brief 与其他窗口协调排列
		 * @param windows 参与排列的窗口列表
		 */
		static void ArrangeWindows(const std::vector<UIWnd*>& windows, LayoutMode mode);

		/*
		 * @brief 获取窗口布局信息
		 * @return 布局信息
		 */
		WindowLayoutInfo GetLayoutInfo() const;

		/*
		 * @brief 重置窗口到原始位置
		 * @param animated 是否使用动画
		 */
		void ResetToOriginalPosition(bool animated = true);

		/*
		 * @brief 保存当前窗口位置为原始位置
		 */
		void SaveCurrentAsOriginal();

		/*
		 * @brief 获取屏幕工作区域
		 * @return 工作区域矩形
		 */
		RECT GetWorkArea() const;

		/*
		 * @brief 获取所有可见窗口的矩形
		 * @return 窗口矩形列表
		 */
		static std::vector<RECT> GetAllVisibleWindowRects();

		/*
		 * @brief 检测窗口碰撞
		 * @param rect 要检测的矩形
		 * @return 是否有碰撞
		 */
		bool DetectWindowCollision(const RECT& rect) const;
		/*设定计时器
		* @param pPropObj - 计时器对象
		* @param uTimerID - 计时器ID
		* @param elapse - 计时器间隔
		*/
		virtual BOOL SetTimer(UIBase *pPropObj, size_t uTimerID, UINT uElapse);
		virtual BOOL SetTimer(size_t uTimerID, UINT uElapse);
		virtual BOOL FindTimer(UIBase *pPropObj, size_t uTimerID);
		/*删除计时器
		* @param pPropObj - 计时器对象
		* @param uTimerID - 计时器ID
		*/
		virtual BOOL KillTimer(UIBase *pPropObj, size_t uTimerID);
		virtual BOOL KillTimer(UIBase *pPropObj);
		virtual BOOL KillTimer();
		virtual BOOL RemoveAllTimer();

		//发送消息
		LRESULT SendMsg(INT uMsg, WPARAM wParam = 0, LPARAM lParam = 0);
		//发送消息回调 当窗口有消息回调时可以发送事件用于接收
		LRESULT SendMsgProc(INT uMsg, WPARAM wParam = 0, LPARAM lParam = 0);

	protected:
		INT wm_nchittest(LPARAM lParam);
		void wm_ps_bkg();
		void wm_ps_bkg(UIWnd* p);
		void wm_size(WPARAM wParam, INT width, INT height);
		void wm_recalcclient(INT width, INT height);
		void wm_renderPresent(UIControl* objChildFirst, ExRectF rcPaint, INT offsetX, INT offsetY);
		void wm_getmmaxinfo(LPARAM lParam);
		BOOL wm_stylechanging(WPARAM wParam, LPARAM lParam);
		void wm_nchittestlist(UIControl* objLast, INT x, INT y, INT* hitCode, UIControl* &objMouse);
		BOOL wm_setcursor(LPARAM lParam);
		INT wm_popupclose();
		void wm_leavecheck(UIControl* objCheck, UIControl* objHittest, LPARAM lParam, BOOL fTrack);
		void wm_ime_composition();
		BOOL wm_keyboard(INT uMsg, WPARAM wParam, LPARAM lParam);
		BOOL wm_handlekeydown(WPARAM wParam, UIControl* objFocus, BOOL bPopupWindowShown, LPARAM lParam);
		BOOL wm_handlekeyup(WPARAM wParam, UIControl* objFocus);
		BOOL wm_handlecopy(UINT uMsg, UIControl* objFocus);
		BOOL wm_handleimechar(WPARAM wParam, LPARAM lParam, UIControl* objFocus);
		void wm_mouse(INT uMsg, WPARAM wParam, LPARAM lParam);
		void wm_obj_settabstop(UIControl* objLastFocus);
		BOOL wm_obj_setnextfocus(UIControl* objEntry, UIControl* objLastFocus);
		void wm_buttondown(UIControl* parent, INT uMsg, WPARAM wParam, LPARAM lParam);
		void wm_obj_untrack(LPARAM lParam, BOOL fMsgDispatch);
		void wm_menucontext(INT uMsg, WPARAM wParam, LPARAM lParam);
		void wm_command(INT uMsg, WPARAM wParam, LPARAM lParam);
		BOOL wm_setbackgImage(UIImage *hImg, INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha);
		void wm_calc_captionrect(RECT& rcCaption);
		void wm_sys_init();
		void wm_initmenupopup(HMENU hMenu);
		void wm_menu_setpos(HWND hWnd, tagWINDOWPOS* pos);
		void wm_menu_createitems(HWND hWnd);
		BOOL wm_measureitem_host(WPARAM wParam, LPARAM lParam);
		BOOL wm_menu_mouse(HWND hWnd, INT uMsg, WPARAM wParam, LONG_PTR* iItem);
		BOOL wm_build_load(HWND hWnd, DWORD dwUIStyle, LPARAM lParam = 0, MsgPROC lpfnMsgProc = 0);
		BOOL wm_createuiwnd(INT x, INT y, INT width, INT height, LPCWSTR lpwzWindowName, INT dwStyle, INT dwStyleEx, DWORD dwUIStyle,
			HWND hWndParent, LPCWSTR lpwzClassName, LPARAM lParam, MsgPROC lpfnMsgProc);
		void wm_xml_load_init(UIZip* hRes, LPVOID node, int depth, UIBase *parent);
		void wm_xml_load_done(UIBase*& parent, UIBase* handle, const std::wstring& root_name, RECT rect, LPVOID node);

		// 窗口吸附和布局管理私有方法
		void wm_snap_init();                                    // 初始化吸附功能
		void wm_snap_cleanup();                                 // 清理吸附功能
		void wm_snap_on_move_start(POINT startPos);            // 开始移动时的吸附处理
		void wm_snap_on_moving(POINT currentPos);              // 移动过程中的吸附处理
		void wm_snap_on_move_end();                            // 移动结束时的吸附处理
		SnapPosition wm_snap_detect_position(const RECT& rect); // 检测吸附位置
		RECT wm_snap_calculate_rect(SnapPosition position);     // 计算吸附矩形
		void wm_snap_show_preview(const RECT& rect);           // 显示吸附预览
		void wm_snap_hide_preview();                           // 隐藏吸附预览
		bool wm_snap_is_near_edge(const RECT& rect, int edge); // 检查是否接近边缘
		std::vector<RECT> wm_snap_get_snap_targets();          // 获取吸附目标
		void wm_layout_apply_position(SnapPosition position, bool animated); // 应用布局位置
		RECT wm_layout_calculate_grid_position(int row, int col, int totalRows, int totalCols); // 计算网格位置

		friend class DropTarget;
	private:
		static LRESULT CALLBACK OnWndProc(HK_THUNK_DATA* pData, UINT uMsg, WPARAM wParam, LPARAM lParam);
		static LRESULT CALLBACK OnToolTipWndProc(HK_THUNK_DATA* pData, UINT uMsg, WPARAM wParam, LPARAM lParam);
		static void CALLBACK OnMouseTrack_Timer(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime);
		static LRESULT CALLBACK OnToastsMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);
		void OnPaintAndCalcControl();
		void OnPaintAndRender(LPVOID hDC, RECT rcPaint, BOOL fLayer);
		struct Tips_Tmp
		{
			std::wstring lpText;
			INT uType;
			INT dwMilliseconds;
			INT ptOffset;
			INT hFontSize;
		};
	
		struct WndData
		{
			HWND hWnd = 0, hWndParent = 0, hWndTips = 0, hWndPopup = 0;
			DWORD dwUIStyle = 0;
			HIMC hImc = 0;
			LPARAM lParam = 0;
			POINT MinTrackSize{};
			ExRectF size{}, margin_caption{};
			SIZE size_caption{};
			std::unique_ptr<UIBrush> crBorder = nullptr, crshadow = nullptr, crbkg = nullptr;
			std::atomic<info_backgroundimage*> lpBackgroundImage = nullptr;
			std::atomic<UIRegion*> hrgn_client = nullptr;
			std::atomic<UIBrush*> hPath_Brush = nullptr;
			std::atomic<UIPath*> hPath_radius = nullptr;
			INT radius = 0, radius_r = 0, size_Border = 0, size_shadow = 0, size_tmp = 0, alpha = 0, dwHitCode = 0, dx_counts = 0, dwWinState = 0, dwFlags = 0;
			FLOAT fBlur = 0;
			BOOL fmodesoft = false;
			MsgPROC pfnMsgProc = nullptr;
			mbp_s* lpMsgParams = nullptr;
			menu_s* lpMenuParams = nullptr;
			HMENU hMenuPopup = nullptr;
			std::atomic<UIFont*> hFont_Menu = nullptr;
			std::atomic<UIWnd*> pMenuHostWnd = nullptr, hParent = nullptr, pMenuPrevWnd = nullptr, pMenuTrackWnd = nullptr;
			size_t dwHitObjPos_Abs = 0;
			ti_s* toolauto = nullptr, * toolrack = nullptr;
			std::atomic<UICanvas*> canvas_display = nullptr, canvas_bkg = nullptr;
			std::atomic<LPVOID> objCaption = nullptr, lpNid = nullptr, lpPopupParams = nullptr, dsToast = nullptr;
			std::atomic<UIControl*> objNext = nullptr, objPrev = nullptr, objFocus = nullptr, objFocusPrev = nullptr, objTrack = nullptr, objTrackPrev = nullptr, objHittest = nullptr,
				objHittestPrev = nullptr, objMenucontext = nullptr;
			VecTimerInfo vecTimers;	UINT uTimerID = 0x1000;
			std::atomic<UIImage*> dstImg = nullptr;
			std::atomic_bool renderMode = false;
			std::deque<Tips_Tmp> hTableTips;

			// 窗口吸附和布局管理相关数据
			SnapConfig snapConfig;                    // 吸附配置
			WindowLayoutInfo layoutInfo;             // 布局信息
			bool isDragging = false;                  // 是否正在拖拽
			bool isSnapping = false;                  // 是否正在吸附
			POINT dragStartPos{};                     // 拖拽起始位置
			RECT snapPreviewRect{};                   // 吸附预览矩形
			std::unique_ptr<UIBrush> snapPreviewBrush = nullptr; // 吸附预览画刷
			HWND snapPreviewWnd = nullptr;            // 吸附预览窗口
		}m_data;
		std::map<std::wstring, LPVOID> m_hTableObjects;
		std::map<INT, std::vector<EX_EVENT_HANDLER>> m_hTableEvent;

		friend class UICanvas;
		friend class UIControl;
		friend class UILayout;
		friend class UIhook;
		friend class UIMenu;
		friend class UIItem;
	};
}