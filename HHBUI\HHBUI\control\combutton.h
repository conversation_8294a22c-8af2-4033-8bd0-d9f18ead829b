﻿#pragma once
#define M_PI (3.1415926536f)

namespace HHBUI
{
	class TOAPI UICombutton : public UIControl
	{
	public:
		UICombutton(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0,
			INT dwTextFormat = -1);
		~UICombutton();
		void SetIcon(HICON dwicon, BOOL fCalcSize = FALSE);
		//设置按钮颜色 默认按钮有效
		void SetCrbutton(UIColor normal, UIColor hover, UIColor down);
		void GetCrbutton(UIColor& normal, UIColor& hover, UIColor& down);
		void SetCrBkgbutton(UIColor normal, UIColor hover, UIColor down);
		void GetCrBkgbutton(UIColor& normal, UIColor& hover, UIColor& down);
		void SetImgbutton(UIImage *img1, UIImage *img2 = 0);
		void Update();
		void Clear();
		INT Reset();
	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		void remove(INT width, INT height);

		int CalcCircleAutoSegmentCount(float radius);
		void SetCircleTessellationMaxError(float max_error);
		struct Combutton_s
		{
			float CircleSegmentMaxError = 0.f, start = 0.f;
			BYTE CircleSegmentCounts[64] = {0};
			UIBrush* hBrush = nullptr;
			UIBrush* hBrush_Bkg = nullptr;
			UIColor crbutton_[3]{};
			UIColor crBkgbutton_[3]{};
			UIImage *imgbutton[2]{};//第二组用于最大化还原
			D2D1_SIZE_U hiconsize{};
			UIImage *hicon = 0;
		}p_data;

	};
}
