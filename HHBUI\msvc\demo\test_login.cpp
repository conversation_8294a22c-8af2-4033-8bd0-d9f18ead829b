﻿#include "hhbui.h"
#include "demo.h"
#include "resource_loader.h"

using namespace HHBUI;
using namespace ResDLL; // 修改为使用ResDLL命名空间

LRESULT CALLBACK OnLoginCallback(LPCWSTR username, LPCWSTR password)
{
    // 在实际应用中，这里应该进行用户身份验证
    if (wcs<PERSON>(username) > 0 && wcslen(password) > 0)
    {
        std::wstring message = L"登录成功！\n用户名: ";
        message += username;
        message += L"\n密码: ";
        message += password;
        MessageBoxW(NULL, message.c_str(), L"登录信息", MB_OK | MB_ICONINFORMATION);
        return S_OK;
    }
    else
    {
        MessageBoxW(NULL, L"用户名或密码不能为空！", L"登录错误", MB_OK | MB_ICONERROR);
        return S_FALSE;
    }
}

LRESULT CALLBACK OnRegisterCallback()
{
    MessageBoxW(NULL, L"您点击了注册按钮\n这里可以跳转到注册页面", L"注册", MB_OK | MB_ICONINFORMATION);
    return S_OK;
}

LRESULT CALLBACK OnForgetPasswordCallback()
{
    MessageBoxW(NULL, L"您点击了忘记密码\n这里可以跳转到找回密码页面", L"找回密码", MB_OK | MB_ICONINFORMATION);
    return S_OK;
}

// 直接处理图片适配模式的回调函数，不再使用菜单弹出方式
LRESULT CALLBACK OnChangeFitModeCallback(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
    if (nCode == WMM_CLICK)
    {
        auto window = (UIWnd*)pWnd;
        if (!window) return S_FALSE;

        // 获取登录控件
        auto loginPanel = (UILogin*)window->FindUIView(L"2000");
        if (!loginPanel) return S_FALSE;

        // 直接在按钮上循环切换左侧背景图的适配模式
        static int currentMode = 0; // 0=COVER, 1=FILL, 2=CONTAIN, 3=CENTER
        
        // 切换到下一个模式
        currentMode = (currentMode + 1) % 4;
        
        // 应用新模式
        UILogin::ImageFitMode mode = UILogin::ImageFitMode::COVER;  // 设置默认值
        switch (currentMode) {
        case 0: 
            mode = UILogin::ImageFitMode::COVER;
            MessageBoxW(window->GethWnd(), L"已设置左侧背景图为覆盖模式", L"图片适配模式", MB_OK | MB_ICONINFORMATION);
            break;
        case 1: 
            mode = UILogin::ImageFitMode::FILL;
            MessageBoxW(window->GethWnd(), L"已设置左侧背景图为填充模式", L"图片适配模式", MB_OK | MB_ICONINFORMATION);
            break;
        case 2: 
            mode = UILogin::ImageFitMode::CONTAIN;
            MessageBoxW(window->GethWnd(), L"已设置左侧背景图为包含模式", L"图片适配模式", MB_OK | MB_ICONINFORMATION);
            break;
        case 3: 
            mode = UILogin::ImageFitMode::CENTER;
            MessageBoxW(window->GethWnd(), L"已设置左侧背景图为居中模式", L"图片适配模式", MB_OK | MB_ICONINFORMATION);
            break;
        }
        
        // 应用适配模式（SetBackgroundFitMode和SetRightBackgroundFitMode现在会重新应用圆角设置）
        loginPanel->SetBackgroundFitMode(mode);
        loginPanel->SetRightBackgroundFitMode(mode);
        
        return S_OK;
    }
    return S_FALSE;
}

// 切换分割线样式的回调函数
LRESULT CALLBACK OnChangeSeparatorStyleCallback(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
    if (nCode == WMM_CLICK)
    {
        auto window = (UIWnd*)pWnd;
        if (!window) return S_FALSE;

        // 获取登录控件
        auto loginPanel = (UILogin*)window->FindUIView(L"2000");
        if (!loginPanel) return S_FALSE;

        // 循环切换分割线样式
        static int currentStyle = 0; // 0=NONE, 1=SOLID, 2=DASHED, 3=GRADIENT
        
        // 切换到下一个样式
        currentStyle = (currentStyle + 1) % 4;
        
        // 应用新样式
        UILogin::SeparatorStyle style = UILogin::SeparatorStyle::SOLID; // 设置默认值
        switch (currentStyle) {
        case 0: 
            style = UILogin::SeparatorStyle::NONE;
            MessageBoxW(window->GethWnd(), L"已关闭分割线", L"分割线样式", MB_OK | MB_ICONINFORMATION);
            break;
        case 1: 
            style = UILogin::SeparatorStyle::SOLID;
            MessageBoxW(window->GethWnd(), L"已设置为实线分割线", L"分割线样式", MB_OK | MB_ICONINFORMATION);
            break;
        case 2: 
            style = UILogin::SeparatorStyle::DASHED;
            MessageBoxW(window->GethWnd(), L"已设置为虚线分割线", L"分割线样式", MB_OK | MB_ICONINFORMATION);
            break;
        case 3: 
            style = UILogin::SeparatorStyle::GRADIENT;
            MessageBoxW(window->GethWnd(), L"已设置为渐变分割线", L"分割线样式", MB_OK | MB_ICONINFORMATION);
            break;
        }
        
        // 设置分割线样式
        loginPanel->SetSeparatorStyle(style);
        
        // 确保分割线显示状态正确
        if (style != UILogin::SeparatorStyle::NONE) {
            loginPanel->ShowSeparator(TRUE);
        } else {
            loginPanel->ShowSeparator(FALSE);
        }
        
        return S_OK;
    }
    return S_FALSE;
}

void testlogin(HWND hWnd)
{
    // 创建一个登录窗口 - 更宽以适应分栏设计
    auto window = new UIWnd(0, 0, 1000, 600, L"用户登录 - HHBUI示例", 0, 0,
        UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN |
        UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT |
        UISTYLE_TITLE | UISTYLE_MOVEABLE, hWnd);

    // 设置窗口样式 - 更现代的视觉效果
    window->SetBackgColor(UIColor(248, 250, 252, 255)); // 更柔和的背景色
    window->SetBorderColor(UIColor(59, 130, 246, 255)); // 品牌蓝色
    window->SetShadowColor(UIColor(59, 130, 246, 155)); // 匹配边框的阴影
    window->SetRadius(12); // 更大的圆角
    window->SetBlur(4.0f, TRUE); // 略微增强模糊效果
    
    // 定义主题颜色
    UIColor themeColor = UIColor(59, 130, 246, 255); // 更现代的蓝色

    // 创建登录控件
    auto loginPanel = new UILogin(window, 0, 0, 1000, 600, 0, 0, 2000);
    loginPanel->SetWindowRadius(12); // 设置登录面板的圆角与窗口一致
    loginPanel->SetColor(color_background, UIColor(0, 0, 0, 0)); // 透明背景

    // 设置内容区宽度比例 (左侧背景图:右侧内容=0.6:0.4)
    loginPanel->SetContentWidthRatio(0.4f);

    // 设置标题和副标题
    loginPanel->SetTitle(L"欢迎回来");
    loginPanel->SetSubTitle(L"请使用您的账号继续访问");

    // 设置输入框提示文本
    loginPanel->SetUsernamePlaceholder(L"用户名 / 邮箱 / 手机号");
    loginPanel->SetPasswordPlaceholder(L"请输入密码");
    loginPanel->SetInputBorderRadius(8);  // 现代圆角
    
    // 设置输入框边框颜色
    loginPanel->SetInputBorderColor(UIColor(120, 120, 120, 255)); // 较深的边框颜色
    loginPanel->SetInputFocusBorderColor(themeColor); // 聚焦时使用主题色

    // 设置按钮文本
    loginPanel->SetLoginButtonText(L"登 录");
    loginPanel->SetRegisterButtonText(L"创建新账号");
    loginPanel->SetRememberMeText(L"记住登录状态");
    loginPanel->SetForgetPasswordText(L"忘记密码?");

    // 显示忘记密码按钮
    loginPanel->ShowForgetPassword(TRUE);

    // 设置回调函数
    loginPanel->SetLoginCallback(OnLoginCallback);
    loginPanel->SetRegisterCallback(OnRegisterCallback);
    loginPanel->SetForgetPasswordCallback(OnForgetPasswordCallback);

    // 设置主题颜色 - 现代品牌蓝
    loginPanel->SetThemeColor(themeColor);
    loginPanel->EnableLightEffect(TRUE); // 启用发光效果

    // 设置Logo尺寸
    loginPanel->SetLogoSize(100);
    
    // 设置默认的图片适配模式
    loginPanel->SetBackgroundFitMode(UILogin::ImageFitMode::COVER);
    loginPanel->SetRightBackgroundFitMode(UILogin::ImageFitMode::COVER);
    loginPanel->SetLogoFitMode(UILogin::ImageFitMode::CONTAIN);
    
    // 设置默认分割线样式(初始不显示)
    loginPanel->SetSeparatorStyle(UILogin::SeparatorStyle::SOLID);
    loginPanel->SetSeparatorColor(UIColor(200, 200, 200, 150)); 
    loginPanel->SetSeparatorWidth(1);
    loginPanel->ShowSeparator(FALSE);

    // 尝试加载左侧背景图片
    try {
        // 首先尝试从资源DLL加载背景图片
        std::vector<BYTE> bgImageData;
        if (ResDLL::LoadPngFromResourceDLL(ResDLL::IDP_BG_ANIME, bgImageData))
        {
            OutputDebugStringW(L"[Login Test] 从资源DLL成功加载动漫背景图\n");
            loginPanel->SetBackgroundImage(bgImageData.data(), bgImageData.size());
            loginPanel->SetBackgroundBlur(1.5f);    // 轻微模糊效果
            loginPanel->SetBackgroundDark(0.2f);    // 轻微暗化效果
        }
        // 如果从DLL加载失败，尝试从本地文件加载
        else
        {
            LPVOID imgdata;
            size_t retSize = 0;

            // 使用标准读取函数尝试加载背景图
            if (UIreadFile(LR"(../../Resource/icons-en-US/Backgrounds/anime-character-bg.png)", imgdata, retSize) == S_OK)
            {
                OutputDebugStringW(L"[Login Test] 从本地文件成功加载动漫背景图\n");
                loginPanel->SetBackgroundImage(imgdata, retSize);
                loginPanel->SetBackgroundBlur(1.5f);
                loginPanel->SetBackgroundDark(0.2f);
        }
        // 如果第一个路径失败，尝试其他路径
        else if (UIreadFile(LR"(二次元背景-动漫角色.png)", imgdata, retSize) == S_OK)
        {
                OutputDebugStringW(L"[Login Test] 使用相对路径成功加载动漫背景\n");
            loginPanel->SetBackgroundImage(imgdata, retSize);
            loginPanel->SetBackgroundBlur(1.5f);
            loginPanel->SetBackgroundDark(0.2f);
        }
        // 如果还是失败，使用备选图像
        else if (UIreadFile(LR"(icons\IMG_0783.JPG)", imgdata, retSize) == S_OK)
        {
            OutputDebugStringW(L"[Login Test] 使用备选图像\n");
            loginPanel->SetBackgroundImage(imgdata, retSize);
            loginPanel->SetBackgroundBlur(1.5f);
            loginPanel->SetBackgroundDark(0.2f);
        }
        else
        {
            OutputDebugStringW(L"[Login Test] 无法加载任何背景图像\n");
            }
        }
    }
    catch (const std::exception& e) {
        std::string errMsg = "[Login Test] 左侧背景图加载异常: ";
        errMsg += e.what();
        OutputDebugStringA(errMsg.c_str());
    }
    catch (...) {
        OutputDebugStringW(L"[Login Test] 左侧背景图加载发生未知异常\n");
    }
    
    // 尝试加载右侧背景图片
    try {
        // 首先尝试从资源DLL加载右侧背景图片
        std::vector<BYTE> rightBgImageData;
        if (ResDLL::LoadPngFromResourceDLL(ResDLL::IDP_BG_EVA, rightBgImageData))
        {
            OutputDebugStringW(L"[Login Test] 从资源DLL成功加载EVA背景图\n");
            loginPanel->SetRightBackgroundImage(rightBgImageData.data(), rightBgImageData.size());
            loginPanel->SetRightBackgroundBlur(0.5f);    // 更轻微的模糊效果
            loginPanel->SetRightBackgroundDark(0.0f);    // 不暗化
        }
        // 如果从DLL加载失败，尝试从本地文件加载
        else
        {
        LPVOID imgdata;
        size_t retSize = 0;
        bool bgLoaded = false;
        
        // 尝试右侧背景图
        if (UIreadFile(LR"(../../Resource/icons-en-US/Backgrounds/eva-anime-girl.png)", imgdata, retSize) == S_OK)
        {
            loginPanel->SetRightBackgroundImage(imgdata, retSize);
            loginPanel->SetRightBackgroundBlur(0.5f);    // 更轻微的模糊效果
            loginPanel->SetRightBackgroundDark(0.0f);    // 不暗化
            bgLoaded = true;
        }
        else if (UIreadFile(LR"(icons\200.jpg)", imgdata, retSize) == S_OK) 
        {
            loginPanel->SetRightBackgroundImage(imgdata, retSize);
            loginPanel->SetRightBackgroundBlur(0.5f);
            loginPanel->SetRightBackgroundDark(0.1f);
            bgLoaded = true;
        }
        
        if (!bgLoaded) {
            OutputDebugStringW(L"[Login Test] 无法加载右侧背景图像\n");
            }
        }
    }
    catch (const std::exception& e) {
        std::string errMsg = "[Login Test] 右侧背景图加载异常: ";
        errMsg += e.what();
        OutputDebugStringA(errMsg.c_str());
    }
    catch (...) {
        OutputDebugStringW(L"[Login Test] 右侧背景图加载发生未知异常\n");
    }

    // 尝试加载Logo图片
    try {
        // 首先尝试从资源DLL加载Logo图片
        std::vector<BYTE> logoImageData;
        if (ResDLL::LoadPngFromResourceDLL(ResDLL::IDP_LOGO_96, logoImageData))
        {
            OutputDebugStringW(L"[Login Test] 从资源DLL成功加载Logo图片\n");
            loginPanel->SetLogo(logoImageData.data(), logoImageData.size());
        }
        // 如果从DLL加载失败，尝试从本地文件加载
        else
        {
        LPVOID imgdata;
        size_t retSize = 0;

        // 尝试多个可能的路径加载Logo
        if (UIreadFile(LR"(icons\Segmented_icon.png)", imgdata, retSize) == S_OK)
        {
            loginPanel->SetLogo(imgdata, retSize);
        }
        else if (UIreadFile(LR"(icons\1.png)", imgdata, retSize) == S_OK)
        {
            // 备选图标1
            loginPanel->SetLogo(imgdata, retSize);
        }
        else if (UIreadFile(LR"(icons\lollipop.png)", imgdata, retSize) == S_OK)
        {
            // 备选图标2
            loginPanel->SetLogo(imgdata, retSize);
        }
        else
        {
            // 如果所有图像都加载失败，输出调试信息
            OutputDebugStringW(L"[Login Test] 无法加载任何Logo图像\n");
            }
        }
    }
    catch (const std::exception& e) {
        // 输出具体异常信息以便调试
        std::string errMsg = "[Login Test] Logo加载异常: ";
        errMsg += e.what();
        OutputDebugStringA(errMsg.c_str());
    }
    catch (...) {
        // 其他未知异常
        OutputDebugStringW(L"[Login Test] Logo加载发生未知异常\n");
    }
    
    // 添加图片适配模式切换按钮
    auto fitModeButton = new UIButton(window, 10, 50, 200, 32, L"循环切换图片适配模式", 0, 0, 9000);
    fitModeButton->SetStyle(fill, primary);
    fitModeButton->SetRadius(16); // 设置按钮圆角
    fitModeButton->SetEvent(WMM_CLICK, OnChangeFitModeCallback);
    
    // 设置按钮背景颜色
    UIColor darker1(themeColor);
    UIColor darker2(themeColor);
    darker1.SetColorLights(0.9f); // 暗化10%
    darker2.SetColorLights(0.8f); // 暗化20%
    fitModeButton->SetCrBkg(themeColor, darker1, darker2);
    fitModeButton->SetCrText(UIColor(255, 255, 255, 255), UIColor(255, 255, 255, 255), UIColor(255, 255, 255, 255));
    
    // 添加说明标签
    auto labelInfo = new UIStatic(window, 220, 50, 300, 32, L"点击按钮可以循环切换不同的适配模式", 0, 0, 9001, Left | Middle);
    labelInfo->SetFontFromFamily(L"微软雅黑", 12, HHBUI::FONT_STYLE_NORMAL);
    labelInfo->SetColor(color_text_normal, UIColor(100, 100, 100, 255));
    
    // 添加分割线样式切换按钮
    auto separatorButton = new UIButton(window, 10, 100, 200, 32, L"循环切换分割线样式", 0, 0, 9002);
    separatorButton->SetStyle(fill, primary);
    separatorButton->SetRadius(16); // 设置按钮圆角
    separatorButton->SetEvent(WMM_CLICK, OnChangeSeparatorStyleCallback);
    
    // 使用相同的按钮样式
    separatorButton->SetCrBkg(themeColor, darker1, darker2);
    separatorButton->SetCrText(UIColor(255, 255, 255, 255), UIColor(255, 255, 255, 255), UIColor(255, 255, 255, 255));
    
    // 添加分割线说明标签
    auto separatorLabel = new UIStatic(window, 220, 100, 300, 32, L"点击按钮可以循环切换不同的分割线样式", 0, 0, 9003, Left | Middle);
    separatorLabel->SetFontFromFamily(L"微软雅黑", 12, HHBUI::FONT_STYLE_NORMAL);
    separatorLabel->SetColor(color_text_normal, UIColor(100, 100, 100, 255));

    // 显示窗口
    window->Show();
    window->MessageLoop();
}

// 下面的代码添加到demo.cpp文件的按钮数组中
// testlogin, 