﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseDLL|Win32">
      <Configuration>ReleaseDLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseDLL|x64">
      <Configuration>ReleaseDLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\HHBUI\application\config.cpp" />
    <ClCompile Include="..\..\HHBUI\common\assist.cpp" />
    <ClCompile Include="..\..\HHBUI\common\atom.cpp" />
    <ClCompile Include="..\..\HHBUI\common\data.cpp" />
    <ClCompile Include="..\..\HHBUI\common\memory.cpp" />
    <ClCompile Include="..\..\HHBUI\common\mem_dc.cpp" />
    <ClCompile Include="..\..\HHBUI\common\res_pool.cpp" />
    <ClCompile Include="..\..\HHBUI\common\status_handle.cpp" />
    <ClCompile Include="..\..\HHBUI\common\winapi.cpp" />
    <ClCompile Include="..\..\HHBUI\common\ziparchive.cpp" />
    <ClCompile Include="..\..\HHBUI\control\badge.cpp" />
    <ClCompile Include="..\..\HHBUI\control\button.cpp" />
    <ClCompile Include="..\..\HHBUI\control\chart.cpp" />
    <ClCompile Include="..\..\HHBUI\control\check.cpp" />
    <ClCompile Include="..\..\HHBUI\control\colorpicker.cpp" />
    <ClCompile Include="..\..\HHBUI\control\combobox.cpp" />
    <ClCompile Include="..\..\HHBUI\control\datebox.cpp" />
    <ClCompile Include="..\..\HHBUI\control\edit.cpp" />
    <ClCompile Include="..\..\HHBUI\control\groupbox.cpp" />
    <ClCompile Include="..\..\HHBUI\control\hotkey.cpp" />
    <ClCompile Include="..\..\HHBUI\control\imagebox.cpp" />
    <ClCompile Include="..\..\HHBUI\control\item.cpp" />
    <ClCompile Include="..\..\HHBUI\control\knobs.cpp" />
    <ClCompile Include="..\..\HHBUI\control\list.cpp" />
    <ClCompile Include="..\..\HHBUI\control\loading.cpp" />
    <ClCompile Include="..\..\HHBUI\control\login.cpp" />
    <ClCompile Include="..\..\HHBUI\control\miniblink.cpp" />
    <ClCompile Include="..\..\HHBUI\control\page.cpp" />
    <ClCompile Include="..\..\HHBUI\control\progress.cpp" />
    <ClCompile Include="..\..\HHBUI\control\segmented.cpp" />
    <ClCompile Include="..\..\HHBUI\control\slider.cpp" />
    <ClCompile Include="..\..\HHBUI\control\splashscreen.cpp" />
    <ClCompile Include="..\..\HHBUI\control\static.cpp" />
    <ClCompile Include="..\..\HHBUI\control\combutton.cpp" />
    <ClCompile Include="..\..\HHBUI\control\table.cpp" />
    <ClCompile Include="..\..\HHBUI\control\tabs.cpp" />
    <ClCompile Include="..\..\HHBUI\control\timeline.cpp" />
    <ClCompile Include="..\..\HHBUI\control\tour.cpp" />
    <ClCompile Include="..\..\HHBUI\control\treeview.cpp" />
    <ClCompile Include="..\..\HHBUI\control\waveringview.cpp" />
    <ClCompile Include="..\..\HHBUI\control\wrappanel.cpp" />
    <ClCompile Include="..\..\HHBUI\element\array.cpp" />
    <ClCompile Include="..\..\HHBUI\element\brush.cpp" />
    <ClCompile Include="..\..\HHBUI\element\canvas.cpp" />
    <ClCompile Include="..\..\HHBUI\element\color.cpp" />
    <ClCompile Include="..\..\HHBUI\element\control.cpp" />
    <ClCompile Include="..\..\HHBUI\element\droptarget.cpp" />
    <ClCompile Include="..\..\HHBUI\element\font.cpp" />
    <ClCompile Include="..\..\HHBUI\element\font_pool.cpp" />
    <ClCompile Include="..\..\HHBUI\element\hook.cpp" />
    <ClCompile Include="..\..\HHBUI\element\image.cpp" />
    <ClCompile Include="..\..\HHBUI\element\layout.cpp" />
    <ClCompile Include="..\..\HHBUI\element\listview.cpp" />
    <ClCompile Include="..\..\HHBUI\element\menu.cpp" />
    <ClCompile Include="..\..\HHBUI\element\path.cpp" />
    <ClCompile Include="..\..\HHBUI\element\region.cpp" />
    <ClCompile Include="..\..\HHBUI\element\resource.cpp" />
    <ClCompile Include="..\..\HHBUI\element\scroll.cpp" />
    <ClCompile Include="..\..\HHBUI\element\wnd.cpp" />
    <ClCompile Include="..\..\HHBUI\engine\animation.cpp" />
    <ClCompile Include="..\..\HHBUI\engine\base.cpp" />
    <ClCompile Include="..\..\HHBUI\engine\engine.cpp" />
    <ClCompile Include="..\..\HHBUI\engine\renderd2d.cpp" />
    <ClCompile Include="..\..\HHBUI\engine\dx11_shader.cpp" />
    <ClCompile Include="..\..\HHBUI\engine\dx11_buffer.cpp" />
    <ClCompile Include="..\..\HHBUI\engine\dx11_render_manager.cpp" />
    <ClCompile Include="..\..\HHBUI\engine\render_profiler.cpp" />
    <ClCompile Include="..\..\HHBUI\engine\gdi_plus_integration.cpp" />
    <ClCompile Include="..\..\HHBUI\engine\render_integration.cpp" />
    <ClCompile Include="..\..\HHBUI\pch.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\HHBUI\ThirdParty\pugixml\pugixml.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\HHBUI\application\config.h" />
    <ClInclude Include="..\..\HHBUI\application\define.h" />
    <ClInclude Include="..\..\HHBUI\common\assist.h" />
    <ClInclude Include="..\..\HHBUI\common\atom.h" />
    <ClInclude Include="..\..\HHBUI\common\auto_ptr.hpp" />
    <ClInclude Include="..\..\HHBUI\common\coordinate.h" />
    <ClInclude Include="..\..\HHBUI\common\data.h" />
    <ClInclude Include="..\..\HHBUI\common\Exception.h" />
    <ClInclude Include="..\..\HHBUI\common\lock.hpp" />
    <ClInclude Include="..\..\HHBUI\common\memory.h" />
    <ClInclude Include="..\..\HHBUI\common\mem_dc.h" />
    <ClInclude Include="..\..\HHBUI\common\mem_pool.hpp" />
    <ClInclude Include="..\..\HHBUI\common\object_impl.hpp" />
    <ClInclude Include="..\..\HHBUI\common\res_pool.h" />
    <ClInclude Include="..\..\HHBUI\common\singleton.hpp" />
    <ClInclude Include="..\..\HHBUI\common\status_handle.h" />
    <ClInclude Include="..\..\HHBUI\common\UIString.hpp" />
    <ClInclude Include="..\..\HHBUI\common\vstring.hpp" />
    <ClInclude Include="..\..\HHBUI\common\unknown_impl.hpp" />
    <ClInclude Include="..\..\HHBUI\common\winapi.h" />
    <ClInclude Include="..\..\HHBUI\common\ziparchive.h" />
    <ClInclude Include="..\..\HHBUI\control\badge.h" />
    <ClInclude Include="..\..\HHBUI\control\button.h" />
    <ClInclude Include="..\..\HHBUI\control\chart.h" />
    <ClInclude Include="..\..\HHBUI\control\check.h" />
    <ClInclude Include="..\..\HHBUI\control\colorpicker.h" />
    <ClInclude Include="..\..\HHBUI\control\combobox.h" />
    <ClInclude Include="..\..\HHBUI\control\datebox.h" />
    <ClInclude Include="..\..\HHBUI\control\datebox_lunarday.h" />
    <ClInclude Include="..\..\HHBUI\control\edit.h" />
    <ClInclude Include="..\..\HHBUI\control\groupbox.h" />
    <ClInclude Include="..\..\HHBUI\control\hotkey.h" />
    <ClInclude Include="..\..\HHBUI\control\imagebox.h" />
    <ClInclude Include="..\..\HHBUI\control\item.h" />
    <ClInclude Include="..\..\HHBUI\control\knobs.h" />
    <ClInclude Include="..\..\HHBUI\control\list.h" />
    <ClInclude Include="..\..\HHBUI\control\loading.h" />
    <ClInclude Include="..\..\HHBUI\control\loading_internal.h" />
    <ClInclude Include="..\..\HHBUI\control\login.h" />
    <ClInclude Include="..\..\HHBUI\control\miniblink.h" />
    <ClInclude Include="..\..\HHBUI\control\page.h" />
    <ClInclude Include="..\..\HHBUI\control\progress.h" />
    <ClInclude Include="..\..\HHBUI\control\segmented.h" />
    <ClInclude Include="..\..\HHBUI\control\slider.h" />
    <ClInclude Include="..\..\HHBUI\control\splashscreen.h" />
    <ClInclude Include="..\..\HHBUI\control\static.h" />
    <ClInclude Include="..\..\HHBUI\control\combutton.h" />
    <ClInclude Include="..\..\HHBUI\control\table.h" />
    <ClInclude Include="..\..\HHBUI\control\tabs.h" />
    <ClInclude Include="..\..\HHBUI\control\timeline.h" />
    <ClInclude Include="..\..\HHBUI\control\tour.h" />
    <ClInclude Include="..\..\HHBUI\control\treeview.h" />
    <ClInclude Include="..\..\HHBUI\control\waveringview.h" />
    <ClInclude Include="..\..\HHBUI\control\wrappanel.h" />
    <ClInclude Include="..\..\HHBUI\element\array.h" />
    <ClInclude Include="..\..\HHBUI\element\brush.h" />
    <ClInclude Include="..\..\HHBUI\element\canvas.h" />
    <ClInclude Include="..\..\HHBUI\element\color.h" />
    <ClInclude Include="..\..\HHBUI\element\control.h" />
    <ClInclude Include="..\..\HHBUI\element\droptarget.h" />
    <ClInclude Include="..\..\HHBUI\element\font.h" />
    <ClInclude Include="..\..\HHBUI\element\font_pool.h" />
    <ClInclude Include="..\..\HHBUI\element\hook.h" />
    <ClInclude Include="..\..\HHBUI\element\image.h" />
    <ClInclude Include="..\..\HHBUI\element\layout.h" />
    <ClInclude Include="..\..\HHBUI\element\listview.h" />
    <ClInclude Include="..\..\HHBUI\element\menu.h" />
    <ClInclude Include="..\..\HHBUI\element\path.h" />
    <ClInclude Include="..\..\HHBUI\element\region.h" />
    <ClInclude Include="..\..\HHBUI\element\resource.h" />
    <ClInclude Include="..\..\HHBUI\element\scroll.h" />
    <ClInclude Include="..\..\HHBUI\element\wnd.h" />
    <ClInclude Include="..\..\HHBUI\engine\animation.h" />
    <ClInclude Include="..\..\HHBUI\engine\base.h" />
    <ClInclude Include="..\..\HHBUI\engine\engine.h" />
    <ClInclude Include="..\..\HHBUI\engine\matrix.h" />
    <ClInclude Include="..\..\HHBUI\engine\object_api.h" />
    <ClInclude Include="..\..\HHBUI\engine\renderd2d.h" />
    <ClInclude Include="..\..\HHBUI\engine\text_render.hpp" />
    <ClInclude Include="..\..\HHBUI\engine\render_api.h" />
    <ClInclude Include="..\..\HHBUI\engine\dx11_shader.h" />
    <ClInclude Include="..\..\HHBUI\engine\dx11_buffer.h" />
    <ClInclude Include="..\..\HHBUI\engine\dx11_render_manager.h" />
    <ClInclude Include="..\..\HHBUI\engine\render_profiler.h" />
    <ClInclude Include="..\..\HHBUI\engine\gdi_plus_integration.h" />
    <ClInclude Include="..\..\HHBUI\hhbui.h" />
    <ClInclude Include="..\..\HHBUI\pch.h" />
    <ClInclude Include="..\..\HHBUI\ThirdParty\pugixml\pugiconfig.hpp" />
    <ClInclude Include="..\..\HHBUI\ThirdParty\pugixml\pugixml.hpp" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="hhbui_lib.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\HHBUI\ThirdParty\zlib\zlib.vcxproj">
      <Project>{5fc3449d-1789-4f5a-a9d9-498de8ba449a}</Project>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{65d80576-a6ff-45d6-afff-4ee5c36b642d}</ProjectGuid>
    <RootNamespace>HHBUI</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>hhbui_lib</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <IntDir>$(SolutionDir)$(Configuration)\HHBUI_tmp\</IntDir>
    <TargetName>hhbuiu_x86</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IntDir>$(SolutionDir)$(Platform)\$(Configuration)\HHBUI_tmp\</IntDir>
    <TargetName>hhbuiu_x64</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IntDir>$(SolutionDir)$(Platform)\$(Configuration)\HHBUI_tmp\</IntDir>
    <TargetName>hhbui_x64</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|x64'">
    <IntDir>$(SolutionDir)$(Platform)\$(Configuration)\HHBUI_dll\</IntDir>
    <TargetName>hhbui_x64</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <IntDir>$(SolutionDir)$(Configuration)\HHBUI_tmp\</IntDir>
    <TargetName>hhbui_x86</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|Win32'">
    <IntDir>$(SolutionDir)$(Configuration)\HHBUI_dll\</IntDir>
    <TargetName>hhbui_x86</TargetName>
    <OutDir>$(SolutionDir)Debug\</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;EX_CFG_DEBUG_INTERRUPT;EX_CFG_DEBUG_CHECK_PARAM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <AdditionalIncludeDirectories>.\;$(SolutionDir)HHBUI\</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <CallingConvention>StdCall</CallingConvention>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <ModuleDefinitionFile>exports.def</ModuleDefinitionFile>
      <AdditionalDependencies>d3dcompiler.lib;windowscodecs.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <AdditionalIncludeDirectories>.\;$(SolutionDir)HHBUI\</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <CallingConvention>StdCall</CallingConvention>
      <Optimization>MinSpace</Optimization>
      <FavorSizeOrSpeed>Size</FavorSizeOrSpeed>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <ModuleDefinitionFile>exports.def</ModuleDefinitionFile>
      <AdditionalDependencies>d3dcompiler.lib;windowscodecs.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;HHBUIDLL_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <AdditionalIncludeDirectories>.\;$(SolutionDir)HHBUI\</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <CallingConvention>StdCall</CallingConvention>
      <FavorSizeOrSpeed>Size</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <Optimization>MinSpace</Optimization>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;_USRDLL;EX_CFG_DEBUG_INTERRUPT;EX_CFG_DEBUG_CHECK_PARAM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <AdditionalIncludeDirectories>.\;$(SolutionDir)HHBUI\</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <CallingConvention>StdCall</CallingConvention>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <ModuleDefinitionFile>exports.def</ModuleDefinitionFile>
      <AdditionalDependencies>d3dcompiler.lib;windowscodecs.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <AdditionalIncludeDirectories>.\;$(SolutionDir)HHBUI\</AdditionalIncludeDirectories>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>false</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <ModuleDefinitionFile>exports.def</ModuleDefinitionFile>
      <AdditionalDependencies>d3dcompiler.lib;windowscodecs.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDLL|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;HHBUIDLL_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <AdditionalIncludeDirectories>.\;$(SolutionDir)HHBUI\</AdditionalIncludeDirectories>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <CallingConvention>StdCall</CallingConvention>
      <Optimization>MinSpace</Optimization>
      <FavorSizeOrSpeed>Size</FavorSizeOrSpeed>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>