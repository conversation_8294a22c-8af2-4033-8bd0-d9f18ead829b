﻿#include "pch.h"
#include "miniblink.h"

HHBUI::UIMiniBlink::UIMiniBlink(UIBase *hParent, INT x, INT y, INT width, INT height, INT nID, INT dwStyle, INT dwStyleEx)
{
	InitSubControl(hParent, x, y, width, height, L"form-miniblink", NULL, dwStyle, dwStyleEx, nID);
}

wkeWebView HHBUI::UIMiniBlink::SetWkeDllPath(LPCWSTR libPath)
{
	wkeSetWkeDllPath(libPath);
	wkeInitialize();

	p_data.hWebView = wkeCreateWebView();
	if (p_data.hWebView)
	{
		// 设置UA
		wkeSetUserAgent(p_data.hWebView, "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.2228.0 Safari/537.36");
		//通知无窗口模式下，webview开启透明模式。
		wkeSetTransparent(p_data.hWebView, true);
		wkeSetHandle(p_data.hWebView, m_data.pWnd->GethWnd());
		wkeOnPaintUpdated(p_data.hWebView, OnWkePaintUpdate, this);
		wkeOnTitleChanged(p_data.hWebView, OnWkeTitleChanged, this);
		wkeOnURLChanged(p_data.hWebView, OnWkeURLChanged, this);
		wkeOnNavigation(p_data.hWebView, OnWkeNavigation, this);
		wkeOnCreateView(p_data.hWebView, OnWkeCreateView, this);
		wkeOnDocumentReady(p_data.hWebView, OnWkeDocumentReady, this);
		wkeOnDownload(p_data.hWebView, OnWkeDownload, this);
	}
	return p_data.hWebView;
}

void HHBUI::UIMiniBlink::SetUserAgent(LPCSTR ua)
{
	if (p_data.hWebView)
		wkeSetUserAgent(p_data.hWebView, ua);
}

void HHBUI::UIMiniBlink::LoadURLW(LPCWSTR url)
{
	if (p_data.hWebView)
		wkeLoadURLW(p_data.hWebView, url);
}

void HHBUI::UIMiniBlink::LoadFileW(LPCWSTR file)
{
	if (p_data.hWebView)
		wkeLoadFileW(p_data.hWebView, file);
}

void HHBUI::UIMiniBlink::LoadHTMLW(LPCWSTR html)
{
	if (p_data.hWebView)
		wkeLoadHTMLW(p_data.hWebView, html);
}

void HHBUI::UIMiniBlink::GoBack()
{
	if (p_data.hWebView)
		wkeGoBack(p_data.hWebView);
}

void HHBUI::UIMiniBlink::GoForward()
{
	if (p_data.hWebView)
		wkeGoForward(p_data.hWebView);
}

void HHBUI::UIMiniBlink::Reload()
{
	if (p_data.hWebView)
		wkeReload(p_data.hWebView);
}

void HHBUI::UIMiniBlink::RunJSW(LPCWSTR cJs)
{
	if (p_data.hWebView)
		wkeRunJSW(p_data.hWebView, cJs);
}

LRESULT HHBUI::UIMiniBlink::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_DESTROY)
	{
		if (p_data.hWebView)
			wkeDestroyWebView(p_data.hWebView);
	}
	else if (uMsg == WM_SIZE)
	{
		if (p_data.hWebView)
			wkeResize(p_data.hWebView, GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
	}
	else if (uMsg == WM_MOVE)
	{
		if (p_data.hWebView)
			wkeSetHandleOffset(p_data.hWebView, GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
	}
	else if (uMsg == WM_SETFOCUS)
	{
		if (p_data.hWebView)
		{
			m_data.pWnd->SetIme(TRUE);
			wkeSetFocus(p_data.hWebView);
		}
	}
	else if (uMsg == WM_KILLFOCUS)
	{
		if (p_data.hWebView)
		{
			m_data.pWnd->SetIme(FALSE);
			wkeKillFocus(p_data.hWebView);
		}
	}
	else if (uMsg == WM_CONTEXTMENU)
	{
		if (p_data.hWebView)
			return wkeFireContextMenuEvent(p_data.hWebView, LOWORD(lParam), HIWORD(lParam), 0);
	}
	else if (uMsg >= WM_MOUSEMOVE && uMsg <= WM_MBUTTONDBLCLK)
	{
		if (p_data.hWebView)
		{
			bool fResult = wkeFireMouseEvent(p_data.hWebView, uMsg, LOWORD(lParam), HIWORD(lParam), GetEventFlags(wParam, FALSE));
			UpdateCursor();
			return fResult;
		}
	}
	else if (uMsg == WKE_MSG_MOUSEWHEEL)
	{
		if (p_data.hWebView)
			return wkeFireMouseWheelEvent(p_data.hWebView, LOWORD(lParam), HIWORD(lParam), HIWORD(wParam), GetEventFlags(LOWORD(wParam), FALSE));
	}
	else if (uMsg == WM_KEYDOWN || uMsg == WM_SYSKEYDOWN)
	{
		if (p_data.hWebView)
			return wkeFireKeyDownEvent(p_data.hWebView, wParam, GetEventFlags(lParam, TRUE), uMsg == WM_SYSKEYDOWN);
	}
	else if (uMsg == WM_KEYUP || uMsg == WM_SYSKEYUP)
	{
		if (p_data.hWebView)
			return wkeFireKeyUpEvent(p_data.hWebView, wParam, GetEventFlags(lParam, TRUE), uMsg == WM_SYSKEYUP);
	}
	else if (uMsg == WM_CHAR || uMsg == WM_SYSCHAR)
	{
		if (p_data.hWebView)
			return wkeFireKeyPressEvent(p_data.hWebView, wParam, GetEventFlags(lParam, TRUE), uMsg == WM_SYSCHAR);
	}
	else if (uMsg == WM_SETCURSOR)
	{
		return TRUE;
	}
	else if (uMsg == WM_MOUSEHOVER)
	{
		HCURSOR curosr = ::LoadCursor(NULL, IDC_ARROW);
		::SetCursor(curosr);
	}
	else if (uMsg == WM_IME_COMPOSITION)
	{
		if (p_data.hWebView)
		{
			ExRectF rcClient{};
			GetRect(rcClient);
			wkeRect caret = wkeGetCaretRect(p_data.hWebView);
			CANDIDATEFORM form{};
			form.dwIndex = 0;
			form.dwStyle = CFS_EXCLUDE;
			form.ptCurrentPos.x = caret.x + rcClient.left;
			form.ptCurrentPos.y = caret.y + caret.h + rcClient.top;
			form.rcArea.top = caret.y + rcClient.top;
			form.rcArea.bottom = caret.y + caret.h + rcClient.top;
			form.rcArea.left = caret.x + rcClient.left;
			form.rcArea.right = caret.x + caret.w + rcClient.left;
			COMPOSITIONFORM compForm{};
			compForm.ptCurrentPos = form.ptCurrentPos;
			compForm.rcArea = form.rcArea;
			compForm.dwStyle = CFS_POINT;

			HIMC hIMC = ImmGetContext(hWnd);
			ImmSetCandidateWindow(hIMC, &form);
			ImmSetCompositionWindow(hIMC, &compForm);
			ImmReleaseContext(hWnd, hIMC);
			return TRUE;
		}
	}
	return S_OK;
}

void HHBUI::UIMiniBlink::OnPaintProc(ps_context ps)
{
	if (p_data.hWebView)
	{
		HDC hDC = nullptr;
		ps.hCanvas->GetDC(&hDC);

		RECT rcClip;
		::GetClipBox(hDC, &rcClip);
		RECT rcClient{ 0,0,(LONG)ps.uWidth,(LONG)ps.uHeight };
		RECT rcInvalid;
		::IntersectRect(&rcInvalid, &rcClip, &rcClient);
		int invalidWidth = rcInvalid.right - rcInvalid.left;
		int invalidHeight = rcInvalid.bottom - rcInvalid.top;
		HDC hdcWke = wkeGetViewDC(p_data.hWebView);
		BOOL ret = false;
		if (GetAlpha() != 0xff)
		{
			BLENDFUNCTION bf = { AC_SRC_OVER, 0, (BYTE)GetAlpha(), AC_SRC_ALPHA};
			ret = AlphaBlend(hDC, rcInvalid.left, rcInvalid.top, invalidWidth, invalidHeight,
				hdcWke, rcInvalid.left - rcClient.left, rcInvalid.top - rcClient.top, invalidWidth, invalidHeight, bf);
		}
		else
		{
			ret = BitBlt(hDC, rcInvalid.left, rcInvalid.top, invalidWidth, invalidHeight,
				hdcWke, rcInvalid.left - rcClient.left, rcInvalid.top - rcClient.top, SRCCOPY);
		}
		ps.hCanvas->ReleaseDC();
	}
}

DWORD HHBUI::UIMiniBlink::GetEventFlags(WPARAM wParam, BOOL fKeyEvent)
{
	DWORD dwFlags = 0;
	if (fKeyEvent)
	{
		if ((HIWORD(wParam) & KF_REPEAT) == KF_REPEAT)
		{
			dwFlags = dwFlags | WKE_REPEAT;
		}
		if ((HIWORD(wParam) & KF_EXTENDED) == KF_EXTENDED)
		{
			dwFlags = dwFlags | WKE_REPEAT;
		}
	}
	else {
		if ((wParam & MK_LBUTTON) == MK_LBUTTON)
		{
			dwFlags = dwFlags | WKE_LBUTTON;
		}
		if ((wParam & MK_MBUTTON) == MK_MBUTTON)
		{
			dwFlags = dwFlags | WKE_MBUTTON;
		}
		if ((wParam & MK_RBUTTON) == MK_RBUTTON)
		{
			dwFlags = dwFlags | WKE_RBUTTON;
		}
		if ((wParam & MK_CONTROL) == MK_CONTROL)
		{
			dwFlags = dwFlags | WKE_CONTROL;
		}
		if ((wParam & MK_SHIFT) == MK_SHIFT)
		{
			dwFlags = dwFlags | WKE_SHIFT;
		}
	}
	return dwFlags;
}

void HHBUI::UIMiniBlink::UpdateCursor()
{
	int cursorInfo = wkeGetCursorInfoType(p_data.hWebView);
	if (p_data.cursor != cursorInfo)
	{
		HCURSOR curosr = ::LoadCursor(NULL, IDC_ARROW);
		p_data.cursor = cursorInfo;
		switch (cursorInfo)
		{
		case WkeCursorInfoPointer:
			curosr = ::LoadCursor(NULL, IDC_ARROW);
			break;
		case WkeCursorInfoCross:
			curosr = ::LoadCursor(NULL, IDC_CROSS);
			break;
		case WkeCursorInfoHand:
			curosr = ::LoadCursor(NULL, IDC_HAND);
			break;
		case WkeCursorInfoIBeam:
			curosr = ::LoadCursor(NULL, IDC_IBEAM);
			break;
		case WkeCursorInfoWait:
			curosr = ::LoadCursor(NULL, IDC_WAIT);
			break;
		case WkeCursorInfoHelp:
			curosr = ::LoadCursor(NULL, IDC_HELP);
			break;
		case WkeCursorInfoEastResize:
			curosr = ::LoadCursor(NULL, IDC_SIZEWE);
			break;
		case WkeCursorInfoNorthResize:
			curosr = ::LoadCursor(NULL, IDC_SIZENS);
			break;
		case WkeCursorInfoNorthEastResize:
			curosr = ::LoadCursor(NULL, IDC_SIZENESW);
			break;
		case WkeCursorInfoNorthWestResize:
			curosr = ::LoadCursor(NULL, IDC_SIZENWSE);
			break;
		case WkeCursorInfoSouthResize:
			curosr = ::LoadCursor(NULL, IDC_SIZENS);
			break;
		case WkeCursorInfoSouthEastResize:
			curosr = ::LoadCursor(NULL, IDC_SIZENWSE);
			break;
		case WkeCursorInfoSouthWestResize:
			curosr = ::LoadCursor(NULL, IDC_SIZENESW);
			break;
		case WkeCursorInfoWestResize:
			curosr = ::LoadCursor(NULL, IDC_SIZEWE);
			break;
		case WkeCursorInfoNorthSouthResize:
			curosr = ::LoadCursor(NULL, IDC_SIZENS);
			break;
		case WkeCursorInfoEastWestResize:
			curosr = ::LoadCursor(NULL, IDC_SIZEWE);
			break;
		case WkeCursorInfoNorthEastSouthWestResize:
			curosr = ::LoadCursor(NULL, IDC_SIZEALL);
			break;
		case WkeCursorInfoNorthWestSouthEastResize:
			curosr = ::LoadCursor(NULL, IDC_SIZEALL);
			break;
		case WkeCursorInfoColumnResize:
		case WkeCursorInfoRowResize:
			curosr = ::LoadCursor(NULL, IDC_ARROW);
			break;
		default:
			break;
		}
		::SetCursor(curosr);
	}
}

wkeWebView WKE_CALL_TYPE HHBUI::UIMiniBlink::OnWkeCreateView(wkeWebView webView, void* mb, wkeNavigationType navigationType, const wkeString url, const wkeWindowFeatures* windowFeatures)
{
	auto pimb = (UIMiniBlink*)mb;
	pimb->DispatchNotify(WMM_MB_CREATEVIEW, (size_t)windowFeatures, (size_t)url);
	return webView;
}

void WKE_CALL_TYPE HHBUI::UIMiniBlink::OnWkePaintUpdate(wkeWebView hWebView, void* mb, const HDC hDC, INT x, INT y, INT cx, INT cy)
{
	auto pimb = (UIMiniBlink*)mb;
	cx = cx + x;
	cy = cy + y;
	RECT rc = { x,y,cx,cy };
	pimb->Redraw(&rc);
}

void WKE_CALL_TYPE HHBUI::UIMiniBlink::OnWkeTitleChanged(wkeWebView webView, void* mb, wkeString title)
{
	auto pimb = (UIMiniBlink*)mb;
	pimb->DispatchNotify(WMM_MB_TITLECHANGED, 0, (size_t)wkeGetStringW(title));
}

void WKE_CALL_TYPE HHBUI::UIMiniBlink::OnWkeURLChanged(wkeWebView webView, void* mb, wkeString url)
{
	auto pimb = (UIMiniBlink*)mb;
	wkeTempCallbackInfo* temInfo = wkeGetTempCallbackInfo(webView);
	if (::wkeIsMainFrame(webView, temInfo->frame))
	{
		LPCTSTR pStrUrl = wkeGetStringW(url);
		pimb->p_data.curpageurl = pStrUrl;
		pimb->DispatchNotify(WMM_MB_URLCHANGED, 0, (size_t)pStrUrl);
	}
}

bool WKE_CALL_TYPE HHBUI::UIMiniBlink::OnWkeNavigation(wkeWebView webView, void* mb, wkeNavigationType navigationType, wkeString url)
{
	auto pimb = (UIMiniBlink*)mb;
	LPCTSTR pStrUrl = wkeGetStringW(url);

	return pimb->DispatchNotify(WMM_MB_NAVIGATION, (size_t)navigationType, (size_t)pStrUrl) == S_OK;
}

void WKE_CALL_TYPE HHBUI::UIMiniBlink::OnWkeDocumentReady(wkeWebView webView, void* mb)
{
	auto pimb = (UIMiniBlink*)mb;
	pimb->DispatchNotify(WMM_MB_DOCUMENTREADY, 0, 0);
}

bool WKE_CALL_TYPE HHBUI::UIMiniBlink::OnWkeDownload(wkeWebView webView, void* mb, const char* url)
{
	auto pimb = (UIMiniBlink*)mb;
	return pimb->DispatchNotify(WMM_MB_DOWNLOAD, 0, (size_t)url) == S_OK;
}


