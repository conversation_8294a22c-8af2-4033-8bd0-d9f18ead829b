﻿#include "pch.h"
#include "colorpicker.h"
#include <common/winapi.h>
#include <wrl.h>
#include <algorithm> 
#include <common/Exception.h>
#define _IS_IN(min, max, x)  (((x)>(min)) && ((x)<(max)))
#define CLAMP(x, m, M){\
if ((x)<(m)) (x) = (m); \
if ((x)>(M)) (x) = (M); \
}
#define MAX1(a, b, c) ((a)>(b)? ((a)>(c)?(a):(c)) : ((b)>(c)?(b):(c)))
#define MIN1(a, b, c) ((a)<(b)? ((a)<(c)?(a):(c)) : ((b)<(c)?(b):(c)))

#define eos_picker_info 256

const int	int_extend = 20;
#define	HSV_0()	RGB ((BYTE) (coef1 >> int_extend),(BYTE) (coef3 >> int_extend),(BYTE) (val >> int_extend))

#define	HSV_HUE_ADV_0() coef1 += coef1_adv,coef3 += coef3_adv

#define	HSV_1()	RGB ((BYTE) (coef1 >> int_extend),(BYTE) (val >> int_extend),(BYTE) (coef2 >> int_extend))

#define	HSV_HUE_ADV_1()	coef1 += coef1_adv,	coef2 += coef2_adv


#define	HSV_2()	RGB ((BYTE) (coef3 >> int_extend),(BYTE) (val >> int_extend),(BYTE) (coef1 >> int_extend))

#define	HSV_HUE_ADV_2()		HSV_HUE_ADV_0()

#define	HSV_3()	RGB ((BYTE) (val >> int_extend),(BYTE) (coef2 >> int_extend),(BYTE) (coef1 >> int_extend))

#define	HSV_HUE_ADV_3()		HSV_HUE_ADV_1()

#define	HSV_4()	RGB ((BYTE) (val >> int_extend),(BYTE) (coef1 >> int_extend),(BYTE) (coef3 >> int_extend))

#define	HSV_HUE_ADV_4()		HSV_HUE_ADV_0()

#define	HSV_5()	 RGB ((BYTE) (coef2 >> int_extend),(BYTE) (coef1 >> int_extend),(BYTE) (val >> int_extend))

#define	HSV_HUE_ADV_5()		HSV_HUE_ADV_1()
#define	HSV_LOOP_STEPS(w)	((w) - 1)
// initialize for HSV colorspace in SAT mode, for HUE between 0 and 1 (0 and 60 deg)
#define	HSV_SAT_INIT_0()	coef3 = coef1,	coef3_adv = (int) ((val - coef3) / HSV_LOOP_STEPS (j))

// advance for HSV colorspace in SAT mode, for HUE between 0 and 1 (0 and 60 deg)
#define	HSV_SAT_ADV_0()	    coef3 += coef3_adv

#define	HSV_SAT_INIT_1()	coef2 = val, coef2_adv = (int) ((val * (1.0 - sat) - coef2) / HSV_LOOP_STEPS (j))

#define	HSV_SAT_ADV_1()	    coef2 += coef2_adv

#define	HSV_SAT_INIT_2()	HSV_SAT_INIT_0()
#define	HSV_SAT_ADV_2()		HSV_SAT_ADV_0()

#define	HSV_SAT_INIT_3()	HSV_SAT_INIT_1()
#define	HSV_SAT_ADV_3()		HSV_SAT_ADV_1()

#define	HSV_SAT_INIT_4()	HSV_SAT_INIT_0()
#define	HSV_SAT_ADV_4()		HSV_SAT_ADV_0()

#define	HSV_SAT_INIT_5()	HSV_SAT_INIT_1()
#define	HSV_SAT_ADV_5()		HSV_SAT_ADV_1()

void HSV_SAT(DWORD* buffer, int samples, double hue, double val_fp)
{
	// value, but as integer in [0, 255 << int_extend]
	int		val;

	// loop counter
	int		j;

	// coefficients and advances
	signed int		coef1, coef2, coef3;
	signed int		coef1_adv, coef2_adv, coef3_adv;

	double	intp, frac;

	//
	// hue - const, in [0, 360)
	//	intp - const in 0, 1, 2, 3, 4, 5
	//	frac - const in [0, 1)
	// sat - increments, in [0, 1]; indirectly (coefficients)
	// val - const, in [0, (255 << int_extend)]
	//
	// coef1 => val * (1 - sat)              => changes from val to 0
	// coef2 => val * (1 - sat * frac)       => changes from val to val * (1 - frac)
	// coef3 => val * (1 - sat * (1 - frac)) => changes from val to val * frac
	//

	// constants
	val = (int)(val_fp * 255) << int_extend;
	frac = modf(hue / 60.0, &intp);

	// prepare
	j = samples;

	coef1 = val;
	coef1_adv = -coef1 / samples;
	coef2 = val;
	coef2_adv = (int)((1 - frac) * val - coef2) / samples;
	coef3 = val;
	coef3_adv = (int)(frac * val - coef3) / samples;

	switch ((int)intp)
	{
	case	0:
		while (j--) *buffer++ = HSV_0(), HSV_HUE_ADV_0();
		break;
	case	1:
		while (j--) *buffer++ = HSV_1(), HSV_HUE_ADV_1();
		break;
	case	2:
		while (j--) *buffer++ = HSV_2(), HSV_HUE_ADV_2();
		break;
	case	3:
		while (j--) *buffer++ = HSV_3(), HSV_HUE_ADV_3();
		break;
	case	4:
		while (j--) *buffer++ = HSV_4(), HSV_HUE_ADV_4();
		break;
	case	5:
		while (j--) *buffer++ = HSV_5(), HSV_HUE_ADV_5();
		break;
	}
}
void HSV_HUE(std::vector<HHBUI::UIColor>& buffer, int samples, double sat, double val_fp)
{
	// value, but as integer in [0, 255 << int_extend]
	int		val;

	// loop counter
	int		j;

	// coefficients and advances
	int		coef1, coef2, coef3;
	int		coef2_adv, coef3_adv;

	// current position and advance to the next one
	double	pos, pos_adv;

	//
	// hue increments in [0, 360); indirectly
	//	intp changes - 0, 1, 2, 3, 4, 5; indirectly (separate loops)
	//	frac increments in [0, 1) six times; indirectly (coefficients)
	// sat - const, in [0, 1]
	// val - const, in [0, (255 << int_extend)]
	//
	// coef1 => val * (1 - sat)              => const, = val * (1 - sat)
	// coef2 => val * (1 - sat * frac)       => changes from val to val * (1 - sat)
	// coef3 => val * (1 - sat * (1 - frac)) => changes from val * (1 - sat) to val
	//

	// constants
	val = (int)(val_fp * 255) << int_extend;
	coef1 = (int)(val * (1 - sat));

	// prepare
	pos = 0;
	pos_adv = (double)samples / 6.0;

	// hue in [0, 60)
	pos += pos_adv;
	j = (int)pos;
	HSV_SAT_INIT_0();
	while (j--) buffer.push_back(HHBUI::UIColor(HSV_0())), HSV_SAT_ADV_0();

	pos += pos_adv;
	j = (int)pos - (int)(1 * pos_adv);
	HSV_SAT_INIT_1();
	while (j--) buffer.push_back(HHBUI::UIColor(HSV_1())), HSV_SAT_ADV_1();

	pos += pos_adv;
	j = (int)pos - (int)(2 * pos_adv);
	HSV_SAT_INIT_2();
	while (j--) buffer.push_back(HHBUI::UIColor(HSV_2())), HSV_SAT_ADV_2();

	pos += pos_adv;
	j = (int)pos - (int)(3 * pos_adv);
	HSV_SAT_INIT_3();
	while (j--) buffer.push_back(HHBUI::UIColor(HSV_3())), HSV_SAT_ADV_3();

	pos += pos_adv;
	j = (int)pos - (int)(4 * pos_adv);
	HSV_SAT_INIT_4();
	while (j--) buffer.push_back(HHBUI::UIColor(HSV_4())), HSV_SAT_ADV_4();

	pos += (pos_adv + 0.1);	// + 0.1 because of floating-point math's rounding errors
	j = (int)pos - (int)(5 * pos_adv);
	HSV_SAT_INIT_5();
	while (j--) buffer.push_back(HHBUI::UIColor(HSV_5())), HSV_SAT_ADV_5();
}
void HSVtoRGB(double h, double s, double v, float& r, float& g, float& b)
{
	int i = (int)(h / 60.0) % 6;
	double f = (h / 60.0) - i;
	double p = v * (1.0 - s);
	double q = v * (1.0 - f * s);
	double t = v * (1.0 - (1.0 - f) * s);

	switch (i) {
	case 0: r = (float)v; g = (float)t; b = (float)p; break;
	case 1: r = (float)q; g = (float)v; b = (float)p; break;
	case 2: r = (float)p; g = (float)v; b = (float)t; break;
	case 3: r = (float)p; g = (float)q; b = (float)v; break;
	case 4: r = (float)t; g = (float)p; b = (float)v; break;
	case 5: r = (float)v; g = (float)p; b = (float)q; break;
	}
}
unsigned int RGBAToHex(unsigned char R, unsigned char G, unsigned char B, unsigned char A)
{
	return static_cast<unsigned int>(A) << 24 | static_cast<unsigned int>(R) << 16 | static_cast<unsigned int>(G) << 8 | B;
}
unsigned int RGBToHex(unsigned char R, unsigned char G, unsigned char B)
{
	return static_cast<unsigned int>(R) << 16 | static_cast<unsigned int>(G) << 8 | B;
}
// HSV 转 RGB
void fromHSVtoRGB(WORD h, WORD s, WORD v, WORD &r, WORD &g, WORD &b)
{
	int conv;
	double hue, sat, val;
	int base;

	hue = (float)h / 100.0f;
	sat = (float)s / 100.0f;
	val = (float)v / 100.0f;

	if ((float)s == 0) // Acromatic color (gray). Hue doesn't mind.
	{
		conv = (unsigned short)(255.0f * val);
		r = b = g = conv;
		return;
	}

	base = (unsigned short)(255.0f * (1.0 - sat) * val);

	switch ((unsigned short)((float)h / 60.0f))
	{
	case 0:
		r = (unsigned short)(255.0f * val);
		g = (unsigned short)((255.0f * val - base) * (h / 60.0f) + base);
		b = base;
		break;

	case 1:
		r = (unsigned short)((255.0f * val - base) * (1.0f - ((h % 60) / 60.0f)) + base);
		g = (unsigned short)(255.0f * val);
		b = base;
		break;

	case 2:
		r = base;
		g = (unsigned short)(255.0f * val);
		b = (unsigned short)((255.0f * val - base) * ((h % 60) / 60.0f) + base);
		break;

	case 3:
		r = base;
		g = (unsigned short)((255.0f * val - base) * (1.0f - ((h % 60) / 60.0f)) + base);
		b = (unsigned short)(255.0f * val);
		break;

	case 4:
		r = (unsigned short)((255.0f * val - base) * ((h % 60) / 60.0f) + base);
		g = base;
		b = (unsigned short)(255.0f * val);
		break;

	case 5:
		r = (unsigned short)(255.0f * val);
		g = base;
		b = (unsigned short)((255.0f * val - base) * (1.0f - ((h % 60) / 60.0f)) + base);
		break;
	}
}
// RGB转HSV
void fromRGBtoHSV(WORD r, WORD g, WORD b, WORD& v, WORD& s, WORD& h)
{
	unsigned short max, min, delta;
	short temp;

	max = MAX1(r, g, b);
	min = MIN1(r, g, b);
	delta = max - min;

	if (max == 0)
	{
		s = h = v = 0;
		return;
	}
	v = (unsigned short)((double)max / 255.0 * 100.0);
	s = (unsigned short)(((double)delta / max) * 100.0);

	if (r == max)
		temp = (short)(60 * ((g - b) / (double)delta));
	else if (g == max)
		temp = (short)(60 * (2.0 + (b - r) / (double)delta));
	else
		temp = (short)(60 * (4.0 + (r - g) / (double)delta));

	if (temp < 0)
		h = temp + 360;
	else
		h = temp;
}
void fromRGBtoHSL(float rgb[], float hsl[])
{
	const float maxRGB = (std::max)(rgb[0], (std::max)(rgb[1], rgb[2]));
	const float minRGB = (std::min)(rgb[0], (std::min)(rgb[1], rgb[2]));
	const float delta2 = maxRGB + minRGB;
	hsl[2] = delta2 * 0.5f;

	const float delta = maxRGB - minRGB;
	if (delta < FLT_MIN)
		hsl[0] = hsl[1] = 0.0f;
	else
	{
		hsl[1] = delta / (hsl[2] > 0.5f ? 2.0f - delta2 : delta2);
		if (rgb[0] >= maxRGB)
		{
			hsl[0] = (rgb[1] - rgb[2]) / delta;
			if (hsl[0] < 0.0f)
				hsl[0] += 6.0f;
		}
		else if (rgb[1] >= maxRGB)
			hsl[0] = 2.0f + (rgb[2] - rgb[0]) / delta;
		else
			hsl[0] = 4.0f + (rgb[0] - rgb[1]) / delta;
	}
}
HHBUI::UIColorPicker::UIColorPicker(UIBase *hParent, INT x, INT y, INT width, INT height, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-colorpicker", 0, dwStyle, dwStyleEx, nID, dwTextFormat);
}
void HHBUI::UIColorPicker::Popup()
{
	if (GetTickCount64() - p_data.nProcessTime < 200)
	{
		EndMenu();
		p_data.nProcessTime = GetTickCount64();
		return;
	}
	else if (IsVisible())
	{
		if (p_data.pWnd)
		{
			delete p_data.pWnd;
			p_data.pWnd = nullptr;
			return;
		}
		p_data.pWnd = new UIWnd(0, 0, 0, 0, NULL, WS_BORDER | WS_SYSMENU | WS_POPUP, WS_EX_TOPMOST | WS_EX_TOOLWINDOW | WS_EX_LAYERED,
			UISTYLE_NOINHERITBKG | UISTYLE_NOCAPTIONTOPMOST | UISTYLE_NOTITLEBAR | UISTYLE_POPUPWINDOW | UISTYLE_NOSHADOW | EWS_COMBOWINDOW, m_data.pWnd->GethWnd(), NULL, (size_t)this, OnWndMsgCrProc);
		if (p_data.pWnd)
		{
			UIColor background; UIColor border;
			GetColor(color_background, background);
			if (background.empty())
				background = UIColor(255, 255, 255, 255);
			p_data.pWnd->SetBackgColor(background);
			GetColor(color_border, border);
			p_data.pWnd->SetBorderColor(border, 1);

			if (!m_data.radius.empty())
			{
				p_data.pWnd->SetRadius((m_data.radius.left + m_data.radius.top + m_data.radius.right + m_data.radius.bottom) / 4.0f);
			}
			RECT tmp{};ExRectF unknown{};
			GetWindowRect(m_data.pWnd->GethWnd(), &tmp);

			GetRect(unknown, grt_window, TRUE);
			tmp.left += unknown.left - 1;
			tmp.top += unknown.bottom + 2;
			tmp.right = UIEngine::ScaleValue(267);
			tmp.bottom = UIEngine::ScaleValue(300);
			RECT screen, desk{};
			HHBUI::UIWinApi::GetWndScreenRectEx(m_data.pWnd->GethWnd(), screen, desk);

			if (tmp.top + tmp.bottom - screen.bottom > 0)//超出屏幕底部
			{
				unknown.bottom = m_data.Frame.bottom - m_data.Frame.top;
				tmp.top -= tmp.bottom + unknown.bottom + UIEngine::ScaleValue(4);
			}
			if (tmp.left - screen.right - screen.left + tmp.right + 20 > 0)//超出屏幕右边
			{
				tmp.left = screen.right - tmp.right;
			}
			if (tmp.left < 0)//超出屏幕左边
			{
				tmp.left = 0;
			}
			p_data.pWnd->Move(tmp.left, tmp.top, tmp.right, tmp.bottom);
			p_data.hColorS = new UIColorPicker(p_data.pWnd, 0, 0, 0, 0, eos_picker_colors, 0, 0, -1);
			p_data.hColorS->SetlParam((size_t)this);
			p_data.hColorS->p_data.color = p_data.color;
			p_data.hColorS->Lock(3, 3, -1, -1, 200, 200);

			p_data.hColorH = new UIColorPicker(p_data.pWnd, 0, 0, 0, 0, eos_picker_colorh, 0, 0, -1);
			p_data.hColorH->SetlParam((size_t)this);
			p_data.hColorH->p_data.color = p_data.color;
			p_data.hColorH->Lock(206, 3, -1, -1, 25, 200);

			p_data.hColorA = new UIColorPicker(p_data.pWnd, 0, 0, 0, 0, eos_picker_colora, 0, 0, -1);
			p_data.hColorA->SetlParam((size_t)this);
			p_data.hColorA->p_data.color = p_data.color;
			p_data.hColorA->Lock(-1, 3, 7, -1, 25, 200);



			p_data.hinfo = new UIColorPicker(p_data.pWnd, 0, 0, 0, 0, eos_picker_info, 0, 0, -1);
			p_data.hinfo->SetlParam((size_t)this);
			p_data.hinfo->Lock(7, 206, 7, -1, -1, 175);
			p_data.pWnd->Show();

			p_data.fAngle = 180.f;;
			Redraw();
			//UIAnimation::Start(this, 0, 180, 0, 0, AniEffect::Default, 15, 0, 1, 1);
		}
	}
}
void HHBUI::UIColorPicker::Close()
{
	if (p_data.pWnd)
	{
		delete p_data.pWnd;
		p_data.pWnd = nullptr;
	}
}
BOOL HHBUI::UIColorPicker::IsPopup()
{
	return p_data.pWnd != nullptr;
}
void HHBUI::UIColorPicker::SetColour(UIColor dwColor)
{
	p_data.color = dwColor;
	auto obj = (UIColorPicker*)GetlParam();
	if (obj)
	{
		obj->p_data.hColorA->p_data.color = p_data.color;
		obj->p_data.hColorS->p_data.color = p_data.color;
		obj->p_data.hColorH->p_data.color = p_data.color;
	}
	Redraw();
}
void HHBUI::UIColorPicker::GetColour(UIColor& dwColor)
{
	dwColor = p_data.color;
}
void HHBUI::UIColorPicker::SetColourArrow(UIColor dwColor)
{
	p_data.colorarrow = dwColor;
}


LRESULT HHBUI::UIColorPicker::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_CREATE)
	{
		p_data.hBrush = new UIBrush(UIColor(30, 30, 30, 255));
		if ((m_data.dwStyle & eos_picker_colorh) != 0)
		{
		}
		else if ((m_data.dwStyle & eos_picker_colors) != 0)
		{
		}
		else if ((m_data.dwStyle & eos_picker_colora) != 0)
		{
		}
		else if ((m_data.dwStyle & eos_picker_info) != 0)
		{
			SetFontFromFamily(0, 11);
		}
		else
		{
			p_data.color = UIColor(37, 37, 38, 255);
			p_data.colorarrow = p_data.color;
		}
	}
	else if (uMsg == WM_SIZE)
	{
		if ((m_data.dwStyle & eos_picker_colors) != 0)
		{
			INT w = GET_X_LPARAM(lParam);
			INT h = GET_Y_LPARAM(lParam);
			if (w != 0)
			{
				Init_colors(w, h);
			}
		}
		else if ((m_data.dwStyle & eos_picker_colorh) != 0)
		{
			INT w = GET_X_LPARAM(lParam);
			INT h = GET_Y_LPARAM(lParam);
			if (w != 0)
			{
				Init_colorh(w - 3, h);
			}
		}
	}
	else if (uMsg == WM_DESTROY)
	{
		delete p_data.hBrush;
		if ((m_data.dwStyle & eos_picker_colorh) != 0)
		{
			delete p_data.dstImgwhee;
		}
		else if ((m_data.dwStyle & eos_picker_colors) != 0)
		{
			delete p_data.dstImgwhee;
			if (p_data.bmp_handle)
				::DeleteObject(p_data.bmp_handle);
		}
		else if ((m_data.dwStyle & eos_picker_info) != 0)
		{

		}
		else if ((m_data.dwStyle & eos_picker_colora) != 0)
		{

		}
	}
	else if (uMsg == WM_EX_EASING)
	{
		auto easing = (HHBUI::info_Animation*)lParam;
		p_data.fAngle = easing->nCurrentX;
		Redraw();
	}
	else if (uMsg == WM_MOUSEHOVER)
	{
		if ((m_data.dwStyle & eos_picker_info) == 0)
		{
			auto obj = (UIColorPicker*)GetlParam();
			if (obj && obj->p_data.hinfo->p_data.hEdit)
				obj->p_data.hinfo->p_data.hEdit->Show(FALSE);
		}
	}
	else if (uMsg == WM_MOUSELEAVE)
	{
		if ((m_data.dwStyle & eos_picker_info) == 0)
		{
			p_data.Index = -1;
		}
	}
	else if (uMsg == WM_LBUTTONDOWN)
	{
		p_data.down = TRUE;
		if ((m_data.dwStyle & eos_picker_colorh) != 0)
		{
			Picker_ColorH_down(lParam);
		}
		else if ((m_data.dwStyle & eos_picker_colors) != 0)
		{
			Picker_ColorS_down(lParam);
		}
		else if ((m_data.dwStyle & eos_picker_info) != 0)
		{
			auto obj = (UIColorPicker*)GetlParam();
			if (p_data.Index != -1 && p_data.Index != 7)
			{
				std::wstring str;
				ExRectF rect = p_data.rect[p_data.Index];
				switch (p_data.Index) {
				case 0: str = std::to_wstring(static_cast<int>(obj->p_data.color.R())); break;
				case 1: str = std::to_wstring(static_cast<int>(obj->p_data.color.G())); break;
				case 2: str = std::to_wstring(static_cast<int>(obj->p_data.color.B())); break;
				case 3: str = std::to_wstring(static_cast<int>(obj->p_data.color.A())); break;
				case 4: str = std::to_wstring(obj->p_data.hColorH->p_data.hue);break;
				case 5: str = std::to_wstring(obj->p_data.hColorH->p_data.s);break;
				case 6: str = std::to_wstring(obj->p_data.hColorH->p_data.v);break;
				case 8: str = obj->p_data.html;break;
				default: str = L"Unknown"; break;
				}
				if (!p_data.hEdit)
				{
					p_data.hEdit = new UIEdit(this, rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, str.c_str(), eos_nodpiscale, eos_ex_focusable, 0, Center | Middle | SingleLine);
					p_data.hEdit->SetMsgProc(On_free_Proc);
					p_data.hEdit->Style(p_data.Index == 8 ? eos_edit_readonly | eos_edit_hideselection : eos_edit_numericinput | eos_edit_disablemenu);
					p_data.hEdit->SetColor(color_background, UIColor(L"#1F314B"));
					p_data.hEdit->SetColor(color_text_normal, UIColor(255, 255, 255));
					p_data.hEdit->SetColorCaret(UIColor(255, 255, 255));
					p_data.hEdit->SetlParam((size_t)this);
					if (p_data.Index == 8)
						p_data.hEdit->SetLimitText(-1);
					else
						p_data.hEdit->SetLimitText(3);
					p_data.hEdit->SetFontFromFamily(0, 11);
					p_data.hEdit->SetFocus();
				}
				else
				{
					if (p_data.Index == 8)
						p_data.hEdit->SetLimitText(-1);
					else
						p_data.hEdit->SetLimitText(3);
					p_data.hEdit->Style(p_data.Index == 8 ? eos_edit_readonly | eos_edit_hideselection : eos_edit_numericinput | eos_edit_disablemenu);
					p_data.hEdit->SetText(str.c_str());
					p_data.hEdit->Move(rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, FALSE, TRUE);
					p_data.hEdit->SetFocus();
					p_data.hEdit->Show(TRUE);
				}
			}
			else
			{
				if (p_data.hEdit)
					p_data.hEdit->Show(FALSE);
			}
				
		}
		else if ((m_data.dwStyle & eos_picker_colora) != 0)
		{
			Picker_ColorA_down(lParam);
		}
		else
		{

		}
		SetState(state_hover | state_down, FALSE);
	}
	else if (uMsg == WM_LBUTTONUP)
	{
		p_data.down = FALSE;
		if ((m_data.dwStyle & eos_picker_colorh) != 0)
		{
		}
		else if ((m_data.dwStyle & eos_picker_colors) != 0)
		{
			Redraw();
		}
		else if ((m_data.dwStyle & eos_picker_info) != 0)
		{
			return S_OK;
		}
		else if ((m_data.dwStyle & eos_picker_colora) != 0)
		{

		}
		else
		{
			Popup();
			return S_OK;
		}
		auto obj = (UIColorPicker*)GetlParam();
		if (obj)
			obj->DispatchNotify(MCM_GETCOLOR, 0, 0);
		else
			DispatchNotify(MCM_GETCOLOR, 0, 0);
	}
	else if (uMsg == WM_MOUSEMOVE)
	{
		if ((m_data.dwStyle & eos_picker_colorh) != 0)
		{
			if (p_data.down)
				Picker_ColorH_down(lParam);
		}
		else if ((m_data.dwStyle & eos_picker_colors) != 0)
		{
			if (p_data.down)
				Picker_ColorS_down(lParam);
		}
		else if ((m_data.dwStyle & eos_picker_info) != 0)
		{
			for (int i = 0; i < 9; ++i) {
				if(p_data.rect[i].PtInRect(static_cast<float>(GET_X_LPARAM(lParam)), static_cast<float>(GET_Y_LPARAM(lParam))))
				{
					p_data.Index = i;
					return S_OK;
				}
			}
			p_data.Index = -1;


		}
		else if ((m_data.dwStyle & eos_picker_colora) != 0)
		{
			if (p_data.down)
				Picker_ColorA_down(lParam);
		}
		else
		{

		}
	}
	else if (uMsg == WM_SYSCOLORCHANGE)
	{
		if (wParam == color_border)
		{
			if (p_data.pWnd)
			{
				UIColor border;
				GetColor(color_border, border);
				p_data.pWnd->SetBorderColor(border);
			}
		}
	}
	return S_OK;
}
void HHBUI::UIColorPicker::OnPaintProc(ps_context ps)
{
	if ((m_data.dwStyle & eos_picker_colorh) != 0)
	{
		Picker_ColorH_paint(ps);
	}
	else if ((m_data.dwStyle & eos_picker_colors) != 0)
	{
		Picker_ColorS_paint(ps);
	}
	else if ((m_data.dwStyle & eos_picker_info) != 0)
	{
		Picker_Info_paint(ps);
	}
	else if ((m_data.dwStyle & eos_picker_colora) != 0)
	{
		Picker_ColorA_paint(ps);
	}
	else
	{
		Picker_paint(ps);
	}
}
LRESULT HHBUI::UIColorPicker::OnWndMsgCrProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIColorPicker*)window->GetlParam();
	if (uMsg == WM_DESTROY)
	{
		obj->p_data.nProcessTime = GetTickCount64();
		obj->SetState(state_hover | state_down, TRUE);
		obj->p_data.pWnd = nullptr;

		obj->p_data.fAngle = 0.f;
		obj->Redraw();
		//UIAnimation::Start(obj, 180, 0, 0, 0, AniEffect::Default, 10, 0, 1, 1);

	}
	return S_OK;
}
LRESULT HHBUI::UIColorPicker::On_free_Proc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto obj = (UIEdit*)UIView;
	if (uMsg == WM_KEYDOWN)
	{
		if (wParam == VK_RETURN)
		{
			auto hobj = (UIColorPicker*)obj->GetlParam();
			auto str = obj->GetText();
			if (str && hobj->p_data.Index != 8)
			{
				auto obj_s = (UIColorPicker*)hobj->GetlParam();
				UIColor color;
				try {
					int num = std::stoi(str);
					if (num >= 0) {

						if (hobj->p_data.Index == 0)
						{
							num = std::min(num, 255);
							color = UIColor(num, obj_s->p_data.color.G(), obj_s->p_data.color.B(), obj_s->p_data.color.A());
						}
						else if (hobj->p_data.Index == 1)
						{
							num = std::min(num, 255);
							color = UIColor(obj_s->p_data.color.R(), num, obj_s->p_data.color.B(), obj_s->p_data.color.A());
						}
						else if (hobj->p_data.Index == 2)
						{
							num = std::min(num, 255);
							color = UIColor(obj_s->p_data.color.R(), obj_s->p_data.color.G(), num, obj_s->p_data.color.A());
						}
						else if (hobj->p_data.Index == 3)
						{
							num = std::min(num, 255);
							color = UIColor(obj_s->p_data.color.R(), obj_s->p_data.color.G(), obj_s->p_data.color.B(), num);
						}
						else if (hobj->p_data.Index == 4)
						{
							WORD r = 0, g = 0, b = 0;
							num = std::min(num, 359);

							fromHSVtoRGB(num, obj_s->p_data.hColorS->p_data.s, obj_s->p_data.hColorS->p_data.v, r, g, b);
							color = UIColor(r, g, b);
						}
						else if (hobj->p_data.Index == 5)
						{
							WORD r = 0, g = 0, b = 0;
							num = std::min(num, 100);

							fromHSVtoRGB(obj_s->p_data.hColorS->p_data.hue, num, obj_s->p_data.hColorS->p_data.v, r, g, b);
							color = UIColor(r, g, b);
						}
						else if (hobj->p_data.Index == 6)
						{
							WORD r = 0, g = 0, b = 0;
							num = std::min(num, 100);

							fromHSVtoRGB(obj_s->p_data.hColorS->p_data.hue, obj_s->p_data.hColorS->p_data.s, num, r, g, b);
							color = UIColor(r, g, b);

						}
						else if (hobj->p_data.Index == 7)
						{
							return S_OK;
						}
						else if (hobj->p_data.Index == 8)
						{
							return S_OK;
						}
						if (!color.isInRange())
							return S_OK;
						obj_s->p_data.color = color;
						obj_s->p_data.hColorH->p_data.color = color;
						obj_s->p_data.hColorH->Redraw();

						obj_s->p_data.hColorS->p_data.color = color;
						if (obj_s->p_data.hColorS->p_data.dstImgwhee)
						{
							delete obj_s->p_data.hColorS->p_data.dstImgwhee;
							obj_s->p_data.hColorS->p_data.dstImgwhee = nullptr;
						}
						obj_s->p_data.hColorS->Redraw();
						obj_s->p_data.hColorA->p_data.color = color;
						obj_s->p_data.hColorA->Redraw();
						obj_s->Redraw();
						hobj->p_data.Index = -1;
						hobj->Redraw();

					}
				}
				catch (const std::invalid_argument& e) {
					OutputDebugStringA(e.what());
				}
				catch (const std::out_of_range& e) {
					OutputDebugStringA(e.what());
				}
			}
			obj->Show(FALSE);

		}
	}
	return S_OK;
}

void HHBUI::UIColorPicker::Picker_paint(ps_context ps)
{
	auto pDeviceContext = (ID2D1DeviceContext*)ps.hCanvas->GetContext(0);
	p_data.hBrush->SetColor(p_data.colorarrow);
	UIDrawContext::DrawRotatedLines(pDeviceContext, (ID2D1Brush*)p_data.hBrush->GetContext(), ps.rcPaint.right - 35, (ps.uHeight - static_cast<FLOAT>(35)) / 2, 35, 35, p_data.fAngle);

	p_data.hBrush->SetColor(p_data.color);
	FLOAT radius = (m_data.radius.left + m_data.radius.top + m_data.radius.right + m_data.radius.bottom) / 4.0f;
	ps.hCanvas->FillRoundRect(p_data.hBrush, UIEngine::ScaleValue(6), UIEngine::ScaleValue(6), ps.uHeight - UIEngine::ScaleValue(6), ps.uHeight - UIEngine::ScaleValue(6), radius);
	UIColor border;
	GetColor(color_border, border);
	if (!border.empty())
	{
		p_data.hBrush->SetColor(UIColor(155, 155, 155, 155));
		ps.hCanvas->DrawRoundRect(p_data.hBrush, UIEngine::ScaleValue(6), UIEngine::ScaleValue(6), ps.uHeight - UIEngine::ScaleValue(6), ps.uHeight - UIEngine::ScaleValue(6), radius, 1);
	}
	p_data.html = vstring::format(L"#%.6x", RGBToHex(static_cast<int>(p_data.color.R()), static_cast<int>(p_data.color.G()), static_cast<int>(p_data.color.B())));
	UIColor text_normal;
	GetColor(color_text_normal, text_normal);
	ps.hCanvas->DrawTextByColor(ps.hFont, p_data.html.c_str(), DT_SINGLELINE | DT_VCENTER | DT_LEFT | DT_WORD_ELLIPSIS, ps.uHeight,
		5,
		ps.uWidth + 8 - ps.uHeight,
		ps.uHeight - 3, text_normal);

}

void HHBUI::UIColorPicker::Picker_ColorS_paint(ps_context ps)
{
	WORD v = 0, s = 0, h = 0;
	fromRGBtoHSV(p_data.color.R(), p_data.color.G(), p_data.color.B(), v, s, h);
	if (s == 0)
		s = 4;
	if (s == 100)
		s = 96;
	if (v == 0)
		v = 4;
	if (v == 100)
		v = 95;
	int width = ps.uWidth, height = ps.uHeight;
	p_data.v = v;
	p_data.s = s;
	p_data.hue = h;
	if (!p_data.dstImgwhee)
	{
		double	val, val_adv;
		DWORD* p = p_data.bmp_data;
		// outer loop - rows
		int i = height;

		// initial and change
		val = 0.0;
		val_adv = 1.0 / height;

		while (i--)
		{
			HSV_SAT(p, width, h, val);
			val += val_adv;
			p += width;
		}

		p_data.dstImgwhee = new UIImage(p_data.bmp_handle, nullptr, FALSE);
	}

	ps.hCanvas->DrawImage(p_data.dstImgwhee, 5, 5);

	double step = 100.0 / height;
	p_data.hBrush->SetColor(UIColor(RGB(255 - p_data.color.R(), 255 - p_data.color.G(), 255 - p_data.color.B())));
	if (p_data.down)
		ps.hCanvas->DrawEllipse(p_data.hBrush, (int)(s / step) - 9, height - (int)(v / step) - 9, (int)(s / step) + 10, height - (int)(v / step) + 10, 2);
	else
		ps.hCanvas->DrawEllipse(p_data.hBrush, (int)(s / step) - 4, height - (int)(v / step) - 4, (int)(s / step) + 5, height - (int)(v / step) + 5, 2);


}

void HHBUI::UIColorPicker::Picker_ColorA_paint(ps_context ps)
{
	fromRGBtoHSV(p_data.color.R(), p_data.color.G(), p_data.color.B(), p_data.v, p_data.s, p_data.hue);
	int Value = p_data.color.A();

	ID2D1SolidColorBrush* pBrush = nullptr;
	ID2D1LinearGradientBrush* pGradientBrush = nullptr;

	float rf = p_data.color.GetR();
	float gf = p_data.color.GetG();
	float bf = p_data.color.GetB();
	float af = 1.f;

	int w = ps.uWidth - 3;
	int h = ps.uHeight, uHeight = ps.uHeight - 7;

	BOOL flag = FALSE;
	for (int i = 5; i < h; i++) {

		COLORREF cb, cw;

		if (!(i % (w / 2 - 1)))
			flag = !flag;

		float af = 1.0f - (float)i / h;

		// step
		cb = RGB((bf * af) * 255, (gf * af) * 255, (rf * af) * 255);
		cw = RGB((bf * af + 1 - af) * 255, (gf * af + 1 - af) * 255, (rf * af + 1 - af) * 255);

		if (flag)
		{
			p_data.hBrush->SetColor(UIColor(cw));
			for (int x = 2; x < (w / 2) + 2; x++)
				ps.hCanvas->DrawPoint(p_data.hBrush, x, i, 1.f);
			p_data.hBrush->SetColor(UIColor(cb));
			for (int x = (w / 2) + 2; x < w + 1; x++)
				ps.hCanvas->DrawPoint(p_data.hBrush, x, i, 1.f);
		}
		else
		{
			p_data.hBrush->SetColor(UIColor(cb));
			for (int x = 2; x < (w / 2) + 2; x++)
				ps.hCanvas->DrawPoint(p_data.hBrush, x, i, 1.f);
			p_data.hBrush->SetColor(UIColor(cw));
			for (int x = (w / 2) + 2; x < w + 1; x++)
				ps.hCanvas->DrawPoint(p_data.hBrush, x, i, 1.f);
		}


	}

	int a = (Value / 255.f) * uHeight;
	UIPath* dpath = new UIPath();
	dpath->BeginPath();
	if (dpath->StartFigure(0, 5 + uHeight - (a - 5)) == S_OK)
	{
		dpath->LineTo(0, 5 + uHeight - (a + 5));
		dpath->LineTo(7, 5 + uHeight - (a));
		dpath->LineTo(0, 5 + uHeight - (a - 5));

	}
	dpath->EndPath();
	p_data.hBrush->SetColor(UIColor(255, 255, 255, 255));
	ps.hCanvas->FillPath(p_data.hBrush, dpath);
	p_data.hBrush->SetColor(UIColor(0, 0, 0, 255));
	ps.hCanvas->DrawPath(p_data.hBrush, dpath, 1.f);

	dpath->Reset();
	dpath->BeginPath();
	if (dpath->StartFigure(ps.uWidth - 2, 5 + uHeight - (a - 5)) == S_OK)
	{
		dpath->LineTo(ps.uWidth - 2, 5 + uHeight - (a + 5));
		dpath->LineTo(ps.uWidth - 9, 5 + uHeight - (a));
		dpath->LineTo(ps.uWidth - 2, 5 + uHeight - (a - 5));

	}
	dpath->EndPath();
	p_data.hBrush->SetColor(UIColor(255, 255, 255, 255));
	ps.hCanvas->FillPath(p_data.hBrush, dpath);
	p_data.hBrush->SetColor(UIColor(0, 0, 0, 255));
	ps.hCanvas->DrawPath(p_data.hBrush, dpath, 1.f);
	delete dpath;

}

void HHBUI::UIColorPicker::Picker_Info_paint(ps_context ps)
{
	auto obj = (UIColorPicker*)GetlParam();
	fromRGBtoHSV(obj->p_data.color.R(), obj->p_data.color.G(), obj->p_data.color.B(), p_data.v, p_data.s, p_data.hue);
	p_data.hBrush->SetColor(UIColor(L"#1F314B"));

	float hsl[3] = { };
	float rgb[3] = { obj->p_data.color.GetR(), obj->p_data.color.GetG(), obj->p_data.color.GetB() };
	fromRGBtoHSL(rgb, hsl);

	int numRectangles = 4;
	int gap = 2;
	int totalGapWidth = (numRectangles - 1) * gap;
	int remainingWidth = ps.uWidth - totalGapWidth;
	int rectWidth = remainingWidth / numRectangles;
	int startX = 1;
	std::wstring str;
	for (int i = 0; i < numRectangles; ++i) {
		switch (i) {
		case 0: str = L"R：" + std::to_wstring(static_cast<int>(obj->p_data.color.R())); break;
		case 1: str = L"G：" + std::to_wstring(static_cast<int>(obj->p_data.color.G())); break;
		case 2: str = L"B：" + std::to_wstring(static_cast<int>(obj->p_data.color.B())); break;
		case 3: str = L"A：" + std::to_wstring(static_cast<int>(obj->p_data.color.A())); break;
		default: str = L"Unknown"; break;
		}

		p_data.rect[i] = { (float)startX, 0.f, (float)(startX + rectWidth),(float)(26 * ps.dpi) };
		ps.hCanvas->FillRect(p_data.hBrush, p_data.rect[i].left, p_data.rect[i].top, p_data.rect[i].right, p_data.rect[i].bottom);
		ps.hCanvas->DrawTextByColor(ps.hFont, str.c_str(), Middle | Center | SingleLine,
			p_data.rect[i].left, p_data.rect[i].top, p_data.rect[i].right, p_data.rect[i].bottom, UIColor(255, 255, 255, 255));

		switch (i) {
		case 0: str = L"H：" + std::to_wstring(static_cast<int>(p_data.hue)); break;
		case 1: str = L"S：" + std::to_wstring(static_cast<int>(p_data.s)); break;
		case 2: str = L"V：" + std::to_wstring(static_cast<int>(p_data.v)); break;
		case 3: str = L"L：" + std::to_wstring(static_cast<int>(hsl[0])); break;
		default: str = L"Unknown"; break;
		}

		p_data.rect[4 + i] = { (float)startX, (float)((26 * ps.dpi) + gap), (float)(startX + rectWidth), (float)((26 * ps.dpi) + gap + (26 * ps.dpi)) };
		ps.hCanvas->FillRect(p_data.hBrush, p_data.rect[4 + i].left, p_data.rect[4 + i].top, p_data.rect[4 + i].right, p_data.rect[4 + i].bottom);
		ps.hCanvas->DrawTextByColor(ps.hFont, str.c_str(), Middle | Center | SingleLine,
			p_data.rect[4 + i].left, p_data.rect[4 + i].top, p_data.rect[4 + i].right, p_data.rect[4 + i].bottom, UIColor(255, 255, 255, 255));

		startX += rectWidth + gap;
	}

	p_data.rect[8] = { 1.f, (float)((26 * ps.dpi) * 2 + gap + 1), ps.uWidth - 2.f, (float)((26 * ps.dpi) * 2 + gap + 1 + (26 * ps.dpi)) };
	ps.hCanvas->FillRect(p_data.hBrush, p_data.rect[8].left, p_data.rect[8].top, p_data.rect[8].right, p_data.rect[8].bottom);
	p_data.html = vstring::format(L"#%.6x", RGBToHex(static_cast<int>(obj->p_data.color.R()), static_cast<int>(obj->p_data.color.G()), static_cast<int>(obj->p_data.color.B())));
	ps.hCanvas->DrawTextByColor(ps.hFont, p_data.html.c_str(), Middle | Left | SingleLine, p_data.rect[8].left + 5, p_data.rect[8].top, p_data.rect[8].right, p_data.rect[8].bottom,
		UIColor(255, 255, 255, 255));
}

void HHBUI::UIColorPicker::Picker_ColorH_paint(ps_context ps)
{
	fromRGBtoHSV(p_data.color.R(), p_data.color.G(), p_data.color.B(), p_data.v, p_data.s, p_data.hue);

	ps.hCanvas->DrawImage(p_data.dstImgwhee, 1, 0);

	double hue, step, uHeight = 5;
	step = 359.0 / (ps.uHeight - 2);

	hue = p_data.hue / step;

	UIPath* dpath = new UIPath();
	dpath->BeginPath();
	if (dpath->StartFigure(0, ((int)(hue + 1))) == S_OK)
	{
		dpath->LineTo(0, uHeight + (hue + 5));
		dpath->LineTo(7, uHeight + (hue));
		dpath->LineTo(0, uHeight + (hue - 5));

	}
	dpath->EndPath();
	p_data.hBrush->SetColor(UIColor(255, 255, 255, 255));
	ps.hCanvas->FillPath(p_data.hBrush, dpath);
	p_data.hBrush->SetColor(UIColor(0, 0, 0, 255));
	ps.hCanvas->DrawPath(p_data.hBrush, dpath, 1.f);

	dpath->Reset();
	dpath->BeginPath();
	if (dpath->StartFigure(ps.uWidth - 2, ((int)(hue + 1))) == S_OK)
	{
		dpath->LineTo(ps.uWidth - 2, uHeight + (hue + 5));
		dpath->LineTo(ps.uWidth - 9, uHeight + (hue));
		dpath->LineTo(ps.uWidth - 2, uHeight + (hue - 5));

	}
	dpath->EndPath();
	p_data.hBrush->SetColor(UIColor(255, 255, 255, 255));
	ps.hCanvas->FillPath(p_data.hBrush, dpath);
	p_data.hBrush->SetColor(UIColor(0, 0, 0, 255));
	ps.hCanvas->DrawPath(p_data.hBrush, dpath, 1.f);
	delete dpath;

}

void HHBUI::UIColorPicker::Init_colorh(INT uWidth, INT uHeight)
{
	
	ID2D1DeviceContext* pDeviceContext = nullptr;
	if (UIDrawContext::ToList.d2d_device->CreateDeviceContext(D2D1_DEVICE_CONTEXT_OPTIONS_NONE, &pDeviceContext) == 0)
	{
		pDeviceContext->SetUnitMode(D2D1_UNIT_MODE_PIXELS);
		//pDeviceContext->SetAntialiasMode(D2D1_ANTIALIAS_MODE_PER_PRIMITIVE);
		ID2D1Bitmap* pBitmap = nullptr;
		pBitmap = UIDrawContext::CreateBitmap(uWidth, uHeight);
		
		pDeviceContext->SetTarget(pBitmap);
		pDeviceContext->BeginDraw();
		/*
		// Create the linear brush for hue
		std::vector<D2D1_GRADIENT_STOP>  gst(360);
		for (int i = 0; i < 360; i++)
		{
			float hsl[3] = { 1,1,p_data.L };
			hsl[0] = (360 - i) / 360.0f;
			hsl[0] *= 6.0f;
			float rgb[3] = { };
			UIColor::HSLtoRGB(hsl, rgb);
			gst[i].position = i / 360.0f;
			gst[i].color.r = rgb[0];
			gst[i].color.g = rgb[1];
			gst[i].color.b = rgb[2];
			gst[i].color.a = 1.0f;


		}
		ID2D1GradientStopCollection* pGradientStops = nullptr;
		ID2D1LinearGradientBrush* lbr = nullptr;
		pDeviceContext->CreateGradientStopCollection(
			gst.data(),
			360,
			D2D1_GAMMA_2_2,
			D2D1_EXTEND_MODE_CLAMP,
			&pGradientStops
		);
		pDeviceContext->CreateLinearGradientBrush(
			D2D1::LinearGradientBrushProperties(
				D2D1::Point2F(p_data.HRect.rect.left, p_data.HRect.rect.top),
				D2D1::Point2F(p_data.HRect.rect.right, p_data.HRect.rect.bottom)),
			pGradientStops,
			&lbr);
		if (lbr)
		{
			ExMatrix3x2 matrix;
			matrix.Translate(p_data.HRect.rect.left, p_data.HRect.rect.top);
			matrix.Rotate(150);
			matrix.Translate(-p_data.HRect.rect.left, -p_data.HRect.rect.top);
			D2D1_MATRIX_3X2_F tranform = MatrixEx(matrix);
			pDeviceContext->SetTransform(tranform);
			pDeviceContext->FillRectangle(p_data.HRect.rect, lbr);
			matrix.Reset();
			pDeviceContext->SetTransform(tranform);
	
		}
		pGradientStops->Release();
		*/


		int m_nBuffSize = uHeight;
		std::vector<UIColor> colorBuffer;
		HSV_HUE(colorBuffer, m_nBuffSize, 1, 1);
		ID2D1SolidColorBrush* m_brush = nullptr;
		UIDrawContext::ToList.d2d_dc->CreateSolidColorBrush(D2D1::ColorF(0, 0, 0), &m_brush);
		if (m_brush)
		{
			int i = uHeight - 5, y = uHeight - 1;
			while (i--)
			{
				m_brush->SetColor(colorBuffer[i].GetDxObject());
				D2D1_RECT_F rect = D2D1::RectF(0, y, uWidth, y + 1);
				pDeviceContext->FillRectangle(rect, m_brush);
				y -= 1;
			}

		}
		pDeviceContext->EndDraw();


		IWICBitmap* pWicBitmap = nullptr;
		UIDrawContext::ToWicBitmap(pBitmap, uWidth, uHeight, &pWicBitmap);
		if (pWicBitmap)
		{
			if (p_data.dstImgwhee)
				delete p_data.dstImgwhee;
			p_data.dstImgwhee = new UIImage(pWicBitmap);
		}
		SafeRelease(pBitmap);
		SafeRelease(pDeviceContext);
	}
}

void HHBUI::UIColorPicker::Init_colors(INT uWidth, INT uHeight)
{
	if (p_data.bmp_handle)
		::DeleteObject(p_data.bmp_handle);

	BITMAPINFO bmp_info{};
	bmp_info.bmiHeader.biSize = sizeof(BITMAPINFO);
	bmp_info.bmiHeader.biWidth = uWidth;
	bmp_info.bmiHeader.biHeight = uHeight;
	bmp_info.bmiHeader.biPlanes = 1;
	bmp_info.bmiHeader.biBitCount = 32;
	bmp_info.bmiHeader.biCompression = BI_RGB;
	bmp_info.bmiHeader.biSizeImage = uWidth * uHeight * 32 / 8;
	bmp_info.bmiHeader.biXPelsPerMeter =
		bmp_info.bmiHeader.biYPelsPerMeter = 72 * 2 * 1000;
	bmp_info.bmiHeader.biClrUsed = 0;
	bmp_info.bmiHeader.biClrImportant = 0;
	p_data.bmp_data = NULL;
	p_data.bmp_handle = CreateDIBSection(
		NULL, &bmp_info, DIB_RGB_COLORS, (void**)&p_data.bmp_data, NULL, 0);

}

void HHBUI::UIColorPicker::Picker_ColorS_down(LPARAM lParam)
{
	ExRectF rc;
	GetRect(rc, grt_client, TRUE);
	auto x = GET_X_LPARAM(lParam);
	auto y = GET_Y_LPARAM(lParam);
	CLAMP(x, 0, rc.right - 5);
	CLAMP(y, 5, rc.bottom - 5);

	auto obj = (UIColorPicker*)GetlParam();
	if (SUCCEEDED(p_data.dstImgwhee->GetPixel(x, y, p_data.color)) && obj)
	{
		obj->p_data.color = p_data.color;
		obj->Redraw();

		obj->p_data.hColorA->p_data.color = p_data.color;
		obj->p_data.hColorA->Redraw();
		obj->p_data.hinfo->Redraw();
	}
	Redraw();
}

void HHBUI::UIColorPicker::Picker_ColorA_down(LPARAM lParam)
{
	ExRectF rc;
	GetRect(rc, grt_client, TRUE);
	auto x = GET_X_LPARAM(lParam);
	auto y = GET_Y_LPARAM(lParam);
	int h = rc.bottom - 7;
	CLAMP(y, 0, h);

	int sliderValue = static_cast<int>((float)y / h * 255);
	p_data.color.A(255 - sliderValue);
	auto obj = (UIColorPicker*)GetlParam();
	if (obj)
	{
		obj->p_data.color = p_data.color;
		obj->Redraw();

		obj->p_data.hinfo->Redraw();
	}
	Redraw();
}

void HHBUI::UIColorPicker::Picker_ColorH_down(LPARAM lParam)
{
	ExRectF rc;
	GetRect(rc, grt_client, TRUE);
	auto x = GET_X_LPARAM(lParam);
	auto y = GET_Y_LPARAM(lParam);

	CLAMP(y, 5, rc.bottom - 2);
	UIColor tmpColor;
	if (SUCCEEDED(p_data.dstImgwhee->GetPixel(x, y, tmpColor)))
	{
		if (tmpColor.empty())
			return;

		p_data.color = tmpColor;
		Redraw();

		auto obj = (UIColorPicker*)GetlParam();
		if (obj)
		{
			obj->p_data.color = tmpColor;
			obj->Redraw();


			obj->p_data.hColorA->p_data.color = tmpColor;
			obj->p_data.hColorA->Redraw();

			obj->p_data.hColorS->p_data.color = tmpColor;
			if (obj->p_data.hColorS->p_data.dstImgwhee)
			{
				delete obj->p_data.hColorS->p_data.dstImgwhee;
				obj->p_data.hColorS->p_data.dstImgwhee = nullptr;
			}
			obj->p_data.hColorS->Redraw();
			obj->p_data.hinfo->Redraw();
		}
	}
}

