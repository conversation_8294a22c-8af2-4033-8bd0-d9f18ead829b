﻿/**
** =====================================================================================
**
**       文件名称: base.cpp
**       创建时间: 2025-08-03 (优化版本)
**       文件描述: 【HHBUI】引擎基础类库 - 现代化C++17线程和基础设施框架 （实现文件）
**
**       主要功能:
**       - 高性能现代化渲染线程管理实现
**       - 智能FPS计算与帧率控制系统实现
**       - 线程安全的高性能队列容器实现
**       - 基础UI对象抽象与生命周期管理实现
**       - 定时器信息管理与资源追踪实现
**
**       技术特性:
**       - 采用现代C++17标准与智能指针管理
**       - 异常安全保证与RAII资源管理
**       - 高性能原子操作与无锁编程
**       - 智能线程池与任务调度机制
**       - 实时性能监控与调试诊断
**
** =====================================================================================
**/

#include "pch.h"
#include "base.h"
#include <algorithm>
#include <numeric>

namespace HHBUI
{
	// ==================== UIRenderThread 实现 ====================

	UIRenderThread::UIRenderThread() noexcept
		: m_thread(nullptr)
	{
	}

	UIRenderThread::~UIRenderThread() noexcept
	{
		Stop();
	}

	UIRenderThread::UIRenderThread(UIRenderThread&& other) noexcept
		: m_thread(std::move(other.m_thread))
		, m_state(other.m_state.load(std::memory_order_acquire))
	{
		other.m_state.store(ThreadState::STOPPED, std::memory_order_release);
	}

	UIRenderThread& UIRenderThread::operator=(UIRenderThread&& other) noexcept
	{
		if (this != &other)
		{
			Stop();
			m_thread = std::move(other.m_thread);
			m_state.store(other.m_state.load(std::memory_order_acquire), std::memory_order_release);
			other.m_state.store(ThreadState::STOPPED, std::memory_order_release);
		}
		return *this;
	}

	bool UIRenderThread::Start(bool pause) noexcept
	{
		try
		{
			ThreadState expected = ThreadState::STOPPED;
			if (!m_state.compare_exchange_strong(expected, pause ? ThreadState::PAUSED : ThreadState::RUNNING, std::memory_order_acq_rel))
			{
				return false; // 线程已经在运行
			}

			std::unique_lock<std::shared_mutex> lock(m_thread_mutex);
			m_thread = std::make_unique<std::thread>(&UIRenderThread::ThreadMain, this);

			return true;
		}
		catch (...)
		{
			m_state.store(ThreadState::STOPPED, std::memory_order_release);
			return false;
		}
	}

	void UIRenderThread::Pause() noexcept
	{
		ThreadState expected = ThreadState::RUNNING;
		m_state.compare_exchange_strong(expected, ThreadState::PAUSED, std::memory_order_acq_rel);
	}

	void UIRenderThread::Resume() noexcept
	{
		ThreadState expected = ThreadState::PAUSED;
		if (m_state.compare_exchange_strong(expected, ThreadState::RUNNING, std::memory_order_acq_rel))
		{
			m_condition.notify_one();
		}
	}

	void UIRenderThread::Stop() noexcept
	{
		try
		{
			// 设置停止状态
			m_state.store(ThreadState::STOPPING, std::memory_order_release);
			m_condition.notify_all();

			// 等待线程结束
			std::unique_lock<std::shared_mutex> lock(m_thread_mutex);
			if (m_thread && m_thread->joinable())
			{
				lock.unlock(); // 释放锁以避免死锁
				m_thread->join();
				lock.lock();
				m_thread.reset();
			}

			m_state.store(ThreadState::STOPPED, std::memory_order_release);
		}
		catch (...)
		{
			// 异常情况下也要确保状态正确
			m_state.store(ThreadState::STOPPED, std::memory_order_release);
		}
	}

	std::optional<std::thread::id> UIRenderThread::GetThreadId() const noexcept
	{
		std::shared_lock<std::shared_mutex> lock(m_thread_mutex);
		if (m_thread)
		{
			return m_thread->get_id();
		}
		return std::nullopt;
	}

	void UIRenderThread::ThreadMain() noexcept
	{
		try
		{
			OnThreadStart();

			while (m_state.load(std::memory_order_acquire) != ThreadState::STOPPING)
			{
				// 检查是否需要暂停
				if (m_state.load(std::memory_order_acquire) == ThreadState::PAUSED)
				{
					std::unique_lock<std::mutex> lock(m_condition_mutex);
					m_condition.wait(lock, [this] {
						auto state = m_state.load(std::memory_order_acquire);
						return state == ThreadState::RUNNING || state == ThreadState::STOPPING;
					});
				}

				if (m_state.load(std::memory_order_acquire) == ThreadState::STOPPING)
					break;

				// 执行渲染逻辑
				RenderThread();
			}

			OnThreadStop();
		}
		catch (...)
		{
			// 异常处理：确保线程能够正常退出
			OnThreadStop();
		}
	}

	// ==================== UIFPSCounter 实现 ====================

	UIFPSCounter::UIFPSCounter() noexcept
		: m_last_time(std::chrono::high_resolution_clock::now())
		, m_frame_start_time(m_last_time)
		, m_next_frame_time(m_last_time)
	{
		m_frame_times.fill(0.0f);
	}
	float UIFPSCounter::CalculateFPS() noexcept
	{
		auto current_time = std::chrono::high_resolution_clock::now();
		auto duration = std::chrono::duration_cast<std::chrono::microseconds>(current_time - m_last_time);

		if (duration.count() > 0)
		{
			float frame_time_ms = duration.count() / 1000.0f;
			UpdateStats(frame_time_ms);

			// 计算当前FPS
			float current_fps = 1000.0f / frame_time_ms;
			m_current_fps.store(current_fps, std::memory_order_release);

			m_last_time = current_time;
			m_frame_count.fetch_add(1, std::memory_order_relaxed);

			return current_fps;
		}

		return m_current_fps.load(std::memory_order_acquire);
	}

	void UIFPSCounter::SetTargetFPS(float target_fps) noexcept
	{
		m_target_fps.store(target_fps, std::memory_order_release);

		if (target_fps > 0.0f)
		{
			m_frame_duration = std::chrono::nanoseconds(static_cast<int64_t>(1000000000.0 / target_fps));
		}
		else
		{
			m_frame_duration = std::chrono::nanoseconds(0);
		}
	}

	float UIFPSCounter::GetTargetFPS() const noexcept
	{
		return m_target_fps.load(std::memory_order_acquire);
	}

	void UIFPSCounter::LimitFrameRate() noexcept
	{
		if (m_frame_duration.count() > 0)
		{
			auto current_time = std::chrono::high_resolution_clock::now();
			auto elapsed = current_time - m_frame_start_time;

			if (elapsed < m_frame_duration)
			{
				std::this_thread::sleep_for(m_frame_duration - elapsed);
			}

			m_next_frame_time = std::chrono::high_resolution_clock::now() + m_frame_duration;
		}
	}

	void UIFPSCounter::Reset() noexcept
	{
		std::unique_lock<std::shared_mutex> lock(m_stats_mutex);

		m_current_fps.store(0.0f, std::memory_order_release);
		m_frame_count.store(0, std::memory_order_release);
		m_last_time = std::chrono::high_resolution_clock::now();
		m_frame_start_time = m_last_time;
		m_next_frame_time = m_last_time;

		m_stats = FPSStats{};
		m_frame_times.fill(0.0f);
		m_sample_index = 0;
		m_window_filled = false;
	}

	FPSStats UIFPSCounter::GetStats() const noexcept
	{
		std::shared_lock<std::shared_mutex> lock(m_stats_mutex);
		return m_stats;
	}

	float UIFPSCounter::GetCurrentFPS() const noexcept
	{
		return m_current_fps.load(std::memory_order_acquire);
	}

	float UIFPSCounter::GetAverageFPS() const noexcept
	{
		std::shared_lock<std::shared_mutex> lock(m_stats_mutex);
		return m_stats.average_fps;
	}

	void UIFPSCounter::BeginFrame() noexcept
	{
		m_frame_start_time = std::chrono::high_resolution_clock::now();
	}

	void UIFPSCounter::EndFrame() noexcept
	{
		auto end_time = std::chrono::high_resolution_clock::now();
		auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - m_frame_start_time);
		float frame_time_ms = duration.count() / 1000.0f;

		UpdateStats(frame_time_ms);
		m_frame_count.fetch_add(1, std::memory_order_relaxed);
	}

	void UIFPSCounter::UpdateStats(float frame_time_ms) noexcept
	{
		std::unique_lock<std::shared_mutex> lock(m_stats_mutex);

		// 更新滑动窗口
		m_frame_times[m_sample_index] = frame_time_ms;
		m_sample_index = (m_sample_index + 1) % SAMPLE_WINDOW_SIZE;

		if (!m_window_filled && m_sample_index == 0)
		{
			m_window_filled = true;
		}

		// 计算统计信息
		size_t sample_count = m_window_filled ? SAMPLE_WINDOW_SIZE : m_sample_index;
		if (sample_count > 0)
		{
			float sum = std::accumulate(m_frame_times.begin(), m_frame_times.begin() + sample_count, 0.0f);
			float avg_frame_time = sum / sample_count;

			m_stats.average_fps = avg_frame_time > 0.0f ? 1000.0f / avg_frame_time : 0.0f;
			m_stats.current_fps = frame_time_ms > 0.0f ? 1000.0f / frame_time_ms : 0.0f;

			// 更新最小值和最大值
			if (m_stats.current_fps > 0.0f)
			{
				m_stats.min_fps = std::min(m_stats.min_fps, m_stats.current_fps);
				m_stats.max_fps = std::max(m_stats.max_fps, m_stats.current_fps);
			}
		}

		m_stats.total_frames = m_frame_count.load(std::memory_order_relaxed);
	}

	// ==================== UITimerManager 实现 ====================

	size_t UITimerManager::AddTimer(TimerInfo timer_info)
	{
		std::unique_lock<std::shared_mutex> lock(m_mutex);

		size_t timer_id = m_next_timer_id.fetch_add(1, std::memory_order_relaxed);
		timer_info.nLocalID = timer_id;

		m_timers.emplace(timer_id, std::move(timer_info));
		return timer_id;
	}

	bool UITimerManager::RemoveTimer(size_t timer_id) noexcept
	{
		try
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);

			auto it = m_timers.find(timer_id);
			if (it != m_timers.end())
			{
				it->second.Kill();
				m_timers.erase(it);
				return true;
			}
			return false;
		}
		catch (...)
		{
			return false;
		}
	}

	std::optional<TimerInfo> UITimerManager::GetTimer(size_t timer_id) const noexcept
	{
		try
		{
			std::shared_lock<std::shared_mutex> lock(m_mutex);

			auto it = m_timers.find(timer_id);
			if (it != m_timers.end())
			{
				return std::make_optional(it->second);
			}
			return std::nullopt;
		}
		catch (...)
		{
			return std::nullopt;
		}
	}

	void UITimerManager::CleanupInvalidTimers() noexcept
	{
		try
		{
			std::unique_lock<std::shared_mutex> lock(m_mutex);

			auto it = m_timers.begin();
			while (it != m_timers.end())
			{
				if (!it->second.IsValid())
				{
					it = m_timers.erase(it);
				}
				else
				{
					++it;
				}
			}
		}
		catch (...)
		{
			// 异常情况下保持静默
		}
	}

	size_t UITimerManager::GetActiveTimerCount() const noexcept
	{
		try
		{
			std::shared_lock<std::shared_mutex> lock(m_mutex);

			return std::count_if(m_timers.begin(), m_timers.end(),
				[](const auto& pair) { return pair.second.IsValid(); });
		}
		catch (...)
		{
			return 0;
		}
	}

} // namespace HHBUI
