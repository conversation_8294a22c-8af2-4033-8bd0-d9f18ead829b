﻿#pragma once
namespace HHBUI
{
	class TOAPI UIPath
	{
	public:
		UIPath();
		~UIPath();
		HRESULT Reset();
		//开始描述路径
		HRESULT BeginPath(bool winding_mode = false);
		//结束描述
		HRESULT EndPath();
		//开始描述图形
		HRESULT StartFigure(float x, float y);
		//结束描述图形
		HRESULT FinishFigure(bool close_figure = false);

		HRESULT GetCurPoint(ExPointF* r_point);
		HRESULT MoveTo(float x, float y, bool relative = false);
		HRESULT LineTo(float x, float y, bool relative = false);
		HRESULT LineTo(float x1, float y1, float x2, float y2);
		HRESULT AngleArcTo(float left, float top, float right, float bottom,
			float start_angle, float sweep_angle, bool relative = false);
		HRESULT Arc(float x1, float y1, float x2, float y2, float radiusX, float radiusY, bool fClockwise = false);
		/*
		 * @brief 圆弧
		 * @param  radius_horz 弧的 x 半径
		 * @param  radius_vert 弧的 y 半径
		 * @param  rotate 指定椭圆相对于当前坐标系在顺时针方向上旋转的度数
		 * @param  large_arc 指定给定圆弧是否大于 180 度的值
		 * @param  clockwise 该值指定弧扫描是顺时针还是逆时针
		 * @param  end_x 圆弧的终点x
		 * @param  end_y 圆弧的终点y
		 * @param  relative 终点是否相加
		 */
		HRESULT ArcTo(float radius_horz, float radius_vert, float rotate,
			bool large_arc, bool clockwise, float end_x, float end_y, bool relative = false);
		HRESULT RoundTo(float ctrl_x, float ctrl_y, float end_x, float end_y,
			float radius, bool relative = false);
		//二次贝塞尔曲线
		HRESULT CurveTo(float ctrl_x, float ctrl_y, float end_x, float end_y,
			bool relative = false);
		HRESULT BezierTo(float ctrl1_x, float ctrl1_y, float ctrl2_x, float ctrl2_y,
			float end_x, float end_y, bool relative = false);
		//添加矩形  [!!不需要开始描述图形]
		HRESULT AddRect(float left, float top, float right, float bottom);
		//添加椭圆 [!!不需要开始描述图形]
		HRESULT AddEllipse(float left, float top, float right, float bottom);
		HRESULT AddSuperEllipse(float org_x, float org_y, float radius_x, float radius_y,
			float order, float step = 1.0F);
		//添加圆角矩形  [!!不需要开始描述图形]
		HRESULT AddRoundRect(float left, float top, float right, float bottom, float radius);
		HRESULT AddCustomRoundRect(float left, float top, float right, float bottom,
			float radius_left_top, float radius_right_top, float radius_right_bottom, float radius_left_bottom
		);
		/*
		 * @brief 添加多边形坐标 [!!不需要开始描述图形]
		 * @param points 坐标组
		 * @param count 坐标组数量
		 * @param close_figure 是否结束当前图形
		 * @return [HRESULT]
		 */
		HRESULT AddPolygon(const ExPointF* points, uint32_t count, bool close_figure = false);
		/*
         * @brief 添加多边形 [!!不需要开始描述图形]
         * @param left 多边形外接椭圆的坐标
         * @param top 多边形外接椭圆的坐标
         * @param right 多边形外接椭圆的坐标
         * @param bottom 多边形外接椭圆的坐标
         * @param NumberOfEdges 边数
         * @param Angle 角度
		 * @param close_figure 是否结束当前图形
         * @return [HRESULT]
         */
		HRESULT AddPolygon(FLOAT left, FLOAT top, FLOAT right, FLOAT bottom, size_t NumberOfEdges, FLOAT Angle, bool close_figure = false);
		HRESULT AddPolygon(D2D1_POINT_2F* points, size_t NumberOfEdges, bool close_figure = false);

		HRESULT AddText(UIFont *font, LPCTSTR text, DWORD text_format,
			float left, float top, float right, float bottom);

		HRESULT HitTest(float x, float y, const ExMatrix* tranform = nullptr) const;
		HRESULT GetBounds(ExRectF* r_rect, const ExMatrix* tranform = nullptr) const;
		//取描述表
		LPVOID GetContext(INT index) const;
	private:
		static void MakeRoundRectFigure(ID2D1GeometrySink* sink, float left, float top, float right, float bottom,
			float radius_left_top, float radius_right_top, float radius_right_bottom, float radius_left_bottom, float strokeWidth);

		friend class UIRegion;
		friend class UICanvas;

	private:
		ID2D1PathGeometry* m_geometry = nullptr;
		ID2D1GeometrySink* m_sink = nullptr;
		D2D1_POINT_2F m_cur_point{};
		bool m_figure_started{ false };
	};
}

