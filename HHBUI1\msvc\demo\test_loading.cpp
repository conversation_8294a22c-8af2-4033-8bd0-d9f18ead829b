﻿#include "hhbui.h"
using namespace HHBUI;
#define PI_1 3.14159265358979323846f
constexpr float PI_2 = PI_1 * 2.f;
constexpr float PI_DIV_4 = PI_1 / 4.f;
constexpr float PI_DIV_2 = PI_1 / 2.f;
UILoading* m_loading;
UISlider* m_slider[99];
UIColorPicker* m_pickercr[2];

LRESULT CALLBACK OnLoadingMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UILoading*)UIView;
	if (uMsg == WM_TIMER)
	{
		if (obj->GetStyle() == e_st_dots)
		{
			if (obj->GetMode() == 100)
			{
				static float nextdot2 = 0;
				float velocity = 1.f;
				if (m_slider[0])
					velocity = m_slider[0]->GetValue();

				nextdot2 -= 0.2f * velocity;
				obj->SetNextdot(&nextdot2);
			}
		}



		obj->Redraw();
	}
	else if (uMsg == MCM_GETCOLOR)
	{
		if (m_loading)
		{
			UIColor Color;
			((UIColorPicker*)UIView)->GetColour(Color);
			if (nID == 2001)
			{
				m_loading->SetCrHue(Color);
			}
			else if (nID == 2002)
			{
				m_loading->SetCrBg(Color);
			}

		}
	}
	else if (uMsg == MCM_GETCURSEL)
	{
		auto loading = (UILoading*)window->FindUIView(L"1001");
		if (loading)
		{
			float velocity = 1.f, radius = 50.f;
			if (m_slider[0])
				velocity = m_slider[0]->GetValue();
			if (m_slider[1])
				radius = m_slider[1]->GetValue();
			m_pickercr[1]->Show(FALSE);

			switch (lParam)
			{
			case 1:
				loading->SetStyle(e_st_rainbow);
				loading->SetRainbow(radius, 6.f, velocity, 0.f, 0.f, 1, 0);
				break;
			case 2:
				loading->SetStyle(e_st_rainbow);
				loading->SetRainbow(radius, 6.f, velocity, 0.f, 0.f, 4, 0);
				break;
			case 3:
				loading->SetStyle(e_st_rainbow);
				loading->SetRainbow(radius, 6.f, velocity, 0.f, 0.f, 1, 1);
				break;
			case 4:
				loading->SetStyle(e_st_dots);
				loading->SetDots(radius, 13.f, velocity, 12, -1.f, 0);
				break;
			case 5:
				loading->SetStyle(e_st_dots);
				loading->SetDots(radius, 13.f, velocity, 12, -1.f, 1);
				break;
			case 6:
				loading->SetStyle(e_st_dots);
				loading->SetSpeed(0.3f);
				loading->SetDots(radius, 13.f, velocity, 12, 0.f, 100);//此风格设置SetNextdot方法在Timer消息
				break;
			case 7:
				loading->SetStyle(e_st_incdots);
				loading->SetIncDots(radius, 8.f, velocity, 6);
				break;
			case 8:
				loading->SetStyle(e_st_incfulldots);
				loading->SetIncFullDots(radius, 8.f, velocity, 4);
				break;
			case 9:
				loading->SetStyle(e_st_scaledots);
				loading->SetScaleDots(radius, 8.f, velocity);
				break;
			case 10:
				loading->SetStyle(e_st_incscaledots);
				loading->SetSpeed(6.6f);
				loading->SetIncScaleDots(radius, 9.f, velocity, 6);
				break;
			case 11:
				loading->SetStyle(e_st_movingdots);
				loading->SetMovingDots(radius, 12.f, velocity, 3);
				break;
			case 12:
				loading->SetStyle(e_st_rotatedots);
				loading->SetRotateDots(radius, 12.f, velocity, 2);
				break;
			case 13:
				loading->SetStyle(e_st_barsrotatefade);
				loading->SetBarsRotateFade(20.f, radius, 5.f, velocity);
				break;
			case 14:
				loading->SetStyle(e_st_ang);
				loading->SetAng(radius, 8.f, velocity, 0.75f * PI_2, 0);
				m_pickercr[1]->Show(TRUE);
				break;
			case 15:
				loading->SetStyle(e_st_ang);
				loading->SetSpeed(8.f);
				loading->SetAng(radius, 8.f, velocity, PI_DIV_2, 0);
				m_pickercr[1]->Show(TRUE);
				break;
			case 16:
				loading->SetStyle(e_st_ang);
				loading->SetSpeed(8.5f);
				loading->SetAng(radius, 8.f, velocity, PI_DIV_2, 0);
				m_pickercr[1]->Show(TRUE);
				break;
			case 17:
				loading->SetStyle(e_st_vdots);
				loading->SetVDots(radius, 8.f, velocity, 16, 10, 0);
				m_pickercr[1]->Show(TRUE);
				break;
			case 18:
				loading->SetStyle(e_st_bounce_ball);
				loading->SetBounceBall(radius, 8.f, velocity, 1, TRUE);
				break;
			case 19:
				loading->SetStyle(e_st_eclipse);
				loading->SetAngEclipse(radius, 5.f, velocity, 0.f);
				break;
			case 20:
				loading->SetStyle(e_st_ingyang);
				loading->SetIngYang(radius, 7.f, false, 0.f, velocity, 0.f);
				m_pickercr[1]->Show(TRUE);
				break;
			case 21:
				loading->SetStyle(e_st_ingyang);
				loading->SetIngYang(radius, 5.f, true, 0.1f, velocity, PI_1 * 0.8f);
				m_pickercr[1]->Show(TRUE);
				break;
			case 22:
				loading->SetStyle(e_st_ingyang);
				loading->SetIngYang(radius, 5.f, true, 3.f, velocity, PI_1 * 0.8f);
				m_pickercr[1]->Show(TRUE);
				break;
			case 23:
				loading->SetStyle(e_st_bouncedots);
				loading->SetBounceDots(radius, 6.f, velocity, 3);
				break;
			case 24:
				loading->SetStyle(e_st_fadedots);
				loading->SetFadeDots(radius, 6.f, velocity, 7);
				break;
			case 25:
				loading->SetStyle(e_st_clock);
				loading->SetClock(radius, 5.f, velocity);
				m_pickercr[1]->Show(TRUE);
				break;
			case 26:
				loading->SetStyle(e_st_barchartsine);
				loading->SetBarChartSine(radius, 7.f, velocity, 12);
				break;
			case 27:
				loading->SetStyle(e_st_barchartsine);
				loading->SetBarChartSine(radius, 7.f, velocity, 12, 1);
				break;
			case 28:
				loading->SetStyle(e_st_twinang180);
				loading->SetTwinAng180(radius, radius / 2.f, 6.f, velocity, 0.f);
				m_pickercr[1]->Show(TRUE);
				break;
			case 29:
				loading->SetStyle(e_st_twinang180);
				loading->SetTwinAng180(radius, radius / 2.f, 6.f, velocity, PI_1, 1);
				m_pickercr[1]->Show(TRUE);
				break;
			case 30:
				loading->SetStyle(e_st_fadebars);
				loading->SetFadeBars(radius / 2.f, 1.f);
				break;
			case 31:
				loading->SetStyle(e_st_fadebars);
				loading->SetSpeed(6.8f);
				loading->SetFadeBars(radius / 2.f, 1.f, 3, true);
				break;
			case 32:
				loading->SetStyle(e_st_pulsar);
				loading->SetPulsar(radius, 4.f, velocity, true);
				m_pickercr[1]->Show(TRUE);
				break;
			case 33:
				loading->SetStyle(e_st_pulsar);
				loading->SetPulsar(radius, 4.f, velocity, false);
				m_pickercr[1]->Show(TRUE);
				break;
			case 34:
				loading->SetStyle(e_st_pulsar);
				loading->SetPulsar(radius, 4.f, velocity, true, PI_2, 1);
				m_pickercr[1]->Show(TRUE);
				break;
			case 35:
				loading->SetStyle(e_st_barchartrainbow);
				loading->SetBarChartRainbow(radius, 6.f, velocity, 6, 1, TRUE);
				break;
			case 36:
				loading->SetStyle(e_st_angtwin);
				loading->SetAngTwin(radius, radius / 2, 5.f, velocity, PI_DIV_2);
				m_pickercr[1]->Show(TRUE);
				break;
			case 37:
				loading->SetStyle(e_st_angtwin);
				loading->SetAngTwin(radius / 2, radius, 5.f, velocity, PI_DIV_2);
				m_pickercr[1]->Show(TRUE);
				break;
			case 38:
				loading->SetStyle(e_st_angtwin);
				loading->SetAngTwin(radius / 2, radius, 5.f, velocity, PI_DIV_2, 2);
				m_pickercr[1]->Show(TRUE);
				break;
			case 39:
				loading->SetStyle(e_st_angtwin);
				loading->SetAngTwin(radius, radius / 2, 5.f, velocity, PI_DIV_2, 2);
				m_pickercr[1]->Show(TRUE);
				break;
			case 40:
				loading->SetStyle(e_st_angtwin);
				loading->SetSpeed(5.f);
				loading->SetAngTwin(radius, radius / 2, 5.f, velocity, PI_1 / 1.5f, 2, 1);
				m_pickercr[1]->Show(TRUE);
				break;
			case 41:
				loading->SetStyle(e_st_twinpulsar);
				loading->SetTwinPulsar(radius, 5.f, velocity, 2);
				break;
			case 42:
				loading->SetStyle(e_st_blocks);
				loading->SetBlocks(radius, 17.f, velocity);
				m_pickercr[1]->Show(TRUE);
				break;
			case 43:
				loading->SetStyle(e_st_twinball);
				loading->SetTwinBall(radius, radius / 2, 5.f, 9.5f, velocity);
				m_pickercr[1]->Show(TRUE);
				break;
			case 44:
				loading->SetStyle(e_st_twinball);
				loading->SetTwinBall(radius / 2, radius, 5.f, 9.5f, velocity, 3);
				m_pickercr[1]->Show(TRUE);
				break;
			case 45:
				loading->SetStyle(e_st_twinball);
				loading->SetSpeed(5.f);
				loading->SetTwinBall(radius, radius, 5.f, 9.5f, velocity, 1);
				m_pickercr[1]->Show(TRUE);
				break;
			case 46:
				loading->SetStyle(e_st_gooeyballs);
				loading->SetGooeyBalls(radius, velocity);
				break;
			case 47:
				loading->SetStyle(e_st_gooeyballs);
				loading->SetGooeyBalls(radius, velocity, 1);
				break;
			case 48:
				loading->SetStyle(e_st_moonline);
				loading->SetMoonLine(radius, 5.f, velocity);
				m_pickercr[1]->Show(TRUE);
				break;
			case 49:
				loading->SetStyle(e_st_fluid);
				loading->SetFluid(radius, velocity, 4);
				break;
			case 50:
				loading->SetStyle(e_st_arcfade);
				loading->SetArcFade(radius, 7.f, velocity);
				break;
			case 51:
				loading->SetStyle(e_st_arcfade);
				loading->SetSpeed(3.f);
				loading->SetArcFade(radius, 7.f, velocity, 4, 1);
				break;
			case 52:
				loading->SetStyle(e_st_fadepulsar);
				loading->SetFadePulsar(radius, velocity, 1);
				break;
			case 53:
				loading->SetStyle(e_st_fadepulsar);
				loading->SetSpeed(0.9f);
				loading->SetFadePulsar(radius, velocity, 2);
				break;
			case 54:
				loading->SetStyle(e_st_fadepulsar);
				loading->SetSpeed(2.f);
				loading->SetFadePulsar(radius, velocity, 0, 2);
				break;
			case 55:
				loading->SetStyle(e_st_filledarcfade);
				loading->SetFilledArcFade(radius, velocity, 4);
				break;
			case 56:
				loading->SetStyle(e_st_filledarcfade);
				loading->SetSpeed(6.f);
				loading->SetFilledArcFade(radius, velocity, 6);
				break;
			case 57:
				loading->SetStyle(e_st_filledarcfade);
				loading->SetSpeed(8.f);
				loading->SetFilledArcFade(radius, velocity, 12);
				break;
			case 58:
				loading->SetStyle(e_st_filledarcfade);
				loading->SetSpeed(6.f);
				loading->SetFilledArcFade(radius, velocity, 6, 1);
				break;
			case 59:
				loading->SetStyle(e_st_rotatedatom);
				loading->SetRotatedAtom(radius, 4.f, velocity);
				break;
			case 60:
				loading->SetStyle(e_st_rotatedatom);
				loading->SetRotatedAtom(radius, 4.f, velocity, 3, 2);
				break;
			case 61:
				loading->SetStyle(e_st_rotatedatom);
				loading->SetRotatedAtom(radius, 4.f, velocity, 2, 5);
				break;
			case 62:
				loading->SetStyle(e_st_rotatedatom);
				loading->SetRotatedAtom(radius, 4.f, velocity, 2, 1);
				break;
			case 63:
				loading->SetStyle(e_st_rotatedatom);
				loading->SetRotatedAtom(radius, 4.f, velocity, 3, 5);
				break;
			case 64:
				loading->SetStyle(e_st_rainbowballs);
				loading->SetRainbowBalls(radius, 12.f, velocity);
				break;
			case 65:
				loading->SetStyle(e_st_rainbowballs);
				loading->SetRainbowBalls(radius, 12.f, velocity, 3, 1);
				break;
			case 66:
				loading->SetStyle(e_st_rainbowballs);
				loading->SetRainbowBalls(radius, 12.f, velocity, 4, 5);
				break;
			case 67:
				loading->SetStyle(e_st_scaleblocks);
				loading->ScaleBlocks(radius, 20.f, velocity);
				break;
			case 68:
				loading->SetStyle(e_st_scaleblocks);
				loading->ScaleBlocks(radius, 20.f, velocity, 1);
				break;
			case 69:
				loading->SetStyle(e_st_scaleblocks);
				loading->ScaleBlocks(radius, 20.f, velocity, 2);
				break;
			case 70:
				loading->SetStyle(e_st_hbodots);
				loading->SetHboDots(radius, 4.f, 0.f, 0.f, velocity);
				break;
			case 71:
				loading->SetStyle(e_st_hbodots);
				loading->SetHboDots(radius, 4.f, 0.1f, 0.5f, velocity);
				break;
			case 72:
				loading->SetStyle(e_st_hbodots);
				loading->SetHboDots(radius, 2.f, 0.f, 0.f, velocity, 10);
				break;
			case 73:
				loading->SetStyle(e_st_hbodots);
				loading->SetHboDots(radius, 4.f, 0.1f, 0.5f, velocity, 2);
				break;
			case 74:
				loading->SetStyle(e_st_hbodots);
				loading->SetHboDots(radius, 4.f, 0.1f, 0.5f, velocity, 3);
				break;
			case 75:
				loading->SetStyle(e_st_swingdots);
				loading->SetSwingDots(radius, 6.f, velocity);
				break;
			case 76:
				loading->SetStyle(e_st_wavedots);
				loading->SetWaveDots(radius, 6.f, velocity);
				break;
			case 77:
				loading->SetStyle(e_st_sinsquares);
				loading->SetSinSquares(radius, 6.f, velocity);
				break;
			case 78:
				loading->SetStyle(e_st_sinsquares);
				loading->SetSinSquares(radius, 4.f, velocity, 1);
				break;
			case 79:
				loading->SetStyle(e_st_sinsquares);
				loading->SetSinSquares(radius, 4.f, velocity, 2);
				break;
			case 80:
				loading->SetStyle(e_st_zipdots);
				loading->SetZipDots(radius, 6.f, velocity);
				break;
			case 81:
				loading->SetStyle(e_st_zipdots);
				loading->SetZipDots(radius, 6.f, velocity, 5, 0.5f, 1);
				break;
			case 82:
				loading->SetStyle(e_st_trianglesshift);
				loading->SetTrianglesShift(radius, 20.f, velocity, 8);
				break;
			case 83:
				loading->SetStyle(e_st_circularlines);
				loading->SetCircularLines(radius, velocity);
				break;
			case 84:
				loading->SetStyle(e_st_circularlines);
				loading->SetCircularLines(radius, velocity, 16, 4);
				break;
			case 85:
				loading->SetStyle(e_st_patternrings);
				loading->SetPatternRings(radius, 4.f, velocity);
				break;
			case 86:
				loading->SetStyle(e_st_patternrings);
				loading->SetPatternRings(radius, 4.f, velocity, 3, 1);
				break;
			case 87:
				loading->SetStyle(e_st_pointsshift);
				loading->SetPointsShift(radius, 10.f, velocity);
				break;
			case 88:
				loading->SetStyle(e_st_circularpoints);
				loading->SetCircularPoints(radius, 4.f, velocity);
				break;
			case 89:
				loading->SetStyle(e_st_curvedcircle);
				loading->SetCurvedCircle(radius, 4.f, velocity, 3);
				break;
			case 90:
				loading->SetStyle(e_st_patterneclipse);
				loading->SetPatternEclipse(radius, 4.f, velocity, 5);
				break;
			case 91:
				loading->SetStyle(e_st_patterneclipse);
				loading->SetPatternEclipse(radius, 4.f, velocity, 9, 4.f, 1.f);
				break;
			case 92:
				loading->SetStyle(e_st_rainbowshot);
				loading->SetRainbowShot(radius, 9.f, velocity);
				break;
			case 93:
				loading->SetStyle(e_st_spiral);
				loading->SetSpiral(radius, 4.f, velocity);
				break;
			case 94:
				loading->SetStyle(e_st_spiral);
				loading->SetSpiral(radius, 4.f, velocity, 4, 1);
				break;
			case 95:
				loading->SetStyle(e_st_dnadots);
				loading->SetDnaDots(radius, 9.f, velocity, 0.25f);
				break;
			case 96:
				loading->SetStyle(e_st_dnadots);
				loading->SetDnaDots(radius, 9.f, velocity, 0.25f, true);
				break;
			case 97:
				loading->SetStyle(e_st_rotatedots);
				loading->SetRotateDots(radius, 12.f, velocity, -1);
				break;
			case 98:
				loading->SetStyle(e_st_rotatedots);
				loading->SetRotateDots(radius, 12.f, velocity, 16, 1);
				break;
			case 99:
				loading->SetStyle(e_st_rotatedots);
				loading->SetRotateDots(radius, 12.f, velocity, 6);
				break;
			case 100:
				loading->SetStyle(e_st_rotatedots);
				loading->SetRotateDots(radius, 12.f, velocity, 5, 2);
				break;
			case 101:
				loading->SetStyle(e_st_rotatedots);
				loading->SetRotateDots(radius, 12.f, velocity, 4, 3);
				break;
			case 102:
				loading->SetStyle(e_st_solarballs);
				loading->SetSolarBalls(radius, 9.f, velocity);
				m_pickercr[1]->Show(TRUE);
				break;
			case 103:
				loading->SetStyle(e_st_solarballs);
				loading->SetSpeed(1.f);
				loading->SetSolarBalls(radius, 4.f, velocity, 36, true);
				break;
			case 104:
				loading->SetStyle(e_st_rotatingheart);
				loading->SetRotatingHeart(radius, 7.f, velocity);
				break;
			case 105:
				loading->SetStyle(e_st_fluidpoints);
				loading->SetFluidPoints(radius, 5.f, velocity, 4, 0.45f);
				break;
			case 106:
				loading->SetStyle(e_st_dotstopoints);
				loading->SetDotsToPoints(radius, 7.f, 0.5f, velocity);
				break;
			case 107:
				loading->SetStyle(e_st_threedots);
				loading->SetThreeDots(radius, 12.f, velocity);
				break;
			case 108:
				loading->SetStyle(e_st_caleidospcope);
				loading->SetCaleidospcope(radius, 12.f, velocity);
				break;
			case 109:
				loading->SetStyle(e_st_fivedots);
				loading->SetFiveDots(radius, 12.f, velocity);
				break;
			case 110:
				loading->SetStyle(e_st_herbertballs);
				loading->SetHerbertBalls(radius, 9.f, velocity, 4);
				break;
			case 111:
				loading->SetStyle(e_st_herbertballs3d);
				loading->SetHerbertBalls3D(radius, 9.f, velocity);
				break;
			case 112:
				loading->SetStyle(e_st_squareloading);
				loading->SetSquareLoading(radius, 9.f, velocity);
				break;
			case 113:
				//loading->SetCrHue(UIColor(0, 0, 0));
				loading->SetStyle(e_st_textfading);
				loading->SetText(L"hello HHBUI");
				loading->SetFontFromFamily(L"字魂甜豆体(商用需授权)", 30, ::FontStyle::Bold);
				break;
			case 114:
				loading->SetStyle(e_st_bouncedots);
				loading->SetBounceDots(radius, 8.f, velocity, 6, 1);
				break;
			case 115:
				loading->SetStyle(e_st_bouncedots);
				loading->SetSpeed(1.f);
				loading->SetBounceDots(radius, 8.f, velocity, 6, 2);
				break;
			case 116:
				loading->SetStyle(e_st_twinang360);
				loading->SetTwinAng360(radius, 38.f, 7.f, velocity, 2.1f, 1);
				break;
			case 117:
				loading->SetStyle(e_st_angtwin);
				loading->SetAngTwin(radius, radius / 2, 5.f, velocity, 1.3f, 3, 1);
				m_pickercr[1]->Show(TRUE);
				break;
			case 118:
				loading->SetStyle(e_st_angtwin);
				loading->SetAngTwin(radius / 2, radius, 5.f, velocity, 3.14f, 1, 2);
				m_pickercr[1]->Show(TRUE);
				break;
			case 119:
				loading->SetStyle(e_st_angtwin);
				loading->SetAngTwin(radius / 2, radius, 5.f, velocity, 1.57f, 3, 2);
				m_pickercr[1]->Show(TRUE);
				break;
			case 120:
				loading->SetStyle(e_st_ang);
				loading->SetSpeed(8.f);
				loading->SetAng(radius, 8.f, velocity, PI_DIV_2, 1);
				m_pickercr[1]->Show(TRUE);
				break;
			case 121:
				loading->SetStyle(e_st_ang);
				loading->SetSpeed(8.f);
				loading->SetAng(radius, 8.f, velocity, 0.75f * PI_2, 2);
				m_pickercr[1]->Show(TRUE);
				break;
			case 122:
				loading->SetStyle(e_st_ang);
				loading->SetSpeed(8.f);
				loading->SetAng(radius, 8.f, velocity, PI_DIV_2, 3);
				m_pickercr[1]->Show(TRUE);
				break;
			case 123:
				loading->SetStyle(e_st_ang);
				loading->SetSpeed(2.8f);
				loading->SetAng(radius, 7.f, velocity, PI_DIV_2, 4);
				m_pickercr[1]->Show(TRUE);
				break;
			case 124:
				loading->SetStyle(e_st_ang);
				loading->SetSpeed(8.f);
				loading->SetAng(radius, 7.f, velocity, PI_1, 1);
				m_pickercr[1]->Show(TRUE);
				break;
			case 125:
				loading->SetStyle(e_st_pulsarball);
				loading->SetPulsarBall(radius, 8.f, velocity, TRUE);
				break;
			case 126:
				loading->SetStyle(e_st_rainbowmix);
				loading->SetRainbowMix(radius, 8.f, velocity, 0.f, PI_2, 5, 1);
				break;
			case 127:
				loading->SetStyle(e_st_angmix);
				loading->SetAngMix(radius, 8.f, velocity, PI_1, 4);
				break;
			case 128:
				loading->SetStyle(e_st_angmix);
				loading->SetAngMix(radius, 8.f, velocity, PI_DIV_2, 6, 1);
				break;
			case 129:
				loading->SetStyle(e_st_fadedots);
				loading->SetSpeed(8.f);
				loading->SetFadeDots(radius, 6.f, velocity, 1);
				break;
			case 130:
				loading->SetStyle(e_st_fadedots);
				loading->SetSpeed(5.f);
				loading->SetFadeDots(radius, 6.f, velocity, 8);
				break;
			case 131:
				loading->SetStyle(e_st_twinhbodots);
				loading->SetTwinHboDots(radius, 8.f, 0.1f, 0.5f, velocity);
				break;
			case 132:
				loading->SetStyle(e_st_twinhbodots);
				loading->SetSpeed(3.1f);
				loading->SetTwinHboDots(radius, 8.f, 0.1f, 0.5f, velocity, 3, -0.5f);
				break;
			case 133:
				loading->SetStyle(e_st_moondots);
				loading->SetMoonDots(radius, 30.f, velocity);
				m_pickercr[1]->Show(TRUE);
				break;
			case 134:
				loading->SetStyle(e_st_rotatesegmentspulsar);
				loading->SetRotateSegmentsPulsar(radius, 7.f, velocity, 4, 2);
				break;
			case 135:
				loading->SetStyle(e_st_rotatesegmentspulsar);
				loading->SetRotateSegmentsPulsar(radius, 7.f, velocity, 1, 3);
				break;
			case 136:
				loading->SetStyle(e_st_rotatesegmentspulsar);
				loading->SetRotateSegmentsPulsar(radius, 7.f, velocity, 3, 3);
				break;
			case 137:
				loading->SetStyle(e_st_pointsarcbounce);
				loading->SetPointsArcBounce(radius, 7.f, velocity, 12, 1);
				break;
			case 138:
				loading->SetStyle(e_st_pointsarcbounce);
				loading->SetPointsArcBounce(radius, 7.f, velocity, 12, 1, 0.5f);
				break;
			case 139:
				loading->SetStyle(e_st_pointsarcbounce);
				loading->SetPointsArcBounce(radius, 7.f, velocity, 12, 2, 0.3f);
				break;
			case 140:
				loading->SetStyle(e_st_pointsarcbounce);
				loading->SetPointsArcBounce(radius, 7.f, velocity, 12, 3, 0.3f);
				break;
			case 141:
				loading->SetStyle(e_st_somescaledots);
				loading->SetSpeed(5.6f);
				loading->SetSomeScaleDots(radius, 7.f, velocity, 6);
				break;
			case 142:
				loading->SetStyle(e_st_somescaledots);
				loading->SetSpeed(6.6f);
				loading->SetSomeScaleDots(radius, 7.f, velocity, 6, 1);
				break;
			case 143:
				loading->SetStyle(e_st_twinang360);
				loading->SetTwinAng360(radius, 38.f, 4.f, velocity);
				break;
			case 144:
				loading->SetStyle(e_st_dots);
				loading->SetSpeed(0.3f);
				loading->SetDots(radius, 17.f, velocity, 6, 1.49f, 0);
				break;
			case 145:
				loading->SetStyle(e_st_dots);
				loading->SetSpeed(0.3f);
				loading->SetDots(radius, 15.f, velocity, 4, 1.49f, 1);
				break;
			case 146:
				loading->SetStyle(e_st_dots);
				loading->SetDots(radius, 17.f, velocity, 3, -1.f, 2);
				break;
			case 147:
				loading->SetStyle(e_st_dots);
				loading->SetDots(radius, 17.f, velocity, 13, -1.f, 2);
				break;
			case 148:
				loading->SetStyle(e_st_twinang180);
				loading->SetSpeed(0.5f);
				loading->SetTwinAng180(radius, radius / 2.f, 6.f, velocity, PI_DIV_4, 2);
				m_pickercr[1]->Show(TRUE);
				break;
			case 149:
				loading->SetStyle(e_st_dots);
				loading->SetDots(radius, 17.f, velocity, 12, -1.f, 3);
				break;
			case 150:
				loading->SetStyle(e_st_dots);
				loading->SetDots(radius, 17.f, velocity, 12, -1.f, 4);
				break;
			case 151:
				loading->SetStyle(e_st_vdots);
				loading->SetVDots(radius, 17.f, velocity, 12, 7, 1);
				break;
			case 152:
				loading->SetStyle(e_st_incscaledots);
				loading->SetSpeed(6.6f);
				loading->SetIncScaleDots(radius, 9.f, velocity, 16, 1.f, 5);
				break;
			case 153:
				loading->SetStyle(e_st_twinpulsar);
				loading->SetTwinPulsar(radius, 5.f, velocity, 5, 5);
				break;
			case 154:
				loading->SetStyle(e_st_twinpulsar);
				loading->SetTwinPulsar(radius, 5.f, velocity, 5);
				break;
			}

			m_slider[2]->SetValue(loading->GetDots());
			m_slider[2]->Redraw();
			UIColor normalcr, bgcr;
			loading->GetCrHue(normalcr);loading->GetCrBg(bgcr);
			m_pickercr[0]->SetColour(normalcr);
			m_pickercr[1]->SetColour(bgcr);
		}
	}
	else if (uMsg == TBM_GETPOS)
	{
		auto value = ((UISlider*)UIView)->GetValue();
		if (nID == 3001)
		{
			m_loading->SetVelocity(value);
		}
		else if (nID == 3002)
		{
			m_loading->SetRadius(value);
		}
		else if (nID == 3003)
		{
			m_loading->SetDots((INT)value);
		}
	}
	return S_OK;
}
void testloading(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 750, 400, L"hello Loading", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto bstatic1 = new UIStatic(window, 20, 70, 100, 30, L"选择样式：");
	bstatic1->SetColor(color_text_normal, UIColor(184, 2, 11, 255));
	auto bstatic2 = new UIStatic(window, 20, 120, 100, 30, L"选择颜色：");
	bstatic2->SetColor(color_text_normal, UIColor(184, 2, 11, 255));
	m_pickercr[0] = new UIColorPicker(window, 120, 120, 120, 30, 0, 0, 2001);
	m_pickercr[0]->SetColor(color_border, UIColor(L"#3E3E42"));
	m_pickercr[0]->SetColour(UIColor(L"#0099FF"));
	m_pickercr[0]->SetMsgProc(OnLoadingMsgProc);

	m_pickercr[1] = new UIColorPicker(window, 260, 120, 120, 30, eos_hidden, 0, 2002);
	m_pickercr[1]->SetColor(color_border, UIColor(L"#3E3E42"));
	m_pickercr[1]->SetColour(UIColor(0, 0, 0, 255));
	m_pickercr[1]->SetMsgProc(OnLoadingMsgProc);

	auto Combo = new UIComboBox(window, 120, 70, 200, 30, L"", eos_edit_disablemenu | eos_edit_readonly, 0, 0, SingleLine | Middle);
	Combo->SetColor(color_border, UIColor(L"#3E3E42"));
	Combo->SetMsgProc(OnLoadingMsgProc);

	std::vector<std::wstring> wstrings = {
		L"1. rainbow",
		L"2. rainbow2",
		L"3. rainbow3",
		L"4. dots",
		L"5. dots2",
		L"6. dotsWoBg",
		L"7. IncDots",
		L"8. IncFullDots",
		L"9. ScaleDots",
		L"10. ScaleDots2",
		L"11. MovingDots",
		L"12. RotateDots",
		L"13. BarsRotateFade",
		L"14. Ang270",
		L"15. Ang90",
		L"16. Ang90-to2",
		L"17. VDots",
		L"18. BounceBall",
		L"19. AngEclipse",
		L"20. IngYang",
		L"21. IngYangR",
		L"22. IngYangR2",
		L"23. BounceDots",
		L"24. FadeDots",
		L"25. Clock",
		L"26. ChartSine1",
		L"27. ChartSine2",
		L"28. TwinAng",
		L"29. TwinAng2",
		L"30. FadeBars",
		L"31. FadeScaleBars",
		L"32. Pulsar",
		L"33. Pulsar2",
		L"34. Pulsar3",
		L"35. BarChartRainbow",
		L"36. AngTwin1",
		L"37. AngTwin2",
		L"38. AngTwin3",
		L"39. AngTwin4",
		L"40. AngTwin5",
		L"41. TwinPulsar",
		L"42. Blocks",
		L"43. TwinBall1",
		L"44. TwinBall2",
		L"45. TwinBall3",
		L"46. GooeyBalls",
		L"47. GooeyBalls2",
		L"48. MoonLine",
		L"49. Fluid",
		L"50. ArcFade",
		L"51. ArcFade/2",
		L"52. FadePulsar",
		L"53. FadePulsar2",
		L"54. DoubleFadePulsar",
		L"55. FilledArcFade",
		L"56. FilledArcFade6-1",
		L"57. FilledArcFade6-2",
		L"58. FilledArcFade7",
		L"59. RotatedAtom",
		L"60. RotatedAtom2",
		L"61. RotatedAtom/5",
		L"62. RotatedAtom/2",
		L"63. RotatedAtom/3",
		L"64. RainbowBalls",
		L"65. RainbowBalls/1",
		L"66. RainbowBalls/5",
		L"67. ScaleBlocks",
		L"68. ScaleBlocks2",
		L"69. ScaleSquares",
		L"70. HboDots1",
		L"71. HboDots2",
		L"72. HboDots3",
		L"73. HboDots4",
		L"74. HboDots5",
		L"75. SwingDots",
		L"76. WaveDots",
		L"77. SinSquares",
		L"78. SinSquares/1",
		L"79. SinSquares/2",
		L"80. ZipDots",
		L"81. ZipDotsToBar",
		L"82. TrianglesShift",
		L"83. CircularLines",
		L"84. CircularLines/4",
		L"85. PatternRings",
		L"86. RingSynchronous",
		L"87. PointsShift",
		L"88. CircularPoints",
		L"89. CurvedCircle",
		L"90. PatternEclipse1",
		L"91. PatternEclipse2",
		L"92. RainbowShot",
		L"93. Spiral",
		L"94. SpiralEye",
		L"95. DnaDots1",
		L"96. DnaDots2",
		L"97. RotateDots2",
		L"98. RotateDots3",
		L"99. RotateDots4",
		L"100. RotateDots5",
		L"101. RotateDots6",
		L"102. SolarBalls",
		L"103. SolarScaleBalls",
		L"104. RotatingHeart",
		L"105. FluidPoints",
		L"106. DotsToPoints",
		L"107. ThreeDots",
		L"108. 4Caleidospcope",
		L"109. FiveDots",
		L"110. HerbertBalls",
		L"111. HerbertBalls3D",
		L"112. SquareLoading",
		L"113. TextFading",
		L"114. BounceDots",
		L"115. BounceDots2",
		L"116. TwinAng360",
		L"117. AngTwin",
		L"118. AngTwin4",
		L"119. AngTwin5",
		L"120. Ang90Gravity",
		L"121. Ang90SinRad",
		L"122. Ang90",
		L"123. Ang3",
		L"124. Ang6",
		L"125. PulsarBall",
		L"126. RainbowMix",
		L"127. AngMix1",
		L"128. AngMix2",
		L"129. FadeDots1",
		L"130. FadeDots2",
		L"131. TwinHboDots1",
		L"132. TwinHboDots2",
		L"133. MoonDots",
		L"134. RotateSegmentsPulsar1",
		L"135. RotateSegmentsPulsar2",
		L"136. RotateSegmentsPulsar3",
		L"137. PointsArcBounce1",
		L"138. PointsArcBounce2",
		L"139. PointsArcBounce3",
		L"140. PointsArcBounce4",
		L"141. SomeScaleDots1",
		L"142. SomeScaleDots2",
		L"143. TwinAng360",
		L"144. DotsWoBg2",
		L"145. DotsWoBg3",
		L"146. DotsX3",
		L"147. DotsX13",
		L"148. TwinAngX",
		L"149. Dots/3",
		L"150. Dots/4",
		L"151. VDots/1",
		L"152. IncScaleDots",
		L"153. TwinPulsar/5",
		L"154. TwinPulsar/0"
	};


	for (const auto& wstr : wstrings) {
		ListItem* item1 = new ListItem();
		item1->text = wstr.c_str();
		Combo->AddItem(item1);
	}
	Combo->SetCurSelItem(1);

	m_loading = new UILoading(window, 400, 70, 300, 300, 0, 0, 1001);
	m_loading->SetColor(color_border, UIColor(L"#3E3E42"));
	m_loading->SetMsgProc(OnLoadingMsgProc);
	m_loading->SetStyle(e_st_rainbow);
	m_loading->SetRainbow(50.f, 6.f, 1.f, 0.f, 0.f, 1, 0);
	m_loading->SetTimer(2020, 10);
	m_loading->SetCrHue(UIColor(L"#0099FF"));

	auto bstatic3 = new UIStatic(window, 20, 170, 100, 30, L"设置速度：");
	bstatic3->SetColor(color_text_normal, UIColor(184, 2, 11, 255));

	m_slider[0] = new UISlider(window, 120, 170, 200, 30, L"velocity = {{value}}", 3001);
	m_slider[0]->SetMsgProc(OnLoadingMsgProc);
	m_slider[0]->SetType(UISlider::slider_type::slidBar);
	m_slider[0]->SetSliderColor({}, UIColor(202, 81, 0, 255));
	m_slider[0]->SetRange(0.f, 10.f);
	m_slider[0]->SetValue(1.f);
	m_slider[0]->SetBarSize(25);
	m_slider[0]->SetBarRadius(FALSE);


	auto bstatic4 = new UIStatic(window, 20, 220, 100, 30, L"设置半径：");
	bstatic4->SetColor(color_text_normal, UIColor(184, 2, 11, 255));

	m_slider[1] = new UISlider(window, 120, 220, 200, 30, L"radius = {{value}}", 3002);
	m_slider[1]->SetMsgProc(OnLoadingMsgProc);
	m_slider[1]->SetType(UISlider::slider_type::slidBar);
	m_slider[1]->SetSliderColor({}, UIColor(202, 81, 0, 255));
	m_slider[1]->SetRange(10.f, 100.f);
	m_slider[1]->SetValue(50.f);
	m_slider[1]->SetBarSize(25);
	m_slider[1]->SetBarRadius(FALSE);

	auto bstatic5 = new UIStatic(window, 20, 270, 100, 30, L"圆点数量：");
	bstatic5->SetColor(color_text_normal, UIColor(184, 2, 11, 255));

	m_slider[2] = new UISlider(window, 120, 270, 200, 30, L"dots = {{value}}", 3003);
	m_slider[2]->SetMsgProc(OnLoadingMsgProc);
	m_slider[2]->SetType(UISlider::slider_type::slidBar);
	m_slider[2]->SetSliderColor({}, UIColor(202, 81, 0, 255));
	m_slider[2]->SetRange(1.f, 50.f);
	m_slider[2]->SetValue(1.f);
	m_slider[2]->SetBarSize(25);
	m_slider[2]->SetBarRadius(FALSE);


	window->Show();
	//window->MessageLoop();
}