﻿/**
 * @file memory.h
 * @brief 内存管理
 */
#pragma once
namespace HHBUI
{
	/**
	 * @brief 内存_申请
	 * 申请一段内存
	 * @param size 申请的内存大小
	 * @return 返回分配的内存地址
	 * @attention 使用完毕后，需要调用{ExMemFree}释放内存
	 */
	LPVOID ExMemAlloc(size_t size);

	/**
	 * @brief 内存_重分配
	 * 重新分配一段内存
	 * @param ptr 旧内存地址
	 * @param new_size 新的内存大小
	 * @return 返回新的内存地址
	 * @attention 使用完毕后，需要调用{ExMemFree}释放内存
	 */
	LPVOID ExMemReAlloc(LPVOID ptr, size_t new_size);

	/**
	 * @brief 内存_释放
	 * 释放一段内存
	 * @param ptr 内存地址
	 */
	void ExMemFree(LPVOID ptr);

	/**
	 * @brief 内存_取尺寸
	 * 获取一段内存的尺寸
	 * @param ptr 内存地址
	 * @return 返回内存大小
	 */
	size_t ExMemGetSize(LPVOID ptr);

#define ExAlloc(size) ExMemAlloc(size)
#define ExreAlloc(ptr, new_size) ExMemReAlloc(ptr, new_size)
#define ExFree(ptr) ExMemFree(ptr)
#define ExSafeRelease(pPointer)			{ if (pPointer) { pPointer->Release(); pPointer = NULL; } }
#define ExSafeDelete(pPointer)			{ try { if (pPointer) {delete pPointer;} } catch (...) { assert(false); } pPointer = NULL; } 

}
