﻿#pragma once
#include "hhbui.h"
#include "resource_loader.h"

#include <vector>

// 函数声明
void Demo_Code();
void Demo_xml();

LRESULT CALLBACK OnButtonEvent(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam);
LRESULT CALLBACK OnWndMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);
LRESULT CALLBACK OnViewMsgProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);

/*
* 测试代码
*/
void testbutton(HWND hWnd);
void testemoji(HWND hWnd);
void testcombobox(HWND hWnd);
void testknobs(HWND hWnd);
void testmenu(HWND hWnd);
void testpage(HWND hWnd);
void testlayout(HWND hWnd);
void testfree(HWND hWnd);
void testloading(HWND hWnd);
void testtoast(HWND hWnd);
void testimagebox(HWND hWnd);
void testchart(HWND hWnd);
void testhotkey(HWND hWnd);
void teststatic(HWND hWnd);
void testtabs(HWND hWnd);
void testtimeline(HWND hWnd);
void testtreeview(HWND hWnd);
void testtable(HWND hWnd);
void testcheck(HWND hWnd);
void testgroupbox(HWND hWnd);
void testprogress(HWND hWnd);
void testslider(HWND hWnd);
void testedit(HWND hWnd);
void testlist(HWND hWnd);
void testcolorpicker(HWND hWnd);
void testdatebox(HWND hWnd);
void testsegmented(HWND hWnd);
void testminiblink(HWND hWnd);
void testanimation(HWND hWnd);
void testtour(HWND hWnd);
void testmodal(HWND hWnd);
void testsplashscreen(HWND hWnd);
void testlogin(HWND hWnd);
void testwindowsnap(HWND hWnd);
