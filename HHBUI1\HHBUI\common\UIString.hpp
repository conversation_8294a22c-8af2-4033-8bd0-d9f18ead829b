﻿#pragma once
#include <string>

namespace HHBUI
{
    /**
     * @brief HHBUI库的字符串类型封装
     */
    class UIString : public std::wstring
    {
    public:
        // 构造函数
        UIString() : std::wstring() {}
        UIString(const wchar_t* s) : std::wstring(s ? s : L"") {}
        UIString(const std::wstring& s) : std::wstring(s) {}
        UIString(const UIString& s) : std::wstring(s) {}

        // 赋值操作符
        UIString& operator=(const wchar_t* s)
        {
            std::wstring::operator=(s ? s : L"");
            return *this;
        }

        UIString& operator=(const std::wstring& s)
        {
            std::wstring::operator=(s);
            return *this;
        }

        UIString& operator=(const UIString& s)
        {
            if (this != &s)
                std::wstring::operator=(s);
            return *this;
        }

        // 转换为LPCWSTR
        operator LPCWSTR() const
        {
            return c_str();
        }

        // 与LPCWSTR的比较运算符
        bool operator==(LPCWSTR s) const
        {
            return s ? compare(s) == 0 : empty();
        }

        bool operator!=(LPCWSTR s) const
        {
            return !operator==(s);
        }
    };
} 
