﻿#include "pch.h"

void SleepEx(INT ms)
{
    if (ms > 1)
        ms--;
    timeBeginPeriod(1);
    std::this_thread::sleep_for(std::chrono::milliseconds(ms));
    timeEndPeriod(1);
}

LONG_PTR __get(LPVOID lpAddr, LONG_PTR offset)
{
    size_t a = (size_t)lpAddr + offset;
    return *(LONG_PTR*)a;
}
LONG_PTR __gets(LPVOID lpAddr, INT offset)
{
    return __get(lpAddr, static_cast<ptrdiff_t>(offset) * static_cast<ptrdiff_t>(sizeof(size_t)));
}

INT __get_int(LPVOID lpAddr, LONG_PTR offset)
{
    size_t a = (size_t)lpAddr + offset;
    return *(INT*)a;
}
BOOL __get_bool(LPVOID lpAddr, LONG_PTR offset)
{
    size_t a = (size_t)lpAddr + offset;
    return *(BOOL*)a;
}
FLOAT __get_float(LPVOID lpAddr, LONG_PTR offset)
{
    size_t a = (size_t)lpAddr + offset;
    return *(FLOAT*)a;
}

SHORT __get_short(LPVOID lpAddr, LONG_PTR offset)
{
    size_t a = (size_t)lpAddr + offset;
    return *(SHORT*)a;
}

SHORT __set_short(LPVOID lpAddr, LONG_PTR offset, SHORT value)
{
    size_t a = (size_t)lpAddr + offset;
    SHORT old = *(SHORT*)a;
    *(SHORT*)a = value;
    return old;
}


LONG_PTR __set(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value)
{
    size_t* a = (size_t*)((size_t)lpAddr + offset);
    LONG_PTR old = *a;
    *a = value;
    return old;
}

INT __set_int(LPVOID lpAddr, LONG_PTR offset, INT value)
{
    size_t a = (size_t)lpAddr + offset;
    INT old = *(INT*)a;
    *(INT*)a = value;
    return old;
}

FLOAT __set_float(LPVOID lpAddr, LONG_PTR offset, FLOAT value)
{
    size_t a = (size_t)lpAddr + offset;
    FLOAT old = *(FLOAT*)a;
    *(FLOAT*)a = value;
    return old;
}

BOOL __query(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value) //OK
{
    return (*(LONG_PTR*)((size_t)lpAddr + offset) & value) == value;
}

BOOL __query_int(LPVOID lpAddr, LONG_PTR offset, INT value) //OK
{
    return (*(INT*)((size_t)lpAddr + offset) & value) == value;
}

void __del(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value) //OK
{
    size_t a = (size_t)lpAddr + offset;
    *(LONG_PTR*)a = *(LONG_PTR*)a - (*(LONG_PTR*)a & value);
}

void __add(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value) //OK
{
    size_t a = (size_t)lpAddr + offset;
    *(LONG_PTR*)a = *(LONG_PTR*)a | value;
}

void __addn(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value) //OK
{
    size_t a = (size_t)lpAddr + offset;
    *(LONG_PTR*)a = *(LONG_PTR*)a + value;
}

void __subn(LPVOID lpAddr, LONG_PTR offset, LONG_PTR value) //OK
{
    size_t a = (size_t)lpAddr + offset;
    *(LONG_PTR*)a = *(LONG_PTR*)a - value;
}


