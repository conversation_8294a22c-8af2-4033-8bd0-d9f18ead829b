﻿#pragma once
namespace HHBUI
{
	class TOAPI UIZip : public CZipArchive
	{
	public:
		UIZip() = default;
		UIZip(LPVOID pBytes, DWORD dwByteCounts, LPCSTR pszPsw = NULL);
		UIZip(LPCSTR path, LPCSTR pszPsw = NULL);
		~UIZip();
		/*
		 * EnumFile
		 * @brief    枚举资源文件,回调返回文件路径 注意：只有通过内存加载的方式才支持Unicode文件名
		 * @param    LPCTSTR pszPath --  子路径 可为NULL
		 * @param    EnumResCallback funEnumCB --  枚举使用的回调函数 类型为BOOL(CALLBACK*)(LPCTSTR, LPARAM) BOOL返回FALSE继续枚举 返回TRUE结束枚举
		 * @param    LPARAM lp --  传给回调的附加参数
		 * @return   void
		 */
		void EnumFile(LPCWSTR pszPath, EnumFileCallback funEnumCB, LPARAM lp = 0);

		//获取资源包文件总数 包含目录数量
		INT GetCount();
		//读取zip资源 zFile为0取默认主题
		BOOL ReadSource(LPCWSTR lpname, LPVOID* retData, DWORD* uncompressed_size);
		/*
		 * @brief    读取rc资源 该方法不依赖创建 直接使用
		 * @param    lpname --  资源ID
		 * @param    lpType --  资源类型
		 * @param    retData --  返回的数据
		 * @return   BOOL
		 */
		static BOOL ReadRcSource(WORD lpname, LPCWSTR lpType, std::vector<CHAR>* retData);

	protected:
		const char* m_lpKey = nullptr;
		LPVOID m_crcTzip = nullptr;
		BOOL m_IsPath = FALSE;
	};
}
