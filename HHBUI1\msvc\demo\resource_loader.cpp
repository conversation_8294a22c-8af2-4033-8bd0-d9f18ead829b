#include "resource_loader.h"
#include <string>

namespace ResDLL {
    // 资源DLL模块句柄
    static HMODULE hResourceDll = NULL;

    // 资源DLL函数指针类型定义
    typedef HRSRC (*GetResourceHandleFunc)(UINT);
    typedef HRSRC (*GetPngResourceHandleFunc)(UINT);
    typedef HRSRC (*GetGifResourceHandleFunc)(UINT);
    typedef HGLOBAL (*LoadResourceDataFunc)(HRSRC);
    typedef LPVOID (*LockResourceDataFunc)(HGLOBAL);
    typedef DWORD (*GetResourceSizeFunc)(HRSRC);

    // 函数指针
    static GetResourceHandleFunc pGetResourceHandle = NULL;
    static GetPngResourceHandleFunc pGetPngResourceHandle = NULL;
    static GetGifResourceHandleFunc pGetGifResourceHandle = NULL;
    static LoadResourceDataFunc pLoadResourceData = NULL;
    static LockResourceDataFunc pLockResourceData = NULL;
    static GetResourceSizeFunc pGetResourceSize = NULL;

    // 加载资源DLL
    bool LoadResourceDLL()
    {
        if (hResourceDll != NULL)
            return true; // 已加载

#ifdef _WIN64
        hResourceDll = LoadLibrary(L"res_x64.dll");
#else
        hResourceDll = LoadLibrary(L"res_x86.dll");
#endif

        if (hResourceDll == NULL)
            return false;

        // 获取函数指针
        pGetResourceHandle = (GetResourceHandleFunc)GetProcAddress(hResourceDll, "GetResourceHandle");
        pGetPngResourceHandle = (GetPngResourceHandleFunc)GetProcAddress(hResourceDll, "GetPngResourceHandle");
        pGetGifResourceHandle = (GetGifResourceHandleFunc)GetProcAddress(hResourceDll, "GetGifResourceHandle");
        pLoadResourceData = (LoadResourceDataFunc)GetProcAddress(hResourceDll, "LoadResourceData");
        pLockResourceData = (LockResourceDataFunc)GetProcAddress(hResourceDll, "LockResourceData");
        pGetResourceSize = (GetResourceSizeFunc)GetProcAddress(hResourceDll, "GetResourceSize");

        return pGetResourceHandle != NULL &&
               pGetPngResourceHandle != NULL &&
               pGetGifResourceHandle != NULL &&
               pLoadResourceData != NULL &&
               pLockResourceData != NULL &&
               pGetResourceSize != NULL;
    }

    // 卸载资源DLL
    void UnloadResourceDLL()
    {
        if (hResourceDll != NULL)
        {
            FreeLibrary(hResourceDll);
            hResourceDll = NULL;
            pGetResourceHandle = NULL;
            pGetPngResourceHandle = NULL;
            pGetGifResourceHandle = NULL;
            pLoadResourceData = NULL;
            pLockResourceData = NULL;
            pGetResourceSize = NULL;
        }
    }

    // 从资源DLL加载PNG图像数据
    bool LoadPngFromResourceDLL(UINT resourceID, std::vector<BYTE>& imageData)
    {
        if (!LoadResourceDLL() || pGetPngResourceHandle == NULL)
            return false;

        HRSRC hResInfo = pGetPngResourceHandle(resourceID);
        if (hResInfo == NULL)
            return false;

        DWORD size = pGetResourceSize(hResInfo);
        HGLOBAL hResData = pLoadResourceData(hResInfo);
        if (hResData == NULL)
            return false;

        LPVOID pResData = pLockResourceData(hResData);
        if (pResData == NULL)
            return false;

        imageData.resize(size);
        memcpy(imageData.data(), pResData, size);
        return true;
    }

    // 从资源DLL加载GIF图像数据
    bool LoadGifFromResourceDLL(UINT resourceID, std::vector<BYTE>& imageData)
    {
        if (!LoadResourceDLL() || pGetGifResourceHandle == NULL)
            return false;

        HRSRC hResInfo = pGetGifResourceHandle(resourceID);
        if (hResInfo == NULL)
            return false;

        DWORD size = pGetResourceSize(hResInfo);
        HGLOBAL hResData = pLoadResourceData(hResInfo);
        if (hResData == NULL)
            return false;

        LPVOID pResData = pLockResourceData(hResData);
        if (pResData == NULL)
            return false;

        imageData.resize(size);
        memcpy(imageData.data(), pResData, size);
        return true;
    }
} 