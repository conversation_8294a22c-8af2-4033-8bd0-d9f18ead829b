﻿#pragma once
/*多选框 by:来自群管理员[E.globulus]贡献*/
namespace HHBUI
{
	enum check_state {
		unchecked,		//未选中
		checked,		//选中
		indeterminate	//中间态 单选模式不支持
	};
	class TOAPI UICheck : public UIControl
	{
	public:
		UICheck() = default;
		UICheck(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT nID = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT textFormat = DT_LEFT | DT_VCENTER | DT_SINGLELINE);
		//是否单选模式
		void SetRadio(BOOL isRadio);
		//置选择框位置（isLeft=TRUE为左边，反之右边，默认在左）
		void SetBoxPosition(BOOL isLeft);
		//置选择框颜色（nor为未选中框线颜色，checked为选中高亮颜色，state为框内标识颜色）
		void SetBoxColor(UIColor nor, UIColor checked, UIColor state);
		//置选择框圆角度
		void SetRadius(FLOAT radius);
		//置选择框大小（边长）
		void SetBoxSize(UINT size);

		//置选中状态
		void SetState(check_state state);
		//取选中状态
		check_state GetState();
		//是否开启单击触发中间态（默认单击只会在未选中和选中之间切换）
		void EnableIndeterminate(BOOL enable);

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		void calcPoints();
		struct check_s
		{
			UIBrush* hBrush = nullptr;
			FLOAT rad = 2.f, boxs = 16.f;
			BOOL isl = TRUE, eni = FALSE, radio = FALSE;
			UIColor clr[3] = { UIColor(159,159,161,255),UIColor(20,126,255,255),UIColor(253,253,255,255) };
			ExPointF pots[5]{};
		}p_data;
	};
}
