﻿#include "pch.h"
#include "renderd2d.h"
#include <element/font_pool.h>
#include <common/Exception.h>
#include <wrl.h>
#include <set>

HRESULT HHBUI::UIDrawContext::Init(int device)
{
	ToList.module_dcomp = LoadLibraryW(L"dcomp.dll");
	if (ToList.module_dcomp) {
		ToList.DCompositionCreateDevice2 = (PFN_DCompositionCreateDevice2)
			GetProcAddress(ToList.module_dcomp, "DCompositionCreateDevice2");
	}
	else
		throw_ex(EE_NOREADY, L"渲染器API初始化失败");

	// 创建 D2D 工厂
	throw_if_failed(D2D1CreateFactory(
		D2D1_FACTORY_TYPE_MULTI_THREADED,
		__uuidof(ID2D1Factory1),
		reinterpret_cast<void**>(&ToList.d2d_factory)
	), L"创建D2D工厂失败");
	IDXGIFactory2* DXGIFactory = nullptr;
	//寻找指定设备
	throw_if_failed(CreateDXGIFactory(IID_IDXGIFactory2, (void**)&DXGIFactory), L"创建DXGI工厂失败");
	UINT i = 0;
	IDXGIAdapter* pAdapter;
	std::vector <std::pair<IDXGIAdapter*, std::wstring>> vAdapters;
	while (DXGIFactory->EnumAdapters(i, &pAdapter) != DXGI_ERROR_NOT_FOUND)
	{
		DXGI_ADAPTER_DESC desc;
		pAdapter->GetDesc(&desc);
		vAdapters.push_back(std::make_pair(pAdapter, desc.Description));
		++i;
	}

	pAdapter = nullptr;
	if (device == -1)
		pAdapter = vAdapters[0].first;
	else if (device < (int)vAdapters.size())
		pAdapter = vAdapters[device].first;
	else
		throw_ex(EE_NOREADY, L"没有找到可用的显卡设备！");
	// 创建文本绘制厂类
	throw_if_failed(DWriteCreateFactory(DWRITE_FACTORY_TYPE_SHARED,
		__uuidof(IDWriteFactory), (IUnknown**)&ToList.dwrite_factory
	), L"创建DWrite工厂失败");
	throw_if_failed(DWriteCreateFactory(DWRITE_FACTORY_TYPE_SHARED,
		__uuidof(IDWriteFactory4), (IUnknown**)&ToList.dwrite_factory4
	), L"创建DWrite工厂4失败");
	// 创建WIC厂类
	throw_if_failed(
		CoCreateInstance(CLSID_WICImagingFactory, nullptr, CLSCTX_INPROC_SERVER,
			IID_IWICImagingFactory, (LPVOID*)&ToList.wic_factory
		), L"创建WIC工厂失败");

	// 创建D3D设备
	D3D_FEATURE_LEVEL featureLevels[] = {
			   D3D_FEATURE_LEVEL_11_1,
			   D3D_FEATURE_LEVEL_11_0,
			   D3D_FEATURE_LEVEL_10_1,
			   D3D_FEATURE_LEVEL_10_0,
			   D3D_FEATURE_LEVEL_9_3,
			   D3D_FEATURE_LEVEL_9_2,
			   D3D_FEATURE_LEVEL_9_1
	};
	throw_if_failed(D3D11CreateDevice(pAdapter, D3D_DRIVER_TYPE_UNKNOWN,
		nullptr, D3D11_CREATE_DEVICE_BGRA_SUPPORT, featureLevels, ARRAYSIZE(featureLevels), D3D11_SDK_VERSION,
		&ToList.pd3dDevice, nullptr, &ToList.pd3dDeviceContext
	), L"创建D3D设备失败");

	// 通过D3D设备拿到DXGI设备
	IDXGIDevice1* dxgi_device = nullptr;
	throw_if_failed(
		ToList.pd3dDevice->QueryInterface(IID_IDXGIDevice1, (LPVOID*)&dxgi_device),
		L"获取DXGI设备失败"
	);


	// 通过D2D工厂创建D2D设备
	throw_if_failed(ToList.d2d_factory->CreateDevice(dxgi_device, &ToList.d2d_device), L"创建D2D设备失败");
	SafeRelease(dxgi_device);

	// 创建D2D设备上下文
	throw_if_failed(
		ToList.d2d_device->CreateDeviceContext(D2D1_DEVICE_CONTEXT_OPTIONS_ENABLE_MULTITHREADED_OPTIMIZATIONS, &ToList.d2d_dc),
		L"创建D2D设备上下文失败"
	);

	//获取GDI兼容对象
	throw_if_failed(ToList.d2d_dc->QueryInterface(&ToList.d2d_gdiInterop),
		L"获取GDI兼容对象失败"
	);

	//初始化配置
	ToList.d2d_dc->SetUnitMode(D2D1_UNIT_MODE_PIXELS);
	ToList.d2d_dc->SetTextAntialiasMode(D2D1_TEXT_ANTIALIAS_MODE_GRAYSCALE);//使用灰度抗锯齿
	ToList.d2d_dc->SetAntialiasMode(D2D1_ANTIALIAS_MODE_PER_PRIMITIVE);

	ToList.fpsCounter.SetTargetFPS(-1.f);
	// 初始化Gdiplus
	Gdiplus::GdiplusStartupInput gdiplusStartupInput;
	throw_if_failed(
		Gdiplus::GdiplusStartup(&ToList.gdip_hToken, &gdiplusStartupInput, NULL),
		L"初始化Gdiplus失败"
	);
	ExFontPoolD2D::GetInstance();
	SafeRelease(DXGIFactory);
	return S_OK;
}

void HHBUI::UIDrawContext::UnInit()
{
	SAFE_FREE(ToList.module_dcomp, FreeLibrary);
	Gdiplus::GdiplusShutdown(ToList.gdip_hToken);
	SafeRelease(ToList.d2d_dc);
	SafeRelease(ToList.d2d_device);
	SafeRelease(ToList.d2d_factory);
	SafeRelease(ToList.wic_factory);
	SafeRelease(ToList.dwrite_factory);
	SafeRelease(ToList.dwrite_factory4);
	SafeRelease(ToList.d2d_gdiInterop);
}

ID2D1Bitmap1* HHBUI::UIDrawContext::CreateBitmap(INT width, INT height)
{
	ID2D1Bitmap1* pBitmap = nullptr;
	D2D1_SIZE_U size;
	size.width = width;
	size.height = height;

	D2D1_BITMAP_PROPERTIES1 pro = {};
	pro.pixelFormat.alphaMode = D2D1_ALPHA_MODE_PREMULTIPLIED;
	pro.pixelFormat.format = DXGI_FORMAT_B8G8R8A8_UNORM;
	pro.dpiX = 96;
	pro.dpiY = 96;

	pro.bitmapOptions = D2D1_BITMAP_OPTIONS_TARGET | D2D1_BITMAP_OPTIONS_GDI_COMPATIBLE;

	ToList.d2d_dc->CreateBitmap(size, NULL, 0, pro, (ID2D1Bitmap1**)&pBitmap);
	return pBitmap;
}

BOOL HHBUI::UIDrawContext::ToWicBitmap(ID2D1Bitmap* pBitmap, INT nWidth, INT nHeight, IWICBitmap** pWICBitmap)
{
	D2D1_BITMAP_PROPERTIES1 bp1 = D2D1::BitmapProperties1(
		D2D1_BITMAP_OPTIONS_CANNOT_DRAW | D2D1_BITMAP_OPTIONS_CPU_READ,
		D2D1::PixelFormat(DXGI_FORMAT_B8G8R8A8_UNORM, D2D1_ALPHA_MODE_PREMULTIPLIED)
	);

	ID2D1Bitmap1* pBitmap1 = NULL;
	//创建临时位图
	if (SUCCEEDED(ToList.d2d_dc->CreateBitmap(D2D1::SizeU(nWidth, nHeight), NULL, 0, bp1, &pBitmap1)))
	{
		//从画布位图拷贝
		if (SUCCEEDED(pBitmap1->CopyFromBitmap(NULL, pBitmap, NULL)))
		{
			//锁住
			D2D1_MAPPED_RECT mrc;
			if (SUCCEEDED(pBitmap1->Map(D2D1_MAP_OPTIONS_READ, &mrc)))
			{
				//创建一个空目标位图
				if (SUCCEEDED(ToList.wic_factory->CreateBitmap(nWidth, nHeight, GUID_WICPixelFormat32bppPBGRA, WICBitmapCacheOnDemand, pWICBitmap)))
				{
					//锁定位图
					WICRect rect = { 0, 0, (INT)nWidth, (INT)nHeight };
					IWICBitmapLock* pLocker = NULL;
					if (SUCCEEDED((*pWICBitmap)->Lock(&rect, WICBitmapLockWrite, &pLocker)))
					{
						LPBYTE aBits = NULL;
						UINT cbFrame = 0;
						UINT cbStride = 0;

						//获取缓冲区指针及行跨步
						if (SUCCEEDED(pLocker->GetDataPointer(&cbFrame, &aBits)))
						{
							pLocker->GetStride(&cbStride);

							//拷贝点阵数据到WIC位图中
							cbStride = std::min(cbStride, mrc.pitch);
							for (INT y = 0; y < nHeight; y++)
							{
								RtlMoveMemory(aBits + cbStride * y, mrc.bits + mrc.pitch * y, cbStride);
							}

							//释放锁
							pLocker->Release();
						}
					}
				}
				pBitmap1->Unmap();
			}
		}
		pBitmap1->Release();
	}
	return TRUE;
}

BOOL HHBUI::UIDrawContext::ToWicImage(ID2D1Image* pImg, D2D1_SIZE_U size, ID2D1Bitmap** pBitmap)
{
	ID2D1Image* oldTarget = NULL;
	ID2D1Bitmap1* targetBitmap = NULL;

	//Create a Bitmap with "D2D1_BITMAP_OPTIONS_TARGET"
	D2D1_BITMAP_PROPERTIES1 bitmapProperties =
		D2D1::BitmapProperties1(
			D2D1_BITMAP_OPTIONS_TARGET,
			D2D1::PixelFormat(DXGI_FORMAT_B8G8R8A8_UNORM, D2D1_ALPHA_MODE_PREMULTIPLIED)
		);
	ToList.d2d_dc->CreateBitmap(size, 0, 0, bitmapProperties, &targetBitmap);

	//Save current Target, replace by ID2D1Bitmap
	ToList.d2d_dc->GetTarget(&oldTarget);
	if (oldTarget)
	{
		ToList.d2d_dc->SetTarget(targetBitmap);
		//Draw Image on Target (if currently not drawing also call Begin/EndDraw)
		ToList.d2d_dc->DrawImage(pImg);

		//Set previous Target
		ToList.d2d_dc->SetTarget(oldTarget);

		oldTarget->Release();

		*pBitmap = targetBitmap;
		return TRUE;
	}
	return FALSE;
}

void HHBUI::UIDrawContext::DrawImageRotate(ID2D1DeviceContext* pContext, ID2D1Bitmap* pBitmapSource, FLOAT Left, FLOAT Top, FLOAT fAngle)
{
	if (pContext && pBitmapSource)
	{
		D2D1_SIZE_F SIZE = pBitmapSource->GetSize();

		// 创建位图
		pContext->Flush();

		// 创建仿射变换效果
		Microsoft::WRL::ComPtr<ID2D1Effect> affineTransformEffect;
		pContext->CreateEffect(CLSID_D2D12DAffineTransform, &affineTransformEffect);

		HHBUI::ExMatrix3x2 matrix;
		matrix.Translate((FLOAT)SIZE.width / 2, (FLOAT)SIZE.height / 2);
		matrix.Rotate(fAngle, SIZE.width, SIZE.height);
		matrix.Translate(-(FLOAT)SIZE.width / 2, -(FLOAT)SIZE.height / 2);

		// 设置效果参数
		affineTransformEffect->SetInput(D2D1_BITMAPSOURCE_PROP_WIC_BITMAP_SOURCE, pBitmapSource);
		affineTransformEffect->SetValue(D2D1_2DAFFINETRANSFORM_PROP_TRANSFORM_MATRIX, matrix);

		// 绘制图像
		D2D1_POINT_2F targetOffset = { Left, Top };
		pContext->DrawImage(affineTransformEffect.Get(), &targetOffset);

		matrix.Reset();
	}
}

void HHBUI::UIDrawContext::DrawBrushRotate(ID2D1DeviceContext* pContext, UIbrush* pBrushSource, FLOAT Left, FLOAT Top, FLOAT Width, FLOAT Height, FLOAT fAngle)
{
	if (pContext && pBrushSource)
	{
		HHBUI::ExMatrix3x2 matrix;
		matrix.Translate(Width / 2, Height / 2);
		matrix.Rotate(fAngle, Width, Height);
		matrix.Translate(-Width / 2, -Height / 2);
		((UIBrush*)pBrushSource)->SetTransForm(matrix);

		pContext->FillRectangle(D2D1::RectF(Left, Top, Left + Width, Top + Height), (ID2D1Brush*)((UIBrush*)pBrushSource)->GetContext());
		matrix.Reset();
		((UIBrush*)pBrushSource)->SetTransForm(matrix);
	}
}

D2D1_POINT_2F HHBUI::UIDrawContext::RotatePoint(D2D1_POINT_2F point, D2D1_POINT_2F center, FLOAT angle)
{
	FLOAT radian = angle * 3.14159265358979323846f / 180.0f;
	FLOAT cosTheta = cos(radian);
	FLOAT sinTheta = sin(radian);

	FLOAT x = point.x - center.x;
	FLOAT y = point.y - center.y;

	FLOAT newX = x * cosTheta - y * sinTheta;
	FLOAT newY = x * sinTheta + y * cosTheta;

	D2D1_POINT_2F rotatedPoint = { newX + center.x, newY + center.y };
	return rotatedPoint;
}
void HHBUI::UIDrawContext::DrawRotatedLines(ID2D1DeviceContext* pDeviceContext, ID2D1Brush* brush, FLOAT uLeft, FLOAT uTop, FLOAT uWidth, FLOAT uHeight, FLOAT angle)
{
	D2D1_POINT_2F center = { uWidth / 2.0f + uLeft, uHeight / 2.0f + uTop }; 
	auto drawRotatedLine = [pDeviceContext, brush, center, angle](FLOAT Left, FLOAT Top, FLOAT x1, FLOAT y1, FLOAT x2, FLOAT y2) {
		D2D1_POINT_2F point1 = { x1 + Left, y1 + Top };
		D2D1_POINT_2F point2 = { x2 + Left, y2 + Top };

		D2D1_POINT_2F rotatedPoint1 = RotatePoint(point1, center, angle);
		D2D1_POINT_2F rotatedPoint2 = RotatePoint(point2, center, angle);

		pDeviceContext->DrawLine(rotatedPoint1, rotatedPoint2, brush, UIEngine::ScaleValue(1.0f));
	};

	drawRotatedLine(uLeft, uTop, 12 + 0.5f, 15 + 0.5f, uWidth - 18 + 0.5f, uHeight - 15 + 0.5f);
	drawRotatedLine(uLeft, uTop, uWidth - 13 + 0.5f, 15 + 0.5f, uWidth - 18 + 0.5f, uHeight - 15 + 0.5f);
}
BOOL HHBUI::UIDrawContext::EnumRenderDevice(UIDevice* devices, int& numDevices)
{
	Microsoft::WRL::ComPtr<IDXGIFactory2> pFactory;
	HRESULT hr = CreateDXGIFactory(IID_IDXGIFactory2, reinterpret_cast<void**>(pFactory.GetAddressOf()));
	if (FAILED(hr))
	{
		std::wstring errorMsg = L"CreateDXGIFactory FAILED! errcode: 0x" + std::to_wstring(hr);
		ExOutError(_M_DBG_INFO_(M_INFO_DBG), errorMsg);
		return FALSE;
	}

	// 排除的无效设备描述名
	const WCHAR* invalidDevices[] = {
		L"Microsoft Basic Render Driver",
		L"Another Invalid Adapter Name"
	};

	numDevices = 0;
	UINT i = 0;
	Microsoft::WRL::ComPtr<IDXGIAdapter1> pAdapter;

	// 遍历适配器
	while (pFactory->EnumAdapters1(i, pAdapter.GetAddressOf()) != DXGI_ERROR_NOT_FOUND)
	{
		DXGI_ADAPTER_DESC desc;
		hr = pAdapter->GetDesc(&desc);
		if (FAILED(hr))
		{
			++i;
			continue;
		}

		// 如果是无效设备，跳过
		bool isInvalid = false;
		for (const auto& invalidDevice : invalidDevices)
		{
			if (wcscmp(desc.Description, invalidDevice) == 0)
			{
				isInvalid = true;
				break;
			}
		}

		if (isInvalid)
		{
			++i;
			continue;
		}

		devices[numDevices].deviceName = StrDupW(desc.Description); 
		devices[numDevices].deviceIndex = i;
		++numDevices;

		++i;
	}

	return TRUE;
}

