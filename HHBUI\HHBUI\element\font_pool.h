﻿/**
 * @file font_pool.h
 * @brief 字体管理器
 */
#pragma once
#include <common/atom.h>
#include <common/res_pool.h>
#include <common/data.h>
#include <dwrite.h>
#include <common/unknown_impl.hpp>
#include <common/singleton.hpp>

namespace HHBUI
{
	struct ExFontContextD2D
	{
		EXATOM atom;
		LOGFONT* LogFont;
		IDWriteTextFormat* font;
	};

	struct ExFontFileContextD2D
	{
		EXATOM atom;
		ExData data;
		IDWriteFontCollection* collection;
	};

	class ExFontPoolD2D : public ExLazySingleton<ExFontPoolD2D>
	{
	public:
		ExFontPoolD2D();
		~ExFontPoolD2D();

		ExFontFileContextD2D* LoadFontFile(LPVOID data, size_t size, LPCTSTR lpwzFontFace) MAYTHROW;
		void UnLoadFontFile(EXATOM atom) MAYTHROW;

		ExFontContextD2D* CreateFontInfo(LOGFONTW* info) MAYTHROW;
		bool DestroyFont(EXATOM atom) MAYTHROW;

	private:
		static HRESULT CALLBACK OnInitFontFileContext(IExResPool* pool, EXATOM key,
			const void* data, WPARAM wparam, LPARAM lparam, DWORD flags, void* r_res);
		static HRESULT CALLBACK OnFreeFontFileContext(IExResPool* pool, EXATOM key,
			DWORD flags, void* res);

		static HRESULT CALLBACK OnInitFontContext(IExResPool* pool, EXATOM key,
			const void* data, WPARAM wparam, LPARAM lparam, DWORD flags, void* r_res);
		static HRESULT CALLBACK OnFreeFontContext(IExResPool* pool, EXATOM key,
			DWORD flags, void* res);

		IExResPool* m_font_pool;
		IExResPool* m_file_pool;
		wchar_t m_local_name[130]{};
	};

}

