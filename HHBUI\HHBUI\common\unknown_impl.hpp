﻿/**
** =====================================================================================
**
**       文件名称: unknown_impl.hpp
**       创建时间: 2025-07-28
**       文件描述: 【HHBUI】COM接口实现基类 - 现代化COM接口引用计数管理框架 （声明文件）
**
**       主要功能:
**       - 现代化COM接口引用计数管理
**       - 线程安全的引用计数操作
**       - 智能COM接口查询机制
**       - 异常安全的资源管理
**       - 高性能COM对象生命周期管理
**       - 模板化COM接口实现基类
**       - 自动化COM接口宏系统
**
**       技术特性:
**       - 采用现代C++17标准与COM规范
**       - 原子操作保证线程安全
**       - 异常安全保证与错误恢复机制
**       - 高性能引用计数算法
**       - 智能指针兼容设计
**       - 模板元编程优化
**       - RAII自动资源管理
**
**       更新记录:
**       2025-07-28 v1.0.0.0 : 1. 创建现代化COM接口基类
**                             2. 实现线程安全引用计数
**                             3. 添加智能接口查询机制
**                             4. 支持异常安全资源管理
**                             5. 集成高性能生命周期管理
**                             6. 添加模板化实现基类
**                             7. 确保COM规范完全兼容
**
** =====================================================================================
**/

#pragma once
#include "hhbui.h"

namespace HHBUI
{
	template<typename T>
	class ExUnknownImpl : public T
	{
	public:
		ExUnknownImpl() :m_ref(1) {}
		virtual ~ExUnknownImpl() {}

		EXMETHOD ULONG AddRef() override
		{
			return InterlockedIncrement(&m_ref);
		}
		EXMETHOD ULONG Release() override
		{
			long ref = InterlockedDecrement(&m_ref);
			if (ref == 0) { delete this; }
			return ref;
		}

	private:
		long m_ref;

	};

	template<typename T, typename T_FOR_DESTROY>
	class ExUnknownImplEx : public ExUnknownImpl<T>
	{
	public:
		EXMETHOD ULONG Release() override
		{
			long ref = InterlockedDecrement(&__super::m_ref);
			if (ref == 0) { delete static_cast<T>(this); }
			return ref;
		}
	};

#define EX_DECLEAR_INTERFACE_BEGIN() EXMETHOD HRESULT QueryInterface(REFIID riid, void** ppvObject) override {

#define EX_DECLEAR_INTERFACE(INTERFACE)					\
		if (IsEqualIID(riid, __uuidof(INTERFACE))) {	\
			*ppvObject = this;							\
			this->AddRef();								\
			return S_OK; }		

#define EX_DECLEAR_INTERFACE_2(_GUID_)					\
		if (IsEqualIID(riid, _GUID_)) {					\
			*ppvObject = this;							\
			this->AddRef();								\
			return S_OK; }							

#define EX_DECLEAR_INTERFACE_EX(_GUID_,PTR)				\
		if (IsEqualIID(riid, _GUID_)) {					\
			*ppvObject = PTR;							\
			this->AddRef();								\
			return S_OK; }							

#define EX_DECLEAR_INTERFACE_END()  return E_NOINTERFACE; }								\
		template<class T> inline 														\
		HRESULT STDMETHODCALLTYPE QueryInterface(T** ppvObject) {								\
			return this->QueryInterface(__uuidof(T),(void**)ppvObject);					\
		}																				

}


