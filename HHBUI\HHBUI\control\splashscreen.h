﻿#pragma once
#include <element/wnd.h>

namespace HHBUI
{
    // 启动窗口特效类
    class TOAPI UISplashScreen
    {
    public:
        UISplashScreen();
        ~UISplashScreen();

        // 显示启动窗口
        // lpText: 显示的文本
        // dwBackColor: 背景颜色（支持透明度）
        // dwTextColor: 文本颜色
        // dwFontSize: 字体大小
        // dwTimeout: 自动关闭超时时间（毫秒）
        // fBlur: 模糊效果系数
        BOOL Show(LPCWSTR lpText = nullptr,
            UIColor dwBackColor = UIColor(30, 30, 30, 128),
            UIColor dwTextColor = UIColor(255, 255, 255, 255),
            INT dwFontSize = 28,
            DWORD dwTimeout = 5000,
            FLOAT fBlur = 0.0f);

        // 隐藏启动窗口
        void Hide();

        // 更新显示文本
        void UpdateText(LPCWSTR lpText);

        // 设置进度条进度（0-100）
        void SetProgress(INT nProgress);

        // 设置是否显示进度条
        void ShowProgress(BOOL bShow);

        // 设置背景图片
        BOOL SetBackgImage(LPCWSTR lpImagePath);
        BOOL SetBackgImage(LPVOID pImageData, size_t nSize);
        BOOL SetBackgImage(UIImage* pImage);

        // 设置窗口大小
        void SetSize(INT nWidth, INT nHeight);

        // 设置窗口位置
        void SetPosition(INT x, INT y);

        // 设置窗口圆角
        void SetRadius(INT nRadius);

    private:
        UIWnd* m_pWnd;         // 窗口对象
        UIStatic* m_pText;     // 文本控件
        UIProgress* m_pProgress; // 进度条控件
        std::wstring m_strText; // 显示文本
        UIColor m_dwBackColor;  // 背景颜色
        UIColor m_dwTextColor;  // 文本颜色
        INT m_dwFontSize;       // 字体大小
        INT m_nProgress;        // 进度值
        BOOL m_bShowProgress;   // 是否显示进度条
        UINT_PTR m_dwTimerId;   // 定时器ID
        INT m_nWidth;           // 窗口宽度
        INT m_nHeight;          // 窗口高度
        INT m_nRadius;          // 窗口圆角
        UIImage* m_pBackImage;  // 背景图片

        // 窗口过程回调函数
        static LRESULT CALLBACK OnSplashWndProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);

        // 重绘窗口内容
        void RedrawWindow();
    };
}
