﻿#pragma once
/*百变按钮 by:来自群管理员[E.globulus]贡献*/
namespace HHBUI
{
	struct info_button_config
	{
		UIColor crText[3];
		UIColor crBkg[3];
		UIColor crBorder[3];
		UIImage *imgBkg[3];
	};
	enum info_button_style {
		normal,		//默认样式（白色+蓝色）
		customize,	//自定义（自定义颜色仅此样式生效）
		primary,	//重点样式（蓝色）
		success,	//成功样式（绿色）
		info,		//信息样式（灰色）
		warning,	//警告样式（黄色）
		danger,		//危险样式（红色）
		nostyle     //无样式 取消背景填充
	};
	enum info_button_type {
		fill,		//默认类型（背景颜色填充）
		plain,		//朴素类型（淡色背景+边框）
		circle,		//图标类型（此类型仅显示图标）
		nobkg       //无背景填充（此模式可用于基类自带背景颜色组合）
	};
	class TOAPI UIButton : public UIControl
	{
	public:
		UIButton(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
		//设置按钮类型及样式（自定义颜色需先设置style=customize，再设置颜色）
		void SetStyle(info_button_type type = fill, info_button_style style = normal);
		//设置文本颜色
		void SetCrText(UIColor normal, UIColor hover, UIColor down);
		//设置边框颜色
		void SetCrBorder(UIColor normal, UIColor hover, UIColor down);
		//设置背景颜色
		void SetCrBkg(UIColor normal, UIColor hover, UIColor down);
		//设置背景图像
		void SetImgBkg(UIImage *normal, UIImage *hover, UIImage *down);
		//设置圆角度
		void SetRadius(FLOAT fRadius);
		 /*
         * @brief 设置按钮图标
         * @param  fIconPosition   图标方向 0(默认)靠左，1靠上，2靠下 [图标类型无效]
         */
		void SetIcon(UIImage* fIcon, INT fIconPosition = 0);

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		struct button_s
		{
			UIBrush* hBrush = nullptr;
			UINT iconW = 0, iconH = 0, rad = 8;
			INT IconPosition = 0;
			UIImage *Icon = nullptr;
			info_button_config config{};
			info_button_type type = fill;
			info_button_style style = normal;
		}p_data;

	};
}