# Visual Studio 生成了具有 C++ 设置的 .editorconfig 文件。

# 表明该文件是 EditorConfig 配置文件，顶级配置开始
root = true

[*.{c++,cc,cpp,cppm,cxx,h,h++,hh,hpp,hxx,inl,ipp,ixx,tlh,tli}]

# Visual C++ 代码样式设置

cpp_generate_documentation_comments = xml


# 匹配所有文件（基础规则，后续可针对特定文件类型覆盖）
[*]

# 设置文件编码为UTF-8（无BOM） 
# charset = utf-8

# 编码格式：推荐 UTF-8 BOM（VS 对中文兼容性更好）
charset = utf-8-bom

# 换行符：Windows 推荐 crlf，跨平台可选 lf
end_of_line = crlf

# 行尾是否保留空格：禁止（自动修剪）
trim_trailing_whitespace = true

# 文件末尾是否保留空行：是（多数项目规范）
insert_final_newline = true