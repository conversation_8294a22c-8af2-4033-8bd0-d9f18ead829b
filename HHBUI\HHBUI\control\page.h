﻿#pragma once
namespace HHBUI
{
    enum info_page_config
    {
        eos_paging = 4,                           //页码
        eos_paging_pnarrow = 8,                   //上页下页风格_箭头
        eos_paging_pnatext = 16,                   //上页下页风格_文本
        eos_paging_showtips = 32,                  //显示提示文本
        eos_paging_showjump = 64,                 //显示跳转

        WMM_PAGE_SELCHANGER = 10200,               //事件现行选中 lParam为当前页码，返回1可拦截
    };
    enum info_page_style {
        e_page_left = 0,
        e_page_center,
        e_page_right,
    };
	class TOAPI UIPage : public UIControl
	{
	public:
		UIPage() = default;
		UIPage(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
	
        //设置样式
        void SetStyle(info_page_style style = e_page_left);
         /*
         * @brief 设置总数
         * @param  fCount  设置总页
         * @param  fShowNum  每页显示数量
         */
        void SetCount(INT fCount, INT fShowNum);
        //设置当前页
        void SetCurrent(INT fCurrent);
        //获取当前页
        INT GetCurrent();
        //设置按钮间隔
        void SetInterval(INT fInterval);
        //设置按钮标题
        void SetItemText(LPCWSTR prev, LPCWSTR next = 0, LPCWSTR jump = 0);
        //设置背景颜色
        void SetCrbkg(UIColor normal, UIColor hover = NULL, UIColor checked = NULL, UIColor ban = NULL);
        //设置边框颜色
        void SetCrBorder(UIColor normal, UIColor hover = NULL, UIColor checked = NULL, UIColor ban = NULL);
        //设置文本颜色
        void SetCrText(UIColor normal, UIColor hover = NULL, UIColor checked = NULL, UIColor ban = NULL);
        //设置省略号颜色
        void SetCrEllipsis(UIColor normal, UIColor hover = NULL);
        //设置按钮圆角度
        void SetBtnRound(INT fRound);
    
    protected:
        EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
        EXMETHOD void OnPaintProc(ps_context ps) override;
        void ResetPageSize();
        struct Page_s
        {
            info_page_style mod = e_page_left;
            // 背景颜色
            UIColor crbkgnormal = {};
            UIColor crbkghover = {};
            UIColor crbkgchecked = {};
            UIColor crbkgban = {};

            // 边框颜色
            UIColor crbordernormal = {};
            UIColor crborderhover = {};
            UIColor crborderchecked = {};
            UIColor crborderban = {};

            // 文本颜色
            UIColor crtextnormal = {};
            UIColor crtexthover = {};
            UIColor crtextchecked = {};
            UIColor crtextban = {};

            // 省略号颜色
            UIColor crcromitnormal = {};
            UIColor crcromithover = {};

            // 数量和页码
            int32_t count = 0;     // 总页数
            int32_t shownum = 0;   // 每页显示数量
            int32_t pagenum = 0;   // 页面数量
            int32_t current = 0;   // 当前页

            // 编辑框和按钮相关
            UIEdit* edit = nullptr;
            int32_t left_edit = 0;     // 编辑框左边位置
            int32_t pwidth = 0;        // 按钮宽度
            int32_t pheight = 0;       // 按钮高度
            int32_t hotid = 0;         // 热点按钮id
            int32_t interval = 0;      // 间隔
            int32_t Round = 4;
            int32_t totalWidth = 0;

            // 按钮相关
            int32_t* lpbuttonleft = nullptr;    // 按钮左边位置数组
            int32_t* lpbuttonwidth = nullptr;   // 按钮宽度数组
            int32_t* lpbuttonpage = nullptr;    // 按钮页码数组

            // 页面按钮图片
            UIImage* prev_normal = nullptr;     // 上一页_正常
            UIImage* prev_light = nullptr;      // 上一页_点燃
            UIImage* prev_ban = nullptr;        // 上一页_禁止
            UIImage* next_normal = nullptr;     // 下一页_正常
            UIImage* next_light = nullptr;      // 下一页_点燃
            UIImage* next_ban = nullptr;        // 下一页_禁止
            UIImage* topfive_normal = nullptr;  // 前五页_正常
            UIImage* topfive_light = nullptr;   // 前五页_点燃
            UIImage* lastfive_normal = nullptr; // 后五页_正常
            UIImage* lastfive_light = nullptr;  // 后五页_点燃

            // 页面按钮文本
            LPCWSTR itemtext_prev = 0;    // 上一页标题
            LPCWSTR itemtext_next = 0;    // 下一页标题
            LPCWSTR itemtext_jump = 0;    // 跳转标题
        }p_data;
	};
}
