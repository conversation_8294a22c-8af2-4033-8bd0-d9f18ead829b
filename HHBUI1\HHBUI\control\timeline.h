﻿#pragma once
namespace HHBUI
{
	class TOAPI UITimeLine : public UIControl
	{
		public:
			UITimeLine() = default;
			UITimeLine(UIBase* hParent, INT x, INT y, INT w, INT h, INT nID = 0);

			/**
			 * @brief 添加节点项
			 * @param text 节点内容
			 * @param time 时间戳
			 * @param title 标题
			 * @param index 添加位置（负数为末尾）
			 * @return 添加成功返回节点索引，失败返回-1
			 */
			INT AddItem(LPCWSTR text, LPCWSTR time, LPCWSTR title = nullptr, INT index = -1);
			//设置节点项内容
			BOOL SetItemText(INT index, LPCWSTR text);
			//设置节点项时间戳
            BOOL SetItemTime(INT index, LPCWSTR time);
			//设置节点项标题
            BOOL SetItemTitle(INT index, LPCWSTR title);
			//设置节点颜色
            BOOL SetItemNodeColor(INT index, UIColor color);
			//设置节点项内容颜色
			BOOL SetItemTextColor(INT index, UIColor color);
			//设置节点项时间颜色
			BOOL SetItemTimeColor(INT index, UIColor color);
			//设置节点项标题颜色
            BOOL SetItemTitleColor(INT index, UIColor color);
			//删除节点项
			BOOL DelItem(INT index);
			//清空节点项
            void ClearItem();

			//获取节点项内容
			LPCWSTR GetItemText(INT index);
			//获取节点项时间戳
			LPCWSTR GetItemTime(INT index);
			//获取节点项标题
			LPCWSTR GetItemTitle(INT index);
			//获取节点项数量
			INT GetItemCount();

			//设置节点项内容字体
			void SetItemTextFont(LPCWSTR name, INT size = 12, INT Style = 0);
			//设置节点项时间戳字体
			void SetItemTimeFont(LPCWSTR name, INT size = 11, INT Style = 0);
			//设置节点项标题字体
			void SetItemTitleFont(LPCWSTR name, INT size = 12, INT Style = 1);

			//设置是否显示标题
			void SetShowTitle(BOOL fShow);
			//设置时间戳显示位置（true=顶部，false=底部）
			void SetShowTimeTop(BOOL fTop);
			//设置节点大小（半径）
			void SetNodeSize(float size = 2.f);

			//更新
			void Update();

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;

		void calu_data();

		struct item_info
		{
			LPCWSTR text = nullptr, time = nullptr, title = nullptr;

			UIColor crNode = UIColor(179, 179, 181);
			UIColor crText = UIColor(29, 29, 31);
			UIColor crTime = UIColor(149, 149, 151);
            UIColor crTitle = UIColor(29, 29, 31);

			ExRectF rc{  };	//节点项矩形
			ExRectF trc{  };	//内容矩形
			ExRectF tic{  };	//时间戳矩形
			ExRectF tlc{  };	//标题矩形
			ExPointF npt{  };	//节点坐标
		};

		struct data_info
		{
			int w = 0, h = 0, vh = 0;
			float ns = 5.f;					//节点大小

			bool fShowT = false;			//是否显示标题
			bool ftTop = true;				//时间戳显示位置（true=顶部，false=底部）

			std::vector<item_info> list;

			UIBrush* brGen = nullptr;
			UIFont* ftTime = nullptr;
			UIFont* ftTitle = nullptr;
		}p_data;
	};
}
