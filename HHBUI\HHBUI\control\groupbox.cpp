﻿#include "pch.h"
#include "groupbox.h"

HHBUI::UIGroupbox::UIGroupbox(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-groupbox", lpszName, dwStyle, dwStyleEx, nID, dwTextFormat);
}

void HHBUI::UIGroupbox::SetCrBorder(UIColor normal)
{
	p_data.clr[0] = normal;
}

void HHBUI::UIGroupbox::SetCrBkg(UIColor normal)
{
	p_data.clr[1] = normal;
}

void HHBUI::UIGroupbox::SetStrokeWidth(FLOAT fWidth)
{
	p_data.strokewidth = fWidth;
}

void HHBUI::UIGroupbox::SetRadius(FLOAT radius)
{
	p_data.radius = radius;
}

void HHBUI::UIGroupbox::SetTextOffset(FLOAT Offset)
{
	p_data.textoffset = Offset;
}

LRESULT HHBUI::UIGroupbox::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	return S_OK;
}

void HHBUI::UIGroupbox::OnPaintProc(ps_context ps)
{
	auto pstrTitle = GetText(); RECT rcText{};
	if (pstrTitle)
	{
		if (p_data.textoffset == 0)
			p_data.textoffset = 10;
		FLOAT retWidth, retHeight;
		ps.hCanvas->CalcTextSize(ps.hFont, pstrTitle, DT_LEFT | DT_TOP | DT_SINGLELINE, ps.uWidth, ps.uHeight, &retWidth, &retHeight);
		rcText.right = retWidth;
		rcText.bottom = retHeight;

	}
	auto br = UIBrush(p_data.clr[0]);
	ps.rcPaint.right -= p_data.strokewidth;
	ps.rcPaint.bottom -= p_data.strokewidth;
	ps.rcPaint.top += p_data.strokewidth;
	ps.rcPaint.left += p_data.strokewidth;

	auto pfill = UIPath();
	pfill.BeginPath();
	pfill.StartFigure(ps.rcPaint.left + p_data.textoffset + rcText.right + p_data.radius, ps.rcPaint.top + rcText.bottom / 2);
	pfill.Arc(ps.rcPaint.right - p_data.radius, ps.rcPaint.top + rcText.bottom / 2, ps.rcPaint.right, ps.rcPaint.top + rcText.bottom / 2 + p_data.radius, p_data.radius, p_data.radius, TRUE);
	pfill.Arc(ps.rcPaint.right, ps.rcPaint.bottom - p_data.radius, ps.rcPaint.right - p_data.radius, ps.rcPaint.bottom, p_data.radius, p_data.radius, TRUE);
	pfill.Arc(ps.rcPaint.left + p_data.radius, ps.rcPaint.bottom, ps.rcPaint.left, ps.rcPaint.bottom - p_data.radius, p_data.radius, p_data.radius, TRUE);
	pfill.Arc(ps.rcPaint.left, ps.rcPaint.top + rcText.bottom / 2 + p_data.radius, ps.rcPaint.left + p_data.radius, ps.rcPaint.top + rcText.bottom / 2, p_data.radius, p_data.radius, TRUE);
	pfill.LineTo(ps.rcPaint.left + p_data.radius, ps.rcPaint.top + rcText.bottom / 2, ps.rcText.left + p_data.radius + p_data.textoffset - 5, ps.rcPaint.top + rcText.bottom / 2);


	pfill.FinishFigure();
	pfill.EndPath();

	if (!p_data.clr[0].empty())
		ps.hCanvas->DrawPath(&br, &pfill, p_data.strokewidth);
	if (!p_data.clr[1].empty())
	{
		br.SetColor(p_data.clr[1]);
		ps.hCanvas->FillPath(&br, &pfill);
	}

	if (pstrTitle)
	{
		UIColor yColor;
		GetColor(color_text_normal, yColor);

		ps.hCanvas->DrawTextByColor(ps.hFont, pstrTitle, DT_LEFT | DT_TOP | DT_SINGLELINE, ps.rcText.left + p_data.radius + p_data.textoffset,
			ps.rcText.top,
			ps.rcText.right,
			ps.rcText.bottom, yColor);
	}
}

