﻿#include "pch.h"
#include "slider.h"

using namespace std;

HHBUI::UISlider::UISlider(UIBase *hParent, INT x, INT y, INT width, INT height, LPCTSTR title, INT nID, INT dwStyle, INT dwStyleEx, INT textFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-slider", title, dwStyle, dwStyleEx, nID, textFormat);
	p_data.bRad = min(width, height) / 2;
	p_data.brush = new UIBrush();
	SetFontFromFamily(NULL, 12);
}

void HHBUI::UISlider::SetBarSize(INT Size)
{
	if (Size >= 5)
		p_data.BarSize = Size;
}

void HHBUI::UISlider::SetBarRadius(BOOL IsRadius)
{
	p_data.bRad = IsRadius;
}


void HHBUI::UISlider::SetType(slider_type type)
{
	if (p_data.type != type) {
        p_data.type = type;
	}
}

void HHBUI::UISlider::SetPoint(slider_point point)
{
	if (p_data.point != point) {
		p_data.point = point;

	}
}

void HHBUI::UISlider::SetSliderColor(UIColor nor, UIColor fill)
{
	if (!nor.empty()) p_data.clr[0] = nor;
    if (!fill.empty()) p_data.clr[1] = fill;
}

void HHBUI::UISlider::SetValue(float lVal)
{
	p_data.lVal = lVal;
}

void HHBUI::UISlider::SetRange(float lpMin, float lpMax)
{
	if (lpMin < p_data.max)
		p_data.min = lpMin;
	if (lpMax > p_data.min)
		p_data.max = lpMax;
}

float HHBUI::UISlider::GetValue()
{
    return p_data.lVal;
}

float HHBUI::UISlider::GetMin()
{
	return p_data.min;
}

float HHBUI::UISlider::GetMax()
{
	return p_data.max;
}


void HHBUI::UISlider::IsShwoText(BOOL isshow)
{
	p_data.bshowText = isshow;
}

LRESULT HHBUI::UISlider::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	switch (uMsg)
	{
	case WM_MOUSEMOVE:
		OnMouseMove(hWnd, lParam);
		break;

	case WM_LBUTTONDOWN:
		OnLButtonDown(hWnd, lParam);
		break;

	case WM_LBUTTONUP:
		OnLButtonUp();
		break;

	case WM_MOUSEHOVER:
		SetState(state_hover, FALSE);
		Redraw();
		break;

	case WM_MOUSELEAVE:
		SetState(state_hover, TRUE);
		Redraw();
		break;

	case WM_DESTROY:
		delete p_data.brush;
		break;
	case WM_SIZE:
		int pwidth = GET_X_LPARAM(lParam);
		int pheight = GET_Y_LPARAM(lParam);
		Redraw();
		break;
	}

	return S_OK;
}

void HHBUI::UISlider::OnMouseMove(HWND hWnd, LPARAM lParam)
{
	if (p_data.bLDown) {
		HitTest(lParam);
	}
}

void HHBUI::UISlider::OnLButtonDown(HWND hWnd, LPARAM lParam)
{
	p_data.bLDown = TRUE;
	HitTest(lParam);
}

void HHBUI::UISlider::OnLButtonUp()
{
	p_data.bLDown = FALSE;
}
void HHBUI::UISlider::OnGetRect(ExRectF fChannel, ExRectF& rc, float tw)
{
	FLOAT value = (FLOAT)(p_data.max - p_data.min);

	if (value == 0.0f) {
		value = 0.0f; 
	}
	else {
		value = (FLOAT)((p_data.lVal - p_data.min) / value); // 计算百分比
	}

	value = max(0.0f, min(1.0f, value));

	INT fwidth = fChannel.right - fChannel.left;
	INT fheight = fChannel.bottom - fChannel.top;
	INT BarSize = p_data.BarSize;

	if (p_data.point == UISlider::top || p_data.point == UISlider::bottom)
	{
		if (p_data.point == UISlider::bottom)
		{
			rc.left = fwidth / 2.f - BarSize;
			rc.top = (fheight - BarSize - tw) + BarSize - (fheight - BarSize - tw) * value;
			rc.right = fwidth / 2.f + BarSize;
			rc.bottom = (fheight - BarSize - tw) - (fheight - BarSize - tw) * value;
		}
		else
		{
			rc.left = fwidth / 2.f - BarSize;
			rc.top = (fheight - BarSize - tw) * value;
			rc.right = fwidth / 2.f + BarSize;
			rc.bottom = (fheight - BarSize - tw) * value + BarSize;
		}
	}
	else
	{
		if (p_data.point == UISlider::right)
		{
			rc.left = (fwidth - BarSize - tw) + BarSize - (fwidth - BarSize - tw) * value;
			rc.top = fheight / 2.f - BarSize;
			rc.right = (fwidth - BarSize - tw) - (fwidth - BarSize - tw) * value;
			rc.bottom = fheight / 2.f + BarSize;
		}
		else
		{
			rc.left = (fwidth - BarSize - tw) * value;
			rc.top = fheight / 2.f - BarSize;
			rc.right = (fwidth - BarSize - tw) * value + BarSize;
			rc.bottom = fheight / 2.f + BarSize;
		}
	}
}

void HHBUI::UISlider::HitTest(LPARAM lParam)
{
	FLOAT value = NULL;
	ExRectF rc{};
	GetRect(rc);
	rc.right -= rc.left;
	rc.bottom -= rc.top;
	INT Offset = p_data.bshowText ? p_data.tw - p_data.BarSize : 0;
	if (p_data.type == slidBar)
		Offset = 0;

	if (p_data.point == UISlider::top || p_data.point == UISlider::bottom)
	{
		if (p_data.point == UISlider::bottom)
		{
			value = (UIEngine::ScaleValue((FLOAT)rc.bottom - p_data.BarSize) - Offset - (short)HIWORD(lParam)) / UIEngine::ScaleValue((FLOAT)rc.bottom - p_data.BarSize * 2);
		}
		else
		{
			value = ((short)HIWORD(lParam) - UIEngine::ScaleValue(p_data.BarSize) + Offset) / UIEngine::ScaleValue((FLOAT)rc.bottom - p_data.BarSize * 2);
		}
	}
	else
	{
		if (p_data.point == UISlider::right)
		{
			value = (UIEngine::ScaleValue((FLOAT)rc.right - p_data.BarSize) - Offset - (short)LOWORD(lParam)) / UIEngine::ScaleValue((FLOAT)rc.right - p_data.BarSize * 2);
		}
		else
		{
			value = ((short)LOWORD(lParam) - UIEngine::ScaleValue(p_data.BarSize) + Offset) / UIEngine::ScaleValue((FLOAT)rc.right - p_data.BarSize * 2);
		}
	}
	value = p_data.min + value * (p_data.max - p_data.min); // ' 得到百分比

	auto tmp = p_data.min;
	if (value < tmp)
	{
		value = tmp;
	}
	tmp = p_data.max;
	if (value > tmp)
	{
		value = tmp;
	}
	if (value != p_data.lVal)
	{
		p_data.lVal = value;
		DispatchNotify(TBM_GETPOS, 0, value);
		Redraw();
	}
}


void HHBUI::UISlider::OnPaintProc(ps_context ps)
{
	INT bsr = min(ps.uHeight, ps.uWidth) / 2;
	UIColor crTitle;
	GetColor(color_text_normal, crTitle);
	float tw = 0, th = 0;
	if (p_data.bshowText && p_data.type != slidBar)
	{
		auto tit = vstring::replace_all(GetText(), L"{{value}}", std::to_wstring((int)(p_data.lVal)));
		ps.hCanvas->CalcTextSize(ps.hFont, tit.c_str(), TextFormat::Middle, ps.uWidth, ps.uHeight, &tw, &th);
		tw += 5;
		th += 5;
	}
	ExRectF RC = {};

	if (p_data.type == linear || p_data.type == stroke || p_data.type == slidBar) {
		if (p_data.point == left || p_data.point == right) {
			OnGetRect(m_data.Frame, RC, tw);
			p_data.tw = tw;
			p_data.brush->SetColor(p_data.clr[0]);
			auto Offset = p_data.BarSize / 2.f;
			auto BarSize = p_data.BarSize;
			auto y = (ps.uHeight - BarSize) / 2.f;
			auto atop = (RC.left + RC.right) / 2.f;
			if (p_data.type != slidBar)
			{
				ps.hCanvas->DrawLine(p_data.brush, Offset, ps.uHeight / 2.f, ps.uWidth - Offset - tw, ps.uHeight / 2.f, Offset, D2D1_DASH_STYLE_SOLID, p_data.bRad);
				p_data.brush->SetColor(p_data.clr[1]);
				if (p_data.lVal == 0.f)
					Offset = 0.f;
				ps.hCanvas->DrawLine(p_data.brush, atop, ps.uHeight / 2.f, (p_data.point == right ? ps.uWidth - Offset - tw : Offset), ps.uHeight / 2.f, Offset, D2D1_DASH_STYLE_SOLID, p_data.bRad);
			}
			else
			{
				Offset = p_data.BarSize;
				ps.hCanvas->DrawLine(p_data.brush, Offset / 2.f, ps.uHeight / 2.f, ps.uWidth - Offset / 2.f, ps.uHeight / 2.f, Offset, D2D1_DASH_STYLE_SOLID, p_data.bRad);
				p_data.brush->SetColor(p_data.clr[1]);
			}

			if (p_data.type == linear || p_data.type == slidBar)
			{
				if (p_data.point == right)
				{
					if (p_data.bRad == 0)
						ps.hCanvas->FillRect(p_data.brush, RC.right, y, RC.right + BarSize, y + BarSize);
					else
						ps.hCanvas->FillEllipse(p_data.brush, RC.right, y, RC.right + BarSize, y + BarSize);
				}
				else
				{
					if (p_data.bRad == 0)
						ps.hCanvas->FillRect(p_data.brush, RC.left, y, RC.left + BarSize, y + BarSize);
					else
						ps.hCanvas->FillEllipse(p_data.brush, RC.left, y, RC.left + BarSize, y + BarSize);
				}
			}
			if (p_data.bshowText)
			{
				if (p_data.type == slidBar)
				{
					auto tit = vstring::replace_all(GetText(), L"{{value}}", vstring::format(L"%.2f", p_data.lVal));
					ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Center | TextFormat::Middle, 0, 0, ps.uWidth, ps.uHeight, crTitle);
				}
				else
				{
					auto tit = vstring::replace_all(GetText(), L"{{value}}", std::to_wstring((int)(p_data.lVal)));
					ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Center | TextFormat::Middle, ps.uWidth, 0, ps.uWidth - tw, ps.uHeight, crTitle);
				}
			}
		}
		else if (p_data.point == top || p_data.point == bottom) {
			OnGetRect(m_data.Frame, RC, th);
			p_data.tw = th;
			p_data.brush->SetColor(p_data.clr[0]);
			auto Offset = p_data.BarSize / 2.f;
			auto BarSize = p_data.BarSize;
			auto x = (ps.uWidth - BarSize) / 2.f;
			auto atop = (RC.top + RC.bottom) / 2.f;

			if (p_data.type != slidBar)
			{
				ps.hCanvas->DrawLine(p_data.brush, ps.uWidth / 2.f, Offset, ps.uWidth / 2.f, ps.uHeight - Offset - th, Offset, D2D1_DASH_STYLE_SOLID, p_data.bRad);
				p_data.brush->SetColor(p_data.clr[1]);
				ps.hCanvas->DrawLine(p_data.brush, ps.uWidth / 2.f, atop, ps.uWidth / 2.f, (p_data.point == bottom ? ps.uHeight - Offset - th : Offset), Offset, D2D1_DASH_STYLE_SOLID, p_data.bRad);
			}
			else
			{
				Offset = p_data.BarSize;
				ps.hCanvas->DrawLine(p_data.brush, ps.uWidth / 2.f, Offset / 2.f, ps.uWidth / 2.f, ps.uHeight - Offset - th, Offset / 2.f, D2D1_DASH_STYLE_SOLID, p_data.bRad);
				p_data.brush->SetColor(p_data.clr[1]);
			}

			if (p_data.type == linear || p_data.type == slidBar)
			{
				if (p_data.point == bottom)
				{
					if (p_data.bRad == 0)
						ps.hCanvas->FillRect(p_data.brush, x, RC.bottom, x + BarSize, RC.bottom + BarSize);
					else
						ps.hCanvas->FillEllipse(p_data.brush, x, RC.bottom, x + BarSize, RC.bottom + BarSize);
				}
				else
				{
					if (p_data.bRad == 0)
						ps.hCanvas->FillRect(p_data.brush, x, RC.top, x + BarSize, RC.top + BarSize);
					else
						ps.hCanvas->FillEllipse(p_data.brush, x, RC.top, x + BarSize, RC.top + BarSize);
				}
			}
			if (p_data.bshowText)
			{
				if (p_data.type == slidBar)
				{
					auto tit = vstring::replace_all(GetText(), L"{{value}}", vstring::format(L"%.2f", p_data.lVal));
					ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Center | TextFormat::Middle, 0, 0, ps.uWidth, ps.uHeight, crTitle);
				}
				else
				{
					auto tit = vstring::replace_all(GetText(), L"{{value}}", std::to_wstring((int)(p_data.lVal)));
					ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Vertical, 0, ps.uHeight, th, ps.uHeight - th, crTitle);
				}
			}
		}
	}
	else if (p_data.type == range) {

	}
	else if (p_data.type == stops) {
		if (p_data.point == left || p_data.point == right) {
			OnGetRect(m_data.Frame, RC, tw);
			p_data.tw = tw;
			p_data.brush->SetColor(p_data.clr[0]);
			auto Offset = p_data.BarSize / 2.f;
			auto BarSize = p_data.BarSize;
			auto y = (ps.uHeight - BarSize) / 2.f;
			auto atop = (RC.left + RC.right) / 2.f;
			auto segmentWidth = (ps.uWidth - tw) / p_data.max; // 计算每段的宽度
			auto gap = segmentWidth * 0.2f; // 间隔为每段宽度的 20%
			for (int i = 0; i < p_data.max; i++) {
				// 计算每个线段的起始点和结束点
				float startX = i * segmentWidth + gap;
				float endX = startX + segmentWidth - 1.2f * gap;
				// 确保结束点不超出画布右边界
				if (endX > ps.uWidth - tw - gap) {
					endX = ps.uWidth - tw - gap;
				}
				ps.hCanvas->DrawLine(p_data.brush, startX, ps.uHeight / 2.f, endX, ps.uHeight / 2.f, Offset, D2D1_DASH_STYLE_SOLID, p_data.bRad);
			}
			p_data.brush->SetColor(p_data.clr[1]);
			ps.hCanvas->DrawLine(p_data.brush, atop, ps.uHeight / 2.f, (p_data.point == right ? ps.uWidth - Offset - tw : Offset), ps.uHeight / 2.f, Offset, D2D1_DASH_STYLE_SOLID, p_data.bRad);

			if (p_data.point == right)
				ps.hCanvas->FillEllipse(p_data.brush, RC.right, y, RC.right + BarSize, y + BarSize);
			else
				ps.hCanvas->FillEllipse(p_data.brush, RC.left, y, RC.left + BarSize, y + BarSize);
			if (p_data.bshowText)
			{
				auto tit = vstring::replace_all(GetText(), L"{{value}}", std::to_wstring((int)(p_data.lVal)));
				ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), TextFormat::Center | TextFormat::Middle, ps.uWidth, 0, ps.uWidth - tw, ps.uHeight, crTitle);
			}
		}
	}
}
