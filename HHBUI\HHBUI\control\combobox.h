﻿#pragma once
namespace HHBUI
{
	enum info_ComboBox_Event {
		WMM_CMB_ITEMCHANGED = -151
	};

	class TOAPI UIComboBox : public UIControl
	{
	public:
		UIComboBox(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
		//弹出或关闭
		void Popup();
		//关闭
		void Close();
		//是否已弹出
		BOOL IsPopup();
		//设置下拉箭头颜色
		void SetColourArrow(UIColor dwColor);
		//设置下拉列表宽度(默认随控件宽度)
		void SetDroppedWidth(INT width);
		//设置表项高度
		void SetItemHeight(INT height);
		//设置项目宽度
		void SetItemWidth(INT nWidth);
		/*添加一个列表项目
		* @param item - 自定义列表项目
		* @param index - 列表位置 -1=尾部
		*/
		INT AddItem(ListItem* item, INT index = -1);

		/*获取一个列表项目指针
		* @param index - 列表索引 -1=尾部
		*/
		void GetItem(int index, ListItem** item);

		/*设置当前选中的列表项目
		* @param index - 索引项目 -1=不选中
		*/
		void SetCurSelItem(int index);

		//获取当前选中的列表项目索引 -1=未选中
		int GetCurSelItem();

		//获取列表项目总数
		INT GetItemListCount();

		/*删除一个列表项目
		* @param index - 列表索引 -1=尾部
		*
		* @return 当前列表项目总数
		*/
		int DeleteItem(int index);

		/*删除所有列表项
		* @param delItem - 是否删除列表内存
		*/
		void DeleteAllItem(bool delItem = true);
		//设置项目热点颜色
		void SetCrHot(UIColor hover, UIColor checked, UIColor down);
		//取内容
		LPCWSTR GetText();

		//取编辑框
        LPVOID GetEdit();

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		static LRESULT CALLBACK OnWndMsgCrProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);
		static LRESULT CALLBACK On_free_Proc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);
		void combobox_ToScreen(float& x, float& y);
		bool IndexCheck(INT index);

		struct ComboBox_s
		{
			UIWnd* pWnd = nullptr;
			UIEdit* edit = nullptr;
			UIList* listview = nullptr;
			INT state = 0, nProcessTime = 0, itemheight = 30, itemwidth = 0, CurSelItem = 0;
			BOOL shwook = 0;
			HWND boxhwnd = 0;//弹出面板窗口句柄
			RECT pRC = {};
			UIarray* pArray = nullptr;
			INT droppedwidth = 0;//下拉列表宽度
			BOOL boxid = 0;//判断弹出
			UIBrush* dstBrush = nullptr;
			FLOAT fAngle = 0.f;
			UIColor ItemCr[3] = { UIColor(L"#e6e7e8"),UIColor(L"#c2c3c9"),UIColor(L"#cccedb") };
		}p_data;
	};
}
