﻿#include "pch.h"
#include "animation.h"
#include <common/winapi.h>
#include <deque>
#include <thread>

struct anilist_s
{
    AnimationPROC pfnSub = 0;
    LPVOID UIView = nullptr;
    DWORD Effect = 0;
    INT BeginX = 0;
    INT BeginY = 0;
    INT EndX = 0;
    INT EndY = 0;
    INT Frame = 0;
    LONG_PTR param1 = 0;
    LONG_PTR param2 = 0;
    LONG_PTR param3 = 0;
    LONG_PTR param4 = 0;
};

std::deque<anilist_s> m_hanilist;

BOOL HHBUI::UIAnimation::Start(UIBase *hParent, INT BeginX, INT EndX, INT BeginY, INT EndY, DWORD Effect, INT Frame, BOOL wait, INT num_iterations, 
    BOOL alternate_direction, BOOL alternate_Begin, AnimationPROC pfnSub, LPARAM param1, LPARAM param2, LPARAM param3, LPARAM param4)
{
    if (hParent->m_UIView)
    {
        auto objChild = (UIControl*)hParent->m_UIView;
        if (alternate_direction)
        {
            if (!m_hanilist.empty())
            {
                if (!alternate_Begin)
                {
                    const auto& tmp = m_hanilist.back();
                    BeginX = tmp.EndX;
                    BeginY = tmp.EndY;
                }
                m_hanilist.emplace_back(anilist_s{ pfnSub,hParent->m_UIView,Effect,BeginX,BeginY ,EndX,EndY,Frame,param1 ,param2 ,param3 ,param4 });
                return TRUE;
            }
            m_hanilist.clear();
            m_hanilist.emplace_back(anilist_s{ pfnSub,hParent->m_UIView,Effect,BeginX,BeginY ,EndX,EndY,Frame,param1 ,param2 ,param3 ,param4 });
        }
    }
    else
    {
        return FALSE;
    }
    auto param = new AniThreadParam();
    param->handle = hParent->m_UIView;
    param->pfnSub = pfnSub;
    param->BeginX = BeginX;
    param->BeginY = BeginY;
    param->EndX = EndX;
    param->EndY = EndY;
    param->Frame = Frame < 5 || Frame > 100 ? 20 : Frame;
    param->Effect = Effect;
    param->alternate_direction = alternate_direction;
    param->num_iterations = num_iterations <= 0 ? 1 : num_iterations;
    param->param1 = param1;
    param->param2 = param2;
    param->param3 = param3;
    param->param4 = param4;
   
    std::thread aniThread = std::thread(&AniThread, param);
    if (wait)
        aniThread.join();
    else
        aniThread.detach();
  
    return TRUE;
}
void Ex_Sleep(INT us)
{
    LARGE_INTEGER li{};
    li.QuadPart = (LONGLONG)(-10 * us);
    HANDLE hTimer = CreateWaitableTimerW(0, 0, 0);
    if (hTimer)
    {
        SetWaitableTimer(hTimer, &li, 0, 0, 0, 0);
        while (MsgWaitForMultipleObjects(1, &hTimer, 0, -1, 255) != 0)
        {
            MSG msg;
            while (PeekMessageW(&msg, 0, 0, 0, 1))
            {
                TranslateMessage(&msg);
                DispatchMessageW(&msg);
            }
        }
        CloseHandle(hTimer);
    }
}
void HHBUI::UIAnimation::AniThread(AniThreadParam *param)
{
    info_Animation EasingInfo{};
    EasingInfo.param1 = param->param1;
    EasingInfo.param2 = param->param2;
    EasingInfo.param3 = param->param3;
    EasingInfo.param4 = param->param4;
    INT BeginX = param->BeginX;
    INT BeginY = param->BeginY;
    INT EndX = param->EndX;
    INT EndY = param->EndY;
    DWORD Effect = param->Effect;
    INT num_iterations = param->num_iterations;
    AnimationPROC pfnSub = param->pfnSub;
    INT count = 0;
    INT Frame = param->Frame;
    INT i = 0;
    float per = 0.f;
    auto objChild = (UIControl*)param->handle;
    while (num_iterations)
    {
        EasingInfo.nInit = TRUE;
        i++;
        while (count < Frame)
        {
            SleepEx(5);
            count++;
            per = (float)count * 1.f / (float)Frame;
            if (BeginX != 0 || EndX != 0)
                EasingInfo.nCurrentX = EquationAnimation(BeginX, EndX, per, Effect);
            if (BeginY != 0 || EndY != 0)
                EasingInfo.nCurrentY = EquationAnimation(BeginY, EndY, per, Effect);
            EasingInfo.nProgress = (count == Frame ? 1.0f : per);
            EasingInfo.nIndex = i;
            if (pfnSub)
                pfnSub(objChild, i, EasingInfo.nIsEnd, EasingInfo.nInit, EasingInfo.nProgress, EasingInfo.nCurrentX, EasingInfo.nCurrentY, EasingInfo.param1, EasingInfo.param2, EasingInfo.param3, EasingInfo.param4);
            else
                objChild->OnBaseProc(objChild->GethWnd(), WM_EX_EASING, 0, (size_t)&EasingInfo);

            EasingInfo.nInit = FALSE;
        }
        if (param->alternate_direction)
        {
            num_iterations = (INT)m_hanilist.size();
            if (num_iterations != i && num_iterations != 0)
            {
                const auto& tmp = m_hanilist[i];
                BeginX = tmp.BeginX;
                BeginY = tmp.BeginY;
                EndX = tmp.EndX;
                EndY = tmp.EndY;
                objChild = (UIControl*)tmp.UIView;
                pfnSub = tmp.pfnSub;
                Effect = tmp.Effect;
                Frame = tmp.Frame;
                EasingInfo.param1 = tmp.param1;
                EasingInfo.param2 = tmp.param2;
                EasingInfo.param3 = tmp.param3;
                EasingInfo.param4 = tmp.param4;
            }
            else
            {
                num_iterations = 0;
            }
        }
        else
        {
            num_iterations -= 1;
        }
        per = 0.f;
        count = 0;
    }
    EasingInfo.nProgress = 1.0f;
    EasingInfo.nIsEnd = TRUE;
    if (pfnSub)
        pfnSub(objChild, i, EasingInfo.nIsEnd, EasingInfo.nInit, EasingInfo.nProgress, EasingInfo.nCurrentX, EasingInfo.nCurrentY, EasingInfo.param1, EasingInfo.param2, EasingInfo.param3, EasingInfo.param4);
    else
        objChild->OnBaseProc(objChild->GethWnd(), WM_EX_EASING, 0, (size_t)&EasingInfo);
    if (param->alternate_direction)
        m_hanilist.clear();
    delete param;
}

float HHBUI::UIAnimation::EquationAnimation(int Begin, int End, float Percents, int Type)
{
    float ret = 0;
    float& t = Percents;
    float d = 1.f;
    float b = (float)Begin;
    float c = float(End - Begin);

    auto easeOutBounce = [](float t, float b, float c, float d) -> float {

        if ((t /= d) < (1.f / 2.75f)) {
            return c * (7.5625f * t * t) + b;
        }
        else if (t < (2.f / 2.75f)) {
            return c * (7.5625f * (t -= (1.5f / 2.75f)) * t + .75f) + b;
        }
        else if (t < (2.5f / 2.75f)) {
            return c * (7.5625f * (t -= (2.25f / 2.75f)) * t + .9375f) + b;
        }
        else {
            return c * (7.5625f * (t -= (2.625f / 2.75f)) * t + .984375f) + b;
        }
        };


    switch (Type)
    {
        //二次渐变
    case AniEffect::Quadratic_In:
        ret = c * (t /= d) * t + b;
        break;
    case AniEffect::Quadratic_Out:
        ret = -c * (t /= d) * (t - 2) + b;
        break;
    case AniEffect::Quadratic_InOut:
        if ((t /= d / 2) < 1)
            ret = c / 2 * t * t + b;
        else
            ret = -c / 2 * ((--t) * (t - 2) - 1) + b;
        break;

        //正弦渐变
    case AniEffect::Sinusoidal_In:
        ret = -c * cos(t / d * (EASING_PI / 2)) + c + b;
        break;
    case AniEffect::Sinusoidal_Out:
        ret = c * sin(t / d * (EASING_PI / 2)) + b;
        break;
    case AniEffect::Sinusoidal_InOut:
        ret = -c / 2 * (cos(EASING_PI * t / d) - 1) + b;
        break;

        //指数渐变
    case AniEffect::Exponential_In:
        ret = (t == 0) ? b : c * pow(2.f, 10.f * (t / d - 1.f)) + b;
        break;
    case AniEffect::Exponential_Out:
        ret = (t == d) ? b + c : c * (-pow(2.f, -10.f * t / d) + 1.f) + b;
        break;
    case AniEffect::Exponential_InOut:
        if (t == 0) {
            ret = b;
            break;
        }
        else if (t == d) {
            ret = b + c;
            break;
        }
        else if ((t /= d / 2.f) < 1.f) {
            ret = c / 2.f * pow(2.f, 10.f * (t - 1.f)) + b;
            break;
        }
        else
            ret = c / 2 * (-pow(2.f, -10.f * --t) + 2) + b;
        break;

        //圆曲线
    case AniEffect::Circular_In:
        ret = -c * (sqrt(1 - (t /= d) * t) - 1) + b;
        break;
    case AniEffect::Circular_Out:
        ret = c * sqrt(1 - (t = t / d - 1) * t) + b;
        break;
    case AniEffect::Circular_InOut:
        if ((t /= d / 2) < 1)
            ret = -c / 2 * (sqrt(1 - t * t) - 1) + b;
        else
            ret = c / 2 * (sqrt(1 - (t -= 2) * t) + 1) + b;
        break;

        //三次方
    case AniEffect::Cubic_In:
        ret = c * (t /= d) * t * t + b;
        break;
    case AniEffect::Cubic_Out:
        ret = c * ((t = t / d - 1) * t * t + 1) + b;
        break;
    case AniEffect::Cubic_InOut:
        if ((t /= d / 2) < 1)
            ret = c / 2 * t * t * t + b;
        else
            ret = c / 2 * ((t -= 2) * t * t + 2) + b;
        break;

        //四次方
    case AniEffect::Quartic_In:
        ret = c * (t /= d) * t * t * t + b;
        break;
    case AniEffect::Quartic_Out:
        ret = -c * ((t = t / d - 1) * t * t * t - 1) + b;
        break;
    case AniEffect::Quartic_InOut:
        if ((t /= d / 2) < 1)
            ret = c / 2 * t * t * t * t + b;
        else
            ret = -c / 2 * ((t -= 2) * t * t * t - 2) + b;
        break;

        //五次方
    case AniEffect::Quintic_In:
        ret = c * (t /= d) * t * t * t * t + b;
        break;
    case AniEffect::Quintic_Out:
        ret = c * ((t = t / d - 1) * t * t * t * t + 1) + b;
        break;
    case AniEffect::Quintic_InOut:
        if ((t /= d / 2) < 1)
            ret = c / 2 * t * t * t * t * t + b;
        else
            ret = c / 2 * ((t -= 2) * t * t * t * t + 2) + b;
        break;

        //指数衰减正弦曲线
    case AniEffect::Elastic_In:
    {
        float s = 1.70158f;
        float p = 0;
        float a = c;
        if (t == 0) {
            ret = b;
            break;
        }
        if ((t /= d) == 1) {
            ret = b + c;
            break;
        }
        if (!p) p = d * 0.3f;
        if (a < abs(c)) {
            a = c;
            s = p / 4;
        }
        else
            s = p / (2 * EASING_PI) * asin(c / a);
        ret = -(a * pow(2.f, 10.f * (t -= 1.f)) * sin((t * d - s) * (2.f * EASING_PI) / p)) + b;
        break;
    }
    case AniEffect::Elastic_Out:
    {
        float s = 1.70158f;
        float p = 0;
        float a = c;
        if (t == 0) {
            ret = b;
            break;
        }
        if ((t /= d) == 1) {
            ret = b + c;
            break;
        }
        if (!p) p = d * 0.3f;
        if (a < abs(c)) {
            a = c;
            s = p / 4;
        }
        else
            s = p / (2 * EASING_PI) * asin(c / a);
        ret = a * pow(2.f, -10.f * t) * sin((t * d - s) * (2.f * EASING_PI) / p) + c + b;
        break;
    }
    case AniEffect::Elastic_InOut:
    {
        float s = 1.70158f;
        float p = 0;
        float a = c;
        if (t == 0) {
            ret = b;
            break;
        }
        if ((t /= d / 2) == 2) {
            ret = b + c;
            break;
        }
        if (!p) p = d * (0.3f * 1.5f);
        if (a < abs(c)) {
            a = c;
            s = p / 4;
        }
        else
            s = p / (2 * EASING_PI) * asin(c / a);
        if (t < 1)
            ret = -0.5f * (a * pow(2.f, 10.f * (t -= 1.f)) * sin((t * d - s) * (2.f * EASING_PI) / p)) + b;
        else
            ret = a * pow(2.f, -10.f * (t -= 1.f)) * sin((t * d - s) * (2.f * EASING_PI) / p) * 0.5f + c + b;
        break;
    }

    //Back
    case AniEffect::Back_In:
    {
        float s = 1.70158f;
        ret = c * (t /= d) * t * ((s + 1) * t - s) + b;
        break;
    }
    case AniEffect::Back_Out:
    {
        float s = 1.70158f;
        ret = c * ((t = t / d - 1) * t * ((s + 1) * t + s) + 1) + b;
        break;
    }
    case AniEffect::Back_InOut:
    {
        float s = 1.70158f;
        if ((t /= d / 2.f) < 1.f)
            ret = c / 2 * (t * t * (((s *= (1.525f)) + 1.f) * t - s)) + b;
        else
            ret = c / 2.f * ((t -= 2.f) * t * (((s *= (1.525f)) + 1.f) * t + s) + 2.f) + b;
        break;
    }
    case AniEffect::Bounce_In:
        ret = c - easeOutBounce(d - t, 0, c, d) + b;
        break;
    case AniEffect::Bounce_Out:
        ret = easeOutBounce(t, b, c, d);
        break;
    case AniEffect::Bounce_InOut:
    {
        auto easeInBounce = [&](float t, float b, float d, float c) {
            return c - easeOutBounce(d - t, 0, c, d) + b;
            };
        if (t < d / 2)
            ret = easeInBounce(t * 2, 0, c, d) * 0.5f + b;
        else
            ret = easeOutBounce(t * 2 - d, 0, c, d) * 0.5f + c * 0.5f + b;
        break;
    }

    //线性缓动
    default:
        ret = c * t / d + b;
        break;
    }
    return ret;
}


