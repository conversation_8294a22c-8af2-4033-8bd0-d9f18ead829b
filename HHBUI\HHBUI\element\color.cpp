﻿#include "pch.h"
#include "color.h"
#define ImFmod(X, Y)        fmodf((X), (Y))
#define ImFabs(X)           fabsf(X)
#define RGBGetB(argb) (LOBYTE(argb))
#define RGBGetG(argb) (LOBYTE(((WORD)(argb)) >> 8))
#define RGBGetR(argb) (LOBYTE((argb) >> 16))
#define RGBGetA(argb) (LOBYTE((argb) >> 24))
#define ARGB(r, g, b, a) ((COLORREF)(RGB(b, g, r) | (a << 24)))
template<typename T> static inline void ImSwap(T& a, T& b) { T tmp = a; a = b; b = tmp; }

HHBUI::UIColor::UIColor() :DxColorObject(0.f, 0.f, 0.f, 0.f)
{
}

HHBUI::UIColor::UIColor(const UIColor& ColorObject) : DxColorObject(ColorObject.DxColorObject)
{
}

HHBUI::UIColor::UIColor(const D2D1_COLOR_F& ColorObject) : DxColorObject(ColorObject.r, ColorObject.g, ColorObject.b, ColorObject.a)
{
}

HHBUI::UIColor::UIColor(const ColorEnum& Color, const float& AlphaValue) : DxColorObject(Color, AlphaValue)
{
}

HHBUI::UIColor::UIColor(const float& R, const float& G, const float& B, const float& A) : DxColorObject(R, G, B, A)
{
}
HHBUI::UIColor::UIColor(const float& R, const float& G, const float& B) : DxColorObject(R, G, B)
{
}
HHBUI::UIColor::UIColor(const int& R, const int& G, const int& B, const int& A) : DxColorObject(R / 255.f, G / 255.f, B / 255.f, A / 255.f)
{
}
HHBUI::UIColor::UIColor(const int& R, const int& G, const int& B) : DxColorObject(R / 255.f, G / 255.f, B / 255.f)
{
}
HHBUI::UIColor::UIColor(COLORREF rgb)
{
	DxColorObject.r = RGBGetR(rgb) / 255.f;
	DxColorObject.g = RGBGetG(rgb) / 255.f;
	DxColorObject.b = RGBGetB(rgb) / 255.f;
	DxColorObject.a = 1.f;
}
HHBUI::UIColor::UIColor(INT rgba)
{
	if (rgba != 0)
	{
		DxColorObject.r = RGBGetR(rgba) / 255.f;
		DxColorObject.g = RGBGetG(rgba) / 255.f;
		DxColorObject.b = RGBGetB(rgba) / 255.f;
		DxColorObject.a = RGBGetA(rgba) / 255.f;
	}
}
void HexStringLowerToUpper(std::wstring& HexStringNumber)
{
	size_t StringLength = HexStringNumber.size();
	for (size_t Count = 0; Count < StringLength; ++Count)
	{
		if (HexStringNumber[Count] >= L'A' && HexStringNumber[Count] <= 'Z')
		{
			HexStringNumber[Count] += 32;
		}
	}
}
short SingleHexLetterToNum(const wchar_t& Hex)
{
	if (Hex >= '0' && Hex <= '9')
	{
		return Hex - L'0';
	}

	return Hex - 87;
}
short HexStringToNum(const std::wstring& HexNumberString)
{
	short FirstCount = SingleHexLetterToNum(HexNumberString[0]) * 16;
	short SecondCount = SingleHexLetterToNum(HexNumberString[1]);

	return FirstCount + SecondCount;
}
INT fmt_getatom(std::wstring lpValue)
{
	INT atomSrc = 0;
	size_t commaPos = lpValue.find(L',');
	if (commaPos != std::wstring::npos)  // 如果找到逗号
	{
		atomSrc = 1;
	}
	else
	{
		size_t hashPos = lpValue.find(L'#');
		if (hashPos != std::wstring::npos)  // 如果找到井号
		{
			atomSrc = 2;
		}
	}

	return atomSrc;
}
HHBUI::UIColor::UIColor(LPCWSTR Hex)
{
	std::wstring HexString = Hex;
	auto atomSrc = fmt_getatom(HexString);
	if (atomSrc == 0)
	{
		// 处理整数情况
		auto rgba = _wtoi(HexString.c_str());
		DxColorObject.r = RGBGetR(rgba) / 255.f;
		DxColorObject.g = RGBGetG(rgba) / 255.f;
		DxColorObject.b = RGBGetB(rgba) / 255.f;
		DxColorObject.a = RGBGetA(rgba) / 255.f;
	}
	else if (atomSrc == 1)
	{
		// 处理 ARGB 数组
		std::wstringstream wss(HexString);
		std::wstring item;
		std::vector<int> values;

		while (std::getline(wss, item, L',')) {
			values.push_back(std::stoi(item));
		}

		if (values.size() != 4 && values.size() != 3) {
			return;
		}
		DxColorObject.r = values[0] / 255.f;
		DxColorObject.g = values[1] / 255.f;
		DxColorObject.b = values[2] / 255.f;
		DxColorObject.a = 1.f;
		if (values.size() == 4)
			DxColorObject.a = values[3] / 255.f;

	}
	else if (atomSrc == 2)
	{
		HexStringLowerToUpper(HexString);

		auto  RawHexString = HexString.substr(1, HexString.size() - 1);
		short ColorArrary[3] = { 0, 0, 0 };
		for (size_t Count = 2; Count <= RawHexString.size(); Count += 2)
		{
			ColorArrary[Count / 2 - 1] = HexStringToNum(RawHexString.substr(Count - 2, 2));
		}
		DxColorObject.r = ColorArrary[0] / 255.f;
		DxColorObject.g = ColorArrary[1] / 255.f;
		DxColorObject.b = ColorArrary[2] / 255.f;
		DxColorObject.a = 1.f;
		
	}
}
void HHBUI::UIColor::ToHSV(float& out_h, float& out_s, float& out_v)
{
	float K = 0.f;
	if (DxColorObject.g < DxColorObject.b)
	{
		ImSwap(DxColorObject.g, DxColorObject.b);
		K = -1.f;
	}
	if (DxColorObject.r < DxColorObject.g)
	{
		ImSwap(DxColorObject.r, DxColorObject.g);
		K = -2.f / 6.f - K;
	}

	const float chroma = DxColorObject.r - (DxColorObject.g < DxColorObject.b ? DxColorObject.g : DxColorObject.b);
	out_h = ImFabs(K + (DxColorObject.g - DxColorObject.b) / (6.f * chroma + 1e-20f));
	out_s = chroma / (DxColorObject.r + 1e-20f);
	out_v = DxColorObject.r;
}
void HHBUI::UIColor::HSLtoRGB(const float hsl[], float rgb[])
{
	if (hsl[1] < FLT_MIN)
		rgb[0] = rgb[1] = rgb[2] = hsl[2];
	else if (hsl[2] < FLT_MIN)
		rgb[0] = rgb[1] = rgb[2] = 0.0f;
	else
	{
		const float q = hsl[2] < 0.5f ? hsl[2] * (1.0f + hsl[1]) : hsl[2] + hsl[1] - hsl[2] * hsl[1];
		const float p = 2.0f * hsl[2] - q;
		float t[] = { hsl[0] + 2.0f, hsl[0], hsl[0] - 2.0f };

		for (int i = 0; i < 3; ++i)
		{
			if (t[i] < 0.0f)
				t[i] += 6.0f;
			else if (t[i] > 6.0f)
				t[i] -= 6.0f;

			if (t[i] < 1.0f)
				rgb[i] = p + (q - p) * t[i];
			else if (t[i] < 3.0f)
				rgb[i] = q;
			else if (t[i] < 4.0f)
				rgb[i] = p + (q - p) * (4.0f - t[i]);
			else
				rgb[i] = p;
		}
	}
}
INT HHBUI::UIColor::R(INT r)
{
	if (r != -1)
	{
		DxColorObject.r = r / 255.f;
		return r;
	}
	return (INT)(DxColorObject.r * 255.f);
}

INT HHBUI::UIColor::G(INT g)
{
	if (g != -1)
	{
		DxColorObject.g = g / 255.f;
		return g;
	}
	return (INT)(DxColorObject.g * 255.f);
}

INT HHBUI::UIColor::B(INT b)
{
	if (b != -1)
	{
		DxColorObject.b = b / 255.f;
		return b;
	}
	return (INT)(DxColorObject.b * 255.f);
}

INT HHBUI::UIColor::A(INT a)
{
	if (a != -1)
	{
		DxColorObject.a = a / 255.f;
		return a;
	}
	return (INT)(DxColorObject.a * 255.f);
}

float HHBUI::UIColor::GetR() const
{
	return DxColorObject.r;
}

float HHBUI::UIColor::GetG() const
{
	return DxColorObject.g;
}

float HHBUI::UIColor::GetB() const
{
	return DxColorObject.b;
}

float HHBUI::UIColor::GetA() const
{
	return DxColorObject.a;
}

D2D1::ColorF HHBUI::UIColor::GetDxObject() const
{
	return DxColorObject;
}

COLORREF HHBUI::UIColor::GetRGB()
{
	return RGB(static_cast<BYTE>(DxColorObject.r * 255.0f), static_cast<BYTE>(DxColorObject.g * 255.0f), static_cast<BYTE>(DxColorObject.b * 255.0f));
}

INT HHBUI::UIColor::GetARGB()
{
	return ARGB(static_cast<BYTE>(DxColorObject.r * 255.0f), static_cast<BYTE>(DxColorObject.g * 255.0f), static_cast<BYTE>(DxColorObject.b * 255.0f), static_cast<BYTE>(DxColorObject.a * 255.0f));
}

HHBUI::UIColor HHBUI::UIColor::SetR(const float& R)
{
	DxColorObject.r = R;
	return UIColor(DxColorObject);
}

HHBUI::UIColor HHBUI::UIColor::SetG(const float& G)
{
	DxColorObject.g = G;
	return UIColor(DxColorObject);
}

HHBUI::UIColor HHBUI::UIColor::SetB(const float& B)
{
	DxColorObject.b = B;
	return UIColor(DxColorObject);
}

HHBUI::UIColor HHBUI::UIColor::SetA(const float& A)
{
	DxColorObject.a = A;
	return UIColor(DxColorObject);
}


void HHBUI::UIColor::SetColorLights(float factor)
{
	DxColorObject.r = std::clamp(DxColorObject.r * factor, 0.f, 1.f);
	DxColorObject.g = std::clamp(DxColorObject.g * factor, 0.f, 1.f);
	DxColorObject.b = std::clamp(DxColorObject.b * factor, 0.f, 1.f);
}

bool HHBUI::UIColor::empty()
{
	return (DxColorObject.r == 0.f &&
		DxColorObject.g == 0.f &&
		DxColorObject.b == 0.f &&
		DxColorObject.a == 0.f);
}

bool HHBUI::UIColor::isInRange()
{
	return (DxColorObject.r >= 0.f && DxColorObject.r <= 1.f) &&
		(DxColorObject.g >= 0.f && DxColorObject.g <= 1.f) &&
		(DxColorObject.b >= 0.f && DxColorObject.b <= 1.f);
}

void HHBUI::UIColor::ColorConvertHSVtoRGB(float h, float s, float v, float& out_r, float& out_g, float& out_b)
{
	if (s == 0.0f)
	{
		// gray
		out_r = out_g = out_b = v;
		return;
	}

	h = ImFmod(h, 1.0f) / (60.0f / 360.0f);
	int   i = (int)h;
	float f = h - (float)i;
	float p = v * (1.0f - s);
	float q = v * (1.0f - s * f);
	float t = v * (1.0f - s * (1.0f - f));

	switch (i)
	{
	case 0: out_r = v; out_g = t; out_b = p; break;
	case 1: out_r = q; out_g = v; out_b = p; break;
	case 2: out_r = p; out_g = v; out_b = t; break;
	case 3: out_r = p; out_g = q; out_b = v; break;
	case 4: out_r = t; out_g = p; out_b = v; break;
	case 5: default: out_r = v; out_g = p; out_b = q; break;
	}
}

