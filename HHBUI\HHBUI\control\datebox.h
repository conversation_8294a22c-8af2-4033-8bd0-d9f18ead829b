﻿#pragma once
namespace HHBUI
{
	enum info_datebox {
		eos_date_defaultline = 0x0,          //显示年月日星期并用-分割
		eos_date_simpleline = 0x20,          //只显示年月日并用-分割
		eos_date_allcomma = 0x100,           //显示年月日星期并用,分割

		WMM_DBN_DATETIME = 100062,             //日期已更改,wParam=年,lParam=低位(月)高位(日)
	};
	class TOAPI UIDatebox : public UIControl
	{
	public:
		UIDatebox(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0);
		//弹出或关闭
		void Popup();
		//关闭
		void Close();
		//是否已弹出
		BOOL IsPopup();
		//设置下拉箭头颜色
		void SetColourArrow(UIColor dwColor);
		//设置当前日期
		void SetDate(int nYear, int nMon, int days);
		//获取当前日期和星期
		void GetDate(int& nYear, int& nMon, int& days, int& Wday);


	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		static LRESULT CALLBACK OnWndMsgDateProc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);
		static LRESULT CALLBACK OnCustomDraw_Proc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);
		static LRESULT CALLBACK OnBtnDraw_Proc(HWND hWnd, LPVOID pWnd, LPVOID UIView, INT nID, INT uMsg, WPARAM wParam, LPARAM lParam);
		void update(INT Type = 0);
		void upTodate(int nYear, int nMon);
		void upToTime(int type, int index, int year, int mon, int Mday, int Wday);
		void toshow(int type);
		std::wstring getlocaltime(BOOL dwprintf, INT* year, INT* month, INT* days, INT* hour, INT* min, INT* sec, INT* wdays);

		struct datebox_s
		{
			UIWnd* pWnd = nullptr;
			UIListView* dblistview = nullptr, * listview = nullptr;
			UIStatic* BtnByTitle = nullptr, * BtnByReset = nullptr, * BtnByUpper = nullptr, * BtnByDown = nullptr;
			FLOAT fAngle = 0.f;
			UIFont* hFont = nullptr, * hFontSe = nullptr, * hFont_Calendar = nullptr;
			INT to_Year = 0, to_Mon = 0, to_Mday = 0, Year = 0, Mon = 0, Mday = 0, Wday = 0, nCalendar = 0, nSohwType = 0, lpYear = 0, lpMon = 0, state = 0, nProcessTime = 0;
			time_t time = 0;
			UIBrush* hBrush = nullptr, *TmpBrush = nullptr, * TitleBrush = nullptr;
			LPVOID Items = nullptr;
		}p_data;

	};
}
