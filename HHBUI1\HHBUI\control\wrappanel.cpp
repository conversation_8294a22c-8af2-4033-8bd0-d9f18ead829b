﻿#include "pch.h"
#include "wrappanel.h"
#include <common/winapi.h>

HHBUI::UIWrapPanel::UIWrapPanel(UIBase *hParent, INT x, INT y, INT width, INT height, INT dwStyle, INT dwStyleEx, INT nID)
{
	InitSubControl(hParent, x, y, width, height, L"form-wrappanel", NULL, dwStyle, dwStyleEx, nID);
	p_data.BindObj = new UIStatic(this, 0, 0, 400, 40);
}

BOOL HHBUI::UIWrapPanel::SetLayoutType(INT nType)
{
	p_data.uInit = p_data.BindObj->Layout_Init(nType);
	return p_data.uInit;
}
BOOL HHBUI::UIWrapPanel::AddChild(UIControl* parent)
{
	if (p_data.uInit && parent->SetParent(p_data.BindObj) && p_data.BindObj->Layout_AddChild(parent))
	{
		if (p_data.Padding.left != -1)
			p_data.BindObj->Layout_SetChildProp(parent, elp_padding_left, p_data.Padding.left);
		if (p_data.Padding.top != -1)
			p_data.BindObj->Layout_SetChildProp(parent, elp_padding_top, p_data.Padding.top);
		if (p_data.Padding.right != -1)
			p_data.BindObj->Layout_SetChildProp(parent, elp_padding_right, p_data.Padding.right);
		if (p_data.Padding.bottom != -1)
			p_data.BindObj->Layout_SetChildProp(parent, elp_padding_bottom, p_data.Padding.bottom);

		if (p_data.Margin.left != -1)
			p_data.BindObj->Layout_SetChildProp(parent, elcp_margin_left, p_data.Margin.left);
		if (p_data.Margin.top != -1)
			p_data.BindObj->Layout_SetChildProp(parent, elcp_margin_top, p_data.Margin.top);
		if (p_data.Margin.right != -1)
			p_data.BindObj->Layout_SetChildProp(parent, elcp_margin_right, p_data.Margin.right);
		if (p_data.Margin.bottom != -1)
			p_data.BindObj->Layout_SetChildProp(parent, elcp_margin_bottom, p_data.Margin.bottom);

		if (Layout_GetType() == elt_linear)
		{
			if (p_data.fVertical != -1)
				p_data.BindObj->Layout_SetChildProp(parent, elp_linear_direction, p_data.fVertical);
			if (p_data.nSize != -1)
				p_data.BindObj->Layout_SetChildProp(parent, elcp_linear_size, p_data.nSize);
			if (p_data.nFill != -1)
				p_data.BindObj->Layout_SetChildProp(parent, elcp_linear_align, p_data.nFill);
			if (p_data.nDAlign != -1)
				p_data.BindObj->Layout_SetChildProp(parent, elp_linear_dalign, p_data.nDAlign);
		}

		return TRUE;
	}
	return FALSE;
}
void HHBUI::UIWrapPanel::SetPadding(INT left, INT top, INT right, INT bottom)
{
	p_data.Padding = { left ,top, right ,bottom };
}

void HHBUI::UIWrapPanel::SetMargin(INT left, INT top, INT right, INT bottom)
{
	p_data.Margin = { left ,top, right ,bottom };
}

void HHBUI::UIWrapPanel::SetSize(INT width, INT height)
{
	p_data.AllSize = { width ,height };
}

void HHBUI::UIWrapPanel::Setdirection(INT fVertical)
{
	p_data.fVertical = fVertical;
}

void HHBUI::UIWrapPanel::SetSize(INT nSize)
{
	p_data.nSize = nSize;
}

void HHBUI::UIWrapPanel::SetAlign(INT nFill)
{
	p_data.nFill = nFill;
}

void HHBUI::UIWrapPanel::SetDalign(INT nDAlign)
{
	p_data.nDAlign = nDAlign;
}

LRESULT HHBUI::UIWrapPanel::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_SIZE)
	{
		if (p_data.BindObj)
		{
			auto dpi = UIWinApi::ToList.drawing_default_dpi;
			auto windowWidth = LOWORD(lParam) / dpi;
			auto windowHeight = HIWORD(lParam) / dpi;
			INT pageMargin = p_data.Margin.right;
			INT pageWidth = (windowWidth - 2 * pageMargin);
			INT pageHeight = (windowHeight - 2 * pageMargin);

			INT containerHeight = GetAllHeight(pageWidth);
			p_data.BindObj->Move(0, 0, (INT)windowWidth, containerHeight, FALSE);
			SetScrollShow(FALSE, containerHeight > windowHeight);
			if (containerHeight > pageHeight)
			{
				SetScrollInfo(FALSE, SIF_ALL, 0, containerHeight - pageHeight, 100, 0, TRUE);
			}
			else {
				SetScrollInfo(FALSE, SIF_ALL, 0, 0, 100, 0, TRUE);
			}
		}
	}
	else if (uMsg == WM_VSCROLL)
	{
		auto nPos = PostScrollMsg(uMsg, wParam, lParam, 100);
		auto dpi = HHBUI::UIWinApi::ToList.drawing_default_dpi;
		p_data.BindObj->Move(0, -nPos / dpi, CW_USEDEFAULT, CW_USEDEFAULT, FALSE);
	}
	return S_OK;
}

void HHBUI::UIWrapPanel::OnPaintProc(ps_context ps)
{
}

INT HHBUI::UIWrapPanel::GetAllHeight(INT cWidth)
{
	auto buttonsNumber = (INT)p_data.BindObj->Layout_GetChildCount();
	if (buttonsNumber != 0)
	{
		INT nVCount = (cWidth + p_data.Margin.right) / (p_data.AllSize.x + p_data.Margin.right);
		if (nVCount == 0) return 0;
		INT nHCount = (buttonsNumber % nVCount) ? (buttonsNumber / nVCount + 1) : (buttonsNumber / nVCount);
		INT containerHeight = (nHCount * (p_data.AllSize.y + p_data.Margin.bottom)) - p_data.Margin.bottom;
		return containerHeight;
	}
	return 0;
}

