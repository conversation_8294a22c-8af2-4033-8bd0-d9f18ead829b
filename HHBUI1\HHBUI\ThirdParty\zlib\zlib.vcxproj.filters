﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="adler32.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="compress.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="crc32.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="deflate.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="inffast.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="inflate.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="inftrees.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ioapi.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="trees.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="uncompr.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="unzip.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="zutil.c">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="crc32.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="crypt.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="deflate.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="gzguts.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="inffast.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="inffixed.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="inflate.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="inftrees.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ioapi.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="trees.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="unzip.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="zconf.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="zlib.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="zutil.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
</Project>