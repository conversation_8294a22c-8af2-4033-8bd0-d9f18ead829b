﻿/*****************************************************************//**
 * Nahimic/AVolute音频驱动冲突：
 * AVolute是Nahimic音频增强软件的开发商，通常与MSI、华硕等主板捆绑
 * 项目使用了bass音频库，可能与系统中安装的Nahimic/AVolute音频驱动产生冲突

 * 错误消息 Error 20 (this feature has not been implemented yet) in function AVolute::GetProductInfoT::<lambda_3920e95365a48b95dd51020986e9e351>::operator ()
 *********************************************************************/





#include "pch.h"
#include "waveringview.h"
#include <wrl.h>
#include "loading_internal.h"
#include <cstringt.h>
#include <common/winapi.h>
// BASS_ChannelGetLength/GetPosition/SetPosition modes
#define BASS_POS_BYTE			0		// byte position
#define BASS_POS_MUSIC_ORDER	1		// order.row position, MAKELONG(order,row)
#define BASS_POS_OGG			3		// OGG bitstream number
#define BASS_POS_END			0x10	// trimmed end position
#define BASS_POS_LOOP			0x11	// loop start positiom
#define BASS_POS_FLUSH			0x1000000 // flag: flush decoder/FX buffers
#define BASS_POS_RESET			0x2000000 // flag: reset user file buffers
#define BASS_POS_RELATIVE		0x4000000 // flag: seek relative to the current position
#define BASS_POS_INEXACT		0x8000000 // flag: allow seeking to inexact position
#define BASS_POS_DECODE			0x10000000 // flag: get the decoding (not playing) position
#define BASS_POS_DECODETO		0x20000000 // flag: decode to the position instead of seeking
#define BASS_POS_SCAN			0x40000000 // flag: scan to the position
// Sample info structure
#define BASS_SAMPLE_FLOAT		256	// 32 bit floating-point
#define BASS_DATA_FFT512	0x80000001	// 512 FFT 个采样 FFT（返回 256 个浮点值）
#define BASS_DEVICE_CPSPEAKERS	0x400	// unused
#define BASS_STREAM_DECODE		0x200000
#define BASS_UNICODE			0x80000000	// UTF-16
// Channel attributes
#define BASS_ATTRIB_FREQ			1
#define BASS_ATTRIB_VOL				2
#define BASS_ATTRIB_PAN				3
#define BASS_ATTRIB_EAXMIX			4
#define BASS_ATTRIB_NOBUFFER		5
#define BASS_ATTRIB_VBR				6
#define BASS_ATTRIB_CPU				7
#define BASS_ATTRIB_SRC				8
#define BASS_ATTRIB_NET_RESUME		9
#define BASS_ATTRIB_SCANINFO		10
#define BASS_ATTRIB_NORAMP			11
#define BASS_ATTRIB_BITRATE			12
#define BASS_ATTRIB_BUFFER			13
#define BASS_ATTRIB_GRANULE			14
#define BASS_ATTRIB_USER			15
#define BASS_ATTRIB_TAIL			16
#define BASS_ATTRIB_PUSH_LIMIT		17
#define BASS_ATTRIB_MUSIC_AMPLIFY	0x100
#define BASS_ATTRIB_MUSIC_PANSEP	0x101
#define BASS_ATTRIB_MUSIC_PSCALER	0x102
#define BASS_ATTRIB_MUSIC_BPM		0x103
#define BASS_ATTRIB_MUSIC_SPEED		0x104
#define BASS_ATTRIB_MUSIC_VOL_GLOBAL 0x105
#define BASS_ATTRIB_MUSIC_ACTIVE	0x106
#define BASS_ATTRIB_MUSIC_VOL_CHAN	0x200 // + channel #
#define BASS_ATTRIB_MUSIC_VOL_INST	0x300 // + instrument #
// bpm flags
#define BASS_FX_BPM_BKGRND	1	// if in use, then you can do other processing while detection's in progress. Available only in Windows platforms (BPM/Beat)
#define BASS_FX_BPM_MULT2	2	// if in use, then will auto multiply bpm by 2 (if BPM < minBPM*2)
#define BASS_FX_FREESOURCE			0x10000
#define M_PI (3.1415926536f)
#define SPECTRUM_COL 128 //频谱分析柱形的条数
#define SPACE		20
#define ROW			15
#define COL			15
#define POINT_CNT	ROW * COL
#define AMPLITUDE	50
// Device info structure
typedef struct {
#if defined(_WIN32_WCE) || (defined(WINAPI_FAMILY) && WINAPI_FAMILY != WINAPI_FAMILY_DESKTOP_APP)
    const wchar_t* name;	// description
    const wchar_t* driver;	// driver
#else
    const char* name;	// description
    const char* driver;	// driver
#endif
    DWORD flags;
} BASS_DEVICEINFO;
enum class Alignment	//对齐方式
{
    LEFT,
    RIGHT,
    CENTER
};
typedef BOOL(WINAPI* BASS_GetDeviceInfo)(DWORD device, BASS_DEVICEINFO* info);
typedef BOOL(WINAPI* BASS_Init)(int device, DWORD freq, DWORD flags, HWND win, const void* dsguid);
typedef HPLUGIN(WINAPI* BASS_PluginLoad)(const WCHAR* file, DWORD flags);
typedef HSTREAM(WINAPI* BASS_StreamCreateFile)(BOOL mem, const WCHAR* file, QWORD offset, QWORD length, DWORD flags);
typedef BOOL(WINAPI* BASS_ChannelPlay)(DWORD handle, BOOL restart);
typedef BOOL(WINAPI* BASS_ChannelStop)(DWORD handle);
typedef BOOL(WINAPI* BASS_StreamFree)(DWORD handle);
typedef BOOL(WINAPI* BASS_Stop)();
typedef BOOL(WINAPI* BASS_Free)();
typedef DWORD(WINAPI* BASS_ChannelGetData)(DWORD handle, void* buffer, DWORD length);
typedef BOOL(WINAPI* BASS_ChannelSlideAttribute)(DWORD handle, DWORD attrib, float value, DWORD time);
typedef QWORD(WINAPI* BASS_ChannelGetPosition)(DWORD handle, INT mode);
typedef BOOL(WINAPI* BASS_ChannelSetPosition)(DWORD handle, QWORD pos, DWORD mode);
typedef double(WINAPI* BASS_ChannelBytes2Seconds)(DWORD handle, QWORD pos);
typedef QWORD(WINAPI* BASS_ChannelGetLength)(DWORD handle, DWORD mode);
typedef BOOL(WINAPI* BASS_ChannelSetAttribute)(DWORD handle, DWORD attrib, float value);
typedef void (*BPMPROGRESSPROC)(int channel, float percent, void* user);
typedef float(WINAPI* BASS_FX_BPM_DecodeGet)(
    DWORD chan,
    double startSec,
    double endSec,
    DWORD minMaxBPM,
    DWORD flags,
    BPMPROGRESSPROC* proc,
    void* user
    );
typedef float (WINAPI* BASS_FX_TempoGetRateRatio)(HSTREAM chan);
typedef BOOL(WINAPI* BASS_FX_BPM_Free)(DWORD handle);

HMODULE DLL_Load(LPCWSTR szPath)
{
    HMODULE m_hModule = ::LoadLibraryExW(szPath, NULL, LOAD_WITH_ALTERED_SEARCH_PATH);
    if (NULL == m_hModule)
        m_hModule = ::LoadLibraryW(szPath);
    return m_hModule;
}
void DLL_Unload(HMODULE m_hModule)
{
    if (m_hModule)
    {
        ::FreeLibrary(m_hModule);
        m_hModule = NULL;
    }
}
void* DLL_GetSymbol(HMODULE m_hModule, const char* pProcName)
{
    return (void*)GetProcAddress(m_hModule, pProcName);
}

HHBUI::UIWaveRingView::UIWaveRingView(UIBase* hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
    InitSubControl(hParent, x, y, width, height, L"form-waveringview", lpszName, (dwStyle == 0 ? eos_textoffset : dwStyle), dwStyleEx, nID, dwTextFormat);
    p_data.isRotate = true;
    p_data.isRandom = true;
    p_data.isBase = false;
    p_data.isWave = true;
    p_data.isPoint = true;
    p_data.isPowerOffset = false;
    p_data.isSpread = true;
    p_data.isDrawText = false;
    p_data.isMove = true;
    p_data.isfill = true;
    p_data.isfx = false;
    p_data.isfxBloom = false;
    p_data.scope = 100;
    p_data.distance = 35;
    p_data.speed = 3;
    p_data.list = std::vector<WaveViewBean>();
    p_data.lastRadius = std::vector<float>();
    p_data.centerPoint.x = (m_data.Frame.right - m_data.Frame.left) / 2.f;
    p_data.centerPoint.y = (m_data.Frame.bottom - m_data.Frame.top) / 2.f;
    p_data.radius = 80;
    p_data.lowbpm = 120.f;
    p_data.fxcr = UIColor(255, 0, 255);
    p_data.ColorBrush = new UIBrush(p_data.fxcr);
    p_data.SolidBrush = new UIBrush(p_data.fxcr);
    resetColor();
}

void HHBUI::UIWaveRingView::SetType(info_wavering_type type)
{
    p_data.type = type;
    if (type == DyCircle && !p_data.hCanvas)
        p_data.hCanvas = new UICanvas(m_data.pWnd, m_data.Frame.right, m_data.Frame.bottom);
}

HMODULE HHBUI::UIWaveRingView::SetBassDll(LPCWSTR libPath, BOOL fInitb)
{
    p_data.hModule = DLL_Load(libPath);
    if (fInitb && p_data.hModule)
    {
        auto pFunc = (BASS_Init)DLL_GetSymbol(p_data.hModule, "BASS_Init");
        pFunc(-1, 44100, 0, m_data.pWnd->GethWnd(), NULL);
        //载入BASS插件
        std::wstring plugin_dir = LR"(bass\)";
        std::vector<std::wstring> plugin_files;
        UIGetFiles(plugin_dir + L"cod_*.dll", plugin_files);

        auto pFuncPluginLoad = (BASS_PluginLoad)DLL_GetSymbol(p_data.hModule, "BASS_PluginLoad");
        for (const auto& plugin_file : plugin_files)
        {
            //加载插件
            HPLUGIN handle = pFuncPluginLoad((plugin_dir + plugin_file).c_str(), 0);
        }
    }
    return p_data.hModule;
}

void HHBUI::UIWaveRingView::SetBassDllHandle(HMODULE handle)
{
    p_data.hModule = handle;
}

HMODULE HHBUI::UIWaveRingView::SetBassFXDll(LPCWSTR libPath)
{
    p_data.hFxMod = DLL_Load(libPath);
    return p_data.hFxMod;
}

HSTREAM HHBUI::UIWaveRingView::SetBassPlayFile(LPCWSTR playfile)
{
    if (p_data.hModule)
    {
        auto pFuncStop = (BASS_ChannelStop)DLL_GetSymbol(p_data.hModule, "BASS_ChannelStop");
        auto pFuncPlay = (BASS_ChannelPlay)DLL_GetSymbol(p_data.hModule, "BASS_ChannelPlay");
        auto pFuncFree = (BASS_StreamFree)DLL_GetSymbol(p_data.hModule, "BASS_StreamFree");
        auto pFuncOpen = (BASS_StreamCreateFile)DLL_GetSymbol(p_data.hModule, "BASS_StreamCreateFile");
        pFuncStop(p_data.hBass);
        pFuncFree(p_data.hBass);
        DWORD bpmChan = 0;
        if (p_data.hFxMod)
        {
            // open the same file as played but for bpm decoding detection
            bpmChan = pFuncOpen(FALSE, playfile, 0, 0, BASS_STREAM_DECODE | BASS_UNICODE);
        }
        SetBassHandle(pFuncOpen(FALSE, playfile, 0, 0, BASS_SAMPLE_FLOAT | BASS_UNICODE), bpmChan);
        pFuncPlay(p_data.hBass, FALSE);
    }
    return p_data.hBass;
}

void HHBUI::UIWaveRingView::SetBassHandle(HSTREAM handle, DWORD bpmChan)
{
    p_data.hBass = handle;
    auto pFuncChannelGetLength = (BASS_ChannelGetLength)DLL_GetSymbol(p_data.hModule, "BASS_ChannelGetLength");
    p_data.GetLength = pFuncChannelGetLength(p_data.hBass, BASS_POS_BYTE);
    SetTimer((size_t)this + 3, 100);
    if (p_data.hFxMod && bpmChan)
    {
        double len_sec = GetBassPlayTotalTime();
        auto pFuncChannelGetBPM = (BASS_FX_BPM_DecodeGet)DLL_GetSymbol(p_data.hFxMod, "BASS_FX_BPM_DecodeGet");
        p_data.lowbpm = pFuncChannelGetBPM(bpmChan, 0, len_sec, 0, BASS_FX_BPM_BKGRND | BASS_FX_BPM_MULT2 | BASS_FX_FREESOURCE, nullptr, nullptr);

        //auto pTempoGetRateRatio = (BASS_FX_TempoGetRateRatio)DLL_GetSymbol(p_data.hFxMod, "BASS_FX_TempoGetRateRatio");
        //p_data.lowbpm = bpmValue * pTempoGetRateRatio(bpmChan);

        auto pFuncFX_BPM_Free = (BASS_FX_BPM_Free)DLL_GetSymbol(p_data.hFxMod, "BASS_FX_BPM_Free");
        pFuncFX_BPM_Free(bpmChan);
    }
}

void HHBUI::UIWaveRingView::SetOrRotate(BOOL rotate)
{
    p_data.isRotate = rotate;
}

void HHBUI::UIWaveRingView::SetOrRandom(BOOL random)
{
    p_data.isRandom = random;
}

void HHBUI::UIWaveRingView::SetOrDrawbase(BOOL drawbase)
{
    p_data.isBase = drawbase;
}

void HHBUI::UIWaveRingView::SetOrDrawWave(BOOL drawwave)
{
    p_data.isWave = drawwave;
}

void HHBUI::UIWaveRingView::SetOrDrawPoint(BOOL drawpoint)
{
    p_data.isPoint = drawpoint;
}

void HHBUI::UIWaveRingView::SetOrPoweroffSet(BOOL poweroffset)
{
    p_data.isPowerOffset = poweroffset;
}

void HHBUI::UIWaveRingView::SetOrSpread(BOOL spread)
{
    p_data.isSpread = spread;
}

void HHBUI::UIWaveRingView::SetOrDrawText(BOOL drawtext)
{
    p_data.isDrawText = drawtext;
}

void HHBUI::UIWaveRingView::SetTextOfMove(BOOL textmove)
{
    p_data.isMove = textmove;
}

void HHBUI::UIWaveRingView::SetOrfill(BOOL isfill)
{
    p_data.isfill = isfill;
}

void HHBUI::UIWaveRingView::SetFxORbloom(BOOL fxORbloom)
{
    p_data.isfxBloom = fxORbloom;
}

void HHBUI::UIWaveRingView::SetOrDrawFx(BOOL drawfx)
{
    p_data.isfx = drawfx;
}

void HHBUI::UIWaveRingView::SetOrColor(UIColor Orcolor)
{
    if (Orcolor.empty())
    {
        initColor();
        resetColor();
    }
    else
        p_data.ColorBrush->SetColor(Orcolor);
}

void HHBUI::UIWaveRingView::SetFxColor(UIColor fxcolor)
{
    p_data.fxcr = fxcolor;
}

void HHBUI::UIWaveRingView::SetMaximumlight(float voligh)
{
    p_data.pMaximumlight = voligh;
}

void HHBUI::UIWaveRingView::SetScope(INT scope)
{
    p_data.scope = scope;
}

void HHBUI::UIWaveRingView::SetDistance(INT distance)
{
    p_data.distance = distance;
}

void HHBUI::UIWaveRingView::SetSpeed(INT speed)
{
    p_data.speed = speed;
}

void HHBUI::UIWaveRingView::SetAlbumSize(INT fScaleSize)
{
    if (p_data.type == DyCircle)
    {
        if (p_data.hMigCanvas)
            delete p_data.hMigCanvas;
        p_data.hMigCanvas = new UICanvas(m_data.pWnd, fScaleSize, fScaleSize);
        p_data.hImgscale = fScaleSize;
    }
    else if (p_data.type == Ripple)
    {
        p_data.hImgscale = fScaleSize;
    }
}

void HHBUI::UIWaveRingView::SetAlbum(UIImage* dstImgw)
{
    if (dstImgw != 0)
    {
        if (p_data.type == DyCircle)
        {
            if (!p_data.hMigCanvas)
                p_data.hMigCanvas = new UICanvas(m_data.pWnd, p_data.hImgscale, p_data.hImgscale);
            p_data.hMigCanvas->BeginDraw();
            p_data.hMigCanvas->Clear();
            UIImage* hImg;
            dstImgw->Scale(p_data.hImgscale, p_data.hImgscale, &hImg);
            auto hBrush = new UIBrush(hImg, D2D1_INTERPOLATION_MODE_NEAREST_NEIGHBOR);
            delete hImg;
            p_data.hMigCanvas->FillRoundRect(hBrush, 0, 0, p_data.hImgscale, p_data.hImgscale, p_data.hImgscale / 2.f);
            p_data.hMigCanvas->EndDraw();
            delete hBrush;
        }
        else if (p_data.type == Ripple)
        {
            if (p_data.hImgBrush)
                delete p_data.hImgBrush;
            UIImage* hImg;
            dstImgw->Scale(p_data.hImgscale, p_data.hImgscale, &hImg);
            p_data.hImgBrush = new UIBrush(hImg, D2D1_INTERPOLATION_MODE_NEAREST_NEIGHBOR, BrushModeEx::None);

            int psWidth = m_data.Frame.right - m_data.Frame.left;
            int psHeight = m_data.Frame.bottom - m_data.Frame.top;
            int sy = (psWidth - p_data.hImgscale) / 2;
            p_data.hImgBrush->SetImgTransformToRect(p_data.hImgscale, p_data.hImgscale, sy, sy, sy + p_data.hImgscale, sy + p_data.hImgscale);
            delete hImg;
        }
    }
    else
    {
        delete p_data.hMigCanvas;
        p_data.hMigCanvas = nullptr;
    }
}

double HHBUI::UIWaveRingView::GetBassCurPosition()
{
    if (p_data.hBass == 0 || p_data.hModule == 0)
        return 0;
    auto pFuncChannelGetPosition = (BASS_ChannelGetPosition)DLL_GetSymbol(p_data.hModule, "BASS_ChannelGetPosition");
    QWORD pos_bytes = pFuncChannelGetPosition(p_data.hBass, BASS_POS_BYTE);

    double current_position = static_cast<double>(pos_bytes) / p_data.GetLength * 1000;
    return current_position;
}

void HHBUI::UIWaveRingView::SetBassCurPosition(int position)
{
    if (p_data.hBass == 0 || p_data.hModule == 0)
        return;
    auto pFuncChannelSetPosition = (BASS_ChannelSetPosition)DLL_GetSymbol(p_data.hModule, "BASS_ChannelSetPosition");
    pFuncChannelSetPosition(p_data.hBass, p_data.GetLength / 1000 * position, BASS_POS_BYTE);
}

double HHBUI::UIWaveRingView::GetBassPlayTotalTime()
{
    if (p_data.hBass == 0 || p_data.hModule == 0)
        return 0;
    auto pFuncChannelGetLength = (BASS_ChannelGetLength)DLL_GetSymbol(p_data.hModule, "BASS_ChannelGetLength");
    QWORD pos_GetLength = pFuncChannelGetLength(p_data.hBass, BASS_POS_BYTE);
    auto pFuncChannelBytes2Seconds = (BASS_ChannelBytes2Seconds)DLL_GetSymbol(p_data.hModule, "BASS_ChannelBytes2Seconds");
    return pFuncChannelBytes2Seconds(p_data.hBass, pos_GetLength);
}

double HHBUI::UIWaveRingView::GetBassPlayTime()
{
    if (p_data.hBass == 0 || p_data.hModule == 0)
        return 0;
    auto pFuncChannelGetPosition = (BASS_ChannelGetPosition)DLL_GetSymbol(p_data.hModule, "BASS_ChannelGetPosition");
    QWORD pos_bytes = pFuncChannelGetPosition(p_data.hBass, BASS_POS_BYTE);
    auto pFuncChannelBytes2Seconds = (BASS_ChannelBytes2Seconds)DLL_GetSymbol(p_data.hModule, "BASS_ChannelBytes2Seconds");
    return pFuncChannelBytes2Seconds(p_data.hBass, pos_bytes);
}

void HHBUI::UIWaveRingView::SetBassVolume(float volume)
{
    if (p_data.hBass == 0 || p_data.hModule == 0)
        return;
    auto pFuncChannelSetAttribute = (BASS_ChannelSetAttribute)DLL_GetSymbol(p_data.hModule, "BASS_ChannelSetAttribute");
    pFuncChannelSetAttribute(p_data.hBass, BASS_ATTRIB_VOL, volume);
}

void HHBUI::UIWaveRingView::SetBassVolPan(float volpan)
{
    if (p_data.hBass == 0 || p_data.hModule == 0)
        return;
    auto pFuncChannelSetAttribute = (BASS_ChannelSetAttribute)DLL_GetSymbol(p_data.hModule, "BASS_ChannelSetAttribute");
    pFuncChannelSetAttribute(p_data.hBass, BASS_ATTRIB_PAN, volpan);
}

void HHBUI::UIWaveRingView::SetBassFx(float fInGain)
{
    if (p_data.hBass == 0 || p_data.hModule == 0)
        return;

}

void HHBUI::UIWaveRingView::SetBpm(float fBpm)
{
    p_data.lowbpm = fBpm;
}

float HHBUI::UIWaveRingView::GetBpm()
{
    return p_data.lowbpm;
}

LRESULT HHBUI::UIWaveRingView::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
    if (uMsg == WM_TIMER)
    {
        if (wParam == (size_t)this + 3)
            Redraw();
    }
    else if (uMsg == WM_MOUSEMOVE)
    {
        POINT mpt = { LOWORD(lParam),HIWORD(lParam) };
        int px = (m_data.Frame.right - m_data.Frame.left - p_data.hImgscale) / 2;
        int py = (m_data.Frame.bottom - m_data.Frame.top - p_data.hImgscale) / 2;
        RECT rc = { px ,py ,px + p_data.hImgscale ,py + p_data.hImgscale };
        if (PtInRect(&rc, mpt)) {
            SetState(state_hover, FALSE);
        }
        else
            SetState(state_hover, TRUE);
    }
    else if (uMsg == WM_MOUSELEAVE)
    {
        SetState(state_hover, TRUE);
    }
    else if (uMsg == WM_DESTROY)
    {
        if (p_data.hModule)
        {
            auto pFuncStop = (BASS_ChannelStop)DLL_GetSymbol(p_data.hModule, "BASS_ChannelStop");
            auto pFuncFree = (BASS_StreamFree)DLL_GetSymbol(p_data.hModule, "BASS_StreamFree");
            pFuncStop(p_data.hBass);
            pFuncFree(p_data.hBass);
        }
        p_data.list.clear();
        p_data.lastRadius.clear();
        delete p_data.ColorBrush;
        delete p_data.SolidBrush;
        if (p_data.hCanvas)
            delete p_data.hCanvas;
        if (p_data.hMigCanvas)
            delete p_data.hMigCanvas;
        if (p_data.hImgBrush)
            delete p_data.hImgBrush;
    }
    else if (uMsg == WM_SIZE)
    {
        POINT tmp{};
        tmp.x = GET_X_LPARAM(lParam);
        tmp.y = GET_Y_LPARAM(lParam);
        p_data.angle = 0.f;
        p_data.centerPoint.x = tmp.x / 2.f;
        p_data.centerPoint.y = tmp.y / 2.f;
        if (p_data.hCanvas)
        {
            p_data.hCanvas->Resize(tmp.x, tmp.y);
            resetColor();
        }
    }
    else if (uMsg == WM_SETTEXT)
    {
        /* 计算文本尺寸 */
        UICanvas::CalcTextSize(GetFont(), m_data.pstrTitle, m_data.dwTextFormat,
            m_data.Frame.right - m_data.Frame.left, m_data.Frame.bottom - m_data.Frame.top, &p_data.nTextWidth, &p_data.nTextHeight);

    }
    return S_OK;
}

void HHBUI::UIWaveRingView::OnPaintProc(ps_context ps)
{
    if (p_data.hModule)
    {
        auto pFuncFFTData = (BASS_ChannelGetData)DLL_GetSymbol(p_data.hModule, "BASS_ChannelGetData");
        float m_fft[BASS_SAMPLE_FLOAT];
        INT m_Data = pFuncFFTData(p_data.hBass, m_fft, BASS_DATA_FFT512);
        if (m_Data != -1)
        {
            if (p_data.type == DyCircle)
            {
                EndPaint();
                waveringview_paint(m_fft);
                BeginPaint(ps); //上面缓存画布会清空之前设备 必须从新开始绘制
            }

            switch (p_data.type)
            {
            case DyCircle:
            {
                auto radf = p_data.hImgscale / 2.f;
                INT x = (ps.uWidth - p_data.hImgscale) / 2;
                INT y = (ps.uHeight - p_data.hImgscale) / 2;
                float centerX = x + radf;
                float centerY = y + radf;

                if (p_data.hMigCanvas && p_data.isRotate && (ps.dwState & state_hover) != state_hover)
                {
                    p_data.isDrawText = false;
                    int rotationPerSecond = (p_data.lowbpm / 60) * 360;
                    LONG_PTR dwNewLong = 0;
                    dwNewLong = p_data.angle + (rotationPerSecond / 2.f) / 60.f;
                    p_data.angle = (dwNewLong) % 360;
                    ExMatrix3x2 matrix;
                    matrix.Rotate(p_data.angle, centerX, centerY);
                    float powerPercent = 1.0f;
                    if (p_data.isMove)
                    {
                        //更新估计值和估计误差协方差
                        //kf->x = kf->x + kf->k * (powerPercent - kf->x);
                        //kf->p = (1 - kf->k) * p_;
                        powerPercent += p_data.powerPosition;
                        matrix.Scale(powerPercent, powerPercent, centerX, centerY);
                    }
                    p_data.hMigCanvas->SetTransform(&matrix);
                    p_data.SolidBrush->SetColor(p_data.fxcr);
                    ps.hCanvas->DrawCanvas(p_data.hMigCanvas, x, y, x + p_data.hImgscale, y + p_data.hImgscale, 0.f, 0.f, Blend);

                    ps.hCanvas->DrawShadow(p_data.SolidBrush, x, y, x + p_data.hImgscale, y + p_data.hImgscale, 30.f,
                        radf, radf, radf, radf);
                    p_data.hMigCanvas->SetTransform(0);

                }
                else if (!p_data.isDrawText)
                {
                    p_data.isDrawText = true;
                }
                if (p_data.isfx)
                {
                    ps.hCanvas->DrawBloom(p_data.hCanvas, 0, p_data.fxcr, p_data.isfxBloom, p_data.pMaximumlight, 6.f);
                }
                else
                {
                    ps.hCanvas->DrawCanvas(p_data.hCanvas, 0, 0, ps.uWidth, ps.uHeight, 0.f, 0.f, Blend);
                }
            }
            break;
            case Ripple:
                ripple_paint(m_fft, ps);
                break;
            case RectangleFFT:
                Rectangle_fft(m_fft, ps);
                if (p_data.isfx)
                    ps.hCanvas->DrawBloom(ps.hCanvas, 0, p_data.fxcr, p_data.isfxBloom, p_data.pMaximumlight, 6.f);
                break;
            }
            if (p_data.isRotate && p_data.type == Ripple)
            {
                LONG_PTR dwNewLong = 0;
                dwNewLong = p_data.angle + 2;
                p_data.angle = (dwNewLong) % 360;
                SetRotate(p_data.angle, p_data.isRotate);
            }
        }
        else
        {
            KillTimer();
            p_data.angle = 0.f;
            SetRotate(p_data.angle, FALSE);
            if (!p_data.list.empty())
            {
                p_data.list.clear();
            }
        }
    }
}

void HHBUI::UIWaveRingView::createScope(UIColor pColor, float heightPercent, float leftDistance, float rightDistance)
{
    WaveViewBean siriViewBean{};
    siriViewBean.pColor = pColor;
    p_data.list.emplace_back(siriViewBean);
}

HHBUI::ExPointF HHBUI::UIWaveRingView::calcPoint(int centerX, int centerY, int radius, float angle, ExPointF& point)
{
    point.x = centerX + radius * std::cos(angle * M_PI / 180);
    point.y = centerY + radius * std::sin(angle * M_PI / 180);
    return point;
}

void HHBUI::UIWaveRingView::resetColor()
{
    p_data.list.clear();
    p_data.lastRadius.clear();
    if (p_data.type == DyCircle)
    {
        if (p_data.dstImgwhee)
        {
            if (p_data.ColorBrush)
            {
                delete p_data.ColorBrush;
                p_data.ColorBrush = nullptr;
            }
            UIImage* hImg = nullptr;
            p_data.dstImgwhee->Scale(m_data.Frame.right - m_data.Frame.left, m_data.Frame.bottom - m_data.Frame.top, &hImg);
            p_data.ColorBrush = new UIBrush(hImg, D2D1_EXTEND_MODE_CLAMP);
            delete hImg;
        }
    }
}

float HHBUI::UIWaveRingView::getRandomAngle()
{
    if (p_data.isRandom)
    {
        if (p_data.Randomvalue >= 200)
            return 360.0f;
        else if (p_data.Randomvalue >= 100)
            return 90.0f;
        else if (p_data.Randomvalue >= 80)
            return 180.0f;
        else
            return 270.0f;
        /*
        switch (p_data.randomAngle)
        {
        case 1:
            return 90.0f;
        case 2:
            return 180.0f;
        case 3:
            return 270.0f;
        case 4:
            return 360.0f;
        default:
            return 0.0f;
        }
        */
    }
    return p_data.degress;
}
void HHBUI::UIWaveRingView::initColor()
{
    auto uWidth = UIEngine::ScaleValue(10);
    auto uHeight = UIEngine::ScaleValue(10);
    ID2D1DeviceContext* pDeviceContext = nullptr;
    if (!p_data.dstImgwhee && SUCCEEDED(UIDrawContext::ToList.d2d_device->CreateDeviceContext(D2D1_DEVICE_CONTEXT_OPTIONS_NONE, &pDeviceContext)))
    {
        pDeviceContext->SetUnitMode(D2D1_UNIT_MODE_PIXELS);
        //pDeviceContext->SetAntialiasMode(D2D1_ANTIALIAS_MODE_PER_PRIMITIVE);
        ID2D1Bitmap* pBitmap = nullptr;
        pBitmap = UIDrawContext::CreateBitmap(uWidth, uHeight);

        pDeviceContext->SetTarget(pBitmap);
        pDeviceContext->BeginDraw();

        ID2D1SolidColorBrush* m_brush = nullptr;
        UIDrawContext::ToList.d2d_dc->CreateSolidColorBrush(D2D1::ColorF(0, 0, 0), &m_brush);
        if (m_brush)
        {
            D2D1_RECT_F ff{ 0, 0, uWidth, uHeight };
            ff.right = ff.left + (ff.bottom - ff.top);
            float Resolution = 0.1f;
            float Radius = (ff.right - ff.left) / 2.0f;

            D2D1_POINT_2F Center = { ff.left + (ff.right - ff.left) / 2.0f,
            ff.top + (ff.bottom - ff.top) / 2.0f };
            for (float hue = 0; hue < 360.0f; hue += Resolution)
            {
                for (int sat = 0; sat < (int)Radius; sat++)
                {
                    // Convert S to [0,1]
                    float S = (float)sat / Radius;
                    // Convert H to [0,6]
                    float H = (hue * 6) / 360.0f;

                    float hsl[3] = { H,S,0.5f };
                    float rgb[3] = { 0 };
                    UIColor::HSLtoRGB(hsl, rgb);

                    D2D1_COLOR_F col = { rgb[0],rgb[1],rgb[2],1.0f };

                    m_brush->SetColor(col);

                    // in 360 deg, 2pi
                    // in   hue deg, ?
                    float hue2 = (2 * 3.1415f * hue) / 360.0f;
                    float xPoint = (float)sat;
                    float nX = xPoint * cos(hue2);
                    float nY = -xPoint * sin(hue2);

                    //		    D2D1_POINT_2F p2 = Center;
                    //			p2.x += sat;

                    D2D1_POINT_2F p2 = { nX,nY };
                    p2.x += Center.x;
                    p2.y += Center.y;

                    D2D1_RECT_F r3 = {};
                    r3.left = p2.x;
                    r3.top = p2.y;
                    r3.right = p2.x + 1;
                    r3.bottom = p2.y + 1;

                    pDeviceContext->FillRectangle(r3, m_brush);
                }

            }
            SafeRelease(m_brush);
        }
        pDeviceContext->EndDraw();

        IWICBitmap* pWicBitmap = nullptr;
        UIDrawContext::ToWicBitmap(pBitmap, uWidth, uHeight, &pWicBitmap);
        if (pWicBitmap)
        {
            if (p_data.dstImgwhee)
                delete p_data.dstImgwhee;
            p_data.dstImgwhee = new UIImage(pWicBitmap);
        }
        SafeRelease(pBitmap);
        SafeRelease(pDeviceContext);
    }
}

void HHBUI::UIWaveRingView::setWaveData(float* data)
{
    int maxVa = UIEngine::ScaleValue(p_data.scope);
    int total = BASS_SAMPLE_FLOAT - maxVa;
    if (total < 2)
        total = 2;
    //圆点基准位置半径
    float basePoint = p_data.radius + maxVa - p_data.distance;

    if (p_data.list.size() > 0)
    {
        if (p_data.lastRadius.size() == 0 || p_data.list.size() != p_data.lastRadius.size())
        {
            p_data.lastRadius.clear();
            for (int i = 0; i < total; i++)
            {
                p_data.lastRadius.emplace_back(basePoint);
            }
        }

        for (int i = 0; i < (int)p_data.lastRadius.size(); i++)
        {
            if (i < (int)p_data.list.size())
            {
                if (p_data.list[i].radius < p_data.lastRadius[i])
                {
                    p_data.lastRadius[i] = p_data.list[i].radius;
                }
                else
                {
                    if (p_data.isSpread)
                    {
                        p_data.lastRadius[i] += p_data.speed;
                    }
                    else
                    {
                        p_data.lastRadius[i] = p_data.list[i].radius;
                    }
                }
            }
        }
    }

    p_data.list.clear();
    for (int i = 0; i < total; i++)
    {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dist(10.f, 30.f);
        float m_FFTdata = data[i] * 500.f;
        m_FFTdata = std::min(m_FFTdata, dist(gen));  // 确保不会超出最大半径
        //每个能量在圆环上的角度位置
        float positionAngle = i * 1.0f / total * 360;
        //每个能量的强度百分比
        p_data.powerPercent = p_data.isPowerOffset ? 0.0f : m_FFTdata / BASS_SAMPLE_FLOAT;
        WaveViewBean waveRingViewBean{};

        waveRingViewBean.angle = positionAngle + (p_data.isRotate ? p_data.degress : getRandomAngle());
        waveRingViewBean.powerPercent = m_FFTdata / BASS_SAMPLE_FLOAT;
        waveRingViewBean.radius = basePoint - m_FFTdata - p_data.powerPercent * m_FFTdata - (p_data.distance * p_data.powerPercent);
        calcPoint(p_data.centerPoint.x, p_data.centerPoint.y, (int)(p_data.radius + m_FFTdata) + maxVa, waveRingViewBean.angle, waveRingViewBean.inner);
        calcPoint(p_data.centerPoint.x, p_data.centerPoint.y, (int)(p_data.radius - m_FFTdata - p_data.powerPercent * m_FFTdata) + maxVa, waveRingViewBean.angle, waveRingViewBean.outter);
        calcPoint(p_data.centerPoint.x, p_data.centerPoint.y, p_data.radius + maxVa, waveRingViewBean.angle, waveRingViewBean.center);
        // 计算其他属性并添加到列表
        p_data.list.emplace_back(waveRingViewBean);
    }


    p_data.powerPosition = 0.f;
    if (p_data.isMove)
        p_data.powerPosition = std::min(data[0], 0.1f);
}

void HHBUI::UIWaveRingView::waveringview_paint(float* data)
{
    setWaveData(data);
    p_data.hCanvas->BeginDraw();
    p_data.hCanvas->Clear();
    if (p_data.isRandom)
    {
        p_data.Randomvalue++;
        if (p_data.Randomvalue > 300) {
            p_data.Randomvalue = 0;
        }
    }
    auto total = (int)p_data.list.size();
    ExPointF Points[256];
    for (auto i = 0; i < total; i++) {
        if (p_data.isBase) {
            p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[i].inner.x, p_data.list[i].inner.y, p_data.list[i].outter.x, p_data.list[i].outter.y, 2);
        }

        if (p_data.isPoint) {


            if (i < (int)p_data.lastRadius.size()) {
                calcPoint(p_data.centerPoint.x, p_data.centerPoint.y, (int)p_data.lastRadius[i], p_data.list[i].angle, p_data.list[i].point);
                p_data.hCanvas->DrawPoint(p_data.ColorBrush, p_data.list[i].point.x, p_data.list[i].point.y, 3, TRUE);

                calcPoint(p_data.centerPoint.x, p_data.centerPoint.y, (int)p_data.lastRadius[i] + (p_data.distance / 2), p_data.list[i].angle, p_data.list[i].point);
                Points[i] = p_data.list[i].point;
            }
        }

        if (p_data.isWave) {
            FLOAT strokeWidth = 1.5f;
            FLOAT tension = 2.0f;
            DWORD DashStyle = D2D1_DASH_STYLE_SOLID;
            if (i == 0) {
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[p_data.list.size() - 1].center.x, p_data.list[p_data.list.size() - 1].center.y, p_data.list[0].outter.x, p_data.list[0].outter.y, strokeWidth, DashStyle);
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[0].outter.x, p_data.list[0].outter.y, p_data.list[1].center.x, p_data.list[1].center.y, strokeWidth, DashStyle);
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[1].center.x, p_data.list[1].center.y, p_data.list[0].inner.x, p_data.list[0].inner.y, strokeWidth, DashStyle);
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[0].inner.x, p_data.list[0].inner.y, p_data.list[p_data.list.size() - 1].center.x, p_data.list[p_data.list.size() - 1].center.y, strokeWidth, DashStyle);

            }
            else if (i == p_data.list.size() - 1) {
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[p_data.list.size() - 2].center.x, p_data.list[p_data.list.size() - 2].center.y, p_data.list[p_data.list.size() - 1].outter.x, p_data.list[p_data.list.size() - 1].outter.y, strokeWidth, DashStyle);
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[p_data.list.size() - 1].outter.x, p_data.list[p_data.list.size() - 1].outter.y, p_data.list[0].center.x, p_data.list[0].center.y, strokeWidth, DashStyle);
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[0].center.x, p_data.list[0].center.y, p_data.list[p_data.list.size() - 1].inner.x, p_data.list[p_data.list.size() - 1].inner.y, strokeWidth, DashStyle);
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[p_data.list.size() - 1].inner.x, p_data.list[p_data.list.size() - 1].inner.y, p_data.list[p_data.list.size() - 2].center.x, p_data.list[p_data.list.size() - 2].center.y, strokeWidth, DashStyle);

            }
            else {
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[i - 1].center.x, p_data.list[i - 1].center.y, p_data.list[i].outter.x, p_data.list[i].outter.y, strokeWidth, DashStyle);
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[i].outter.x, p_data.list[i].outter.y, p_data.list[i + 1].center.x, p_data.list[i + 1].center.y, strokeWidth, DashStyle);
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[i + 1].center.x, p_data.list[i + 1].center.y, p_data.list[i].inner.x, p_data.list[i].inner.y, strokeWidth, DashStyle);
                p_data.hCanvas->DrawLine(p_data.ColorBrush, p_data.list[i].inner.x, p_data.list[i].inner.y, p_data.list[i - 1].center.x, p_data.list[i - 1].center.y, strokeWidth, DashStyle);

            }

        }
        if (p_data.isRotate) {
            if (p_data.degress >= 360 || p_data.degress < -360.0f) {
                p_data.degress = 0;
                p_data.nRotatevalue = 0;
            }
            else
            {
                float f2 = data[0] * 100.0f;
                if (f2 > 30.0f) {
                    //高潮反转
                    p_data.degress += -data[i] * 3.1415f;
                    p_data.nRotatevalue += 1;
                }
                else if (p_data.nRotatevalue != 0)
                {
                    p_data.degress += -data[i] * 3.1415f;
                    if (f2 < 10.0f)
                        p_data.nRotatevalue -= 1;
                    if (p_data.nRotatevalue < 0)
                        p_data.nRotatevalue = 0;
                }
                else {
                    p_data.degress += data[i] * 3.1415f;
                }
            }
        }

        /*
        if (p_data.isRotate) {
            if (p_data.degress >= 360) {
                p_data.degress = 0;
            }
            else {
                p_data.degress += 0.07f;
            }
        }
      */
    }
    p_data.hCanvas->DrawCurvesEx(p_data.ColorBrush, Points, total, data[0], TRUE);

    if (p_data.isDrawText && GetText()) {
        ExPointF centerPoint{};
        if (p_data.isMove) {
            calcPoint(p_data.centerPoint.x, p_data.centerPoint.y, static_cast<int>(150 * 1.0f * p_data.list[0].powerPercent), p_data.list[0].angle, centerPoint);
            UIColor crNormal = m_data.Color.crNormal;
            crNormal.SetA(0.3f);
            p_data.hCanvas->DrawTextByColor(GetFont(), GetText(), DT_SINGLELINE | DT_VCENTER | DT_CENTER, centerPoint.x - p_data.nTextWidth * 2, centerPoint.y - p_data.nTextHeight,
                centerPoint.x + p_data.nTextWidth * 2, centerPoint.y + p_data.nTextHeight, crNormal);
        }
        p_data.hCanvas->DrawTextByColor(GetFont(), GetText(), DT_SINGLELINE | DT_VCENTER | DT_CENTER, p_data.centerPoint.x - p_data.nTextWidth * 2, p_data.centerPoint.y - p_data.nTextHeight,
            p_data.centerPoint.x + p_data.nTextWidth * 2, p_data.centerPoint.y + p_data.nTextHeight, m_data.Color.crNormal);

    }
    p_data.hCanvas->EndDraw();
}
void HHBUI::UIWaveRingView::Rectangle_fft(float* data, ps_context ps)
{
    float spectral_data[SPECTRUM_COL]{};
    static float spectral_peak[SPECTRUM_COL]{};		//频谱顶端的高度
    static int fall_count[SPECTRUM_COL];
    for (int i{}; i < BASS_SAMPLE_FLOAT; i++)
    {
        spectral_data[i / (BASS_SAMPLE_FLOAT / SPECTRUM_COL)] += data[i];
    }

    for (int i{}; i < SPECTRUM_COL; i++)
    {
        spectral_data[i] /= (BASS_SAMPLE_FLOAT / SPECTRUM_COL);
        spectral_data[i] = std::sqrtf(spectral_data[i]);		//对每个频谱柱形的值取平方根，以减少不同频率频谱值的差异
        spectral_data[i] *= 60;

        if (spectral_data[i] > spectral_peak[i])
        {
            spectral_peak[i] = spectral_data[i];		//如果当前的频谱比上一次的频谱高，则频谱顶端高度则为当前频谱的高度
            fall_count[i] = 0;
        }
        else if (spectral_data[i] < spectral_peak[i])
        {
            fall_count[i]++;
            float fall_distance = fall_count[i] * (8.18824 / 30 - 0.082353);
            if (fall_distance < 0)
                fall_distance = 0;
            spectral_peak[i] -= fall_distance;		//如果当前频谱比上一次的频谱主低，则频谱顶端的高度逐渐下降
        }
    }
    Rectangle_DrawSpectrum(ps, spectral_data, spectral_peak, ps.rcPaint, 6, 3, SPECTRUM_COL, TRUE, TRUE);
}

void HHBUI::UIWaveRingView::Rectangle_DrawSpectrum(ps_context ps, float* data_spetral, float* data_peak, ExRectF rect, int col_width, int gap_width,
    int cols, bool draw_reflex, bool low_freq_in_center)
{
    Alignment alignment = Alignment::LEFT;
    bool spectrum_low_freq_in_center = low_freq_in_center;  //频谱分析低频部分显示在中间
    int sprctrum_height{ 100 };					//频谱分析高度比例（%）
    auto rc_spectrum_top = rect;
    if (draw_reflex)     //如果要绘制倒影，则倒影占总高度的1/3
        rc_spectrum_top.bottom = rect.top + (rect.Height() * 2 / 3);

    ExRectF rects[SPECTRUM_COL]{};
    rects[0] = rc_spectrum_top;
    rects[0].right = rects[0].left + col_width;

    //频谱的实际宽度
    int width_actrual{ col_width * cols + gap_width * (cols - 1) };
    //如果频谱的实际宽度小于矩形的宽度，则让根据alignment的值让频谱居中或右对齐显示
    if ((width_actrual < rect.Width() && alignment == Alignment::CENTER) || (width_actrual >= rect.Width() && spectrum_low_freq_in_center))
        rects[0].MoveToX(rects[0].left + (rect.Width() - width_actrual) / 2);
    else if (width_actrual < rect.Width() && alignment == Alignment::RIGHT)
        rects[0].MoveToX(rects[0].left + (rect.Width() - width_actrual));

    for (int i{ 1 }; i < cols; i++)
    {
        rects[i] = rects[0];
        rects[i].left += (i * (col_width + gap_width));
        rects[i].right += (i * (col_width + gap_width));
    }
    for (int i{}; i < cols; i++)
    {
        int index;
        if (low_freq_in_center)
        {
            if (i < cols / 2)
                index = (-i + cols / 2) * 2 - 1;
            else
                index = (i - cols / 2) * 2;
        }
        else
        {
            index = i;
        }
        if (index >= cols)
            index = cols;
        if (index < 0)
            index = 0;
        float spetral_data = data_spetral[index * (SPECTRUM_COL / cols)];
        float peak_data = data_peak[index * (SPECTRUM_COL / cols)];

        ExRectF rect_tmp{ rects[i] };
        int spetral_height = static_cast<int>(spetral_data * rects[0].Height() / 30 * sprctrum_height / 100);
        int peak_height = static_cast<int>(peak_data * rects[0].Height() / 30 * sprctrum_height / 100);
        if (spetral_height < 0) spetral_height = 0;		//如果播放出错，不显示频谱
        if (peak_height < 0) peak_height = 0;

        int peak_rect_height = std::max(DPIRound(1.1), gap_width / 2);        //顶端矩形的高度
        spetral_height += peak_rect_height;                                     //频谱至少和顶端矩形一样高
        peak_height += peak_rect_height;

        rect_tmp.top = rect_tmp.bottom - spetral_height;
        if (rect_tmp.top < rects[0].top) rect_tmp.top = rects[0].top;
        p_data.hCanvas->FillRect(p_data.ColorBrush, rect_tmp.left, rect_tmp.top, rect_tmp.right, rect_tmp.bottom);
        //绘制倒影
        if (draw_reflex)
        {
            auto rc_invert = rect_tmp;
            rc_invert.bottom = rect_tmp.top + rect_tmp.Height() * 2 / 3;
            rc_invert.MoveToY(rect_tmp.bottom + gap_width);
            p_data.ColorBrush->SetOpacity(0.2f);
            p_data.hCanvas->FillRect(p_data.ColorBrush, rc_invert.left, rc_invert.top, rc_invert.right, rc_invert.bottom);
            p_data.ColorBrush->SetOpacity(1.f);
        }

        //绘制顶端
        ExRectF rect_peak{ rect_tmp };
        rect_peak.bottom = rect_tmp.bottom - peak_height - gap_width;
        rect_peak.top = rect_peak.bottom - peak_rect_height;
        p_data.hCanvas->FillRect(p_data.ColorBrush, rect_peak.left, rect_peak.top, rect_peak.right, rect_peak.bottom);
    }
}


void HHBUI::UIWaveRingView::ripple_paint(float* data, ps_context ps)
{
    size_t m_FFTdata = std::min(std::abs(data[0] * 400), 10.f);
    size_t rings = m_FFTdata < 5 ? 2 : m_FFTdata;
    // 获取画布的宽高
    int psWidth = m_data.Frame.right - m_data.Frame.left;
    int psHeight = m_data.Frame.bottom - m_data.Frame.top;
    float radius = psWidth / 2.f;
    p_data.ColorBrush->SetOpacity(0.f);

    ImVec2 centre{ psWidth / 2.f,psHeight / 2.f };
    int num_segments = radius;
    const float bg_angle_offset = PI_2 / (num_segments - 1);
    const float koeff = PI_DIV(2 * rings);
    float start = UIEngine::GetUptime() * 0.5f;
    auto circle = [&](const std::function<ImVec2(int)>& point_func, float dbc, float dth) {
        UIPath pathGeometry;
        pathGeometry.BeginPath();
        p_data.ColorBrush->SetOpacity(dbc);

        for (int i = 0; i < num_segments; i++) {
            ImVec2 p = point_func(i);
            if (i == 0) {
                pathGeometry.StartFigure(centre.x + p.x, centre.y + p.y);
            }
            else {
                pathGeometry.LineTo(centre.x + p.x, centre.y + p.y);
            }
        }
        pathGeometry.EndPath();
        ps.hCanvas->DrawPath(p_data.ColorBrush, &pathGeometry, dth);
        };

    for (size_t num_ring = 0; num_ring < rings; ++num_ring) {
        float radius_k = ImSin(ImFmod(start + (num_ring * koeff), PI_DIV_2));
        float radius1 = radius_k * radius;
        float falpha = radius_k > 0.5f ? 2.f - (radius_k * 2.f) : 1.f;

        circle([&](int i) {
            const float a = start + (i * bg_angle_offset);
            return ImVec2(ImCos(a) * radius1, ImSin(a) * radius1);
            }, falpha, 4.f);
    }
    if (p_data.hImgBrush)
    {
        int sy = (psWidth - p_data.hImgscale) / 2;
        ps.hCanvas->FillRoundRect(p_data.hImgBrush, sy, sy, sy + p_data.hImgscale, sy + p_data.hImgscale, p_data.hImgscale / 2.f);

    }
}

int HHBUI::UIWaveRingView::DPIRound(double pixel, double round)
{
    double rtn;
    rtn = static_cast<double>(UIWinApi::ToList.CapsdpiY) * pixel / 96;
    rtn += round;
    return static_cast<int>(rtn);
}


