﻿#include "pch.h"
#include "hotkey.h"
#include <sstream>
HHBUI::UIHotkey::UIHotkey(UIBase *hParent, INT x, INT y, INT width, INT height, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
    InitSubControl(hParent, x, y, width, height, L"form-hotkey", 0, dwStyle, dwStyleEx, nID, dwTextFormat);
}
std::wstring GetKeyName(WORD vk)
{
    std::wstring str;
    switch (vk)
    {
    case VK_ESCAPE:
        str = _T("ESC");
        break;
    case VK_RETURN:
        str = _T("Enter");
        break;
    case VK_UP:
        str = _T("Up");
        break;
    case VK_DOWN:
        str = _T("Down");
        break;
    case VK_LEFT:
        str = _T("Left");
        break;
    case VK_RIGHT:
        str = _T("Right");
        break;
    case VK_HOME:
        str = _T("Home");
        break;
    case VK_END:
        str = _T("End");
        break;
    case VK_PRIOR:
        str = _T("PageUp");
        break;
    case VK_NEXT:
        str = _T("PageDown");
        break;
    case VK_INSERT:
        str = _T("Insert");
        break;
    case VK_SPACE:
        str = _T("Space");
        break;
    case VK_DELETE:
        str = _T("Delete");
        break;
    case VK_PRINT:
        str = _T("Print");
        break;
    default:
        if ((vk >= '0' && vk <= '9') || (vk >= 'A' && vk <= 'Z'))
        {
            std::wstring tchar(1, (TCHAR)vk);
            str = tchar;
        }
        else if (vk >= VK_NUMPAD0 && vk <= VK_NUMPAD9)
        {
            std::wstringstream ss;
            ss << L"Num " << (vk - VK_NUMPAD0);
            str = ss.str();
        }
        else if (vk == VK_MULTIPLY)
            str = _T("Num *");
        else if (vk == VK_ADD)
            str = _T("Num +");
        else if (vk == VK_DECIMAL)
            str = _T("Num Del");
        else if (vk >= VK_F1 && vk <= VK_F12)
        {
            std::wstringstream ss;
            ss << L"F" << (vk - VK_F1 + 1);

            str = ss.str();
        }
        else
        {
            char c = MapVirtualKeyA(vk, 2);
            switch (c)
            {
            case '-':
            case '=':
            case '[':
            case ']':
            case '\\':
            case ';':
            case '\'':
            case ',':
            case '.':
            case '/':
            case '`':
                str += TCHAR(c);
                break;
            }
        }
        break;
    }
    return str;
}
void HHBUI::UIHotkey::SetkeyW(LPCWSTR pszKey)
{
    DWORD dwKey = TranslateAccelKey(pszKey);
    p_data.wModifier = HIWORD(dwKey);
    p_data.wVK = LOWORD(dwKey);
    Redraw();
}

void HHBUI::UIHotkey::SetkeyW(INT vKey, INT wModifier)
{
    p_data.wModifier = wModifier;
    p_data.wVK = vKey;
    Redraw();
}

INT HHBUI::UIHotkey::Getkey()
{
    return p_data.wVK;
}

INT HHBUI::UIHotkey::GetModifier()
{
    return p_data.wModifier;
}

LRESULT HHBUI::UIHotkey::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_CREATE)
	{
		p_data.wInvalidModifier = HotkeyMod_None;
		p_data.wInvalidComb = HotkeyMod_None;
		p_data.bInSetting = FALSE;
	}
    else if (uMsg == WM_LBUTTONDOWN)
    {
        SetFocus();
    }
    else if (uMsg == WM_RBUTTONDOWN)
    {
        KillFocus();
    }
    else if (uMsg == WM_SETFOCUS)
    {
        m_data.pWnd->SetIme(FALSE);
        Redraw();
    }
    else if (uMsg == WM_KILLFOCUS)
    {
        p_data.bInSetting = FALSE;
        Redraw();
    }
    else if (uMsg == WM_KEYDOWN || uMsg == WM_SYSKEYDOWN)
    {
        if (((m_data.dwState & state_focus) == state_focus))
        {
            if (!p_data.bInSetting)
            {
                p_data.bInSetting = TRUE;
                p_data.wVK = 0;
                p_data.wModifier = p_data.wInvalidModifier;
            }
            std::wstring strKey = GetKeyName(wParam);
            if (!strKey.empty())
            {
                p_data.wVK = wParam;
            }
       
            UpdateModifier();
            Redraw();
            return TRUE;
        }
    }
    else if (uMsg == WM_KEYUP)
    {
        if (((m_data.dwState & state_focus) == state_focus))
        {
            if (!p_data.bInSetting)
                return 0;
            if (wParam == p_data.wVK)
            {
                p_data.bInSetting = FALSE;
            }
            else if (p_data.wVK == 0 && (GetKeyState(VK_SHIFT) & 0x8000) == 0 && (GetKeyState(VK_MENU) & 0x8000) == 0 && (GetKeyState(VK_CONTROL) & 0x8000) == 0)
            {
                p_data.bInSetting = FALSE;
                UpdateModifier();
                Redraw();
            }
            else if (wParam == VK_SHIFT || wParam == VK_MENU || wParam == VK_CONTROL)
            {
                UpdateModifier();
                Redraw();
            }
        }
    }
	return S_OK;
}

void HHBUI::UIHotkey::OnPaintProc(ps_context ps)
{
    WORD wModifier = p_data.wModifier;
    WORD wVk = p_data.wVK;
    //output(wVk, wModifier);
    std::wstring str;
    if (wModifier == 0 && wVk == 0)
    {
        str = _T("无");
    }
    else
    {
        if (wModifier & HotkeyMod_Ctrl)
            str = _T("Ctrl+");
        if (wModifier & HotkeyMod_Shift)
            str += _T("Shift+");
        if (wModifier & HotkeyMod_Alt)
            str += _T("Alt+");
        str += GetKeyName(wVk);
    }
    UIColor yColor;
    GetColor(color_text_normal, yColor);

    ps.hCanvas->DrawTextByColor(ps.hFont, str.c_str(), ps.dwTextFormat, ps.rcText.left,
        ps.rcText.top,
        ps.rcText.right,
        ps.rcText.bottom, yColor);

}

void HHBUI::UIHotkey::UpdateModifier()
{
    BOOL bAlt = GetKeyState(VK_MENU) & 0x8000;
    BOOL bCtrl = GetKeyState(VK_CONTROL) & 0x8000;
    BOOL bShift = GetKeyState(VK_SHIFT) & 0x8000;

    WORD wCombKey = 0;
    if (!bAlt && !bCtrl && !bShift)
        wCombKey = HotkeyMod_None;
    else if (bAlt && !bCtrl && !bShift)
        wCombKey = HotkeyMod_Alt;
    else if (!bAlt && bCtrl && !bShift)
        wCombKey = HotkeyMod_Ctrl;
    else if (!bAlt && !bCtrl && bShift)
        wCombKey = HotkeyMod_Shift;
    else if (bAlt && bCtrl && !bShift)
        wCombKey = Mod_CA;
    else if (bAlt && !bCtrl && bShift)
        wCombKey = Mod_SA;
    else if (!bAlt && bCtrl && bShift)
        wCombKey = Mod_SC;
    else
        wCombKey = Mod_SCA;
    if (wCombKey == p_data.wInvalidComb)
        p_data.wModifier = p_data.wInvalidModifier;
    else
        p_data.wModifier = wCombKey;
}

DWORD HHBUI::UIHotkey::TranslateAccelKey(LPCWSTR pszAccelKey)
{
    TCHAR szBuf[101] = { 0 }; //保证字符串结束有两个结束符
    WORD wModifier = HotkeyMod_None;
    WORD wKey = 0;
    int nKeyLen = (int)lstrlenW(pszAccelKey);
    if (nKeyLen >= 100)
        return 0;
    _tcscpy_s(szBuf, pszAccelKey);
    CharLowerBuff(szBuf, nKeyLen);
    TCHAR* nextToken = nullptr;
    LPTSTR pszBuf = szBuf;
    LPTSTR pszKey = _tcstok_s(pszBuf, _T("+"), &nextToken);
    while (pszKey)
    {
        if (_tcscmp(pszKey, _T("ctrl")) == 0)
        {
            wModifier |= HotkeyMod_Ctrl;
        }
        else if (_tcscmp(pszKey, _T("alt")) == 0)
        {
            wModifier |= HotkeyMod_Alt;
        }
        else if (_tcscmp(pszKey, _T("shift")) == 0)
        {
            wModifier |= HotkeyMod_Shift;
        }
        else
        {
            wKey = VkFromString(pszKey);
            break;
        }
        pszBuf += _tcslen(pszKey) + 1;
        pszKey = _tcstok_s(pszBuf, _T("+"), &nextToken);
    }
    return MAKELONG(wKey, wModifier);
}

WORD HHBUI::UIHotkey::VkFromString(LPCWSTR pszKey)
{
    static const std::unordered_map<std::wstring, WORD> keyMap = {
        {L"esc", VK_ESCAPE},
        {L"enter", VK_RETURN},
        {L"up", VK_UP},
        {L"down", VK_DOWN},
        {L"left", VK_LEFT},
        {L"right", VK_RIGHT},
        {L"home", VK_HOME},
        {L"pageup", VK_PRIOR},
        {L"pagedown", VK_NEXT},
        {L"insert", VK_INSERT},
        {L"space", VK_SPACE},
        {L"delete", VK_DELETE},
        {L"print", VK_PRINT}
    };
    std::wstring pszKeyt = pszKey;
    // 直接查询映射表
    auto it = keyMap.find(pszKey);
    if (it != keyMap.end()) {
        return it->second;
    }

    // 处理 F1-F12 键
    if (pszKeyt.length() > 1 && pszKeyt[0] == L'f') {
        int fKey = _wtoi(pszKeyt.substr(1).c_str());
        if (fKey >= 1 && fKey <= 12) {
            return VK_F1 + fKey - 1;
        }
    }

    // 处理数字键盘（num 0-9, num del, num +, num *, num -）
    if (pszKeyt.length() > 4 && pszKeyt.substr(0, 4) == L"num ") {
        std::wstring numPart = pszKeyt.substr(4);
        static const std::unordered_map<std::wstring, WORD> numPadMap = {
            {L"del", VK_DECIMAL},
            {L"*", VK_MULTIPLY},
            {L"+", VK_ADD}
        };

        auto numIt = numPadMap.find(numPart);
        if (numIt != numPadMap.end()) {
            return numIt->second;
        }
        else if (numPart.length() == 1 && numPart[0] >= L'0' && numPart[0] <= L'9') {
            return VK_NUMPAD0 + (numPart[0] - L'0');
        }
    }

    // 处理单个字符（字母和符号）
    if (pszKeyt.length() == 1) {
        wchar_t ch = pszKeyt[0];
        if (ch >= L'a' && ch <= L'z') {
            return ch - L'a' + VK_SPACE;
        }
        else if (ch >= L'A' && ch <= L'Z') {
            return ch;
        }
        else if (ch >= L'0' && ch <= L'9') {
            return ch;
        }

        // 处理特殊符号
        static const std::unordered_map<wchar_t, WORD> symbolMap = {
            {L'-', VK_OEM_MINUS},
            {L'=', VK_OEM_PLUS},
            {L',', VK_OEM_COMMA},
            {L'.', VK_OEM_PERIOD},
            {L';', VK_OEM_1},
            {L'/', VK_OEM_2},
            {L'`', VK_OEM_3},
            {L'[', VK_OEM_4},
            {L'\\', VK_OEM_5},
            {L']', VK_OEM_6},
            {L'\'', VK_OEM_7}
        };

        auto symIt = symbolMap.find(ch);
        if (symIt != symbolMap.end()) {
            return symIt->second;
        }
    }
    return 0;
}



