﻿#pragma once
#include <Uxtheme.h>
#include "font.h"
#include "color.h"
namespace HHBUI
{
	enum ObjStyle
	{
		eos_scroll_controlbutton = 1 << 23,  //滚动条控制按钮
		eos_scroll_amds = 1 << 24,           //自动隐藏滚动条
		eos_scroll_disableno = 1 << 25,      //隐藏滚动条
		eos_nodpiscale = 1 << 26,            //禁止创建时DPI缩放
		eos_disabled = 1 << 27,              //禁止
		eos_hidden = 1 << 28,                //隐藏
		eos_textoffset = 1 << 29,            //文本点击偏移 带文本控件支持
		eos_scroll_v = 1 << 30,              //垂直滚动条
		eos_scroll_h = 1 << 31,              //水平滚动条

	};
	enum ObjStyleEx
	{
		eos_ex_autosize = 1 << 22,               //自适应尺寸
		eos_ex_transparent = 1 << 23,            //鼠标穿透
		eos_ex_bottom = 1 << 24,                 //次底层
		eos_ex_dragdrop = 1 << 25,               //允许拖放 接收来自WM_EX_DROP消息
		eos_ex_acceptfiles = 1 << 26,            //接收文件拖放 必须拥有eos_ex_dragdrop风格 否则无效 接收来自WM_DROPFILES消息
		eos_ex_focusable = 1 << 27,              //允许焦点
		eos_ex_tabstop = 1 << 28,                //允许TAB焦点
		eos_ex_topmost = 1 << 29,                //总在最前
		eos_ex_composited = 1 << 30,             //背景混合
		eos_ex_customdraw = 1 << 31,             //自定义绘制
	};
	enum ObjState
	{
		state_normal = 0,                        //正常
		state_disable = 1,                       //禁止
		state_select = 1 << 1,                   //选择
		state_focus = 1 << 2,                    //焦点
		state_down = 1 << 3,                     //按下
		state_checked = 1 << 4,                  //选中
		state_halfselect = 1 << 5,               //半选中
		state_readonly = 1 << 6,                 //只读
		state_hover = 1 << 7,                    //悬浮
		state_default = 1 << 8,                  //默认
		state_subitem_visiable = 1 << 9,         //子项目_可视
		state_subitem_hidden = 1 << 10,          //子项目_隐藏
		state_busy = 1 << 11,                    //繁忙中
		state_rolling = 1 << 12,                 //滚动中
		state_animating = 1 << 13,               //动画中
		state_hidden = 1 << 14,                  //隐藏
		state_allowsize = 1 << 15,               //允许修改尺寸
		state_allowdrag = 1 << 16,               //允许拖动
		state_allowfocus = 1 << 17,              //允许焦点
		state_allowselect = 1 << 18,             //允许选择
		state_hyperlink_hover = 1 << 19,         //超链接_悬浮
		state_hyperlink_visited = 1 << 20,       //超链接_已访问
		state_allowmultiple = 1 << 21,           //允许多选
		state_password = 1 << 22,                //密码模式
	};
	// 绘制信息结构
	struct ps_context
	{
		UICanvas *hCanvas;  // 	画布
		INT dwStyle;        // 	风格
		INT dwStyleEx;      // 	扩展风格
		INT dwTextFormat;   // 	文本格式
		UIFont *hFont;      // 	字体
		INT dwState;        // 	状态
		UINT uWidth;        // 	宽度
		UINT uHeight;       // 	高度
		ExRectF rcPaint;       // 	绘制矩形
		ExRectF rcText;        // 	文本矩形
		FLOAT dpi; //系统缩放DPI
	};
	// 自定义绘制信息结构
	struct ps_customdraw
	{
		UICanvas *hCanvas; // 	画布句柄
		DWORD dwState;     // 	状态
		DWORD dwStyle;     // 	风格
		INT dwTextFormat;   // 	文本格式
		UIFont *hFont;         // 	字体
		INT iItem;
		LONG_PTR iItemParam;
		ExRectF rcPaint;      // 	绘制矩形
		ExRectF rcText;        // 	文本矩形
		UINT uWidth;        // 	宽度
		UINT uHeight;       // 	高度
		FLOAT dpi; //系统缩放DPI
	};
	enum GetRectFlags
	{
		grt_default,                   //相对位置矩形
		grt_client,                    //客户区矩形
		grt_window,                    //窗口矩形
		grt_dirty,                     //脏区域矩形
		grt_text,                      //文本矩形
		grt_textoff,                   //文本偏移矩形
	};
	enum ColourFlags
	{
		color_background,                                    //背景颜色
		color_border,                                        //边框颜色
		color_focus,                                         //焦点颜色
		color_text_normal,                                   //文本颜色_正常
		color_text_hover,                                    //文本颜色_点燃
		color_text_down,                                     //文本颜色_按下
		color_text_ban,                                      //文本颜色_禁止
		color_text_shadow,                                   //文本颜色_阴影
	};
	// 拖曳信息结构
	struct info_dropinfo
	{
		LPVOID pDataObject; //数据对象指针IDataObject*
		DWORD grfKeyState;  //功能键状态
		INT x;              //鼠标水平位置
		INT y;              //鼠标垂直位置
	};
	class TOAPI UIControl : public UILayout
	{ 
	public:
		UIControl() = default;
		~UIControl();
		/*
		 * @brief 控件基类
		 * @param  hParent   父控件
		 * @param  x,y,width,height  坐标尺寸
		 * @param  lpClsname  控件类名
		 * @param  lpszName  控件标题
		 * @param  dwStyle  控件风格(参考ObjStyle)
		 * @param  dwStyleEx  控件扩展风格(参考ObjStyleEx)
		 * @param  nID 控件ID标识(建议1000开始)
		 * @param  dwTextFormat 文本格式(参考TextFormat) -1默认（水平居中.垂直居中.单行）
		 */
		BOOL InitSubControl(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpClsname, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx,
			INT nID, INT dwTextFormat = -1, LPARAM lParam = 0, MsgPROC pfnCallback = NULL);
		//移动控件(需要保持默认位置为CW_USEDEFAULT)及层次关系
		void Move(INT x, INT y, INT width = CW_USEDEFAULT, INT height = CW_USEDEFAULT, BOOL bRepaint = false, BOOL bNodpiscale = false);
		void SetPos(INT x, INT y, INT width, INT height, HWND hObjInsertAfter = HWND_TOP, INT flags = SWP_NOZORDER | SWP_NOACTIVATE | SWP_NOOWNERZORDER);
		//重画
		void Redraw(ExRectF lprcRedraw = {});
		//开始绘制 如果启用自定义绘制背景WM_ERASEBKGND返回为S_FALSE时此处会返回TRUE拦截内部绘制
		BOOL BeginPaint(ps_context& lpPS);
		//结束绘制 不管开始绘制返回什么状态都该拥有此命令结束绘制
		void EndPaint();
		//可视
		void Show(BOOL fShow);
		//是否可视
		BOOL IsVisible();
		//禁止或启用
		void Enable(BOOL fEnable);
		//是否启用
		BOOL IsEnable();
		//设置焦点
		BOOL SetFocus();
		//获取焦点控件
		LPVOID GetFocus();
		//销毁焦点
		void KillFocus();
		//设置控件文本.
		BOOL SetText(LPCWSTR lpString);
		 /*获取控件文本*/
		LPCWSTR GetText();
		//设置文本格式(参考TextFormat).返回原文本格式
		DWORD SetTextFormat(DWORD dwTextFormat, BOOL bRedraw = false);
		//获取文本格式
		DWORD GetTextFormat();
		//设置控件文本偏移内边距(需要保持默认位置为CW_USEDEFAULT)
		void SetPadding(INT left, INT top, INT right = CW_USEDEFAULT, INT bottom = CW_USEDEFAULT, BOOL fRedraw = false);
		//偏移客户区矩形(需要保持默认位置为CW_USEDEFAULT)
		void SetPaddingClient(INT left, INT top, INT right = CW_USEDEFAULT, INT bottom = CW_USEDEFAULT, BOOL fRedraw = false);
		 /*
		 * @brief 设置提示文本(设置后鼠标悬浮在上面才会显示)
		 * @param  lpText   内容
		 * @param  crBlack  背景颜色
		 * @param  crText   文本颜色
		 */
		void SetTipsText(LPCWSTR lpText, UIColor crBlack = {}, UIColor crText = {});
		/*
		 * @brief 弹出或关闭提示文本(全部参数为默认缺省值则关闭)
		 * @param  lpText   内容
		 * @param  crBlack  背景颜色  
		 * @param  crText   文本颜色  
		 * @param  x,y      显示位置坐标  
		 * @param  dwTime   显示时间(默认500毫秒)  
		 * @param  fShow    是否立即显示  
		 */
		void TooltipsPop(LPCWSTR lpText = 0, UIColor crBlack = {}, UIColor crText = {}, INT x = -1, INT y = -1, INT dwTime = -1, BOOL fShow = true);
		//分发事件和消息
		LRESULT DispatchNotify(INT nCode, WPARAM wParam, LPARAM lParam);
		//发送消息
		LRESULT SendMsg(INT uMsg, WPARAM wParam = 0, LPARAM lParam = 0);
		//发送消息回调 当控件有消息回调时可以发送事件用于接收
		LRESULT SendMsgProc(INT uMsg, WPARAM wParam = 0, LPARAM lParam = 0);

		//设置事件回调
		BOOL SetEvent(INT nEvent, EventHandlerPROC pfnCallback);
		//设置消息回调
		void SetMsgProc(MsgPROC pfnCallback);
		//设置控件消息转发
		void SetMsgCls(ClsPROC pfnClsProc);
		//设置控件状态,用于更新控件界面显示状态
		void SetState(DWORD dwState = state_normal, BOOL fRemove = false, ExRectF lprcRedraw = {});
		//获取控件状态
		INT GetState();
		//获取矩形
		BOOL GetRect(ExRectF& lpRect, INT nType = grt_default, BOOL fScale = false);
		/*
         * @brief 获取特定关系的控件
         * @param  nCmd  GW_开头
         * @return [LPVOID]返回对象
         */
		LPVOID GetNode(INT nCmd);
		//获取控件相关颜色
		void GetColor(INT nIndex, UIColor& rel);
		//设置控件相关颜色
		BOOL SetColor(INT nIndex, UIColor dwColor, BOOL fRedraw = false);
		//取界面句柄
		UIWnd* GetUIWnd();
		//取窗口句柄
		HWND GethWnd();
		//设置父控件
		BOOL SetParent(UIBase *hParent);
		/*
		* @brief 取父控件
		* @param tParent 是否取本类 默认取父类
		*/
		UIBase *GetParent(BOOL tParent = false);
		//取父控件界面句柄
		UIWnd* GetParentUIWnd();
		//取控件类名
		LPCWSTR GetlClassName();
		//设置四方圆角度
		void SetRadius(FLOAT topleft, FLOAT topright, FLOAT bottomright, FLOAT bottomleft, BOOL fUpdate = false);
		//设置字体信息
		BOOL SetFontFromFamily(LPCWSTR lpszFontfamily, INT dwFontsize = 0, INT dwFontstyle = FONT_STYLE_NORMAL, BOOL fRedraw = false);
		BOOL SetFontLogFont(LOGFONTW* Fontinfo, BOOL fRedraw = false);
		//获取字体句柄
		UIFont* GetFont();
		//设置背景图像(会优先级于背景颜色)
		BOOL SetBackgImage(LPVOID lpImage, size_t dwImageLen, INT x = 0, INT y = 0, DWORD dwRepeat = bir_default, RECT* lpGrid = 0, INT dwFlags = bif_default, DWORD dwAlpha = 255);
		BOOL SetBackgImage(UIImage *hImage, BOOL fReset = false, INT x = 0, INT y = 0, DWORD dwRepeat = bir_default, RECT* lpGrid = 0, INT dwFlags = bif_default, DWORD dwAlpha = 255);
		BOOL SetBackgImage(LPCWSTR lpImagefile, INT x = 0, INT y = 0, DWORD dwRepeat = bir_default, RECT* lpGrid = 0, INT dwFlags = bif_default, DWORD dwAlpha = 255);
		//更新背景信息
		void SetBackgInfo(INT x = 0, INT y = 0, RECT* lpGrid = 0, INT dwFlags = bif_default, DWORD dwAlpha = 255);
		//设置背景播放.
		BOOL SetBackgPlay(BOOL fPlayFrames, BOOL fResetFrame = false);
		/*
		 * @brief 设置模糊
		 * @param  fDeviation          [FLOAT]        模糊系数
		 * @param  fmodesoft           [BOOL]         是否硬边模糊
		 */
		void SetBlur(FLOAT fDeviation, BOOL fmodesoft = false, BOOL bRedraw = false);
		/*设置计时器  uTimeout - 定时器循环周期 毫秒为单位*/
		BOOL SetTimer(size_t uTimerID, UINT nElapse);
		/*销毁计时器*/
		BOOL KillTimer(size_t uTimerID);
		BOOL KillTimer();
		//设置透明度
		void SetAlpha(INT fAlpha);
		INT GetAlpha();
		//设置ID 建议1000开始
		void SetID(INT nID);
		//获取ID
		INT GetID();
		//取附加参数
		LPARAM GetlParam();
		//置附加参数
		void SetlParam(LPARAM dwlParam);
		/*
		 * @brief 设置和获取风格
		 * @param  Style   [INT]    (参考ObjStyle)、参数为0获取
		 * @return [INT]如果Style为0返回当前风格 否则返回0
		 */
		INT Style(INT dwStyle = 0);
		/*
		 * @brief 设置和获取扩展风格
		 * @param  dwStyleEx   [INT]    (参考ObjStyleEx)、参数为0获取
		 * @return [INT]如果dwStyleEx为0返回当前风格 否则返回0
		 */
		INT StyleEx(INT dwStyleEx = 0);
		//获取滚动条信息
		BOOL GetScrollInfo(BOOL bHScroll, INT& lpnMin, INT& lpnMax, INT& lpnPos, INT& lpnTrackPos);
		//设置滚动条信息
		INT SetScrollInfo(BOOL bHScroll, INT Mask, INT nMin, INT nMax, INT nPage, INT nPos, BOOL bRedraw = false);
		//获取滚动条位置
		INT GetScrollPos(BOOL bHScroll);
		//设置滚动条位置
		INT SetScrollPos(BOOL bHScroll, INT nPos, BOOL bRedraw = false);
		//设置滚动条范围
		INT SetScrollRange(BOOL bHScroll, INT nMin, INT nMax, BOOL bRedraw = false);
		//获取滚动条范围
		BOOL GetScrollRange(BOOL bHScroll, INT& lpnMinPos, INT& lpnMaxPos);
		//获取滚动条拖动位置
		INT GetScrollTrackPos(BOOL bHScroll);
		//显示/隐藏滚动条
		void SetScrollShow(BOOL bHScroll, BOOL fShow = false);
		/*
		* @brief 禁用/启用滚动条 https://learn.microsoft.com/zh-cn/windows/win32/api/winuser/nf-winuser-enablescrollbar
		* @param wSB 指定滚动条类型：参考SB_开头
		* @param wArrows 指定是启用还是禁用滚动条箭头，并指示启用或禁用哪些箭头 参考ESB_DISABLE_开头
		*/
		void SetScrollEnable(INT wSB = SB_BOTH, INT wArrows = ESB_DISABLE_BOTH);
		/*
		 * @brief 设置滚动条颜色
		 * @param  color_normal  默认
		 * @param  color_hover  点燃
		 * @param  color_down  按下
		 * @param  color_btn_up  上调节按钮
		 * @param  color_btn_down  下调节按钮
		*/
		void SetScrollColor(UIColor color_normal, UIColor color_hover, UIColor color_down, UIColor color_btn_up = {}, UIColor color_btn_down = {}, BOOL bRedraw = false);
		//开启或关闭滚动条圆角风格
		void SetScrollRadius(BOOL bRadius);
		/*
         * @brief 滚动条消息处理
         * @param  nLine  滚动距离 默认40
         * @return [INT]返回滚动值
        */
		INT PostScrollMsg(INT uMsg, WPARAM wParam, LPARAM lParam, INT nLine = 0);
		//设置光标文件 游标必须由 CreateCursor 或 CreateIconIndirect 函数创建，或者由 LoadCursor 或 LoadImage 函数加载。
		void SetCursor(HCURSOR fCursor);
		//设置光标类型 系统游标的 IDC_前缀 https://learn.microsoft.com/zh-cn/windows/win32/menurc/about-cursors
		void SetCursor(LPCTSTR lpCursorName);
		//保存为图像 注意：如果控件不可见或未绘制状态需要打开cdraw
		HRESULT ToImage(UIImage** dstImg, BOOL cdraw = false, FLOAT fBlur = 0.f);
		//查询拖放信息格式 当拥有eos_ex_dragdrop风格时触发 WM_EX_DROP消息中使用 查询格式默认CF_UNICODETEXT
		HRESULT CheckDropFormat(LPVOID pDataObject, DWORD dwFormat = 0);
		//查询拖放文本内容 当拥有eos_ex_dragdrop风格时触发 WM_EX_DROP消息中使用
		INT CheckDropString(LPVOID pDataObject, LPWSTR lpwzBuffer, INT cchMaxLength);
		//查询拖放文件数量 当拥有eos_ex_acceptfiles风格时触发 WM_DROPFILES消息中使用
		UINT CheckDropFileNumber(WPARAM wParam);
		//查询拖放文件内容 需要先查询拖放文件数量 当拥有eos_ex_acceptfiles风格时触发 WM_DROPFILES消息中使用
		UINT CheckDragQueryFile(WPARAM wParam, UINT iFile, LPWSTR lpszFile, UINT cch);
		//锁定控件位置 不需要锁定的参数为-1
		BOOL Lock(INT Left, INT Top, INT Right, INT Bottom, INT Width = -1, INT Height = -1);
		//查找控件、可以是类名(或ID)
		LPVOID FindUIView(LPCWSTR lpName);
		/*获取子控件数量*/
		INT GetChildrenCout() const;
		/*获取子控件*/
		LPVOID GetChildren(INT index) const;
		//旋转控件 0-360
		void SetRotate(FLOAT fRotate, BOOL pStart);


		
	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) { return S_OK; };
		EXMETHOD void OnPaintProc(ps_context ps) {};
		LRESULT OnBaseProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam);
		void OnBrothers(INT uMsg, WPARAM wParam, LPARAM lParam, BOOL bBypassSelf, BOOL bSameClass);
		struct objColor
		{
			UIColor crBackground,crBorder,crNormal,crHover,crDown,crFocus,crBan,crShadow,crBlack_pstrTips,crText_pstrTips;
		};
		//控件数据
		struct ControlData
		{
			//组件相对、客户区、组件窗口、脏区域、文本矩形位置
			ExRectF Frame{},Frame_c{},Frame_w{},Frame_d{},Frame_t{};
			INT dwState = 0, dwStyle = -1, dwStyleEx = -1, nID = 0, alpha = 0, dwFlags = 0;
			DWORD dwTextFormat = 0;
			UIWnd* pWnd = nullptr;
			UIBase *Parent = nullptr;//父控件
			HCURSOR hCursor = 0;
			FLOAT fBlur = 0.f, fRotate = 0.f;
			BOOL fmodesoft = false, ScaleWindow = false, IsRotate = false;
			ExRectF radius = {};
			UIFont *hFont = nullptr;
			UIBrush* hPathBrush = nullptr, * lpBackgBrush = nullptr;
			UIPath* hPath_Window = nullptr,* hPath_Client = nullptr;
			UICanvas* canvas = nullptr;
			UIControl* objNext = nullptr, * objPrev = nullptr, * objVScroll = nullptr, * objHScroll = nullptr;
			LPCWSTR pstrTitle = nullptr, pstrTips = nullptr, lpClsname = nullptr;
			MINMAXINFO *minmax = nullptr;
			objColor Color;
			LPARAM lParam = 0;
			LPVOID lpIDropTarget = nullptr;
			ClsPROC pfnClsProc = NULL;
			MsgPROC pfnSubClass = NULL;
			info_backgroundimage* lpBackgroundImage = nullptr;
		}m_data;
	private:
		INT obj_wm_nchittest(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam);
		void obj_zset(HWND hObjInsertAfter);
		void obj_zclear(UIControl*& hParent, UIBase* &pParent);
		void obj_setzbeforet(UIControl* objChildLast, UIBase* pParent);
		BOOL obj_autosize(INT* width, INT* height);
		void obj_update(BOOL fUpdateWindow);
		BOOL obj_makeupinvalidaterect(ExRectF prc);
		BOOL obj_zcompositedcheck(ExRectF prc, UIControl* objLast, UIControl* objStop, ExRectF lpsrcInsert);
		void obj_updatewindowpostion(BOOL fChild);
		void obj_setchildrenpostion(UIControl* pObjChild, INT x, INT y);
		void obj_regionalpath(UIControl* pObjChild, FLOAT left, FLOAT top, FLOAT right, FLOAT bottom, UIPath*& dpath);
		void obj_tippopup(LPCWSTR lpText, INT x, INT y, INT dwTime, BOOL fShow);
		void obj_scrollrepostion(BOOL fDispatch);
		void obj_scrollup(BOOL bVScroll, INT cLeft, INT cTop, INT cRight, INT cBottom, BOOL fDispatch);
		BOOL obj_setbackgImage(UIImage *hImg, INT x, INT y, DWORD dwRepeat, RECT* lpGrid, INT dwFlags, DWORD dwAlpha);
		void obj_getcontext(ps_context& lpPS);
		BOOL obj_setfont(UIFont *hFont, BOOL fRedraw);
		friend class DropTarget;
		friend class UIWnd;
		friend class UICanvas;
		friend class UILayout;
		friend class UIhook;
		friend class UIScroll;
		friend class UIAnimation;
	};
}