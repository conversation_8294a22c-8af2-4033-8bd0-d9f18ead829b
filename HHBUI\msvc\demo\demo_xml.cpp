﻿#include "demo.h"
using namespace HHBUI;
void Demo_xml()
{
	std::wstring GetVersion = std::wstring(UIEngine::GetVersion());
	std::wstring xml = LR"(
<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<UIWnd Frame="0,0,862,600" Title="xml_load demo )" + GetVersion + LR"(" Title-Format="5" Class-Name="" Bkg-Image="icons\IMG_1236.JPG" Bkg-Color="253, 253, 255, 255"
 Frame-Color="20, 126, 255, 255" Shadow-Color="20, 126, 255, 155" Title-Color="255, 255, 255, 255" Title-Bkg-Color="16, 124, 16, 155" Style=""
 StyleEx="" UIStyle="232839" Layout="6" Title-BtnNormal-Color="255, 255, 255, 255" Title-BtnHot-Color="255, 255, 255, 255" Title-BtnDown-Color="255, 255, 255, 255" Blur="3.5" Radius="10" Alpha="">

 <UIStatic ID="1000" Frame="50,50,500,500" Style="0" StyleEx="0" Title-Format="-1" OnMessage=""  Layout="-1" Layout-value="10%,-1,10%,-1" Bkg-Color="88,124,141,255">
	<UICheck ID="1003" Title="单选框5" Frame="50,64,80,20" Style="0" StyleEx="0" Title-Format="-1" OnMessage=""/>
	<form-edit ID="1005" Title="编辑框" Frame="251,143,196,117" Style="1073741824" StyleEx="1207959552" Title-Format="4" tips="我是一个多行编辑框" Layout="5" Layout-rightof="1000"/>
	<form-combobox ID="1002" Title="组合框" Frame="50,164,103,37" Style="0" StyleEx="0" Title-Format="-1" fnmsg="-99" OnMessage="debug_obj_event"/>
	 <UIStatic ID="1006" Frame="0,400,500,100" Style="0" StyleEx="0" Title-Format="-1" OnMessage="" Bkg-Color="64,157,254,255">
	<UICheck ID="1007" Title="单选框7" Frame="10,14,80,20" Style="0" StyleEx="0" Title-Format="-1" OnMessage="" Text-Color-Normal="255, 255, 255, 255"/>
	<UICheck ID="1018" Title="单选框8" Frame="10,40,80,20" Style="0" StyleEx="0" Title-Format="-1" OnMessage="" Text-Color-Normal="255, 255, 255, 255"/>
	
	<UIStatic ID="1008" Frame="200,0,200,100" Style="0" StyleEx="0" Title-Format="-1" OnMessage="" Bkg-Color="#19caad">
	<UICheck ID="1009" Title="单选框9" Frame="10,14,80,20" Style="0" StyleEx="0" Title-Format="-1" OnMessage="" Text-Color-Normal="255, 255, 255, 255"/>
	<UICheck ID="1010" Title="单选框10" Frame="10,40,80,20" Style="0" StyleEx="0" Title-Format="-1" OnMessage="" Text-Color-Normal="255, 255, 255, 255"/>
     </UIStatic>
	 
     </UIStatic>
 </UIStatic>
	
<form-edit ID="1001" cuebanner="编辑框" Frame="600,143,196,117" Style="1073741824" StyleEx="1207959552" Title-Format="4" tips="我是一个多行编辑框" Layout-right="10"/>

<UICheck ID="1004" Title="单选框" Frame="50,64,80,20" Style="0" StyleEx="0" Title-Format="-1" fontsize="16" fontStyle="8" Text-Color-Normal="255,255,255,255" Text-Color-Hover="255,255,255,255" Bkg-Color="30,30,30,100" radius="15,5,5,15" tips="我是一个圆形单选框"  Layout-right="10"/>

</UIWnd>
	)";

	auto window = new UIWnd();
	window->CreateUIFromXML(0, nullptr, xml.c_str());

	window->Show();
	//window->MessageLoop();
}