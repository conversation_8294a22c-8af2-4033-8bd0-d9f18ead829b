﻿#include "pch.h"
#include "button.h"
#include <common/winapi.h>
#include <common/Exception.h>

HHBUI::UIButton::UIButton(UIBase *hParent, INT x, INT y, INT width, INT height, LPCWSTR lpszName, INT dwStyle, INT dwStyleEx, INT nID, INT dwTextFormat)
{
	InitSubControl(hParent, x, y, width, height, L"form-button", lpszName, (dwStyle == 0 ? eos_textoffset : dwStyle), dwStyleEx, nID, dwTextFormat);
	SetStyle(info_button_type::fill, normal);
	p_data.hBrush = new UIBrush();
}
void HHBUI::UIButton::SetStyle(info_button_type type, info_button_style style)
{
	if (type >= 0 && type <= nobkg) p_data.type = type;

	if (style >= 0 && style <= nostyle) {
		p_data.style = style;
		switch (style)
		{
		case normal:
			p_data.config.crBkg[0] = UIColor(253, 253, 253, 255);
			p_data.config.crBkg[1] = UIColor(164, 204, 253, 255);
			p_data.config.crBkg[2] = UIColor(142, 176, 217, 255);
			p_data.config.crBorder[0] = UIColor(189, 189, 191, 255);
			p_data.config.crBorder[1] = UIColor(0, 108, 190, 255);
			p_data.config.crBorder[2] = UIColor(20, 126, 255, 255);
			p_data.config.crText[0] = UIColor(89, 89, 91, 255);
			p_data.config.crText[1] = UIColor(20, 126, 255, 255);
			p_data.config.crText[2] = UIColor(19, 116, 234, 255);
			break;
		case primary:
			if (p_data.type == plain) {
				p_data.config.crBkg[0] = UIColor(236, 245, 255, 255);
				p_data.config.crBkg[1] = UIColor(20, 126, 255, 155);
				p_data.config.crBkg[2] = UIColor(19, 115, 232, 255);
				p_data.config.crBorder[0] = UIColor(20, 126, 255, 255);
				p_data.config.crBorder[1] = UIColor(20, 126, 255, 255);
				p_data.config.crBorder[2] = UIColor(19, 115, 232, 255);
				p_data.config.crText[0] = UIColor(20, 126, 255, 255);
				p_data.config.crText[1] = UIColor(253, 253, 255, 255);
				p_data.config.crText[2] = UIColor(239, 239, 241, 255);
			}
			else {
				p_data.config.crBkg[0] = UIColor(20, 126, 255, 255);
				p_data.config.crBkg[1] = UIColor(68, 152, 254, 255);
				p_data.config.crBkg[2] = UIColor(19, 115, 232, 255);
				p_data.config.crBorder[0] = UIColor(20, 126, 255, 255);
				p_data.config.crBorder[1] = UIColor(20, 126, 255, 255);
				p_data.config.crBorder[2] = UIColor(20, 126, 255, 255);
				p_data.config.crText[0] = UIColor(253, 253, 255, 255);
				p_data.config.crText[1] = UIColor(253, 253, 255, 255);
				p_data.config.crText[2] = UIColor(239, 239, 241, 255);
			}
			break;
		case success:
			if (p_data.type == plain) {
				p_data.config.crBkg[0] = UIColor(240, 249, 235, 255);
				p_data.config.crBkg[1] = UIColor(103, 194, 58, 155);
				p_data.config.crBkg[2] = UIColor(82, 155, 46, 255);
				p_data.config.crBorder[0] = UIColor(103, 194, 58, 255);
				p_data.config.crBorder[1] = UIColor(103, 194, 58, 255);
				p_data.config.crBorder[2] = UIColor(82, 155, 46, 255);
				p_data.config.crText[0] = UIColor(103, 194, 58, 255);
				p_data.config.crText[1] = UIColor(253, 253, 255, 255);
				p_data.config.crText[2] = UIColor(239, 239, 241, 255);
			}
			else {
				p_data.config.crBkg[0] = UIColor(103, 194, 58, 255);
				p_data.config.crBkg[1] = UIColor(149, 212, 117, 255);
				p_data.config.crBkg[2] = UIColor(82, 155, 46, 255);
				p_data.config.crBorder[0] = UIColor(103, 194, 58, 255);
				p_data.config.crBorder[1] = UIColor(103, 194, 58, 255);
				p_data.config.crBorder[2] = UIColor(103, 194, 58, 255);
				p_data.config.crText[0] = UIColor(253, 253, 255, 255);
				p_data.config.crText[1] = UIColor(253, 253, 255, 255);
				p_data.config.crText[2] = UIColor(239, 239, 241, 255);
			}
			break;
		case info:
			if (p_data.type == plain) {
				p_data.config.crBkg[0] = UIColor(244, 244, 245, 255);
				p_data.config.crBkg[1] = UIColor(144, 147, 153, 155);
				p_data.config.crBkg[2] = UIColor(115, 118, 122, 255);
				p_data.config.crBorder[0] = UIColor(144, 147, 153, 255);
				p_data.config.crBorder[1] = UIColor(144, 147, 153, 255);
				p_data.config.crBorder[2] = UIColor(115, 118, 122, 255);
				p_data.config.crText[0] = UIColor(144, 147, 153, 255);
				p_data.config.crText[1] = UIColor(253, 253, 255, 255);
				p_data.config.crText[2] = UIColor(239, 239, 241, 255);
			}
			else {
				p_data.config.crBkg[0] = UIColor(144, 147, 153, 255);
				p_data.config.crBkg[1] = UIColor(177, 179, 184, 255);
				p_data.config.crBkg[2] = UIColor(115, 118, 122, 255);
				p_data.config.crBorder[0] = UIColor(144, 147, 153, 255);
				p_data.config.crBorder[1] = UIColor(144, 147, 153, 255);
				p_data.config.crBorder[2] = UIColor(144, 147, 153, 255);
				p_data.config.crText[0] = UIColor(253, 253, 255, 255);
				p_data.config.crText[1] = UIColor(253, 253, 255, 255);
				p_data.config.crText[2] = UIColor(239, 239, 241, 255);
			}
			break;
		case warning:
			if (p_data.type == plain) {
				p_data.config.crBkg[0] = UIColor(253, 246, 236, 255);
				p_data.config.crBkg[1] = UIColor(230, 162, 60, 155);
				p_data.config.crBkg[2] = UIColor(184, 130, 48, 255);
				p_data.config.crBorder[0] = UIColor(230, 162, 60, 255);
				p_data.config.crBorder[1] = UIColor(230, 162, 60, 255);
				p_data.config.crBorder[2] = UIColor(184, 130, 48, 255);
				p_data.config.crText[0] = UIColor(230, 162, 60, 255);
				p_data.config.crText[1] = UIColor(253, 253, 255, 255);
				p_data.config.crText[2] = UIColor(239, 239, 241, 255);
			}
			else {
				p_data.config.crBkg[0] = UIColor(230, 162, 60, 255);
				p_data.config.crBkg[1] = UIColor(238, 190, 119, 255);
				p_data.config.crBkg[2] = UIColor(184, 130, 48, 255);
				p_data.config.crBorder[0] = UIColor(230, 162, 60, 255);
				p_data.config.crBorder[1] = UIColor(230, 162, 60, 255);
				p_data.config.crBorder[2] = UIColor(230, 162, 60, 255);
				p_data.config.crText[0] = UIColor(253, 253, 255, 255);
				p_data.config.crText[1] = UIColor(253, 253, 255, 255);
				p_data.config.crText[2] = UIColor(239, 239, 241, 255);
			}
			break;
		case danger:
			if (p_data.type == plain) {
				p_data.config.crBkg[0] = UIColor(254, 240, 240, 255);
				p_data.config.crBkg[1] = UIColor(245, 108, 108, 155);
				p_data.config.crBkg[2] = UIColor(184, 130, 48, 255);
				p_data.config.crBorder[0] = UIColor(245, 108, 108, 255);
				p_data.config.crBorder[1] = UIColor(245, 108, 108, 255);
				p_data.config.crBorder[2] = UIColor(196, 86, 86, 255);
				p_data.config.crText[0] = UIColor(245, 108, 108, 255);
				p_data.config.crText[1] = UIColor(253, 253, 255, 255);
				p_data.config.crText[2] = UIColor(239, 239, 241, 255);
			}
			else {
				p_data.config.crBkg[0] = UIColor(245, 108, 108, 255);
				p_data.config.crBkg[1] = UIColor(248, 152, 152, 255);
				p_data.config.crBkg[2] = UIColor(196, 86, 86, 255);
				p_data.config.crBorder[0] = UIColor(245, 108, 108, 255);
				p_data.config.crBorder[1] = UIColor(245, 108, 108, 255);
				p_data.config.crBorder[2] = UIColor(245, 108, 108, 255);
				p_data.config.crText[0] = UIColor(253, 253, 255, 255);
				p_data.config.crText[1] = UIColor(253, 253, 255, 255);
				p_data.config.crText[2] = UIColor(239, 239, 241, 255);
			}
			break;
		}
	}
}
void HHBUI::UIButton::SetCrText(UIColor normal, UIColor hover, UIColor down)
{
	if (!normal.empty())
		p_data.config.crText[0] = normal;
	if (!hover.empty())
		p_data.config.crText[1] = hover;
	if (!down.empty())
		p_data.config.crText[2] = down;
}
void HHBUI::UIButton::SetCrBorder(UIColor normal, UIColor hover, UIColor down)
{
	if (!normal.empty())
		p_data.config.crBorder[0] = normal;
	if (!hover.empty())
		p_data.config.crBorder[1] = hover;
	if (!down.empty())
		p_data.config.crBorder[2] = down;
}

void HHBUI::UIButton::SetCrBkg(UIColor normal, UIColor hover, UIColor down)
{
	if (!normal.empty())
		p_data.config.crBkg[0] = normal;
	if (!hover.empty())
		p_data.config.crBkg[1] = hover;
	if (!down.empty())
		p_data.config.crBkg[2] = down;
}

void HHBUI::UIButton::SetImgBkg(UIImage *normal, UIImage *hover, UIImage *down)
{
	if (normal)
	{
		if (p_data.config.imgBkg[0])
			delete p_data.config.imgBkg[0];
		p_data.config.imgBkg[0] = normal;
	}
	if (hover)
	{
		if (p_data.config.imgBkg[1])
			delete p_data.config.imgBkg[1];
		p_data.config.imgBkg[1] = hover;
	}
	if (down)
	{
		if (p_data.config.imgBkg[2])
			delete p_data.config.imgBkg[2];
		p_data.config.imgBkg[2] = down;
	}
}

void HHBUI::UIButton::SetRadius(FLOAT fRadius)
{
	p_data.rad = fRadius;
}

void HHBUI::UIButton::SetIcon(UIImage *fIcon, INT fIconPosition)
{
	if (p_data.Icon)
		delete p_data.Icon;
	p_data.Icon = NULL;
	p_data.iconH = p_data.iconW = 0;
	p_data.IconPosition = fIconPosition;
	if (fIcon) {
		p_data.Icon = fIcon;
		ExRectF rc{};
		GetRect(rc);
		SIZE bs{ (LONG)(rc.right - rc.left),(LONG)(rc.bottom - rc.top) };
		UINT icw = 0, ich = 0;

		fIcon->GetSize(icw, ich);
		if (icw > 0 && ich > 0) {
			// 计算给定范围较小值的一半
			UINT targetSize = std::min(bs.cx, bs.cy) / 2;

			// 计算宽高比
			float aspectRatio = static_cast<float>(icw) / ich;

			// 初始化新尺寸为原始尺寸
			p_data.iconW = icw;
			p_data.iconH = ich;

			// 检查是否需要缩放宽度
			if (p_data.iconW > targetSize) {
				p_data.iconW = targetSize;
				p_data.iconH = static_cast<UINT>(p_data.iconW / aspectRatio);
			}

			// 检查是否需要缩放高度
			if (p_data.iconH > targetSize) {
				p_data.iconH = targetSize;
				p_data.iconW = static_cast<UINT>(p_data.iconH * aspectRatio);
			}
		}
	}
}


LRESULT HHBUI::UIButton::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_LBUTTONDOWN)
	{
		SetState(state_down, FALSE);
		Redraw();
	}
	else if (uMsg == WM_LBUTTONUP)
	{
		SetState(state_down, TRUE);
		Redraw();
	}
	else if (uMsg == WM_MOUSEHOVER)
	{
		SetState(state_hover, FALSE);
		Redraw();
	}
	else if (uMsg == WM_MOUSELEAVE)
	{
		SetState(state_hover, TRUE);
		Redraw();
	}
	else if (uMsg == WM_EX_LCLICK || uMsg == BM_CLICK)
	{
		SetState(state_hover, TRUE);
		Redraw();
		if ((m_data.dwFlags & EOF_BMSGBOXCONTROL) == EOF_BMSGBOXCONTROL)
		{
			return EndDialog(hWnd, m_data.nID);
		}
		else
		{
			
		}
	}
	else if (uMsg == WM_DESTROY)
	{    
		// 析构函数中释放资源
		for (auto& img : p_data.config.imgBkg) {
			delete img;
		}
		delete p_data.hBrush;
	}
	return S_OK;
}

void HHBUI::UIButton::OnPaintProc(ps_context ps)
{
	INT sta = 0;
	if ((ps.dwState & state_hover) != 0) sta = 1;
	if ((ps.dwState & state_down) != 0 || (ps.dwState & state_disable) != 0) sta = 2;
	if (p_data.type != nobkg)
	{
		float rod = p_data.rad * ps.dpi;
		p_data.hBrush->SetColor(p_data.config.crBkg[sta]);
		ps.hCanvas->FillRoundRect(p_data.hBrush, 1.f, 1.f, ps.uWidth - 1.f, ps.uHeight - 1.f, rod);

		if (p_data.type == plain || p_data.style == normal) {
			p_data.hBrush->SetColor(p_data.config.crBorder[sta]);
			ps.hCanvas->DrawRoundRect(p_data.hBrush, 1.f, 1.f, ps.uWidth - 1.f, ps.uHeight - 1.f, rod, 1.f);
		}
	}
	std::wstring title = GetText();
	if (p_data.Icon) {
		if (p_data.type == circle) {
			float al = (ps.uWidth - p_data.iconW) / 2, at = (ps.uHeight - p_data.iconH) / 2;
			ps.hCanvas->DrawImageRect(p_data.Icon, al, at, al + p_data.iconW, at + p_data.iconH);
		}
		else {

			float tw = 0, th = 0;
			if (!title.empty()) {
				ps.hCanvas->CalcTextSize(ps.hFont, title.c_str(), ps.dwTextFormat, ps.uWidth - p_data.iconW, ps.uHeight - p_data.iconH, &tw, &th);
			}
			float aw = p_data.iconW + tw + 4;
			float al = (ps.uWidth - aw) / 2, at = (ps.uHeight - p_data.iconH) / 2;
			if (p_data.IconPosition == 0)
			{
				ps.hCanvas->DrawImageRect(p_data.Icon, al, at, al + p_data.iconW, at + p_data.iconH);
				if ((ps.dwState & state_down) != 0 && (ps.dwStyle & eos_textoffset) != 0)
					ps.rcText.Offset(1 * ps.dpi, 1 * ps.dpi);
				ps.hCanvas->DrawTextByColor(ps.hFont, title.c_str(), ps.dwTextFormat, p_data.iconW + 4, ps.rcText.top, ps.rcText.right, ps.rcText.bottom, p_data.config.crText[sta]);
			}
			else if (p_data.IconPosition == 1)
			{
				al = (ps.uWidth - p_data.iconW) / 2;
				at = (ps.uHeight - p_data.iconH) / 2 - p_data.iconH / 3;
				ps.hCanvas->DrawImageRect(p_data.Icon, al, at, al + p_data.iconW, at + p_data.iconH);
				ps.hCanvas->DrawTextByColor(ps.hFont, title.c_str(), ps.dwTextFormat, ps.rcText.left, ps.rcText.top + p_data.iconH / 1.2f, ps.rcText.right, ps.rcText.bottom, p_data.config.crText[sta]);
			}
		}
	}
	else if (p_data.type != circle && !title.empty()) {
		if ((ps.dwState & state_down) != 0 && (ps.dwStyle & eos_textoffset) != 0)
			ps.rcText.Offset(1 * ps.dpi, 1 * ps.dpi);
		ps.hCanvas->DrawTextByColor(ps.hFont, title.c_str(), ps.dwTextFormat, ps.rcText.left, ps.rcText.top, ps.rcText.right, ps.rcText.bottom, p_data.config.crText[sta]);
	}


}

