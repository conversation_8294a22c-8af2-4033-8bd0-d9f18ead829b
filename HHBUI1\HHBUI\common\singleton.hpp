﻿#pragma once

namespace HHBUI
{
	template <class T>
	class ExSingleton
	{
	public:
		static T* Instance()
		{
			static T s_instance;
			return &s_instance;
		}

		template<class... Args>
		static T* Instance(Args... args)
		{
			static T s_instance(args...);
			return &s_instance;
		}
	};

	///////////////////////

	template <class T>
	class ExLazySingleton
	{
	private:
		static T* s_instance;
	public:
		static bool SetInstance(T* instance)
		{
			bool is_null = s_instance == nullptr;
			if (is_null) { s_instance = instance; }
			return is_null;
		}

		static T* GetInstance()
		{
			if (s_instance == nullptr) { s_instance = new T(); }
			return s_instance;
		}

		template<class... Args>
		static T* GetInstance(Args... args)
		{
			if (s_instance == nullptr) { s_instance = new T(args...); }
			return s_instance;
		}

		static T* Instance() { return s_instance; }

		static T* DetachInstance() { T* temp = s_instance; s_instance = nullptr; return temp; }

		static void ClearInstance(bool delete_instance = true)
		{
			if (s_instance != nullptr)
			{
				if (delete_instance) { delete s_instance; }
				s_instance = nullptr;
			}
		}

	};

	template<class T>
	T* ExLazySingleton<T>::s_instance = nullptr;

}


