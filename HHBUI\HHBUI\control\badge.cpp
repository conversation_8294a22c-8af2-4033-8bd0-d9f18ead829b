﻿#include "pch.h"
#include "badge.h"


HHBUI::UIBadge::UIBadge(UIBase *hParent, INT x, INT y, INT width, INT height, INT nID)
{
	UIBase* Parent = nullptr;
	if (hParent->GetRawUIView())
	{
		auto Bind = (UIControl*)hParent->GetRawUIView();
		Bind->GetRect(p_data.bind);
		Parent = Bind->GetParent();
		p_data.BindUIView = hParent->GetRawUIView();
	}
	else
	{
		auto Bind = (UIWnd*)hParent->GetRawUIWindow();
		Bind->GetRect(p_data.bind);
		Parent = (UIWnd*)Bind->GetRawUIWindow();
	}
	InitSubControl(Parent, x, y, width, height, L"form-badge", 0, 0, 0, nID, Middle | Center | SingleLine);
	SetFontFromFamily(0, 11);
}

void HHBUI::UIBadge::SetColor(UIColor dwCrBkg, UIColor dwCrText)
{
	p_data.Color[0] = dwCrBkg;
	p_data.Color[1] = dwCrText;
}

void HHBUI::UIBadge::SetBadgeType(badge_type type)
{
	p_data.type = type;
	if (p_data.type == dot) {
		Move(p_data.bind.right - 14, p_data.bind.top - 13, 10, 10, TRUE);
	}
}

void HHBUI::UIBadge::EnableShowZero(BOOL enable)
{
	p_data.bszero = enable;
}

void HHBUI::UIBadge::SetMaxNumber(INT max)
{
	p_data.max = max;
	SetNumber(p_data.number);
}

void HHBUI::UIBadge::SetNumber(INT number)
{
	p_data.number = number;

	std::wstring tit = L"";
	if (p_data.max != 0 && p_data.number < p_data.max) tit = std::to_wstring(p_data.number).append(L"+").c_str();
	else tit = std::to_wstring(p_data.number).c_str();

	float tw = 0, th = 0;
	UICanvas::CalcTextSize(GetFont(), tit.c_str(), m_data.dwTextFormat, 9999, 9999, &tw, &th);
	if (p_data.number > p_data.max)
		tw += 6;
	Move(p_data.bind.right - (tw / 2) - 18, p_data.bind.top - (th / 2) - 7, tw + 5, th + 4, TRUE);

}


LRESULT HHBUI::UIBadge::OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam)
{
	if (uMsg == WM_SETTEXT)
	{
		float tw = 0, th = 0;
		UICanvas::CalcTextSize(GetFont(), m_data.pstrTitle, m_data.dwTextFormat, 9999, 9999, &tw, &th);
		Move(p_data.bind.right - (tw / 2) - 18, p_data.bind.top - (th / 2) - 7, tw + 6, th + 4, TRUE);
	}
	return S_OK;
}

void HHBUI::UIBadge::OnPaintProc(ps_context ps)
{
	UIBrush* br = new UIBrush(p_data.Color[0]);
	if (p_data.type == dot) {
		ps.hCanvas->DrawPoint(br, 3, 3, 5, TRUE);
	}
	else if (p_data.type == number) {
		if (p_data.number != 0 || p_data.bszero) {
			std::wstring tit = L"";
			if (p_data.max == 0 || p_data.number <= p_data.max) tit = std::to_wstring(p_data.number).c_str();
			else tit = std::to_wstring(p_data.max).append(L"+").c_str();

			ps.hCanvas->FillRoundRect(br, 0, 0, ps.uWidth, ps.uHeight, (float)std::min(ps.uHeight, ps.uWidth) / 2);
			ps.hCanvas->DrawTextByColor(ps.hFont, tit.c_str(), ps.dwTextFormat, 1, 0, ps.uWidth - 2, ps.uHeight - 2, p_data.Color[1]);
		}
	}
	else if (p_data.type == content) {
		if (m_data.pstrTitle || p_data.bszero) {
			ps.hCanvas->FillRoundRect(br, 0, 0, ps.uWidth, ps.uHeight, (float)std::min(ps.uHeight, ps.uWidth) / 2);
			ps.hCanvas->DrawTextByColor(ps.hFont, m_data.pstrTitle, ps.dwTextFormat, 1, 0, ps.uWidth - 2, ps.uHeight - 2, p_data.Color[1]);
		}
	}

	delete br;
}
