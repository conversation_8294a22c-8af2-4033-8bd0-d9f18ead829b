﻿#pragma once
namespace HHBUI
{
	class TOAPI UIItem : public UIControl
	{
	public:
		UIItem(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0,
			INT dwTextFormat = -1);
	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
		EXMETHOD void OnPaintProc(ps_context ps) override;
		UIBrush* m_hBrush = nullptr;
	};
}
