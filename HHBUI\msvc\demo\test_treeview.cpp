﻿#include "hhbui.h"

using namespace HHBUI;
LRESULT CALLBACK OnButton_treeview_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (nCode == WMM_CLICK)
	{
		auto treeview = (UITreeView*)window->FindUIView(L"2000");
		if (nID == 2001)
		{
			INT Count = treeview->GetItemCount() + 1;
			auto str = L"第" + std::to_wstring(Count) + L"项";
			info_tree_nodeitem* itemParent = nullptr;
			treeview->GetIndexItem(treeview->GetSelect(), itemParent);
			treeview->InsertItem(itemParent, 0, Count, str.c_str(), 0, 0, 0, TRUE, {}, 0);
		}
		else if (nID == 2002)
		{
			treeview->DeleteItem(treeview->GetSelect());
		}
		else if (nID == 2003)
		{
			INT Index = treeview->GetSelect();
			treeview->Expand(Index, !treeview->IsExpand(Index));
		}
	}
	else if (nCode == WMM_CHECK)
	{
		auto treeview = (UITreeView*)window->FindUIView(L"2000");
		treeview->Style(eos_elvs_allowmultiple);
		treeview->Redraw();
	}
	return S_OK;
}
void testtreeview(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 700, 500, L"hello Treeview", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto treeview = new UITreeView(window, 50, 50, 300, 400, 
		eos_tree_showaddandsub | eos_tree_showcable | eos_scroll_v | eos_scroll_h, 0, 2000, Middle | Left);
	treeview->SetColor(color_background, UIColor(L"#ebefed"));
	info_tree_nodeitem* itemParent;
	for (int a = 1; a <= 20; a++)
	{
		auto str = L"第" + std::to_wstring(a) + L"项";
		treeview->InsertItem(0, 0, a, str.c_str(), 0, 0, 0, 0, {}, 0);
	}
	treeview->GetIdItem(1, itemParent);
	INT Count = treeview->GetItemCount();
	for (int a = 1; a <= 20; a++)
	{
		auto str = L"第" + std::to_wstring(Count + a) + L"项";
		treeview->InsertItem(itemParent, 0, Count + a, str.c_str(), 0, 0, 0, 0, {}, 0);
	}
	treeview->Update();


	auto btn1 = new UIButton(window, 370, 50, 100, 30, L"插入节点", 0, 0, 2001);
	btn1->SetStyle(fill, primary);
	btn1->SetEvent(WMM_CLICK, OnButton_treeview_Event);
	auto btn2 = new UIButton(window, 370, 100, 100, 30, L"删除节点", 0, 0, 2002);
	btn2->SetStyle(fill, primary);
	btn2->SetEvent(WMM_CLICK, OnButton_treeview_Event);
	auto btn3 = new UIButton(window, 370, 150, 100, 30, L"展开/收缩", 0, 0, 2003);
	btn3->SetStyle(fill, primary);
	btn3->SetEvent(WMM_CLICK, OnButton_treeview_Event);

	auto bCheck = new UICheck(window, 480, 50, 120, 20, L"允许多选");
	bCheck->SetEvent(WMM_CHECK, OnButton_treeview_Event);

	window->Show();
	//window->MessageLoop();
}