﻿#pragma once
#define EASING_PI (3.14159265358979323846)

namespace HHBUI
{
	EXENUM(AniEffect)
	{
		Default = 0,///< 默认

		Quadratic_In,///< 二次渐变-进入
		Quadratic_Out,///< 二次渐变-退出
		Quadratic_InOut,///< 二次渐变-进入退出

		Sinusoidal_In,///< 正弦渐变-进入
		Sinusoidal_Out,///< 正弦渐变-退出
		Sinusoidal_InOut,///< 正弦渐变-进入退出

		Exponential_In,///< 指数渐变-进入
		Exponential_Out,///< 指数渐变-退出
		Exponential_InOut,///< 指数渐变-进入退出

		Circular_In,///< 圆曲线-进入
		Circular_Out,///< 圆曲线-退出
		Circular_InOut,///< 圆曲线-进入退出

		Cubic_In,///< 三次方-进入
		Cubic_Out,///< 三次方-退出
		Cubic_InOut,///< 三次方-进入退出

		Quartic_In,///< 四次方-进入
		Quartic_Out,///< 四次方-退出
		Quartic_InOut,///< 四次方-进入退出

		Quintic_In,///< 五次方-进入
		Quintic_Out,///< 五次方-退出
		Quintic_InOut,///< 五次方-进入退出

		Elastic_In,///< 指数衰减正弦曲线-进入
		Elastic_Out,///< 指数衰减正弦曲线-退出
		Elastic_InOut,///< 指数衰减正弦曲线-进入退出

		Back_In,///< 回退-进入
		Back_Out,///< 回退-退出
		Back_InOut,///< 回退-进入退出

		Bounce_In,///< 弹性-进入
		Bounce_Out,///< 弹性-退出
		Bounce_InOut///< 弹性-进入退出
	};
	struct info_Animation
	{
		INT nIndex;         //  当前任务ID
		BOOL nIsEnd;        //  任务是否结束
		BOOL nInit;         //是否开始初始化 一个任务只初始化一次
		DOUBLE nProgress;   //  进度[0-1]
		DOUBLE nCurrentX;   //  当前值X
		DOUBLE nCurrentY;	//  当前值Y
		LONG_PTR param1;    //  参数1
		LONG_PTR param2;    //  参数2
		LONG_PTR param3;    //  参数3
		LONG_PTR param4;    //  参数4
	};
	class TOAPI UIAnimation
	{
	public:
	  /*开始动画
	  * @param BeginX、BeginY - 开始位置
	  * @param EndX、EndY - 结束位置
	  * @param Effect - 缓动效果 参考AniEffect
	  * @param Frame - 帧数 5-100
	  * @param wait - 是否等待缓动结束
	  * @param num_iterations - 循环次数
	  * @param alternate_direction 迭代之间 一个控件中需要连续动画可打开此功能 此时当前动画如未结束则该动画结束点将被替换。
	  * @param alternate_Begin 迭代之间从新开始[未开启此状态默认BeginX、BeginY为上一个结束点]
	  * @param pfnSub 动画事件回调 挂接此事件控件消息将会失效
	  * @param param1-4 - 附加参数
	  * @return bool
	  */
		static BOOL Start(UIBase *hParent, INT BeginX, INT EndX, INT BeginY = 0, INT EndY = 0, DWORD Effect = AniEffect::Default, INT Frame = 40, BOOL wait = FALSE,
			INT num_iterations = 1, BOOL alternate_direction = FALSE, BOOL alternate_Begin = FALSE, AnimationPROC pfnSub = 0, LPARAM param1 = 0, LPARAM param2 = 0, LPARAM param3 = 0, LPARAM param4 = 0);


	protected:
		struct AniThreadParam
		{
			AnimationPROC pfnSub = 0;
			LPVOID handle = nullptr;
			INT BeginX = 0;//开始位置
			INT BeginY = 0;
			INT EndX = 0;//结束位置
			INT EndY = 0;
			INT Frame = 0;//帧数
			DWORD Effect = 0;//效果
			INT num_iterations = 0;
			BOOL alternate_direction = 0;
			LPARAM param1 = 0;
			LPARAM param2 = 0;
			LPARAM param3 = 0;
			LPARAM param4 = 0;
		};
		static void AniThread(AniThreadParam *param);
		static float EquationAnimation(int Begin, int End, float Percents, int Type);
	};
}
