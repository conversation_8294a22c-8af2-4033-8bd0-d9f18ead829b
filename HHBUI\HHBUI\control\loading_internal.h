﻿#pragma once
#include <immintrin.h>
#define IM_PI                           3.14159265358979323846f
#define IM_ARRAYSIZE(_ARR)          ((int)(sizeof(_ARR) / sizeof(*(_ARR))))
#define ImFabs(X)           fabsf(X)
#define ImSqrt(X)           sqrtf(X)
#define ImFmod(X, Y)        fmodf((X), (Y))
#define ImCos(X)            cosf(X)
#define ImSin(X)            sinf(X)
#define ImAcos(X)           acosf(X)
#define ImAtan2(Y, X)       atan2f((Y), (X))
#define ImAtof(STR)         atof(STR)
#define ImCeil(X)           ceilf(X)

using float_ptr = float*;
constexpr float PI_DIV_4 = IM_PI / 4.f;
constexpr float PI_DIV_2 = IM_PI / 2.f;
constexpr float PI_2 = IM_PI * 2.f;
template<class T> constexpr float PI_DIV(T d) { return IM_PI / (float)d; }
template<class T> constexpr float PI_2_DIV(T d) { return PI_2 / (float)d; }
template<typename T> static inline T ImMin(T lhs, T rhs) { return lhs < rhs ? lhs : rhs; }
template<typename T> static inline T ImMax(T lhs, T rhs) { return lhs >= rhs ? lhs : rhs; }
template<typename T> static inline T ImClamp(T v, T mn, T mx) { return (v < mn) ? mn : (v > mx) ? mx : v; }

static inline float  ImPow(float x, float y) { return powf(x, y); }          // DragBehaviorT/SliderBehaviorT uses ImPow with either float/double and need the precision
static inline double ImPow(double x, double y) { return pow(x, y); }
static inline float  ImLog(float x) { return logf(x); }             // DragBehaviorT/SliderBehaviorT uses ImLog with either float/double and need the precision
static inline double ImLog(double x) { return log(x); }
static inline int    ImAbs(int x) { return x < 0 ? -x : x; }
static inline float  ImAbs(float x) { return fabsf(x); }
static inline double ImAbs(double x) { return fabs(x); }
static inline float  ImSign(float x) { return (x < 0.0f) ? -1.0f : (x > 0.0f) ? 1.0f : 0.0f; } // Sign operator - returns -1, 0 or 1 based on sign of argument
static inline double ImSign(double x) { return (x < 0.0) ? -1.0 : (x > 0.0) ? 1.0 : 0.0; }

static inline float  ImRsqrt(float x) { return _mm_cvtss_f32(_mm_rsqrt_ss(_mm_set_ss(x))); }
static inline double ImRsqrt(double x) { return 1.0 / sqrt(x); }
inline float damped_spring(float mass, float stiffness, float damping, float time, float a = PI_DIV_2, float b = PI_DIV_2) {
    float omega = ImSqrt(stiffness / mass);
    float alpha = damping / (2 * mass);
    float exponent = std::exp(-alpha * time);
    float cosTerm = ImCos(omega * ImSqrt(1 - alpha * alpha) * time);
    float result = exponent * cosTerm;
    return ((result *= a) + b);
};

inline float damped_gravity(float limtime) {
    float time = 0.0f, initialHeight = 10.f, height = initialHeight, velocity = 0.f, prtime = 0.0f;

    while (height >= 0.0) {
        if (prtime >= limtime) { return height / 10.f; }
        time += 0.01f; prtime += 0.01f;
        height = initialHeight - 0.5 * 9.81f * time * time;
        if (height < 0.0) { initialHeight = 0.0; time = 0.0; }
    }
    return 0.f;
}

inline float damped_trifolium(float limtime, float a = 0.f, float b = 1.f) {
    return a * ImSin(limtime) - b * ImSin(3 * limtime);
}

inline float damped_inoutelastic(float t, float amplitude, float period) {
    if (t == 0) return 0;
    t *= 2;
    if (t == 2) return 1;

    float s;
    if (amplitude < 1) {
        amplitude = 1;
        s = period / 4;
    }
    else {
        s = period / (2 * IM_PI) * std::asin(1 / amplitude);
    }

    if (t < 1) return -0.5f * (amplitude * ImPow(2.0f, 10.f * (t - 1.f)) * ImSin((t - 1.f - s) * (2.f * IM_PI) / period));
    return amplitude * ImPow(2.0f, -10 * (t - 1)) * ImSin((t - 1.f - s) * (2.f * IM_PI) / period) * 0.5f + 1.f;
}

inline std::pair<float, float> damped_infinity(float t, float a) {
    return std::make_pair((a * ImCos(t)) / (1 + (powf(ImSin(t), 2.0f))),
        (a * ImSin(t) * ImCos(t)) / (1 + (powf(ImSin(t), 2.0f))));
};

inline float ease_inquad(float time) { return time * time; }
inline float ease_outquad(float time) { return time * (2.f - time); }
inline float ease_inoutquad(float t) { if (t < 0.5f) { return 2 * t * t; } else { return -1 + (4 - 2 * t) * t; } }
inline float ease_inoutquad(float* p) { float tr = ImMax(ImSin(p[0]) - 0.5f, 0.f) * (p[1] * 0.5f); return ease_inoutquad(tr); }
inline float ease_outcubic(float t) { float ft = t - 1; return ft * ft * ft + 1; }
inline float ease_inexpo(float t) { return t == 0 ? 0 : pow(2, 10 * (t - 1)); }
inline float ease_inoutexpo(float t) { if (t == 0) return 0; if (t == 1) return 1; if (t < 0.5f) return 0.5f * pow(2, (20 * t) - 10); return 0.5f * (2 - pow(2, -20 * t + 10)); }
inline float ease_inoutexpo(float* p) { float tr = ImMax(ImSin(p[0]) - 0.5f, 0.f) * (p[1] * 0.4f); return ease_inoutexpo(tr) * (p[1] * 0.3f); }
inline float ease_spring(float* p) { return damped_spring(1, 10.f, 1.0f, ImSin(ImFmod(p[0], p[1])), p[2], p[3]); }
inline float ease_gravity(float* p) { return damped_gravity(p[0]); }
inline float ease_infinity(float* p) { return damped_infinity(p[0], p[1]).second; }
inline float ease_inoutelastic(float* p) { return damped_inoutelastic(p[1], p[2], p[3]); }

enum ease_mode {
    e_ease_none = 0,
    e_ease_inoutquad = 1,
    e_ease_inoutexpo = 2,
    e_ease_spring = 3,
    e_ease_gravity = 4,
    e_ease_infinity = 5,
    e_ease_elastic = 6,
};

template<typename ... Args>
inline float ease(ease_mode mode, Args ... args) {
    static_assert((std::is_same_v<Args, float> && ...), "All arguments should be of type float");
    float params[] = { args... };
    switch (mode) {
    case e_ease_inoutquad: return ease_inoutquad(params);
    case e_ease_inoutexpo: return ease_inoutexpo(params);
    case e_ease_spring: return ease_spring(params);
    case e_ease_gravity: return ease_gravity(params);
    case e_ease_infinity: return ease_infinity(params);
    case e_ease_elastic: return ease_inoutelastic(params);
    case e_ease_none: return (0.f);
    }
    return 0.f;
}
struct ImVec2
{
    float                                   x, y;
    constexpr ImVec2() : x(0.0f), y(0.0f) {}
    constexpr ImVec2(float _x, float _y) : x(_x), y(_y) {}
    float& operator[] (size_t idx) { _ASSERT(idx == 0 || idx == 1); return ((float*)(void*)(char*)this)[idx]; } // We very rarely use this [] operator, so the assert overhead is fine.
    float  operator[] (size_t idx) const { _ASSERT(idx == 0 || idx == 1); return ((const float*)(const void*)(const char*)this)[idx]; }
#ifdef IM_VEC2_CLASS_EXTRA
    IM_VEC2_CLASS_EXTRA     // Define additional constructors and implicit cast operators in imconfig.h to convert back and forth between your math types and ImVec2.
#endif
};
// Helper: ImVec2ih (2D vector, half-size integer, for long-term packed storage)
struct ImVec2ih
{
    short   x, y;
    constexpr ImVec2ih() : x(0), y(0) {}
    constexpr ImVec2ih(short _x, short _y) : x(_x), y(_y) {}
    constexpr explicit ImVec2ih(const ImVec2& rhs) : x((short)rhs.x), y((short)rhs.y) {}
};

#ifndef IM_DRAWLIST_ARCFAST_TABLE_SIZE
#define IM_DRAWLIST_ARCFAST_TABLE_SIZE                          48 // Number of samples in lookup table.
#endif
#define IM_DRAWLIST_ARCFAST_SAMPLE_MAX                          IM_DRAWLIST_ARCFAST_TABLE_SIZE // Sample index _PathArcToFastEx() for 360 angle.
#define IM_ROUNDUP_TO_EVEN(_V)                                  ((((_V) + 1) / 2) * 2)
#define IM_DRAWLIST_CIRCLE_AUTO_SEGMENT_MIN                     4
#define IM_DRAWLIST_CIRCLE_AUTO_SEGMENT_MAX                     512
#define IM_DRAWLIST_CIRCLE_AUTO_SEGMENT_CALC(_RAD,_MAXERROR)    ImClamp(IM_ROUNDUP_TO_EVEN((int)ImCeil(IM_PI / ImAcos(1 - ImMin((_MAXERROR), (_RAD)) / (_RAD)))), IM_DRAWLIST_CIRCLE_AUTO_SEGMENT_MIN, IM_DRAWLIST_CIRCLE_AUTO_SEGMENT_MAX)
