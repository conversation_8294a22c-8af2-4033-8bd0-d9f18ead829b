﻿#include "hhbui.h"

using namespace HHBUI;

LRESULT CALLBACK Ontoast_Event(LPVOID pWnd, LPVOID UIView, INT nID, INT nCode, WPARAM wParam, LPARAM lParam)
{
	auto window = (UIWnd*)pWnd;
	auto obj = (UIControl*)UIView;
	if (nCode == WMM_CLICK)
	{
		INT rel = 0;
		if (nID == 1000)
		{
			rel = window->PopupMsg(L"默认信息框 只有确认", L"提示：", MB_OK | MB_ICONQUESTION);
		}
		else if (nID == 1001)
		{
			rel = window->PopupMsg(L"我带是、否按钮", L"提示：", MB_YESNO | MB_ICONQUESTION);
		}
		else if (nID == 1002)
		{
			static BOOL fChecked = FALSE;
			if (!fChecked)
				rel = window->PopupMsg(L"我拥有不再提示功能", L"提示：", MB_YESNO | MB_ICONQUESTION, L"不再提示", &fChecked);
			output(L"fChecked:", fChecked);
		}
		else if (nID == 1003)
		{
			rel = window->PopupMsg(L"我自动计时5秒关闭", L"提示：", MB_CANCELTRYCONTINUE | MB_ICONQUESTION, NULL, 0, styleex_centewindow, {}, {}, 0, 5000);
		}
		else if (nID == 1004)
		{
			window->PopupToast(L"只有流过血的手指，才能弹出世间的绝唱。 ——泰戈尔", Toast_info | Toast_type_center);
		}
		else if (nID == 1005)
		{
			window->PopupToast(L"当我们真正热爱这世界时，我们才真正生活在这世上。 ——泰戈尔", Toast_error | Toast_type_center);
		}
		else if (nID == 1006)
		{
			window->PopupToast(L"幸福比傲慢更容易蒙住人的眼睛。 ——大仲马", Toast_enquire | Toast_type_center);
		}
		else if (nID == 1007)
		{
			window->PopupToast(L"一条路并不因为它路边长满荆棘而丧失其美丽，旅行者照旧向前进😔。 ——罗曼·罗兰", Toast_success | Toast_type_center);
		}
		else if (nID == 1008)
		{
			window->PopupToast(L"接受每一个人的批评，可是保留你自己的判断❤。 ——莎士比亚", Toast_warning | Toast_type_center);
		}
		//output(L"rel:", rel);
	}
	return S_OK;
}
void testtoast(HWND hWnd)
{
	auto window = new UIWnd(0, 0, 700, 500, L"hello Toast", 0, 0, UISTYLE_BTN_CLOSE | UISTYLE_BTN_MIN | UISTYLE_BTN_MAX |
		UISTYLE_CENTERWINDOW | UISTYLE_ESCEXIT | UISTYLE_TITLE | UISTYLE_SIZEABLE | UISTYLE_BTN_HELP | UISTYLE_MOVEABLE, hWnd);


	window->SetBackgColor(UIColor(253, 253, 255, 255));
	window->SetBorderColor(UIColor(20, 126, 255, 255));
	window->SetShadowColor(UIColor(20, 126, 255, 155));
	window->SetRadius(10);

	auto btn1 = new UIButton(window, 50, 70, 100, 36, L"PopupMsg", 0, 0, 1000);
	btn1->SetEvent(WMM_CLICK, Ontoast_Event);

	auto btn2 = new UIButton(window, 170, 70, 150, 36, L"PopupMsg YesNo", 0, 0, 1001);
	btn2->SetEvent(WMM_CLICK, Ontoast_Event);

	auto btn3 = new UIButton(window, 340, 70, 150, 36, L"PopupMsg Check", 0, 0, 1002);
	btn3->SetEvent(WMM_CLICK, Ontoast_Event);

	auto btn4 = new UIButton(window, 510, 70, 150, 36, L"PopupMsg delayed", 0, 0, 1003);
	btn4->SetEvent(WMM_CLICK, Ontoast_Event);

	auto btn5 = new UIButton(window, 50, 120, 150, 36, L"PopupToast info", 0, 0, 1004);
	btn5->SetEvent(WMM_CLICK, Ontoast_Event);
	btn5->SetStyle(fill, info);

	auto btn6 = new UIButton(window, 50, 170, 150, 36, L"PopupToast error", 0, 0, 1005);
	btn6->SetEvent(WMM_CLICK, Ontoast_Event);
	btn6->SetStyle(fill, danger);

	auto btn7 = new UIButton(window, 50, 220, 150, 36, L"PopupToast enquire", 0, 0, 1006);
	btn7->SetEvent(WMM_CLICK, Ontoast_Event);
	btn7->SetStyle(fill, primary);

	auto btn8 = new UIButton(window, 50, 270, 150, 36, L"PopupToast success", 0, 0, 1007);
	btn8->SetEvent(WMM_CLICK, Ontoast_Event);
	btn8->SetStyle(fill, success);

	auto btn9 = new UIButton(window, 50, 320, 150, 36, L"PopupToast warning", 0, 0, 1008);
	btn9->SetEvent(WMM_CLICK, Ontoast_Event);
	btn9->SetStyle(fill, warning);

	window->Show();
	//window->MessageLoop();
}