﻿#pragma once
#include <windows.h>
#include <vector>

namespace ResDLL {
    // 资源ID定义，与resource_dll/resource.h中的定义一致
    // 管理员图标
    constexpr UINT IDP_ADMIN_48 = 1001;
    constexpr UINT IDP_ADMIN_96 = 1002;

    // 动画图标
    constexpr UINT IDP_ANIMATION_96 = 1101;

    // 背景图
    constexpr UINT IDP_BG_EVA = 1201;
    constexpr UINT IDP_BG_ANIME = 1202;
    constexpr UINT IDP_BG_FUJI = 1203;

    // 主页图标
    constexpr UINT IDP_HOME_48 = 1301;
    constexpr UINT IDP_HOME_96 = 1302;
    constexpr UINT IDP_HOME_144 = 1303;
    constexpr UINT IDP_HOME_240 = 1304;

    // 锁图标
    constexpr UINT IDP_LOCK_48 = 1401;
    constexpr UINT IDP_LOCK_96 = 1402;
    constexpr UINT IDP_LOCK_100 = 1403;
    constexpr UINT IDP_LOCK_144 = 1404;
    constexpr UINT IDP_LOCK_240 = 1405;

    // 解锁图标
    constexpr UINT IDP_UNLOCK_48 = 1501;
    constexpr UINT IDP_UNLOCK_96 = 1502;
    constexpr UINT IDP_UNLOCK_100 = 1503;
    constexpr UINT IDP_UNLOCK_144 = 1504;
    constexpr UINT IDP_UNLOCK_240 = 1505;

    // 登录图标
    constexpr UINT IDG_LOGIN = 1601;
    constexpr UINT IDP_LOGIN_100 = 1602;

    // Logo图标
    constexpr UINT IDP_LOGO_60 = 1701;
    constexpr UINT IDP_LOGO_96 = 1702;

    // 函数声明
    bool LoadResourceDLL();
    void UnloadResourceDLL();
    bool LoadPngFromResourceDLL(UINT resourceID, std::vector<BYTE>& imageData);
    bool LoadGifFromResourceDLL(UINT resourceID, std::vector<BYTE>& imageData);
} 