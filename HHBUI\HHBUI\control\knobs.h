﻿#pragma once
namespace HHBUI
{
	enum info_KnobFlags {
        KnobFlags_Normal =0,
		KnobFlags_NoTitle = 1 << 0,
		KnobFlags_NoInput = 1 << 1,
		KnobFlags_DragHorizontal = 1 << 3,
		KnobFlags_DragVertical = 1 << 4,

        //事件
        WMM_KNOB_VALUE = -155
	};

	enum info_KnobVariant {
		KnobVariant_Tick = 1 << 0,
		KnobVariant_Dot = 1 << 1,
		KnobVariant_Wiper = 1 << 2,
		KnobVariant_WiperOnly = 1 << 3,
		KnobVariant_WiperDot = 1 << 4,
		KnobVariant_Stepped = 1 << 5,
		KnobVariant_Space = 1 << 6,
	};
	class TOAPI UIKnobs : public UIControl
	{
	public:
		UIKnobs(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
        //设置参数 小数
        void SetKnob(
            float p_value,
            float v_min,
            float v_max,
            float speed = 0,
            LPCWSTR format = L"%.3f",
            int variant = KnobVariant_Tick,
            int flags = 0,
            float steps = 1.f,
            int step_size = 10, // variant = KnobVariant_Stepped 有效
            float angle_min = -1,
            float angle_max = -1);
        //设置参数 整数
        void SetKnobInt(
            int p_value,
            int v_min,
            int v_max,
            float speed = 0,
            LPCWSTR format = L"%i",
            int variant = KnobVariant_Tick,
            int flags = 0,
            float steps = 1.f,
            int step_size = 10, // variant = KnobVariant_Stepped 有效
            float angle_min = -1,
            float angle_max = -1);

        //获取进度
        float GetValue();
        //设置进度
        void SetValue(float p_value);
        //设置颜色
        void SetColorV(UIColor base, UIColor hovered, UIColor active);





	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
        EXMETHOD void OnPaintProc(ps_context ps) override;
        UIColor GetSecondaryColorSet(bool is_active, bool is_hovered);
        UIColor GetPrimaryColorSet(bool is_active, bool is_hovered, bool is_a);


        void draw_dot(ps_context ps, float size, float radius, float angle, bool filled, int segments, float is_radius, D2D1_POINT_2F is_center, UIColor color);
        void draw_tick(ps_context ps, float start, float end, float width, float angle, float is_radius, D2D1_POINT_2F is_center, UIColor color);
        void draw_circle(ps_context ps, float size, bool filled, int segments, float is_radius, D2D1_POINT_2F is_center, UIColor color);
        void draw_arc(ps_context ps, float radius, float size, float start_angle, float end_angle, float is_radius, D2D1_POINT_2F is_center, UIColor color);
        void draw_arc_i(ps_context ps, float radius, float start_angle, float end_angle, float thickness, D2D1_POINT_2F is_center, UIColor color);
        void knob_with_drag(ps_context ps, float& is_radius, D2D1_POINT_2F& is_center, bool& is_active, bool& is_hovered, float& is_angle_min, float& is_angle_max,
            float& is_t, float& is_angle, float& is_angle_cos, float& is_angle_sin);

        template <typename T>
        void SetKnobInternal(
            T p_value,
            T v_min,
            T v_max,
            float speed = 0,
            LPCWSTR format = L"%.3f",
            int variant = KnobVariant_Tick,
            int flags = 0,
            float steps = 1.f,
            int step_size = 10,
            float angle_min = -1,
            float angle_max = -1)
        {
            p_data.p_value = p_value;
            p_data.v_min = v_min;
            p_data.v_max = v_max;
            p_data.speed = speed;
            p_data.format = format;
            p_data.variant = variant;
            p_data.flags = flags;
            p_data.steps = steps;
            p_data.step_size = step_size;
            p_data.angle_min = angle_min;
            p_data.angle_max = angle_max;
        }
		struct knobs_s
		{
            int variant = KnobVariant_Tick, flags = 0, start_pos = 0, step_size = 10;
            float p_value = 0.f, start_value = 0.f, v_min = 0.f, v_max = 0.f, speed = 0.f, angle_min = -1, angle_max = -1, steps = 1.f;
            LPCWSTR format = L"%i";
            UIBrush* hBrush = nullptr;
            UIColor base, hovered, active;
		}p_data;

	};
}
